{"name": "flywheel-media-temp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.552.0", "next": "16.0.1", "react": "19.2.0", "react-dom": "19.2.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "16.0.1", "tailwindcss": "^4", "tw-animate-css": "^1.4.0", "typescript": "^5"}}