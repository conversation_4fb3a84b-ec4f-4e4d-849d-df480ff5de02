/**
 * @license lucide-react v0.552.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M17 5H3", key: "1cn7zz" }],
  ["path", { d: "M21 12H8", key: "scolzb" }],
  ["path", { d: "M21 19H8", key: "13qgcb" }],
  ["path", { d: "M3 12v7", key: "1ri8j3" }]
];
const TextQuote = createLucideIcon("text-quote", __iconNode);

export { __iconNode, TextQuote as default };
//# sourceMappingURL=text-quote.js.map
