/**
 * @license lucide-react v0.552.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M11 2h2", key: "isr7bz" }],
  ["path", { d: "m14.28 14-4.56 8", key: "4anwcf" }],
  ["path", { d: "m21 22-1.558-4H4.558", key: "enk13h" }],
  ["path", { d: "M3 10v2", key: "w8mti9" }],
  [
    "path",
    {
      d: "M6.245 15.04A2 2 0 0 1 8 14h12a1 1 0 0 1 .864 1.505l-3.11 5.457A2 2 0 0 1 16 22H4a1 1 0 0 1-.863-1.506z",
      key: "pouggg"
    }
  ],
  ["path", { d: "M7 2a4 4 0 0 1-4 4", key: "78s8of" }],
  ["path", { d: "m8.66 7.66 1.41 1.41", key: "1vaqj8" }]
];
const SolarPanel = createLucideIcon("solar-panel", __iconNode);

export { __iconNode, SolarPanel as default };
//# sourceMappingURL=solar-panel.js.map
