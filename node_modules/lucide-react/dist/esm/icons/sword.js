/**
 * @license lucide-react v0.552.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "m11 19-6-6", key: "s7kpr" }],
  ["path", { d: "m5 21-2-2", key: "1kw20b" }],
  ["path", { d: "m8 16-4 4", key: "1oqv8h" }],
  ["path", { d: "M9.5 17.5 21 6V3h-3L6.5 14.5", key: "pkxemp" }]
];
const Sword = createLucideIcon("sword", __iconNode);

export { __iconNode, Sword as default };
//# sourceMappingURL=sword.js.map
