{"version": 3, "sources": ["../../../src/server/app-render/types.ts"], "sourcesContent": ["import type { LoadComponentsReturnType } from '../load-components'\nimport type { ServerRuntime, SizeLimit } from '../../types'\nimport type {\n  ExperimentalConfig,\n  NextConfigComplete,\n} from '../../server/config-shared'\nimport type { ClientReferenceManifest } from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { NextFontManifest } from '../../build/webpack/plugins/next-font-manifest-plugin'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { ImageConfigComplete } from '../../shared/lib/image-config'\nimport type { __ApiPreviewProps } from '../api-utils'\n\nimport s from 'next/dist/compiled/superstruct'\nimport type { RequestLifecycleOpts } from '../base-server'\nimport type { InstrumentationOnRequestError } from '../instrumentation/types'\nimport type { NextRequestHint } from '../web/adapter'\nimport type { BaseNextRequest } from '../base-http'\nimport type { IncomingMessage } from 'http'\nimport type { RenderResumeDataCache } from '../resume-data-cache/resume-data-cache'\nimport type { ServerCacheStatus } from '../../next-devtools/dev-overlay/cache-indicator'\n\nconst dynamicParamTypesSchema = s.enums(['c', 'ci', 'oc', 'd', 'di'])\n\nconst segmentSchema = s.union([\n  s.string(),\n\n  s.tuple([\n    // Param name\n    s.string(),\n    // Param cache key (almost the same as the value, but arrays are\n    // concatenated into strings)\n    // TODO: We should change this to just be the value. Currently we convert\n    // it back to a value when passing to useParams. It only needs to be\n    // a string when converted to a a cache key, but that doesn't mean we\n    // need to store it as that representation.\n    s.string(),\n    // Dynamic param type\n    dynamicParamTypesSchema,\n  ]),\n])\n\n// unfortunately the tuple is not understood well by Describe so we have to\n// use any here. This does not have any impact on the runtime type since the validation\n// does work correctly.\nexport const flightRouterStateSchema: s.Describe<any> = s.tuple([\n  segmentSchema,\n  s.record(\n    s.string(),\n    s.lazy(() => flightRouterStateSchema)\n  ),\n  s.optional(s.nullable(s.string())),\n  s.optional(\n    s.nullable(\n      s.union([\n        s.literal('refetch'),\n        s.literal('refresh'),\n        s.literal('inside-shared-layout'),\n        s.literal('metadata-only'),\n      ])\n    )\n  ),\n  s.optional(s.boolean()),\n])\n\nexport type ServerOnInstrumentationRequestError = (\n  error: unknown,\n  // The request could be middleware, node server or web server request,\n  // we normalized them into an aligned format to `onRequestError` API later.\n  request: NextRequestHint | BaseNextRequest | IncomingMessage,\n  errorContext: Parameters<InstrumentationOnRequestError>[2]\n) => void | Promise<void>\n\nexport interface RenderOptsPartial {\n  dir?: string\n  previewProps: __ApiPreviewProps | undefined\n  err?: Error | null\n  dev?: boolean\n  basePath: string\n  cacheComponents: boolean\n  trailingSlash: boolean\n  images: ImageConfigComplete\n  clientReferenceManifest?: DeepReadonly<ClientReferenceManifest>\n  supportsDynamicResponse: boolean\n  runtime?: ServerRuntime\n  serverComponents?: boolean\n  enableTainting?: boolean\n  assetPrefix?: string\n  crossOrigin?: '' | 'anonymous' | 'use-credentials' | undefined\n  nextFontManifest?: DeepReadonly<NextFontManifest>\n  botType?: 'dom' | 'html' | undefined\n  serveStreamingMetadata?: boolean\n  incrementalCache?: import('../lib/incremental-cache').IncrementalCache\n  cacheLifeProfiles?: {\n    [profile: string]: import('../use-cache/cache-life').CacheLife\n  }\n  isOnDemandRevalidate?: boolean\n  isPossibleServerAction?: boolean\n  setCacheStatus?: (\n    status: ServerCacheStatus,\n    htmlRequestId: string,\n    requestId: string\n  ) => void\n  setIsrStatus?: (key: string, value: boolean | undefined) => void\n  setReactDebugChannel?: (\n    debugChannel: { readable: ReadableStream<Uint8Array> },\n    htmlRequestId: string,\n    requestId: string\n  ) => void\n  nextExport?: boolean\n  nextConfigOutput?: 'standalone' | 'export'\n  onInstrumentationRequestError?: ServerOnInstrumentationRequestError\n  isDraftMode?: boolean\n  deploymentId?: string\n  onUpdateCookies?: (cookies: string[]) => void\n  loadConfig?: (\n    phase: string,\n    dir: string,\n    customConfig?: object | null,\n    rawConfig?: boolean,\n    silent?: boolean\n  ) => Promise<NextConfigComplete>\n  serverActions?: {\n    bodySizeLimit?: SizeLimit\n    allowedOrigins?: string[]\n  }\n  params?: ParsedUrlQuery\n  isPrefetch?: boolean\n  htmlLimitedBots: string | undefined\n  experimental: {\n    /**\n     * When true, it indicates that the current page supports partial\n     * prerendering.\n     */\n    isRoutePPREnabled?: boolean\n    expireTime: number | undefined\n    staleTimes: ExperimentalConfig['staleTimes'] | undefined\n    clientTraceMetadata: string[] | undefined\n    clientSegmentCache: boolean | 'client-only'\n\n    /**\n     * The origins that are allowed to write the rewritten headers when\n     * performing a non-relative rewrite. When undefined, no non-relative\n     * rewrites will get the rewrite headers.\n     */\n    clientParamParsingOrigins: string[] | undefined\n    dynamicOnHover: boolean\n    inlineCss: boolean\n    authInterrupts: boolean\n  }\n  postponed?: string\n\n  /**\n   * Should wait for react stream allReady to resolve all suspense boundaries,\n   * in order to perform a full page render.\n   */\n  shouldWaitOnAllReady?: boolean\n\n  /**\n   * A prefilled resume data cache. This was either generated for this page\n   * during dev warmup, or when a page with defined params was previously\n   * prerendered, and now its matching optional fallback shell is prerendered.\n   */\n  renderResumeDataCache?: RenderResumeDataCache\n\n  /**\n   * When true, the page will be rendered using the static rendering to detect\n   * any dynamic API's that would have stopped the page from being fully\n   * statically generated.\n   */\n  isDebugDynamicAccesses?: boolean\n\n  /**\n   * This is true when:\n   * - source maps are generated\n   * - source maps are applied\n   * - minification is disabled\n   */\n  hasReadableErrorStacks?: boolean\n\n  /**\n   * The maximum length of the headers that are emitted by React and added to\n   * the response.\n   */\n  reactMaxHeadersLength: number | undefined\n\n  isStaticGeneration?: boolean\n\n  /**\n   * When true, the page is prerendered as a fallback shell, while allowing any\n   * dynamic accesses to result in an empty shell. This is the case when there\n   * are also routes prerendered with a more complete set of params.\n   * Prerendering those routes would catch any invalid dynamic accesses.\n   */\n  allowEmptyStaticShell?: boolean\n}\n\nexport type RenderOpts = LoadComponentsReturnType<AppPageModule> &\n  RenderOptsPartial &\n  RequestLifecycleOpts\n\nexport type PreloadCallbacks = (() => void)[]\n"], "names": ["flightRouterStateSchema", "dynamicParamTypesSchema", "s", "enums", "segmentSchema", "union", "string", "tuple", "record", "lazy", "optional", "nullable", "literal", "boolean"], "mappings": ";;;;+BA8CaA;;;eAAAA;;;oEAhCC;;;;;;AASd,MAAMC,0BAA0BC,oBAAC,CAACC,KAAK,CAAC;IAAC;IAAK;IAAM;IAAM;IAAK;CAAK;AAEpE,MAAMC,gBAAgBF,oBAAC,CAACG,KAAK,CAAC;IAC5BH,oBAAC,CAACI,MAAM;IAERJ,oBAAC,CAACK,KAAK,CAAC;QACN,aAAa;QACbL,oBAAC,CAACI,MAAM;QACR,gEAAgE;QAChE,6BAA6B;QAC7B,yEAAyE;QACzE,oEAAoE;QACpE,qEAAqE;QACrE,2CAA2C;QAC3CJ,oBAAC,CAACI,MAAM;QACR,qBAAqB;QACrBL;KACD;CACF;AAKM,MAAMD,0BAA2CE,oBAAC,CAACK,KAAK,CAAC;IAC9DH;IACAF,oBAAC,CAACM,MAAM,CACNN,oBAAC,CAACI,MAAM,IACRJ,oBAAC,CAACO,IAAI,CAAC,IAAMT;IAEfE,oBAAC,CAACQ,QAAQ,CAACR,oBAAC,CAACS,QAAQ,CAACT,oBAAC,CAACI,MAAM;IAC9BJ,oBAAC,CAACQ,QAAQ,CACRR,oBAAC,CAACS,QAAQ,CACRT,oBAAC,CAACG,KAAK,CAAC;QACNH,oBAAC,CAACU,OAAO,CAAC;QACVV,oBAAC,CAACU,OAAO,CAAC;QACVV,oBAAC,CAACU,OAAO,CAAC;QACVV,oBAAC,CAACU,OAAO,CAAC;KACX;IAGLV,oBAAC,CAACQ,QAAQ,CAACR,oBAAC,CAACW,OAAO;CACrB", "ignoreList": [0]}