{"version": 3, "sources": ["../../../src/server/app-render/staged-rendering.ts"], "sourcesContent": ["import { InvariantError } from '../../shared/lib/invariant-error'\nimport { createPromiseWithResolvers } from '../../shared/lib/promise-with-resolvers'\n\nexport enum RenderStage {\n  Static = 1,\n  Runtime = 2,\n  Dynamic = 3,\n}\n\nexport type NonStaticRenderStage = RenderStage.Runtime | RenderStage.Dynamic\n\nexport class StagedRenderingController {\n  currentStage: RenderStage = RenderStage.Static\n\n  private runtimeStagePromise = createPromiseWithResolvers<void>()\n  private dynamicStagePromise = createPromiseWithResolvers<void>()\n\n  constructor(private abortSignal: AbortSignal | null = null) {\n    if (abortSignal) {\n      abortSignal.addEventListener(\n        'abort',\n        () => {\n          const { reason } = abortSignal\n          if (this.currentStage < RenderStage.Runtime) {\n            this.runtimeStagePromise.promise.catch(ignoreReject) // avoid unhandled rejections\n            this.runtimeStagePromise.reject(reason)\n          }\n          if (this.currentStage < RenderStage.Dynamic) {\n            this.dynamicStagePromise.promise.catch(ignoreReject) // avoid unhandled rejections\n            this.dynamicStagePromise.reject(reason)\n          }\n        },\n        { once: true }\n      )\n    }\n  }\n\n  advanceStage(stage: NonStaticRenderStage) {\n    // If we're already at the target stage or beyond, do nothing.\n    // (this can happen e.g. if sync IO advanced us to the dynamic stage)\n    if (this.currentStage >= stage) {\n      return\n    }\n    this.currentStage = stage\n    // Note that we might be going directly from Static to Dynamic,\n    // so we need to resolve the runtime stage as well.\n    if (stage >= RenderStage.Runtime) {\n      this.runtimeStagePromise.resolve()\n    }\n    if (stage >= RenderStage.Dynamic) {\n      this.dynamicStagePromise.resolve()\n    }\n  }\n\n  private getStagePromise(stage: NonStaticRenderStage): Promise<void> {\n    switch (stage) {\n      case RenderStage.Runtime: {\n        return this.runtimeStagePromise.promise\n      }\n      case RenderStage.Dynamic: {\n        return this.dynamicStagePromise.promise\n      }\n      default: {\n        stage satisfies never\n        throw new InvariantError(`Invalid render stage: ${stage}`)\n      }\n    }\n  }\n\n  waitForStage(stage: NonStaticRenderStage) {\n    return this.getStagePromise(stage)\n  }\n\n  delayUntilStage<T>(\n    stage: NonStaticRenderStage,\n    displayName: string | undefined,\n    resolvedValue: T\n  ) {\n    const ioTriggerPromise = this.getStagePromise(stage)\n\n    const promise = makeDevtoolsIOPromiseFromIOTrigger(\n      ioTriggerPromise,\n      displayName,\n      resolvedValue\n    )\n\n    // Analogously to `makeHangingPromise`, we might reject this promise if the signal is invoked.\n    // (e.g. in the case where we don't want want the render to proceed to the dynamic stage and abort it).\n    // We shouldn't consider this an unhandled rejection, so we attach a noop catch handler here to suppress this warning.\n    if (this.abortSignal) {\n      promise.catch(ignoreReject)\n    }\n    return promise\n  }\n}\n\nfunction ignoreReject() {}\n\n// TODO(restart-on-cache-miss): the layering of `delayUntilStage`,\n// `makeDevtoolsIOPromiseFromIOTrigger` and and `makeDevtoolsIOAwarePromise`\n// is confusing, we should clean it up.\nfunction makeDevtoolsIOPromiseFromIOTrigger<T>(\n  ioTrigger: Promise<any>,\n  displayName: string | undefined,\n  resolvedValue: T\n): Promise<T> {\n  // If we create a `new Promise` and give it a displayName\n  // (with no userspace code above us in the stack)\n  // React Devtools will use it as the IO cause when determining \"suspended by\".\n  // In particular, it should shadow any inner IO that resolved/rejected the promise\n  // (in case of staged rendering, this will be the `setTimeout` that triggers the relevant stage)\n  const promise = new Promise<T>((resolve, reject) => {\n    ioTrigger.then(resolve.bind(null, resolvedValue), reject)\n  })\n  if (displayName !== undefined) {\n    // @ts-expect-error\n    promise.displayName = displayName\n  }\n  return promise\n}\n"], "names": ["RenderStage", "StagedRenderingController", "constructor", "abortSignal", "currentStage", "runtimeStagePromise", "createPromiseWithResolvers", "dynamicStagePromise", "addEventListener", "reason", "promise", "catch", "ignoreReject", "reject", "once", "advanceStage", "stage", "resolve", "getStagePromise", "InvariantError", "waitForStage", "delayUntilStage", "displayName", "resolvedValue", "ioTriggerPromise", "makeDevtoolsIOPromiseFromIOTrigger", "ioTrigger", "Promise", "then", "bind", "undefined"], "mappings": ";;;;;;;;;;;;;;;IAGYA,WAAW;eAAXA;;IAQCC,yBAAyB;eAAzBA;;;gCAXkB;sCACY;AAEpC,IAAA,AAAKD,qCAAAA;;;;WAAAA;;AAQL,MAAMC;IAMXC,YAAY,AAAQC,cAAkC,IAAI,CAAE;aAAxCA,cAAAA;aALpBC;aAEQC,sBAAsBC,IAAAA,gDAA0B;aAChDC,sBAAsBD,IAAAA,gDAA0B;QAGtD,IAAIH,aAAa;YACfA,YAAYK,gBAAgB,CAC1B,SACA;gBACE,MAAM,EAAEC,MAAM,EAAE,GAAGN;gBACnB,IAAI,IAAI,CAACC,YAAY,MAAwB;oBAC3C,IAAI,CAACC,mBAAmB,CAACK,OAAO,CAACC,KAAK,CAACC,cAAc,6BAA6B;;oBAClF,IAAI,CAACP,mBAAmB,CAACQ,MAAM,CAACJ;gBAClC;gBACA,IAAI,IAAI,CAACL,YAAY,MAAwB;oBAC3C,IAAI,CAACG,mBAAmB,CAACG,OAAO,CAACC,KAAK,CAACC,cAAc,6BAA6B;;oBAClF,IAAI,CAACL,mBAAmB,CAACM,MAAM,CAACJ;gBAClC;YACF,GACA;gBAAEK,MAAM;YAAK;QAEjB;IACF;IAEAC,aAAaC,KAA2B,EAAE;QACxC,8DAA8D;QAC9D,qEAAqE;QACrE,IAAI,IAAI,CAACZ,YAAY,IAAIY,OAAO;YAC9B;QACF;QACA,IAAI,CAACZ,YAAY,GAAGY;QACpB,+DAA+D;QAC/D,mDAAmD;QACnD,IAAIA,YAA8B;YAChC,IAAI,CAACX,mBAAmB,CAACY,OAAO;QAClC;QACA,IAAID,YAA8B;YAChC,IAAI,CAACT,mBAAmB,CAACU,OAAO;QAClC;IACF;IAEQC,gBAAgBF,KAA2B,EAAiB;QAClE,OAAQA;YACN;gBAA0B;oBACxB,OAAO,IAAI,CAACX,mBAAmB,CAACK,OAAO;gBACzC;YACA;gBAA0B;oBACxB,OAAO,IAAI,CAACH,mBAAmB,CAACG,OAAO;gBACzC;YACA;gBAAS;oBACPM;oBACA,MAAM,qBAAoD,CAApD,IAAIG,8BAAc,CAAC,CAAC,sBAAsB,EAAEH,OAAO,GAAnD,qBAAA;+BAAA;oCAAA;sCAAA;oBAAmD;gBAC3D;QACF;IACF;IAEAI,aAAaJ,KAA2B,EAAE;QACxC,OAAO,IAAI,CAACE,eAAe,CAACF;IAC9B;IAEAK,gBACEL,KAA2B,EAC3BM,WAA+B,EAC/BC,aAAgB,EAChB;QACA,MAAMC,mBAAmB,IAAI,CAACN,eAAe,CAACF;QAE9C,MAAMN,UAAUe,mCACdD,kBACAF,aACAC;QAGF,8FAA8F;QAC9F,uGAAuG;QACvG,sHAAsH;QACtH,IAAI,IAAI,CAACpB,WAAW,EAAE;YACpBO,QAAQC,KAAK,CAACC;QAChB;QACA,OAAOF;IACT;AACF;AAEA,SAASE,gBAAgB;AAEzB,kEAAkE;AAClE,4EAA4E;AAC5E,uCAAuC;AACvC,SAASa,mCACPC,SAAuB,EACvBJ,WAA+B,EAC/BC,aAAgB;IAEhB,yDAAyD;IACzD,iDAAiD;IACjD,8EAA8E;IAC9E,kFAAkF;IAClF,gGAAgG;IAChG,MAAMb,UAAU,IAAIiB,QAAW,CAACV,SAASJ;QACvCa,UAAUE,IAAI,CAACX,QAAQY,IAAI,CAAC,MAAMN,gBAAgBV;IACpD;IACA,IAAIS,gBAAgBQ,WAAW;QAC7B,mBAAmB;QACnBpB,QAAQY,WAAW,GAAGA;IACxB;IACA,OAAOZ;AACT", "ignoreList": [0]}