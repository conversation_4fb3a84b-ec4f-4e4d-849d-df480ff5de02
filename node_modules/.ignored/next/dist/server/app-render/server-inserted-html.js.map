{"version": 3, "sources": ["../../../src/server/app-render/server-inserted-html.tsx"], "sourcesContent": ["/* eslint-disable @next/internal/no-ambiguous-jsx -- whole module is used in React Client */\n// Provider for the `useServerInsertedHTML` API to register callbacks to insert\n// elements into the HTML stream.\n\nimport type { JSX, ReactNode } from 'react'\nimport * as ReactClient from 'react'\nimport { ServerInsertedHTMLContext } from '../../shared/lib/server-inserted-html.shared-runtime'\n\nexport function createServerInsertedHTML() {\n  const serverInsertedHTMLCallbacks: (() => ReactNode)[] = []\n  const addInsertedHtml = (handler: () => ReactNode) => {\n    serverInsertedHTMLCallbacks.push(handler)\n  }\n\n  return {\n    ServerInsertedHTMLProvider({ children }: { children: JSX.Element }) {\n      return (\n        <ServerInsertedHTMLContext.Provider value={addInsertedHtml}>\n          {children}\n        </ServerInsertedHTMLContext.Provider>\n      )\n    },\n    renderServerInsertedHTML() {\n      return serverInsertedHTMLCallbacks.map((callback, index) => (\n        <ReactClient.Fragment key={'__next_server_inserted__' + index}>\n          {callback()}\n        </ReactClient.Fragment>\n      ))\n    },\n  }\n}\n"], "names": ["createServerInsertedHTML", "serverInsertedHTMLCallbacks", "addInsertedHtml", "handler", "push", "ServerInsertedHTMLProvider", "children", "ServerInsertedHTMLContext", "Provider", "value", "renderServerInsertedHTML", "map", "callback", "index", "ReactClient", "Fragment"], "mappings": "AAAA,0FAA0F,GAC1F,+EAA+E;AAC/E,iCAAiC;;;;;+BAMjBA;;;eAAAA;;;;+DAHa;iDACa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEnC,SAASA;IACd,MAAMC,8BAAmD,EAAE;IAC3D,MAAMC,kBAAkB,CAACC;QACvBF,4BAA4BG,IAAI,CAACD;IACnC;IAEA,OAAO;QACLE,4BAA2B,EAAEC,QAAQ,EAA6B;YAChE,qBACE,qBAACC,0DAAyB,CAACC,QAAQ;gBAACC,OAAOP;0BACxCI;;QAGP;QACAI;YACE,OAAOT,4BAA4BU,GAAG,CAAC,CAACC,UAAUC,sBAChD,qBAACC,OAAYC,QAAQ;8BAClBH;mBADwB,6BAA6BC;QAI5D;IACF;AACF", "ignoreList": [0]}