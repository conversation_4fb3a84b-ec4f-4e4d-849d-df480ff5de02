{"version": 3, "sources": ["../../../src/server/app-render/prospective-render-utils.ts"], "sourcesContent": ["import { getDigestForWellKnownError } from './create-error-handler'\nimport { isReactLargeShellError } from './react-large-shell-error'\n\nexport function printDebugThrownValueForProspectiveRender(\n  thrownValue: unknown,\n  route: string\n) {\n  // We don't need to print well-known Next.js errors.\n  if (getDigestForWellKnownError(thrownValue)) {\n    return\n  }\n\n  if (isReactLargeShellError(thrownValue)) {\n    // TODO: Aggregate\n    console.error(thrownValue)\n    return undefined\n  }\n\n  let message: undefined | string\n  if (\n    typeof thrownValue === 'object' &&\n    thrownValue !== null &&\n    typeof (thrownValue as any).message === 'string'\n  ) {\n    message = (thrownValue as any).message\n    if (typeof (thrownValue as any).stack === 'string') {\n      const originalErrorStack: string = (thrownValue as any).stack\n      const stackStart = originalErrorStack.indexOf('\\n')\n      if (stackStart > -1) {\n        const error = new Error(\n          `Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled.\n          \nOriginal Error: ${message}`\n        )\n        error.stack =\n          'Error: ' + error.message + originalErrorStack.slice(stackStart)\n        console.error(error)\n        return\n      }\n    }\n  } else if (typeof thrownValue === 'string') {\n    message = thrownValue\n  }\n\n  if (message) {\n    console.error(`Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled. No stack was provided.\n          \nOriginal Message: ${message}`)\n    return\n  }\n\n  console.error(\n    `Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled. The thrown value is logged just following this message`\n  )\n  console.error(thrownValue)\n  return\n}\n"], "names": ["printDebugThrownValueForProspectiveRender", "thrownValue", "route", "getDigestForWellKnownError", "isReactLargeShellError", "console", "error", "undefined", "message", "stack", "originalErrorStack", "stackStart", "indexOf", "Error", "slice"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;oCAH2B;sCACJ;AAEhC,SAASA,0CACdC,WAAoB,EACpBC,KAAa;IAEb,oDAAoD;IACpD,IAAIC,IAAAA,8CAA0B,EAACF,cAAc;QAC3C;IACF;IAEA,IAAIG,IAAAA,4CAAsB,EAACH,cAAc;QACvC,kBAAkB;QAClBI,QAAQC,KAAK,CAACL;QACd,OAAOM;IACT;IAEA,IAAIC;IACJ,IACE,OAAOP,gBAAgB,YACvBA,gBAAgB,QAChB,OAAO,AAACA,YAAoBO,OAAO,KAAK,UACxC;QACAA,UAAU,AAACP,YAAoBO,OAAO;QACtC,IAAI,OAAO,AAACP,YAAoBQ,KAAK,KAAK,UAAU;YAClD,MAAMC,qBAA6B,AAACT,YAAoBQ,KAAK;YAC7D,MAAME,aAAaD,mBAAmBE,OAAO,CAAC;YAC9C,IAAID,aAAa,CAAC,GAAG;gBACnB,MAAML,QAAQ,qBAIb,CAJa,IAAIO,MAChB,CAAC,MAAM,EAAEX,MAAM;;gBAET,EAAEM,SAAS,GAHL,qBAAA;2BAAA;gCAAA;kCAAA;gBAId;gBACAF,MAAMG,KAAK,GACT,YAAYH,MAAME,OAAO,GAAGE,mBAAmBI,KAAK,CAACH;gBACvDN,QAAQC,KAAK,CAACA;gBACd;YACF;QACF;IACF,OAAO,IAAI,OAAOL,gBAAgB,UAAU;QAC1CO,UAAUP;IACZ;IAEA,IAAIO,SAAS;QACXH,QAAQC,KAAK,CAAC,CAAC,MAAM,EAAEJ,MAAM;;kBAEf,EAAEM,SAAS;QACzB;IACF;IAEAH,QAAQC,KAAK,CACX,CAAC,MAAM,EAAEJ,MAAM,wOAAwO,CAAC;IAE1PG,QAAQC,KAAK,CAACL;IACd;AACF", "ignoreList": [0]}