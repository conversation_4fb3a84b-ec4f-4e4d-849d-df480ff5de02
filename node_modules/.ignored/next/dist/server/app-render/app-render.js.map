{"version": 3, "sources": ["../../../src/server/app-render/app-render.tsx"], "sourcesContent": ["import type { ComponentType, <PERSON>rrorInfo, JSX, ReactNode } from 'react'\nimport type { RenderOpts, PreloadCallbacks } from './types'\nimport type {\n  ActionResult,\n  DynamicParamTypesShort,\n  FlightRouterState,\n  Segment,\n  CacheNodeSeedData,\n  RSCPayload,\n  FlightData,\n  InitialRSCPayload,\n  FlightDataPath,\n} from '../../shared/lib/app-router-types'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type {\n  PrerenderStoreModernRuntime,\n  RequestStore,\n} from '../app-render/work-unit-async-storage.external'\nimport type { NextParsedUrlQuery } from '../request-meta'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type {\n  ClientReferenceManifest,\n  ManifestNode,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\nimport * as ReactClient from 'react'\n\nimport RenderResult, {\n  type AppPageRenderResultMetadata,\n  type RenderResultOptions,\n} from '../render-result'\nimport {\n  chainStreams,\n  renderToInitialFizzStream,\n  createDocumentClosingStream,\n  continueFizzStream,\n  continueDynamicPrerender,\n  continueStaticPrerender,\n  continueDynamicHTMLResume,\n  streamToBuffer,\n  streamToString,\n  continueStaticFallbackPrerender,\n} from '../stream-utils/node-web-streams-helper'\nimport { stripInternalQueries } from '../internal-utils'\nimport {\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n  NEXT_URL,\n  RSC_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HASH_COOKIE,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_REQUEST_ID_HEADER,\n  NEXT_HTML_REQUEST_ID_HEADER,\n} from '../../client/components/app-router-headers'\nimport { createMetadataContext } from '../../lib/metadata/metadata-context'\nimport { createRequestStoreForRender } from '../async-storage/request-store'\nimport { createWorkStore } from '../async-storage/work-store'\nimport {\n  getAccessFallbackErrorTypeByStatus,\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { getImplicitTags, type ImplicitTags } from '../lib/implicit-tags'\nimport { AppRenderSpan, NextNodeServerSpan } from '../lib/trace/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  createFlightReactServerErrorHandler,\n  createHTMLReactServerErrorHandler,\n  createHTMLErrorHandler,\n  type DigestedError,\n  isUserLandError,\n  getDigestForWellKnownError,\n} from './create-error-handler'\nimport { dynamicParamTypes } from './get-short-dynamic-param-type'\nimport { getSegmentParam } from '../../shared/lib/router/utils/get-segment-param'\nimport { getScriptNonceFromHeader } from './get-script-nonce-from-header'\nimport { parseAndValidateFlightRouterState } from './parse-and-validate-flight-router-state'\nimport { createFlightRouterStateFromLoaderTree } from './create-flight-router-state-from-loader-tree'\nimport { handleAction } from './action-handler'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { warn, error } from '../../build/output/log'\nimport { appendMutableCookies } from '../web/spec-extension/adapters/request-cookies'\nimport { createServerInsertedHTML } from './server-inserted-html'\nimport { getRequiredScripts } from './required-scripts'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport { makeGetServerInsertedHTML } from './make-get-server-inserted-html'\nimport { walkTreeWithFlightRouterState } from './walk-tree-with-flight-router-state'\nimport { createComponentTree, getRootParams } from './create-component-tree'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport {\n  getServerModuleMap,\n  setReferenceManifestsSingleton,\n} from './encryption-utils'\nimport {\n  DynamicState,\n  type PostponedState,\n  DynamicHTMLPreludeState,\n  parsePostponedState,\n} from './postponed-state'\nimport {\n  getDynamicDataPostponedState,\n  getDynamicHTMLPostponedState,\n  getPostponedFromState,\n} from './postponed-state'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport {\n  useFlightStream,\n  createInlinedDataReadableStream,\n} from './use-flight-response'\nimport {\n  StaticGenBailoutError,\n  isStaticGenBailoutError,\n} from '../../client/components/static-generation-bailout'\nimport { getStackWithoutErrorMessage } from '../../lib/format-server-error'\nimport {\n  accessedDynamicData,\n  createRenderInBrowserAbortSignal,\n  formatDynamicAPIAccesses,\n  isPrerenderInterruptedError,\n  createDynamicTrackingState,\n  createDynamicValidationState,\n  trackAllowedDynamicAccess,\n  throwIfDisallowedDynamic,\n  PreludeState,\n  consumeDynamicAccess,\n  type DynamicAccess,\n  logDisallowedDynamicError,\n} from './dynamic-rendering'\nimport {\n  getClientComponentLoaderMetrics,\n  wrapClientComponentLoader,\n} from '../client-component-renderer-logger'\nimport { createServerModuleMap } from './action-utils'\nimport { isNodeNextRequest } from '../base-http/helpers'\nimport { parseRelativeUrl } from '../../shared/lib/router/utils/parse-relative-url'\nimport AppRouter from '../../client/components/app-router'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RequestErrorContext } from '../instrumentation/types'\nimport { getIsPossibleServerAction } from '../lib/server-action-request-meta'\nimport { createInitialRouterState } from '../../client/components/router-reducer/create-initial-router-state'\nimport { createMutableActionQueue } from '../../client/components/app-router-instance'\nimport { getRevalidateReason } from '../instrumentation/utils'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport type { OpaqueFallbackRouteParams } from '../request/fallback-params'\nimport {\n  prerenderAndAbortInSequentialTasksWithStages,\n  processPrelude,\n} from './app-render-prerender-utils'\nimport {\n  type ReactServerPrerenderResult,\n  ReactServerResult,\n  createReactServerPrerenderResult,\n  createReactServerPrerenderResultFromRender,\n  prerenderAndAbortInSequentialTasks,\n} from './app-render-prerender-utils'\nimport { printDebugThrownValueForProspectiveRender } from './prospective-render-utils'\nimport {\n  pipelineInSequentialTasks,\n  scheduleInSequentialTasks,\n} from './app-render-render-utils'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n} from './work-unit-async-storage.external'\nimport { consoleAsyncStorage } from './console-async-storage.external'\nimport { CacheSignal } from './cache-signal'\nimport { getTracedMetadata } from '../lib/trace/utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport { HTML_CONTENT_TYPE_HEADER, INFINITE_CACHE } from '../../lib/constants'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { parseLoaderTree } from '../../shared/lib/router/utils/parse-loader-tree'\nimport {\n  createPrerenderResumeDataCache,\n  createRenderResumeDataCache,\n  type PrerenderResumeDataCache,\n  type RenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { MetadataErrorType } from '../../lib/metadata/resolve-metadata'\nimport isError from '../../lib/is-error'\nimport { createServerInsertedMetadata } from './metadata-insertion/create-server-inserted-metadata'\nimport { getPreviouslyRevalidatedTags } from '../server-utils'\nimport { executeRevalidates } from '../revalidation-utils'\nimport {\n  trackPendingChunkLoad,\n  trackPendingImport,\n  trackPendingModules,\n} from './module-loading/track-module-loading.external'\nimport { isReactLargeShellError } from './react-large-shell-error'\nimport type { GlobalErrorComponent } from '../../client/components/builtin/global-error'\nimport { normalizeConventionFilePath } from './segment-explorer-path'\nimport { getRequestMeta } from '../request-meta'\nimport {\n  getDynamicParam,\n  interpolateParallelRouteParams,\n} from '../../shared/lib/router/utils/get-dynamic-param'\nimport type { ExperimentalConfig } from '../config-shared'\nimport type { Params } from '../request/params'\nimport { createPromiseWithResolvers } from '../../shared/lib/promise-with-resolvers'\nimport { ImageConfigContext } from '../../shared/lib/image-config-context.shared-runtime'\nimport { imageConfigDefault } from '../../shared/lib/image-config'\nimport { RenderStage, StagedRenderingController } from './staged-rendering'\nimport { anySegmentHasRuntimePrefetchEnabled } from './staged-validation'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\n\nexport type GetDynamicParamFromSegment = (\n  // [slug] / [[slug]] / [...slug]\n  segment: string\n) => DynamicParam | null\n\nexport type DynamicParam = {\n  param: string\n  value: string | string[] | null\n  treeSegment: Segment\n  type: DynamicParamTypesShort\n}\n\nexport type GenerateFlight = typeof generateDynamicFlightRenderResult\n\nexport type AppSharedContext = {\n  buildId: string\n}\n\nexport type AppRenderContext = {\n  sharedContext: AppSharedContext\n  workStore: WorkStore\n  url: ReturnType<typeof parseRelativeUrl>\n  componentMod: AppPageModule\n  renderOpts: RenderOpts\n  parsedRequestHeaders: ParsedRequestHeaders\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  query: NextParsedUrlQuery\n  isPrefetch: boolean\n  isPossibleServerAction: boolean\n  requestTimestamp: number\n  appUsingSizeAdjustment: boolean\n  flightRouterState?: FlightRouterState\n  requestId: string\n  htmlRequestId: string\n  pagePath: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  assetPrefix: string\n  isNotFoundPath: boolean\n  nonce: string | undefined\n  res: BaseNextResponse\n  /**\n   * For now, the implicit tags are common for the whole route. If we ever start\n   * rendering/revalidating segments independently, they need to move to the\n   * work unit store.\n   */\n  implicitTags: ImplicitTags\n}\n\ninterface ParseRequestHeadersOptions {\n  readonly isRoutePPREnabled: boolean\n  readonly previewModeId: string | undefined\n}\n\nconst flightDataPathHeadKey = 'h'\nconst getFlightViewportKey = (requestId: string) => requestId + 'v'\nconst getFlightMetadataKey = (requestId: string) => requestId + 'm'\n\nconst filterStackFrame =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .filterStackFrameDEV\n    : undefined\n\ninterface ParsedRequestHeaders {\n  /**\n   * Router state provided from the client-side router. Used to handle rendering\n   * from the common layout down. This value will be undefined if the request is\n   * not a client-side navigation request, or if the request is a prefetch\n   * request.\n   */\n  readonly flightRouterState: FlightRouterState | undefined\n  readonly isPrefetchRequest: boolean\n  readonly isRuntimePrefetchRequest: boolean\n  readonly isRouteTreePrefetchRequest: boolean\n  readonly isHmrRefresh: boolean\n  readonly isRSCRequest: boolean\n  readonly nonce: string | undefined\n  readonly previouslyRevalidatedTags: string[]\n  readonly requestId: string | undefined\n  readonly htmlRequestId: string | undefined\n}\n\nfunction parseRequestHeaders(\n  headers: IncomingHttpHeaders,\n  options: ParseRequestHeadersOptions\n): ParsedRequestHeaders {\n  // runtime prefetch requests are *not* treated as prefetch requests\n  // (TODO: this is confusing, we should refactor this to express this better)\n  const isPrefetchRequest = headers[NEXT_ROUTER_PREFETCH_HEADER] === '1'\n\n  const isRuntimePrefetchRequest = headers[NEXT_ROUTER_PREFETCH_HEADER] === '2'\n\n  const isHmrRefresh = headers[NEXT_HMR_REFRESH_HEADER] !== undefined\n\n  const isRSCRequest = headers[RSC_HEADER] !== undefined\n\n  const shouldProvideFlightRouterState =\n    isRSCRequest && (!isPrefetchRequest || !options.isRoutePPREnabled)\n\n  const flightRouterState = shouldProvideFlightRouterState\n    ? parseAndValidateFlightRouterState(headers[NEXT_ROUTER_STATE_TREE_HEADER])\n    : undefined\n\n  // Checks if this is a prefetch of the Route Tree by the Segment Cache\n  const isRouteTreePrefetchRequest =\n    headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER] === '/_tree'\n\n  const csp =\n    headers['content-security-policy'] ||\n    headers['content-security-policy-report-only']\n\n  const nonce =\n    typeof csp === 'string' ? getScriptNonceFromHeader(csp) : undefined\n\n  const previouslyRevalidatedTags = getPreviouslyRevalidatedTags(\n    headers,\n    options.previewModeId\n  )\n\n  let requestId: string | undefined\n  let htmlRequestId: string | undefined\n\n  if (process.env.NODE_ENV !== 'production') {\n    // The request IDs are only used in development mode to send debug\n    // information to the matching client (identified by the HTML request ID\n    // that was sent to the client with the HTML document) for the current\n    // request (identified by the request ID, as defined by the client).\n\n    requestId =\n      typeof headers[NEXT_REQUEST_ID_HEADER] === 'string'\n        ? headers[NEXT_REQUEST_ID_HEADER]\n        : undefined\n\n    htmlRequestId =\n      typeof headers[NEXT_HTML_REQUEST_ID_HEADER] === 'string'\n        ? headers[NEXT_HTML_REQUEST_ID_HEADER]\n        : undefined\n  }\n\n  return {\n    flightRouterState,\n    isPrefetchRequest,\n    isRuntimePrefetchRequest,\n    isRouteTreePrefetchRequest,\n    isHmrRefresh,\n    isRSCRequest,\n    nonce,\n    previouslyRevalidatedTags,\n    requestId,\n    htmlRequestId,\n  }\n}\n\nfunction createNotFoundLoaderTree(loaderTree: LoaderTree): LoaderTree {\n  const components = loaderTree[2]\n  const hasGlobalNotFound = !!components['global-not-found']\n  const notFoundTreeComponents: LoaderTree[2] = hasGlobalNotFound\n    ? {\n        layout: components['global-not-found']!,\n        page: [() => null, 'next/dist/client/components/builtin/empty-stub'],\n      }\n    : {\n        page: components['not-found'],\n      }\n\n  return [\n    '',\n    {\n      children: [PAGE_SEGMENT_KEY, {}, notFoundTreeComponents],\n    },\n    // When global-not-found is present, skip layout from components\n    hasGlobalNotFound ? components : {},\n  ]\n}\n\n/**\n * Returns a function that parses the dynamic segment and return the associated value.\n */\nfunction makeGetDynamicParamFromSegment(\n  interpolatedParams: Params,\n  fallbackRouteParams: OpaqueFallbackRouteParams | null\n): GetDynamicParamFromSegment {\n  return function getDynamicParamFromSegment(\n    // [slug] / [[slug]] / [...slug]\n    segment: string\n  ) {\n    const segmentParam = getSegmentParam(segment)\n    if (!segmentParam) {\n      return null\n    }\n    const segmentKey = segmentParam.param\n    const dynamicParamType = dynamicParamTypes[segmentParam.type]\n    return getDynamicParam(\n      interpolatedParams,\n      segmentKey,\n      dynamicParamType,\n      fallbackRouteParams\n    )\n  }\n}\n\nfunction NonIndex({\n  createElement,\n  pagePath,\n  statusCode,\n  isPossibleServerAction,\n}: {\n  createElement: typeof ReactClient.createElement\n  pagePath: string\n  statusCode: number | undefined\n  isPossibleServerAction: boolean\n}) {\n  const is404Page = pagePath === '/404'\n  const isInvalidStatusCode = typeof statusCode === 'number' && statusCode > 400\n\n  // Only render noindex for page request, skip for server actions\n  // TODO: is this correct if `isPossibleServerAction` is a false positive?\n  if (!isPossibleServerAction && (is404Page || isInvalidStatusCode)) {\n    return createElement('meta', {\n      name: 'robots',\n      content: 'noindex',\n    })\n  }\n  return null\n}\n\n/**\n * This is used by server actions & client-side navigations to generate RSC data from a client-side request.\n * This function is only called on \"dynamic\" requests (ie, there wasn't already a static response).\n * It uses request headers (namely `next-router-state-tree`) to determine where to start rendering.\n */\nasync function generateDynamicRSCPayload(\n  ctx: AppRenderContext,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n  }\n): Promise<RSCPayload> {\n  // Flight data that is going to be passed to the browser.\n  // Currently a single item array but in the future multiple patches might be combined in a single request.\n\n  // We initialize `flightData` to an empty string because the client router knows how to tolerate\n  // it (treating it as an MPA navigation). The only time this function wouldn't generate flight data\n  // is for server actions, if the server action handler instructs this function to skip it. When the server\n  // action reducer sees a falsy value, it'll simply resolve the action with no data.\n  let flightData: FlightData = ''\n\n  const {\n    componentMod: {\n      routeModule: {\n        userland: { loaderTree },\n      },\n      createElement,\n      createMetadataComponents,\n      Fragment,\n    },\n    getDynamicParamFromSegment,\n    query,\n    requestId,\n    flightRouterState,\n    workStore,\n    url,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  if (!options?.skipFlight) {\n    const preloadCallbacks: PreloadCallbacks = []\n\n    const { Viewport, Metadata, MetadataOutlet } = createMetadataComponents({\n      tree: loaderTree,\n      parsedQuery: query,\n      pathname: url.pathname,\n      metadataContext: createMetadataContext(ctx.renderOpts),\n      getDynamicParamFromSegment,\n      workStore,\n      serveStreamingMetadata,\n    })\n\n    flightData = (\n      await walkTreeWithFlightRouterState({\n        ctx,\n        loaderTreeToFilter: loaderTree,\n        parentParams: {},\n        flightRouterState,\n        // For flight, render metadata inside leaf page\n        rscHead: createElement(\n          Fragment,\n          {\n            key: flightDataPathHeadKey,\n          },\n          createElement(NonIndex, {\n            createElement,\n            pagePath: ctx.pagePath,\n            statusCode: ctx.res.statusCode,\n            isPossibleServerAction: ctx.isPossibleServerAction,\n          }),\n          createElement(Viewport, {\n            key: getFlightViewportKey(requestId),\n          }),\n          createElement(Metadata, {\n            key: getFlightMetadataKey(requestId),\n          })\n        ),\n        injectedCSS: new Set(),\n        injectedJS: new Set(),\n        injectedFontPreloadTags: new Set(),\n        rootLayoutIncluded: false,\n        preloadCallbacks,\n        MetadataOutlet,\n      })\n    ).map((path) => path.slice(1)) // remove the '' (root) segment\n  }\n\n  // If we have an action result, then this is a server action response.\n  // We can rely on this because `ActionResult` will always be a promise, even if\n  // the result is falsey.\n  if (options?.actionResult) {\n    return {\n      a: options.actionResult,\n      f: flightData,\n      b: ctx.sharedContext.buildId,\n    }\n  }\n\n  // Otherwise, it's a regular RSC response.\n  return {\n    b: ctx.sharedContext.buildId,\n    f: flightData,\n    S: workStore.isStaticGeneration,\n  }\n}\n\nfunction createErrorContext(\n  ctx: AppRenderContext,\n  renderSource: RequestErrorContext['renderSource']\n): RequestErrorContext {\n  return {\n    routerKind: 'App Router',\n    routePath: ctx.pagePath,\n    // TODO: is this correct if `isPossibleServerAction` is a false positive?\n    routeType: ctx.isPossibleServerAction ? 'action' : 'render',\n    renderSource,\n    revalidateReason: getRevalidateReason(ctx.workStore),\n  }\n}\n\n/**\n * Produces a RenderResult containing the Flight data for the given request. See\n * `generateDynamicRSCPayload` for information on the contents of the render result.\n */\nasync function generateDynamicFlightRenderResult(\n  req: BaseNextRequest,\n  ctx: AppRenderContext,\n  requestStore: RequestStore,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n    componentTree?: CacheNodeSeedData\n    preloadCallbacks?: PreloadCallbacks\n    temporaryReferences?: WeakMap<any, string>\n  }\n): Promise<RenderResult> {\n  const {\n    clientReferenceManifest,\n    componentMod: { renderToReadableStream },\n    htmlRequestId,\n    renderOpts,\n    requestId,\n    workStore,\n  } = ctx\n\n  const {\n    dev = false,\n    onInstrumentationRequestError,\n    setReactDebugChannel,\n  } = renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    dev,\n    onFlightDataRenderError\n  )\n\n  const debugChannel = setReactDebugChannel && createDebugChannel()\n\n  if (debugChannel) {\n    setReactDebugChannel(debugChannel.clientSide, htmlRequestId, requestId)\n  }\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  const rscPayload = await workUnitAsyncStorage.run(\n    requestStore,\n    generateDynamicRSCPayload,\n    ctx,\n    options\n  )\n\n  const flightReadableStream = workUnitAsyncStorage.run(\n    requestStore,\n    renderToReadableStream,\n    rscPayload,\n    clientReferenceManifest.clientModules,\n    {\n      onError,\n      temporaryReferences: options?.temporaryReferences,\n      filterStackFrame,\n      debugChannel: debugChannel?.serverSide,\n    }\n  )\n\n  return new FlightRenderResult(flightReadableStream, {\n    fetchMetrics: workStore.fetchMetrics,\n  })\n}\n\ntype RenderToReadableStreamServerOptions = NonNullable<\n  Parameters<\n    (typeof import('react-server-dom-webpack/server.node'))['renderToReadableStream']\n  >[2]\n>\n\nasync function stagedRenderToReadableStreamWithoutCachesInDev(\n  ctx: AppRenderContext,\n  requestStore: RequestStore,\n  getPayload: (requestStore: RequestStore) => Promise<RSCPayload>,\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>,\n  options: Omit<RenderToReadableStreamServerOptions, 'environmentName'>\n) {\n  const {\n    componentMod: { renderToReadableStream },\n  } = ctx\n  // We're rendering while bypassing caches,\n  // so we have no hope of showing a useful runtime stage.\n  // But we still want things like `params` to show up in devtools correctly,\n  // which relies on mechanisms we've set up for staged rendering,\n  // so we do a 2-task version (Static -> Dynamic) instead.\n\n  const stageController = new StagedRenderingController()\n  const environmentName = () => {\n    const currentStage = stageController.currentStage\n    switch (currentStage) {\n      case RenderStage.Static:\n        return 'Prerender'\n      case RenderStage.Runtime:\n      case RenderStage.Dynamic:\n        return 'Server'\n      default:\n        currentStage satisfies never\n        throw new InvariantError(`Invalid render stage: ${currentStage}`)\n    }\n  }\n\n  requestStore.stagedRendering = stageController\n  requestStore.asyncApiPromises = createAsyncApiPromisesInDev(\n    stageController,\n    requestStore.cookies,\n    requestStore.mutableCookies,\n    requestStore.headers\n  )\n\n  const rscPayload = await getPayload(requestStore)\n\n  return await workUnitAsyncStorage.run(\n    requestStore,\n    scheduleInSequentialTasks,\n    () => {\n      return renderToReadableStream(\n        rscPayload,\n        clientReferenceManifest.clientModules,\n        {\n          ...options,\n          environmentName,\n        }\n      )\n    },\n    () => {\n      stageController.advanceStage(RenderStage.Dynamic)\n    }\n  )\n}\n\n/**\n * Fork of `generateDynamicFlightRenderResult` that renders using `renderWithRestartOnCacheMissInDev`\n * to ensure correct separation of environments Prerender/Server (for use in Cache Components)\n */\nasync function generateDynamicFlightRenderResultWithStagesInDev(\n  req: BaseNextRequest,\n  ctx: AppRenderContext,\n  initialRequestStore: RequestStore,\n  createRequestStore: (() => RequestStore) | undefined\n): Promise<RenderResult> {\n  const {\n    htmlRequestId,\n    renderOpts,\n    requestId,\n    workStore,\n    componentMod: { createElement },\n  } = ctx\n\n  const {\n    dev = false,\n    onInstrumentationRequestError,\n    setReactDebugChannel,\n    setCacheStatus,\n    clientReferenceManifest,\n  } = renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    dev,\n    onFlightDataRenderError\n  )\n\n  const getPayload = async (requestStore: RequestStore) => {\n    const payload: RSCPayload & RSCPayloadDevProperties =\n      await workUnitAsyncStorage.run(\n        requestStore,\n        generateDynamicRSCPayload,\n        ctx,\n        undefined\n      )\n\n    if (isBypassingCachesInDev(renderOpts, requestStore)) {\n      // Mark the RSC payload to indicate that caches were bypassed in dev.\n      // This lets the client know not to cache anything based on this render.\n      payload._bypassCachesInDev = createElement(WarnForBypassCachesInDev, {\n        route: workStore.route,\n      })\n    }\n\n    return payload\n  }\n\n  let debugChannel: DebugChannelPair | undefined\n  let stream: ReadableStream<Uint8Array>\n\n  if (\n    // We only do this flow if we can safely recreate the store from scratch\n    // (which is not the case for renders after an action)\n    createRequestStore &&\n    // We only do this flow if we're not bypassing caches in dev using\n    // \"disable cache\" in devtools or a hard refresh (cache-control: \"no-store\")\n    !isBypassingCachesInDev(renderOpts, initialRequestStore)\n  ) {\n    // Before we kick off the render, we set the cache status back to it's initial state\n    // in case a previous render bypassed the cache.\n    if (setCacheStatus) {\n      setCacheStatus('ready', htmlRequestId, requestId)\n    }\n\n    const result = await renderWithRestartOnCacheMissInDev(\n      ctx,\n      initialRequestStore,\n      createRequestStore,\n      getPayload,\n      onError\n    )\n    debugChannel = result.debugChannel\n    stream = result.stream\n  } else {\n    // We're either bypassing caches or we can't restart the render.\n    // Do a dynamic render, but with (basic) environment labels.\n\n    assertClientReferenceManifest(clientReferenceManifest)\n\n    // Set cache status to bypass when specifically bypassing caches in dev\n    if (setCacheStatus) {\n      setCacheStatus('bypass', htmlRequestId, requestId)\n    }\n\n    debugChannel = setReactDebugChannel && createDebugChannel()\n\n    stream = await stagedRenderToReadableStreamWithoutCachesInDev(\n      ctx,\n      initialRequestStore,\n      getPayload,\n      clientReferenceManifest,\n      {\n        onError: onError,\n        filterStackFrame,\n        debugChannel: debugChannel?.serverSide,\n      }\n    )\n  }\n\n  if (debugChannel && setReactDebugChannel) {\n    setReactDebugChannel(debugChannel.clientSide, htmlRequestId, requestId)\n  }\n\n  return new FlightRenderResult(stream, {\n    fetchMetrics: workStore.fetchMetrics,\n  })\n}\n\nasync function generateRuntimePrefetchResult(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  requestStore: RequestStore\n): Promise<RenderResult> {\n  const { workStore } = ctx\n  const renderOpts = ctx.renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      // TODO(runtime-ppr): should we use a different value?\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    false,\n    onFlightDataRenderError\n  )\n\n  const metadata: AppPageRenderResultMetadata = {}\n\n  const generatePayload = () => generateDynamicRSCPayload(ctx, undefined)\n\n  const {\n    componentMod: {\n      routeModule: {\n        userland: { loaderTree },\n      },\n    },\n    getDynamicParamFromSegment,\n  } = ctx\n  const rootParams = getRootParams(loaderTree, getDynamicParamFromSegment)\n\n  // We need to share caches between the prospective prerender and the final prerender,\n  // but we're not going to persist this anywhere.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  // We're not resuming an existing render.\n  const renderResumeDataCache = null\n\n  await prospectiveRuntimeServerPrerender(\n    ctx,\n    generatePayload,\n    prerenderResumeDataCache,\n    renderResumeDataCache,\n    rootParams,\n    requestStore.headers,\n    requestStore.cookies,\n    requestStore.draftMode\n  )\n\n  const response = await finalRuntimeServerPrerender(\n    ctx,\n    generatePayload,\n    prerenderResumeDataCache,\n    renderResumeDataCache,\n    rootParams,\n    requestStore.headers,\n    requestStore.cookies,\n    requestStore.draftMode,\n    onError\n  )\n\n  applyMetadataFromPrerenderResult(response, metadata, workStore)\n  metadata.fetchMetrics = ctx.workStore.fetchMetrics\n\n  if (response.isPartial) {\n    res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n  }\n\n  return new FlightRenderResult(response.result.prelude, metadata)\n}\n\nasync function prospectiveRuntimeServerPrerender(\n  ctx: AppRenderContext,\n  getPayload: () => any,\n  prerenderResumeDataCache: PrerenderResumeDataCache | null,\n  renderResumeDataCache: RenderResumeDataCache | null,\n  rootParams: Params,\n  headers: PrerenderStoreModernRuntime['headers'],\n  cookies: PrerenderStoreModernRuntime['cookies'],\n  draftMode: PrerenderStoreModernRuntime['draftMode']\n) {\n  const { implicitTags, renderOpts, workStore } = ctx\n\n  const { clientReferenceManifest, ComponentMod } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  // Prerender controller represents the lifetime of the prerender.\n  // It will be aborted when a Task is complete or a synchronously aborting\n  // API is called. Notably during cache-filling renders this does not actually\n  // terminate the render itself which will continue until all caches are filled\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller represents the lifetime of the React render call. Notably\n  // during the cache-filling render it is different from the prerender controller\n  // because we don't want to end the react render until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  // The cacheSignal helps us track whether caches are still filling or we are ready\n  // to cut the render off.\n  const cacheSignal = new CacheSignal()\n\n  const initialServerPrerenderStore: PrerenderStoreModernRuntime = {\n    type: 'prerender-runtime',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    // During the initial prerender we need to track all cache reads to ensure\n    // we render long enough to fill every cache it is possible to visit during\n    // the final prerender.\n    cacheSignal,\n    // We only need to track dynamic accesses during the final prerender.\n    dynamicTracking: null,\n    // Runtime prefetches are never cached server-side, only client-side,\n    // so we set `expire` and `revalidate` to their minimum values just in case.\n    revalidate: 1,\n    expire: 0,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    renderResumeDataCache,\n    prerenderResumeDataCache,\n    hmrRefreshHash: undefined,\n    captureOwnerStack: undefined,\n    // We only need task sequencing in the final prerender.\n    runtimeStagePromise: null,\n    // These are not present in regular prerenders, but allowed in a runtime prerender.\n    headers,\n    cookies,\n    draftMode,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const initialServerPayload = await workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    getPayload\n  )\n\n  const pendingInitialServerResult = workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    ComponentMod.prerender,\n    initialServerPayload,\n    clientReferenceManifest.clientModules,\n    {\n      filterStackFrame,\n      onError: (err) => {\n        const digest = getDigestForWellKnownError(err)\n\n        if (digest) {\n          return digest\n        }\n\n        if (initialServerPrerenderController.signal.aborted) {\n          // The render aborted before this error was handled which indicates\n          // the error is caused by unfinished components within the render\n          return\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      },\n      // we don't care to track postpones during the prospective render because we need\n      // to always do a final render anyway\n      onPostpone: undefined,\n      // We don't want to stop rendering until the cacheSignal is complete so we pass\n      // a different signal to this render call than is used by dynamic APIs to signify\n      // transitioning out of the prerender environment\n      signal: initialServerRenderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling and for async imports to resolve\n  trackPendingModules(cacheSignal)\n  await cacheSignal.cacheReady()\n\n  initialServerRenderController.abort()\n  initialServerPrerenderController.abort()\n\n  // We don't need to continue the prerender process if we already\n  // detected invalid dynamic usage in the initial prerender phase.\n  if (workStore.invalidDynamicUsageError) {\n    throw workStore.invalidDynamicUsageError\n  }\n\n  try {\n    return await createReactServerPrerenderResult(pendingInitialServerResult)\n  } catch (err) {\n    if (\n      initialServerRenderController.signal.aborted ||\n      initialServerPrerenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, workStore.route)\n    }\n    return null\n  }\n}\n\nasync function finalRuntimeServerPrerender(\n  ctx: AppRenderContext,\n  getPayload: () => any,\n  prerenderResumeDataCache: PrerenderResumeDataCache | null,\n  renderResumeDataCache: RenderResumeDataCache | null,\n  rootParams: Params,\n  headers: PrerenderStoreModernRuntime['headers'],\n  cookies: PrerenderStoreModernRuntime['cookies'],\n  draftMode: PrerenderStoreModernRuntime['draftMode'],\n  onError: (err: unknown) => string | undefined\n) {\n  const { implicitTags, renderOpts } = ctx\n\n  const {\n    clientReferenceManifest,\n    ComponentMod,\n    experimental,\n    isDebugDynamicAccesses,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const selectStaleTime = createSelectStaleTime(experimental)\n\n  let serverIsDynamic = false\n  const finalServerController = new AbortController()\n\n  const serverDynamicTracking = createDynamicTrackingState(\n    isDebugDynamicAccesses\n  )\n\n  const { promise: runtimeStagePromise, resolve: resolveBlockedRuntimeAPIs } =\n    createPromiseWithResolvers<void>()\n\n  const finalServerPrerenderStore: PrerenderStoreModernRuntime = {\n    type: 'prerender-runtime',\n    phase: 'render',\n    rootParams,\n    implicitTags,\n    renderSignal: finalServerController.signal,\n    controller: finalServerController,\n    // All caches we could read must already be filled so no tracking is necessary\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    // Runtime prefetches are never cached server-side, only client-side,\n    // so we set `expire` and `revalidate` to their minimum values just in case.\n    revalidate: 1,\n    expire: 0,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache,\n    hmrRefreshHash: undefined,\n    captureOwnerStack: undefined,\n    // Used to separate the \"Static\" stage from the \"Runtime\" stage.\n    runtimeStagePromise,\n    // These are not present in regular prerenders, but allowed in a runtime prerender.\n    headers,\n    cookies,\n    draftMode,\n  }\n\n  const finalRSCPayload = await workUnitAsyncStorage.run(\n    finalServerPrerenderStore,\n    getPayload\n  )\n\n  let prerenderIsPending = true\n  const result = await prerenderAndAbortInSequentialTasksWithStages(\n    async () => {\n      // Static stage\n      const prerenderResult = await workUnitAsyncStorage.run(\n        finalServerPrerenderStore,\n        ComponentMod.prerender,\n        finalRSCPayload,\n        clientReferenceManifest.clientModules,\n        {\n          filterStackFrame,\n          onError,\n          signal: finalServerController.signal,\n        }\n      )\n      prerenderIsPending = false\n      return prerenderResult\n    },\n    () => {\n      // Advance to the runtime stage.\n      //\n      // We make runtime APIs hang during the first task (above), and unblock them in the following task (here).\n      // This makes sure that, at this point, we'll have finished all the static parts (what we'd prerender statically).\n      // We know that they don't contain any incorrect sync IO, because that'd have caused a build error.\n      // After we unblock Runtime APIs, if we encounter sync IO (e.g. `await cookies(); Date.now()`),\n      // we'll abort, but we'll produce at least as much output as a static prerender would.\n      resolveBlockedRuntimeAPIs()\n    },\n    () => {\n      // Abort.\n      if (finalServerController.signal.aborted) {\n        // If the server controller is already aborted we must have called something\n        // that required aborting the prerender synchronously such as with new Date()\n        serverIsDynamic = true\n        return\n      }\n\n      if (prerenderIsPending) {\n        // If prerenderIsPending then we have blocked for longer than a Task and we assume\n        // there is something unfinished.\n        serverIsDynamic = true\n      }\n      finalServerController.abort()\n    }\n  )\n\n  return {\n    result,\n    // TODO(runtime-ppr): do we need to produce a digest map here?\n    // digestErrorsMap: ...,\n    dynamicAccess: serverDynamicTracking,\n    isPartial: serverIsDynamic,\n    collectedRevalidate: finalServerPrerenderStore.revalidate,\n    collectedExpire: finalServerPrerenderStore.expire,\n    collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n    collectedTags: finalServerPrerenderStore.tags,\n  }\n}\n\n/**\n * Crawlers will inadvertently think the canonicalUrl in the RSC payload should be crawled\n * when our intention is to just seed the router state with the current URL.\n * This function splits up the pathname so that we can later join it on\n * when we're ready to consume the path.\n */\nfunction prepareInitialCanonicalUrl(url: RequestStore['url']) {\n  return (url.pathname + url.search).split('/')\n}\n\nfunction getRenderedSearch(query: NextParsedUrlQuery): string {\n  // Inlined implementation of querystring.encode, which is not available in\n  // the Edge runtime.\n  const pairs = []\n  for (const key in query) {\n    const value = query[key]\n    if (value == null) continue\n    if (Array.isArray(value)) {\n      for (const v of value) {\n        pairs.push(\n          `${encodeURIComponent(key)}=${encodeURIComponent(String(v))}`\n        )\n      }\n    } else {\n      pairs.push(\n        `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`\n      )\n    }\n  }\n\n  // The result should match the format of a web URL's `search` property, since\n  // this is the format that's stored in the App Router state.\n  // TODO: We're a bit inconsistent about this. The x-nextjs-rewritten-query\n  // header omits the leading question mark. Should refactor to always do\n  // that instead.\n  if (pairs.length === 0) {\n    // If the search string is empty, return an empty string.\n    return ''\n  }\n  // Prepend '?' to the search params string.\n  return '?' + pairs.join('&')\n}\n\n// This is the data necessary to render <AppRouter /> when no SSR errors are encountered\nasync function getRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  is404: boolean\n): Promise<InitialRSCPayload & { P: ReactNode }> {\n  const injectedCSS = new Set<string>()\n  const injectedJS = new Set<string>()\n  const injectedFontPreloadTags = new Set<string>()\n  let missingSlots: Set<string> | undefined\n\n  // We only track missing parallel slots in development\n  if (process.env.NODE_ENV === 'development') {\n    missingSlots = new Set<string>()\n  }\n\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: { createMetadataComponents, createElement, Fragment },\n    url,\n    workStore,\n  } = ctx\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const hasGlobalNotFound = !!tree[2]['global-not-found']\n\n  const { Viewport, Metadata, MetadataOutlet } = createMetadataComponents({\n    tree,\n    // When it's using global-not-found, metadata errorType is undefined, which will retrieve the\n    // metadata from the page.\n    // When it's using not-found, metadata errorType is 'not-found', which will retrieve the\n    // metadata from the not-found.js boundary.\n    // TODO: remove this condition and keep it undefined when global-not-found is stabilized.\n    errorType: is404 && !hasGlobalNotFound ? 'not-found' : undefined,\n    parsedQuery: query,\n    pathname: url.pathname,\n    metadataContext: createMetadataContext(ctx.renderOpts),\n    getDynamicParamFromSegment,\n    workStore,\n    serveStreamingMetadata,\n  })\n\n  const preloadCallbacks: PreloadCallbacks = []\n\n  const seedData = await createComponentTree({\n    ctx,\n    loaderTree: tree,\n    parentParams: {},\n    injectedCSS,\n    injectedJS,\n    injectedFontPreloadTags,\n    rootLayoutIncluded: false,\n    missingSlots,\n    preloadCallbacks,\n    authInterrupts: ctx.renderOpts.experimental.authInterrupts,\n    MetadataOutlet,\n  })\n\n  // When the `vary` response header is present with `Next-URL`, that means there's a chance\n  // it could respond differently if there's an interception route. We provide this information\n  // to `AppRouter` so that it can properly seed the prefetch cache with a prefix, if needed.\n  const varyHeader = ctx.res.getHeader('vary')\n  const couldBeIntercepted =\n    typeof varyHeader === 'string' && varyHeader.includes(NEXT_URL)\n\n  const initialHead = createElement(\n    Fragment,\n    {\n      key: flightDataPathHeadKey,\n    },\n    createElement(NonIndex, {\n      createElement,\n      pagePath: ctx.pagePath,\n      statusCode: ctx.res.statusCode,\n      isPossibleServerAction: ctx.isPossibleServerAction,\n    }),\n    createElement(Viewport, null),\n    createElement(Metadata, null),\n    appUsingSizeAdjustment\n      ? createElement('meta', {\n          name: 'next-size-adjust',\n          content: '',\n        })\n      : null\n  )\n\n  const { GlobalError, styles: globalErrorStyles } = await getGlobalErrorStyles(\n    tree,\n    ctx\n  )\n\n  // Assume the head we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // See similar comment in create-component-tree.tsx for more context.\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    // See the comment above the `Preloads` component (below) for why this is part of the payload\n    P: createElement(Preloads, {\n      preloadCallbacks: preloadCallbacks,\n    }),\n    b: ctx.sharedContext.buildId,\n    c: prepareInitialCanonicalUrl(url),\n    q: getRenderedSearch(query),\n    i: !!couldBeIntercepted,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    m: missingSlots,\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  }\n}\n\n/**\n * Preload calls (such as `ReactDOM.preloadStyle` and `ReactDOM.preloadFont`) need to be called during rendering\n * in order to create the appropriate preload tags in the DOM, otherwise they're a no-op. Since we invoke\n * renderToReadableStream with a function that returns component props rather than a component itself, we use\n * this component to \"render  \" the preload calls.\n */\nfunction Preloads({ preloadCallbacks }: { preloadCallbacks: Function[] }) {\n  preloadCallbacks.forEach((preloadFn) => preloadFn())\n  return null\n}\n\n// This is the data necessary to render <AppRouter /> when an error state is triggered\nasync function getErrorRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  ssrError: unknown,\n  errorType: MetadataErrorType | 'redirect' | undefined\n) {\n  const {\n    getDynamicParamFromSegment,\n    query,\n    componentMod: { createMetadataComponents, createElement, Fragment },\n    url,\n    workStore,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const { Viewport, Metadata } = createMetadataComponents({\n    tree,\n    parsedQuery: query,\n    pathname: url.pathname,\n    metadataContext: createMetadataContext(ctx.renderOpts),\n    errorType,\n    getDynamicParamFromSegment,\n    workStore,\n    serveStreamingMetadata: serveStreamingMetadata,\n  })\n\n  const initialHead = createElement(\n    Fragment,\n    {\n      key: flightDataPathHeadKey,\n    },\n    createElement(NonIndex, {\n      createElement,\n      pagePath: ctx.pagePath,\n      statusCode: ctx.res.statusCode,\n      isPossibleServerAction: ctx.isPossibleServerAction,\n    }),\n    createElement(Viewport, null),\n    process.env.NODE_ENV === 'development' &&\n      createElement('meta', {\n        name: 'next-error',\n        content: 'not-found',\n      }),\n    createElement(Metadata, null)\n  )\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n\n  let err: Error | undefined = undefined\n  if (ssrError) {\n    err = isError(ssrError) ? ssrError : new Error(ssrError + '')\n  }\n\n  // For metadata notFound error there's no global not found boundary on top\n  // so we create a not found page with AppRouter\n  const seedData: CacheNodeSeedData = [\n    createElement(\n      'html',\n      {\n        id: '__next_error__',\n      },\n      createElement('head', null),\n      createElement(\n        'body',\n        null,\n        process.env.NODE_ENV !== 'production' && err\n          ? createElement('template', {\n              'data-next-error-message': err.message,\n              'data-next-error-digest': 'digest' in err ? err.digest : '',\n              'data-next-error-stack': err.stack,\n            })\n          : null\n      )\n    ),\n    {},\n    null,\n    false,\n    false, // We don't currently support runtime prefetching for error pages.\n  ]\n\n  const { GlobalError, styles: globalErrorStyles } = await getGlobalErrorStyles(\n    tree,\n    ctx\n  )\n\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    b: ctx.sharedContext.buildId,\n    c: prepareInitialCanonicalUrl(url),\n    q: getRenderedSearch(query),\n    m: undefined,\n    i: false,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  } satisfies InitialRSCPayload\n}\n\nfunction assertClientReferenceManifest(\n  clientReferenceManifest: RenderOpts['clientReferenceManifest']\n): asserts clientReferenceManifest is NonNullable<\n  RenderOpts['clientReferenceManifest']\n> {\n  if (!clientReferenceManifest) {\n    throw new InvariantError('Expected clientReferenceManifest to be defined.')\n  }\n}\n\n// This component must run in an SSR context. It will render the RSC root component\nfunction App<T>({\n  reactServerStream,\n  reactDebugStream,\n  preinitScripts,\n  clientReferenceManifest,\n  ServerInsertedHTMLProvider,\n  nonce,\n  images,\n}: {\n  /* eslint-disable @next/internal/no-ambiguous-jsx -- React Client */\n  reactServerStream: BinaryStreamOf<T>\n  reactDebugStream: ReadableStream<Uint8Array> | undefined\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: ComponentType<{\n    children: JSX.Element\n  }>\n  images: RenderOpts['images']\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = ReactClient.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      reactDebugStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialRenderedSearch: response.q,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  const { HeadManagerContext } =\n    require('../../shared/lib/head-manager-context.shared-runtime') as typeof import('../../shared/lib/head-manager-context.shared-runtime')\n\n  return (\n    <HeadManagerContext.Provider\n      value={{\n        appDir: true,\n        nonce,\n      }}\n    >\n      <ImageConfigContext.Provider value={images ?? imageConfigDefault}>\n        <ServerInsertedHTMLProvider>\n          <AppRouter actionQueue={actionQueue} globalErrorState={response.G} />\n        </ServerInsertedHTMLProvider>\n      </ImageConfigContext.Provider>\n    </HeadManagerContext.Provider>\n  )\n  /* eslint-enable @next/internal/no-ambiguous-jsx -- React Client */\n}\n\n// @TODO our error stream should be probably just use the same root component. But it was previously\n// different I don't want to figure out if that is meaningful at this time so just keeping the behavior\n// consistent for now.\nfunction ErrorApp<T>({\n  reactServerStream,\n  reactDebugStream,\n  preinitScripts,\n  clientReferenceManifest,\n  ServerInsertedHTMLProvider,\n  nonce,\n  images,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  reactDebugStream: ReadableStream<Uint8Array> | undefined\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: ComponentType<{\n    children: JSX.Element\n  }>\n  nonce?: string\n  images: RenderOpts['images']\n}): JSX.Element {\n  /* eslint-disable @next/internal/no-ambiguous-jsx -- React Client */\n  preinitScripts()\n  const response = ReactClient.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      reactDebugStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    // This is not used during hydration, so we don't have to pass a\n    // real timestamp.\n    navigatedAt: -1,\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialRenderedSearch: response.q,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState, null)\n\n  return (\n    <ImageConfigContext.Provider value={images ?? imageConfigDefault}>\n      <ServerInsertedHTMLProvider>\n        <AppRouter actionQueue={actionQueue} globalErrorState={response.G} />\n      </ServerInsertedHTMLProvider>\n    </ImageConfigContext.Provider>\n  )\n  /* eslint-enable @next/internal/no-ambiguous-jsx -- React Client */\n}\n\n// We use a trick with TS Generics to branch streams with a type so we can\n// consume the parsed value of a Readable Stream if it was constructed with a\n// certain object shape. The generic type is not used directly in the type so it\n// requires a disabling of the eslint rule disallowing unused vars\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type BinaryStreamOf<T> = ReadableStream<Uint8Array>\n\nasync function renderToHTMLOrFlightImpl(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  url: ReturnType<typeof parseRelativeUrl>,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  workStore: WorkStore,\n  parsedRequestHeaders: ParsedRequestHeaders,\n  postponedState: PostponedState | null,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  sharedContext: AppSharedContext,\n  interpolatedParams: Params,\n  fallbackRouteParams: OpaqueFallbackRouteParams | null\n) {\n  const isNotFoundPath = pagePath === '/404'\n  if (isNotFoundPath) {\n    res.statusCode = 404\n  }\n\n  // A unique request timestamp used by development to ensure that it's\n  // consistent and won't change during this request. This is important to\n  // avoid that resources can be deduped by React Float if the same resource is\n  // rendered or preloaded multiple times: `<link href=\"a.css?v={Date.now()}\"/>`.\n  const requestTimestamp = Date.now()\n\n  const {\n    clientReferenceManifest,\n    serverActionsManifest,\n    ComponentMod,\n    nextFontManifest,\n    serverActions,\n    assetPrefix = '',\n    enableTainting,\n    cacheComponents,\n  } = renderOpts\n\n  // We need to expose the bundled `require` API globally for\n  // react-server-dom-webpack. This is a hack until we find a better way.\n  if (ComponentMod.__next_app__) {\n    const instrumented = wrapClientComponentLoader(ComponentMod)\n\n    // When we are prerendering if there is a cacheSignal for tracking\n    // cache reads we track calls to `loadChunk` and `require`. This allows us\n    // to treat chunk/module loading with similar semantics as cache reads to avoid\n    // module loading from causing a prerender to abort too early.\n\n    const shouldTrackModuleLoading = () => {\n      if (!cacheComponents) {\n        return false\n      }\n      if (renderOpts.dev) {\n        return true\n      }\n      const workUnitStore = workUnitAsyncStorage.getStore()\n\n      if (!workUnitStore) {\n        return false\n      }\n\n      switch (workUnitStore.type) {\n        case 'prerender':\n        case 'prerender-client':\n        case 'prerender-runtime':\n        case 'cache':\n        case 'private-cache':\n          return true\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n        case 'request':\n        case 'unstable-cache':\n          return false\n        default:\n          workUnitStore satisfies never\n      }\n    }\n\n    const __next_require__: typeof instrumented.require = (...args) => {\n      const exportsOrPromise = instrumented.require(...args)\n      if (shouldTrackModuleLoading()) {\n        // requiring an async module returns a promise.\n        trackPendingImport(exportsOrPromise)\n      }\n      return exportsOrPromise\n    }\n    // @ts-expect-error\n    globalThis.__next_require__ = __next_require__\n\n    const __next_chunk_load__: typeof instrumented.loadChunk = (...args) => {\n      const loadingChunk = instrumented.loadChunk(...args)\n      if (shouldTrackModuleLoading()) {\n        trackPendingChunkLoad(loadingChunk)\n      }\n      return loadingChunk\n    }\n    // @ts-expect-error\n    globalThis.__next_chunk_load__ = __next_chunk_load__\n  }\n\n  if (\n    process.env.NODE_ENV === 'development' &&\n    renderOpts.setIsrStatus &&\n    !cacheComponents\n  ) {\n    // Reset the ISR status at start of request.\n    const { pathname } = new URL(req.url || '/', 'http://n')\n    renderOpts.setIsrStatus(\n      pathname,\n      // Only pages using the Node runtime can use ISR, Edge is always dynamic.\n      process.env.NEXT_RUNTIME === 'edge' ? false : undefined\n    )\n  }\n\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextRequest(req)\n  ) {\n    res.onClose(() => {\n      // We stop tracking fetch metrics when the response closes, since we\n      // report them at that time.\n      workStore.shouldTrackFetchMetrics = false\n    })\n\n    req.originalRequest.on('end', () => {\n      if ('performance' in globalThis) {\n        const metrics = getClientComponentLoaderMetrics({ reset: true })\n        if (metrics) {\n          getTracer()\n            .startSpan(NextNodeServerSpan.clientComponentLoading, {\n              startTime: metrics.clientComponentLoadStart,\n              attributes: {\n                'next.clientComponentLoadCount':\n                  metrics.clientComponentLoadCount,\n                'next.span_type': NextNodeServerSpan.clientComponentLoading,\n              },\n            })\n            .end(\n              metrics.clientComponentLoadStart +\n                metrics.clientComponentLoadTimes\n            )\n        }\n      }\n    })\n  }\n\n  const metadata: AppPageRenderResultMetadata = {\n    statusCode: isNotFoundPath ? 404 : undefined,\n  }\n\n  const appUsingSizeAdjustment = !!nextFontManifest?.appUsingSizeAdjust\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const serverModuleMap = createServerModuleMap({ serverActionsManifest })\n\n  setReferenceManifestsSingleton({\n    page: workStore.page,\n    clientReferenceManifest,\n    serverActionsManifest,\n    serverModuleMap,\n  })\n\n  ComponentMod.patchFetch()\n\n  // Pull out the hooks/references from the component.\n  const {\n    routeModule: {\n      userland: { loaderTree },\n    },\n    taintObjectReference,\n  } = ComponentMod\n  if (enableTainting) {\n    taintObjectReference(\n      'Do not pass process.env to Client Components since it will leak sensitive data',\n      process.env\n    )\n  }\n\n  workStore.fetchMetrics = []\n  metadata.fetchMetrics = workStore.fetchMetrics\n\n  // don't modify original query object\n  query = { ...query }\n  stripInternalQueries(query)\n\n  const { isStaticGeneration } = workStore\n\n  let requestId: string\n  let htmlRequestId: string\n\n  const {\n    flightRouterState,\n    isPrefetchRequest,\n    isRuntimePrefetchRequest,\n    isRSCRequest,\n    isHmrRefresh,\n    nonce,\n  } = parsedRequestHeaders\n\n  if (parsedRequestHeaders.requestId) {\n    // If the client has provided a request ID (in development mode), we use it.\n    requestId = parsedRequestHeaders.requestId\n  } else {\n    // Otherwise we generate a new request ID.\n    if (isStaticGeneration) {\n      requestId = Buffer.from(\n        await crypto.subtle.digest('SHA-1', Buffer.from(req.url))\n      ).toString('hex')\n    } else if (process.env.NEXT_RUNTIME === 'edge') {\n      requestId = crypto.randomUUID()\n    } else {\n      requestId = (\n        require('next/dist/compiled/nanoid') as typeof import('next/dist/compiled/nanoid')\n      ).nanoid()\n    }\n  }\n\n  // If the client has provided an HTML request ID, we use it to associate the\n  // request with the HTML document from which it originated, which is used to\n  // send debug information to the associated WebSocket client. Otherwise, this\n  // is the request for the HTML document, so we use the request ID also as the\n  // HTML request ID.\n  htmlRequestId = parsedRequestHeaders.htmlRequestId || requestId\n\n  const getDynamicParamFromSegment = makeGetDynamicParamFromSegment(\n    interpolatedParams,\n    fallbackRouteParams\n  )\n\n  const isPossibleActionRequest = getIsPossibleServerAction(req)\n\n  const implicitTags = await getImplicitTags(\n    workStore.page,\n    url,\n    fallbackRouteParams\n  )\n\n  const ctx: AppRenderContext = {\n    componentMod: ComponentMod,\n    url,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    getDynamicParamFromSegment,\n    query,\n    isPrefetch: isPrefetchRequest,\n    isPossibleServerAction: isPossibleActionRequest,\n    requestTimestamp,\n    appUsingSizeAdjustment,\n    flightRouterState,\n    requestId,\n    htmlRequestId,\n    pagePath,\n    clientReferenceManifest,\n    assetPrefix,\n    isNotFoundPath,\n    nonce,\n    res,\n    sharedContext,\n    implicitTags,\n  }\n\n  getTracer().setRootSpanAttribute('next.route', pagePath)\n\n  if (isStaticGeneration) {\n    // We're either building or revalidating. In either case we need to\n    // prerender our page rather than render it.\n    const prerenderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `prerender route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      prerenderToStream\n    )\n\n    const response = await prerenderToStreamWithTracing(\n      req,\n      res,\n      ctx,\n      metadata,\n      loaderTree,\n      fallbackRouteParams\n    )\n\n    // If we're debugging partial prerendering, print all the dynamic API accesses\n    // that occurred during the render.\n    // @TODO move into renderToStream function\n    if (\n      response.dynamicAccess &&\n      accessedDynamicData(response.dynamicAccess) &&\n      renderOpts.isDebugDynamicAccesses\n    ) {\n      warn('The following dynamic usage was detected:')\n      for (const access of formatDynamicAPIAccesses(response.dynamicAccess)) {\n        warn(access)\n      }\n    }\n\n    // If we encountered any unexpected errors during build we fail the\n    // prerendering phase and the build.\n    if (workStore.invalidDynamicUsageError) {\n      logDisallowedDynamicError(workStore, workStore.invalidDynamicUsageError)\n      throw new StaticGenBailoutError()\n    }\n    if (response.digestErrorsMap.size) {\n      const buildFailingError = response.digestErrorsMap.values().next().value\n      if (buildFailingError) throw buildFailingError\n    }\n    // Pick first userland SSR error, which is also not a RSC error.\n    if (response.ssrErrors.length) {\n      const buildFailingError = response.ssrErrors.find((err) =>\n        isUserLandError(err)\n      )\n      if (buildFailingError) throw buildFailingError\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n      contentType: HTML_CONTENT_TYPE_HEADER,\n    }\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    applyMetadataFromPrerenderResult(response, metadata, workStore)\n\n    if (response.renderResumeDataCache) {\n      metadata.renderResumeDataCache = response.renderResumeDataCache\n    }\n\n    return new RenderResult(await streamToString(response.stream), options)\n  } else {\n    // We're rendering dynamically\n    const renderResumeDataCache =\n      renderOpts.renderResumeDataCache ??\n      postponedState?.renderResumeDataCache ??\n      null\n\n    const rootParams = getRootParams(loaderTree, ctx.getDynamicParamFromSegment)\n    const devValidatingFallbackParams =\n      getRequestMeta(req, 'devValidatingFallbackParams') || null\n\n    const createRequestStore = createRequestStoreForRender.bind(\n      null,\n      req,\n      res,\n      url,\n      rootParams,\n      implicitTags,\n      renderOpts.onUpdateCookies,\n      renderOpts.previewProps,\n      isHmrRefresh,\n      serverComponentsHmrCache,\n      renderResumeDataCache,\n      devValidatingFallbackParams\n    )\n    const requestStore = createRequestStore()\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      renderOpts.setIsrStatus &&\n      !cacheComponents &&\n      // Only pages using the Node runtime can use ISR, so we only need to\n      // update the status for those.\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req)\n    ) {\n      const setIsrStatus = renderOpts.setIsrStatus\n      req.originalRequest.on('end', () => {\n        const { pathname } = new URL(req.url || '/', 'http://n')\n        const isStatic = !requestStore.usedDynamic && !workStore.forceDynamic\n        setIsrStatus(pathname, isStatic)\n      })\n    }\n\n    if (isRSCRequest) {\n      if (isRuntimePrefetchRequest) {\n        return generateRuntimePrefetchResult(req, res, ctx, requestStore)\n      } else {\n        if (\n          process.env.NODE_ENV === 'development' &&\n          process.env.NEXT_RUNTIME !== 'edge' &&\n          cacheComponents\n        ) {\n          return generateDynamicFlightRenderResultWithStagesInDev(\n            req,\n            ctx,\n            requestStore,\n            createRequestStore\n          )\n        } else {\n          return generateDynamicFlightRenderResult(req, ctx, requestStore)\n        }\n      }\n    }\n\n    const renderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `render route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      renderToStream\n    )\n\n    let didExecuteServerAction = false\n    let formState: null | any = null\n    if (isPossibleActionRequest) {\n      // For action requests, we don't want to use the resume data cache.\n      requestStore.renderResumeDataCache = null\n\n      // For action requests, we handle them differently with a special render result.\n      const actionRequestResult = await handleAction({\n        req,\n        res,\n        ComponentMod,\n        serverModuleMap,\n        generateFlight: generateDynamicFlightRenderResult,\n        workStore,\n        requestStore,\n        serverActions,\n        ctx,\n        metadata,\n      })\n\n      if (actionRequestResult) {\n        if (actionRequestResult.type === 'not-found') {\n          const notFoundLoaderTree = createNotFoundLoaderTree(loaderTree)\n          res.statusCode = 404\n          metadata.statusCode = 404\n          const stream = await renderToStreamWithTracing(\n            requestStore,\n            req,\n            res,\n            ctx,\n            notFoundLoaderTree,\n            formState,\n            postponedState,\n            metadata,\n            undefined, // Prevent restartable-render behavior in dev + Cache Components mode\n            devValidatingFallbackParams\n          )\n\n          return new RenderResult(stream, {\n            metadata,\n            contentType: HTML_CONTENT_TYPE_HEADER,\n          })\n        } else if (actionRequestResult.type === 'done') {\n          if (actionRequestResult.result) {\n            actionRequestResult.result.assignMetadata(metadata)\n            return actionRequestResult.result\n          } else if (actionRequestResult.formState) {\n            formState = actionRequestResult.formState\n          }\n        }\n      }\n\n      didExecuteServerAction = true\n      // Restore the resume data cache\n      requestStore.renderResumeDataCache = renderResumeDataCache\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n      contentType: HTML_CONTENT_TYPE_HEADER,\n    }\n\n    const stream = await renderToStreamWithTracing(\n      // NOTE: in Cache Components (dev), if the render is restarted, it will use a different requestStore\n      // than the one that we're passing in here.\n      requestStore,\n      req,\n      res,\n      ctx,\n      loaderTree,\n      formState,\n      postponedState,\n      metadata,\n      // If we're rendering HTML after an action, we don't want restartable-render behavior\n      // because the result should be dynamic, like it is in prod.\n      // Also, the request store might have been mutated by the action (e.g. enabling draftMode)\n      // and we currently we don't copy changes over when creating a new store,\n      // so the restarted render wouldn't be correct.\n      didExecuteServerAction ? undefined : createRequestStore,\n      devValidatingFallbackParams\n    )\n\n    // Invalid dynamic usages should only error the request in development.\n    // In production, it's better to produce a result.\n    // (the dynamic error will still be thrown inside the component tree, but it's catchable by error boundaries)\n    if (workStore.invalidDynamicUsageError && workStore.dev) {\n      throw workStore.invalidDynamicUsageError\n    }\n\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.pendingRevalidatedTags\n    ) {\n      const pendingPromise = executeRevalidates(workStore).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    // Create the new render result for the response.\n    return new RenderResult(stream, options)\n  }\n}\n\nexport type AppPageRender = (\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  fallbackRouteParams: OpaqueFallbackRouteParams | null,\n  renderOpts: RenderOpts,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  sharedContext: AppSharedContext\n) => Promise<RenderResult<AppPageRenderResultMetadata>>\n\nexport const renderToHTMLOrFlight: AppPageRender = (\n  req,\n  res,\n  pagePath,\n  query,\n  fallbackRouteParams,\n  renderOpts,\n  serverComponentsHmrCache,\n  sharedContext\n) => {\n  if (!req.url) {\n    throw new Error('Invalid URL')\n  }\n\n  const url = parseRelativeUrl(req.url, undefined, false)\n\n  // We read these values from the request object as, in certain cases,\n  // base-server will strip them to opt into different rendering behavior.\n  const parsedRequestHeaders = parseRequestHeaders(req.headers, {\n    isRoutePPREnabled: renderOpts.experimental.isRoutePPREnabled === true,\n    previewModeId: renderOpts.previewProps?.previewModeId,\n  })\n\n  const { isPrefetchRequest, previouslyRevalidatedTags, nonce } =\n    parsedRequestHeaders\n\n  let interpolatedParams: Params\n  let postponedState: PostponedState | null = null\n\n  // If provided, the postpone state should be parsed so it can be provided to\n  // React.\n  if (typeof renderOpts.postponed === 'string') {\n    if (fallbackRouteParams) {\n      throw new InvariantError(\n        'postponed state should not be provided when fallback params are provided'\n      )\n    }\n\n    interpolatedParams = interpolateParallelRouteParams(\n      renderOpts.ComponentMod.routeModule.userland.loaderTree,\n      renderOpts.params ?? {},\n      pagePath,\n      fallbackRouteParams\n    )\n\n    postponedState = parsePostponedState(\n      renderOpts.postponed,\n      interpolatedParams\n    )\n  } else {\n    interpolatedParams = interpolateParallelRouteParams(\n      renderOpts.ComponentMod.routeModule.userland.loaderTree,\n      renderOpts.params ?? {},\n      pagePath,\n      fallbackRouteParams\n    )\n  }\n\n  if (\n    postponedState?.renderResumeDataCache &&\n    renderOpts.renderResumeDataCache\n  ) {\n    throw new InvariantError(\n      'postponed state and dev warmup immutable resume data cache should not be provided together'\n    )\n  }\n\n  const workStore = createWorkStore({\n    page: renderOpts.routeModule.definition.page,\n    renderOpts,\n    // @TODO move to workUnitStore of type Request\n    isPrefetchRequest,\n    buildId: sharedContext.buildId,\n    previouslyRevalidatedTags,\n    nonce,\n  })\n\n  return workAsyncStorage.run(\n    workStore,\n    // The function to run\n    renderToHTMLOrFlightImpl,\n    // all of it's args\n    req,\n    res,\n    url,\n    pagePath,\n    query,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    postponedState,\n    serverComponentsHmrCache,\n    sharedContext,\n    interpolatedParams,\n    fallbackRouteParams\n  )\n}\n\nfunction applyMetadataFromPrerenderResult(\n  response: Pick<\n    PrerenderToStreamResult,\n    | 'collectedExpire'\n    | 'collectedRevalidate'\n    | 'collectedStale'\n    | 'collectedTags'\n  >,\n  metadata: AppPageRenderResultMetadata,\n  workStore: WorkStore\n) {\n  if (response.collectedTags) {\n    metadata.fetchTags = response.collectedTags.join(',')\n  }\n\n  // Let the client router know how long to keep the cached entry around.\n  const staleHeader = String(response.collectedStale)\n  metadata.headers ??= {}\n  metadata.headers[NEXT_ROUTER_STALE_TIME_HEADER] = staleHeader\n\n  // If force static is specifically set to false, we should not revalidate\n  // the page.\n  if (workStore.forceStatic === false || response.collectedRevalidate === 0) {\n    metadata.cacheControl = { revalidate: 0, expire: undefined }\n  } else {\n    // Copy the cache control value onto the render result metadata.\n    metadata.cacheControl = {\n      revalidate:\n        response.collectedRevalidate >= INFINITE_CACHE\n          ? false\n          : response.collectedRevalidate,\n      expire:\n        response.collectedExpire >= INFINITE_CACHE\n          ? undefined\n          : response.collectedExpire,\n    }\n  }\n\n  // provide bailout info for debugging\n  if (metadata.cacheControl.revalidate === 0) {\n    metadata.staticBailoutInfo = {\n      description: workStore.dynamicUsageDescription,\n      stack: workStore.dynamicUsageStack,\n    }\n  }\n}\n\ntype RSCPayloadDevProperties = {\n  /** Only available during cacheComponents development builds. Used for logging errors. */\n  _validation?: Promise<ReactNode>\n  _bypassCachesInDev?: ReactNode\n}\n\nasync function renderToStream(\n  requestStore: RequestStore,\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  tree: LoaderTree,\n  formState: any,\n  postponedState: PostponedState | null,\n  metadata: AppPageRenderResultMetadata,\n  createRequestStore: (() => RequestStore) | undefined,\n  devValidatingFallbackParams: OpaqueFallbackRouteParams | null\n): Promise<ReadableStream<Uint8Array>> {\n  /* eslint-disable @next/internal/no-ambiguous-jsx -- React Client */\n  const {\n    assetPrefix,\n    htmlRequestId,\n    nonce,\n    pagePath,\n    renderOpts,\n    requestId,\n    workStore,\n  } = ctx\n\n  const {\n    basePath,\n    buildManifest,\n    clientReferenceManifest,\n    ComponentMod: {\n      createElement,\n      renderToReadableStream: serverRenderToReadableStream,\n    },\n    crossOrigin,\n    dev = false,\n    experimental,\n    nextExport = false,\n    onInstrumentationRequestError,\n    page,\n    reactMaxHeadersLength,\n    setReactDebugChannel,\n    shouldWaitOnAllReady,\n    subresourceIntegrityManifest,\n    supportsDynamicResponse,\n    cacheComponents,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const getServerInsertedMetadata = createServerInsertedMetadata(nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: subresourceIntegrityManifest?.[polyfill],\n        crossOrigin,\n        noModule: true,\n        nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    assetPrefix,\n    crossOrigin,\n    subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    nonce,\n    page\n  )\n\n  // In development mode, set the request ID as a global variable, before the\n  // bootstrap script is executed, which depends on it during hydration.\n  const bootstrapScriptContent =\n    process.env.NODE_ENV !== 'production'\n      ? `self.__next_r=${JSON.stringify(requestId)}`\n      : undefined\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  const silenceLogger = false\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerResult: null | ReactServerResult = null\n  let reactDebugStream: ReadableStream<Uint8Array> | undefined\n\n  const setHeader = res.setHeader.bind(res)\n  const appendHeader = res.appendHeader.bind(res)\n\n  try {\n    if (\n      // We only want this behavior when we have React's dev builds available\n      process.env.NODE_ENV === 'development' &&\n      // We only want this behavior when running `next dev`\n      dev &&\n      // Edge routes never prerender so we don't have a Prerender environment for anything in edge runtime\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      // We only have a Prerender environment for projects opted into cacheComponents\n      cacheComponents\n    ) {\n      const [resolveValidation, validationOutlet] = createValidationOutlet()\n      let debugChannel: DebugChannelPair | undefined\n      const getPayload = async (\n        // eslint-disable-next-line @typescript-eslint/no-shadow\n        requestStore: RequestStore\n      ) => {\n        const payload: InitialRSCPayload & RSCPayloadDevProperties =\n          await workUnitAsyncStorage.run(\n            requestStore,\n            getRSCPayload,\n            tree,\n            ctx,\n            res.statusCode === 404\n          )\n        // Placing the validation outlet in the payload is safe\n        // even if we end up discarding a render and restarting,\n        // because we're not going to wait for the stream to complete,\n        // so leaving the validation unresolved is fine.\n        payload._validation = validationOutlet\n\n        if (isBypassingCachesInDev(renderOpts, requestStore)) {\n          // Mark the RSC payload to indicate that caches were bypassed in dev.\n          // This lets the client know not to cache anything based on this render.\n          if (renderOpts.setCacheStatus) {\n            // we know this is available  when cacheComponents is enabled, but typeguard to be safe\n            renderOpts.setCacheStatus('bypass', htmlRequestId, requestId)\n          }\n          payload._bypassCachesInDev = createElement(WarnForBypassCachesInDev, {\n            route: workStore.route,\n          })\n        }\n\n        return payload\n      }\n\n      if (\n        // We only do this flow if we can safely recreate the store from scratch\n        // (which is not the case for renders after an action)\n        createRequestStore &&\n        // We only do this flow if we're not bypassing caches in dev using\n        // \"disable cache\" in devtools or a hard refresh (cache-control: \"no-store\")\n        !isBypassingCachesInDev(renderOpts, requestStore)\n      ) {\n        const {\n          stream: serverStream,\n          debugChannel: returnedDebugChannel,\n          requestStore: finalRequestStore,\n        } = await renderWithRestartOnCacheMissInDev(\n          ctx,\n          requestStore,\n          createRequestStore,\n          getPayload,\n          serverComponentsErrorHandler\n        )\n\n        reactServerResult = new ReactServerResult(serverStream)\n        requestStore = finalRequestStore\n        debugChannel = returnedDebugChannel\n      } else {\n        // We're either bypassing caches or we can't restart the render.\n        // Do a dynamic render, but with (basic) environment labels.\n\n        debugChannel = setReactDebugChannel && createDebugChannel()\n\n        const serverStream =\n          await stagedRenderToReadableStreamWithoutCachesInDev(\n            ctx,\n            requestStore,\n            getPayload,\n            clientReferenceManifest,\n            {\n              onError: serverComponentsErrorHandler,\n              filterStackFrame,\n              debugChannel: debugChannel?.serverSide,\n            }\n          )\n        reactServerResult = new ReactServerResult(serverStream)\n      }\n\n      if (debugChannel && setReactDebugChannel) {\n        const [readableSsr, readableBrowser] =\n          debugChannel.clientSide.readable.tee()\n\n        reactDebugStream = readableSsr\n\n        setReactDebugChannel(\n          { readable: readableBrowser },\n          htmlRequestId,\n          requestId\n        )\n      }\n\n      // TODO(restart-on-cache-miss):\n      // This can probably be optimized to do less work,\n      // because we've already made sure that we have warm caches.\n      consoleAsyncStorage.run(\n        { dim: true },\n        spawnDynamicValidationInDev,\n        resolveValidation,\n        tree,\n        ctx,\n        res.statusCode === 404,\n        clientReferenceManifest,\n        requestStore,\n        devValidatingFallbackParams\n      )\n    } else {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload: RSCPayload & RSCPayloadDevProperties =\n        await workUnitAsyncStorage.run(\n          requestStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n      const debugChannel = setReactDebugChannel && createDebugChannel()\n\n      if (debugChannel) {\n        const [readableSsr, readableBrowser] =\n          debugChannel.clientSide.readable.tee()\n\n        reactDebugStream = readableSsr\n\n        setReactDebugChannel(\n          { readable: readableBrowser },\n          htmlRequestId,\n          requestId\n        )\n      }\n\n      reactServerResult = new ReactServerResult(\n        workUnitAsyncStorage.run(\n          requestStore,\n          serverRenderToReadableStream,\n          RSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            filterStackFrame,\n            onError: serverComponentsErrorHandler,\n            debugChannel: debugChannel?.serverSide,\n          }\n        )\n      )\n    }\n\n    // React doesn't start rendering synchronously but we want the RSC render to have a chance to start\n    // before we begin SSR rendering because we want to capture any available preload headers so we tick\n    // one task before continuing\n    await waitAtLeastOneReactRenderTask()\n\n    // If provided, the postpone state should be parsed as JSON so it can be\n    // provided to React.\n    if (typeof renderOpts.postponed === 'string') {\n      if (postponedState?.type === DynamicState.DATA) {\n        // We have a complete HTML Document in the prerender but we need to\n        // still include the new server component render because it was not included\n        // in the static prelude.\n        const inlinedReactServerDataStream = createInlinedDataReadableStream(\n          reactServerResult.tee(),\n          nonce,\n          formState\n        )\n\n        return chainStreams(\n          inlinedReactServerDataStream,\n          createDocumentClosingStream()\n        )\n      } else if (postponedState) {\n        // We assume we have dynamic HTML requiring a resume render to complete\n        const { postponed, preludeState } =\n          getPostponedFromState(postponedState)\n        const resume = (\n          require('react-dom/server') as typeof import('react-dom/server')\n        ).resume\n\n        const htmlStream = await workUnitAsyncStorage.run(\n          requestStore,\n          resume,\n          <App\n            reactServerStream={reactServerResult.tee()}\n            reactDebugStream={reactDebugStream}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={nonce}\n            images={ctx.renderOpts.images}\n          />,\n          postponed,\n          { onError: htmlRendererErrorHandler, nonce }\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        return await continueDynamicHTMLResume(htmlStream, {\n          // If the prelude is empty (i.e. is no static shell), we should wait for initial HTML to be rendered\n          // to avoid injecting RSC data too early.\n          // If we have a non-empty-prelude (i.e. a static HTML shell), then it's already been sent separately,\n          // so we shouldn't wait for any HTML to be emitted from the resume before sending RSC data.\n          delayDataUntilFirstHtmlChunk:\n            preludeState === DynamicHTMLPreludeState.Empty,\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consume(),\n            nonce,\n            formState\n          ),\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        })\n      }\n    }\n\n    // This is a regular dynamic render\n    const renderToReadableStream = (\n      require('react-dom/server') as typeof import('react-dom/server')\n    ).renderToReadableStream\n\n    const htmlStream = await workUnitAsyncStorage.run(\n      requestStore,\n      renderToReadableStream,\n      <App\n        reactServerStream={reactServerResult.tee()}\n        reactDebugStream={reactDebugStream}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        nonce={nonce}\n        images={ctx.renderOpts.images}\n      />,\n      {\n        onError: htmlRendererErrorHandler,\n        nonce,\n        onHeaders: (headers: Headers) => {\n          headers.forEach((value, key) => {\n            appendHeader(key, value)\n          })\n        },\n        maxHeadersLength: reactMaxHeadersLength,\n        bootstrapScriptContent,\n        bootstrapScripts: [bootstrapScript],\n        formState,\n      }\n    )\n\n    const getServerInsertedHTML = makeGetServerInsertedHTML({\n      polyfills,\n      renderServerInsertedHTML,\n      serverCapturedErrors: allCapturedErrors,\n      basePath,\n      tracingMetadata: tracingMetadata,\n    })\n    /**\n     * Rules of Static & Dynamic HTML:\n     *\n     *    1.) We must generate static HTML unless the caller explicitly opts\n     *        in to dynamic HTML support.\n     *\n     *    2.) If dynamic HTML support is requested, we must honor that request\n     *        or throw an error. It is the sole responsibility of the caller to\n     *        ensure they aren't e.g. requesting dynamic HTML for a static page.\n     *\n     *   3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n     *       resolve all suspenses and generate a full HTML. e.g. when it's a\n     *       html limited bot requests, we produce the full HTML content.\n     *\n     * These rules help ensure that other existing features like request caching,\n     * coalescing, and ISR continue working as intended.\n     */\n    const generateStaticHTML =\n      supportsDynamicResponse !== true || !!shouldWaitOnAllReady\n\n    return await continueFizzStream(htmlStream, {\n      inlinedDataStream: createInlinedDataReadableStream(\n        reactServerResult.consume(),\n        nonce,\n        formState\n      ),\n      isStaticGeneration: generateStaticHTML,\n      isBuildTimePrerendering: ctx.workStore.isBuildTimePrerendering === true,\n      buildId: ctx.workStore.buildId,\n      getServerInsertedHTML,\n      getServerInsertedMetadata,\n      validateRootLayout: dev,\n    })\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      metadata.statusCode = res.statusCode\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n      metadata.statusCode = res.statusCode\n\n      const redirectUrl = addPathPrefix(getURLFromRedirectError(err), basePath)\n\n      // If there were mutable cookies set, we need to set them on the\n      // response.\n      const headers = new Headers()\n      if (appendMutableCookies(headers, requestStore.mutableCookies)) {\n        setHeader('set-cookie', Array.from(headers.values()))\n      }\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n      metadata.statusCode = res.statusCode\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      buildManifest,\n      assetPrefix,\n      crossOrigin,\n      subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      nonce,\n      '/_not-found/page'\n    )\n\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      requestStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? null : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      requestStore,\n      serverRenderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        filterStackFrame,\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    if (reactServerResult === null) {\n      // We errored when we did not have an RSC stream to read from. This is not just a render\n      // error, we need to throw early\n      throw err\n    }\n\n    try {\n      const fizzStream = await workUnitAsyncStorage.run(\n        requestStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer:\n            require('react-dom/server') as typeof import('react-dom/server'),\n          element: (\n            <ErrorApp\n              reactServerStream={errorServerStream}\n              reactDebugStream={undefined}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              nonce={nonce}\n              images={ctx.renderOpts.images}\n            />\n          ),\n          streamOptions: {\n            nonce,\n            bootstrapScriptContent,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      /**\n       * Rules of Static & Dynamic HTML:\n       *\n       *    1.) We must generate static HTML unless the caller explicitly opts\n       *        in to dynamic HTML support.\n       *\n       *    2.) If dynamic HTML support is requested, we must honor that request\n       *        or throw an error. It is the sole responsibility of the caller to\n       *        ensure they aren't e.g. requesting dynamic HTML for a static page.\n       *    3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n       *        resolve all suspenses and generate a full HTML. e.g. when it's a\n       *        html limited bot requests, we produce the full HTML content.\n       *\n       * These rules help ensure that other existing features like request caching,\n       * coalescing, and ISR continue working as intended.\n       */\n      const generateStaticHTML =\n        supportsDynamicResponse !== true || !!shouldWaitOnAllReady\n      return await continueFizzStream(fizzStream, {\n        inlinedDataStream: createInlinedDataReadableStream(\n          // This is intentionally using the readable datastream from the\n          // main render rather than the flight data from the error page\n          // render\n          reactServerResult.consume(),\n          nonce,\n          formState\n        ),\n        isStaticGeneration: generateStaticHTML,\n        isBuildTimePrerendering: ctx.workStore.isBuildTimePrerendering === true,\n        buildId: ctx.workStore.buildId,\n        getServerInsertedHTML: makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: [],\n          basePath,\n          tracingMetadata: tracingMetadata,\n        }),\n        getServerInsertedMetadata,\n        validateRootLayout: dev,\n      })\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n  /* eslint-enable @next/internal/no-ambiguous-jsx */\n}\n\nasync function renderWithRestartOnCacheMissInDev(\n  ctx: AppRenderContext,\n  initialRequestStore: RequestStore,\n  createRequestStore: () => RequestStore,\n  getPayload: (requestStore: RequestStore) => Promise<RSCPayload>,\n  onError: (error: unknown) => void\n) {\n  const {\n    htmlRequestId,\n    renderOpts,\n    requestId,\n    componentMod: {\n      routeModule: {\n        userland: { loaderTree },\n      },\n    },\n  } = ctx\n  const {\n    clientReferenceManifest,\n    ComponentMod,\n    setCacheStatus,\n    setReactDebugChannel,\n  } = renderOpts\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const hasRuntimePrefetch =\n    await anySegmentHasRuntimePrefetchEnabled(loaderTree)\n\n  // If the render is restarted, we'll recreate a fresh request store\n  let requestStore: RequestStore = initialRequestStore\n\n  const environmentName = () => {\n    const currentStage = requestStore.stagedRendering!.currentStage\n    switch (currentStage) {\n      case RenderStage.Static:\n        return 'Prerender'\n      case RenderStage.Runtime:\n        return hasRuntimePrefetch ? 'Prefetch' : 'Prefetchable'\n      case RenderStage.Dynamic:\n        return 'Server'\n      default:\n        currentStage satisfies never\n        throw new InvariantError(`Invalid render stage: ${currentStage}`)\n    }\n  }\n\n  //===============================================\n  // Initial render\n  //===============================================\n\n  // Try to render the page and see if there's any cache misses.\n  // If there are, wait for caches to finish and restart the render.\n\n  // This render might end up being used as a prospective render (if there's cache misses),\n  // so we need to set it up for filling caches.\n  const cacheSignal = new CacheSignal()\n\n  // If we encounter async modules that delay rendering, we'll also need to restart.\n  // TODO(restart-on-cache-miss): technically, we only need to wait for pending *server* modules here,\n  // but `trackPendingModules` doesn't distinguish between client and server.\n  trackPendingModules(cacheSignal)\n\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n  const initialReactController = new AbortController()\n  const initialDataController = new AbortController() // Controls hanging promises we create\n  const initialStageController = new StagedRenderingController(\n    initialDataController.signal\n  )\n\n  requestStore.prerenderResumeDataCache = prerenderResumeDataCache\n  // `getRenderResumeDataCache` will fall back to using `prerenderResumeDataCache` as `renderResumeDataCache`,\n  // so not having a resume data cache won't break any expectations in case we don't need to restart.\n  requestStore.renderResumeDataCache = null\n  requestStore.stagedRendering = initialStageController\n  requestStore.asyncApiPromises = createAsyncApiPromisesInDev(\n    initialStageController,\n    requestStore.cookies,\n    requestStore.mutableCookies,\n    requestStore.headers\n  )\n  requestStore.cacheSignal = cacheSignal\n\n  let debugChannel = setReactDebugChannel && createDebugChannel()\n\n  const initialRscPayload = await getPayload(requestStore)\n  const maybeInitialServerStream = await workUnitAsyncStorage.run(\n    requestStore,\n    () =>\n      pipelineInSequentialTasks(\n        () => {\n          // Static stage\n          const stream = ComponentMod.renderToReadableStream(\n            initialRscPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError,\n              environmentName,\n              filterStackFrame,\n              debugChannel: debugChannel?.serverSide,\n              signal: initialReactController.signal,\n            }\n          )\n          // If we abort the render, we want to reject the stage-dependent promises as well.\n          // Note that we want to install this listener after the render is started\n          // so that it runs after react is finished running its abort code.\n          initialReactController.signal.addEventListener('abort', () => {\n            initialDataController.abort(initialReactController.signal.reason)\n          })\n          return stream\n        },\n        (stream) => {\n          // Runtime stage\n          initialStageController.advanceStage(RenderStage.Runtime)\n\n          // If we had a cache miss in the static stage, we'll have to disard this stream\n          // and render again once the caches are warm.\n          if (cacheSignal.hasPendingReads()) {\n            return null\n          }\n\n          // If there's no cache misses, we'll continue rendering,\n          // and see if there's any cache misses in the runtime stage.\n          return stream\n        },\n        async (maybeStream) => {\n          // Dynamic stage\n\n          // If we had cache misses in either of the previous stages,\n          // then we'll only use this render for filling caches.\n          // We won't advance the stage, and thus leave dynamic APIs hanging,\n          // because they won't be cached anyway, so it'd be wasted work.\n          if (maybeStream === null || cacheSignal.hasPendingReads()) {\n            return null\n          }\n\n          // If there's no cache misses, we'll use this render, so let it advance to the dynamic stage.\n          initialStageController.advanceStage(RenderStage.Dynamic)\n          return maybeStream\n        }\n      )\n  )\n\n  if (maybeInitialServerStream !== null) {\n    // No cache misses. We can use the stream as is.\n    return {\n      stream: maybeInitialServerStream,\n      debugChannel,\n      requestStore,\n    }\n  }\n\n  if (process.env.NODE_ENV === 'development' && setCacheStatus) {\n    setCacheStatus('filling', htmlRequestId, requestId)\n  }\n\n  // Cache miss. We will use the initial render to fill caches, and discard its result.\n  // Then, we can render again with warm caches.\n\n  // TODO(restart-on-cache-miss):\n  // This might end up waiting for more caches than strictly necessary,\n  // because we can't abort the render yet, and we'll let runtime/dynamic APIs resolve.\n  // Ideally we'd only wait for caches that are needed in the static stage.\n  // This will be optimized in the future by not allowing runtime/dynamic APIs to resolve.\n\n  await cacheSignal.cacheReady()\n  initialReactController.abort()\n\n  //===============================================\n  // Final render (restarted)\n  //===============================================\n\n  // The initial render acted as a prospective render to warm the caches.\n  requestStore = createRequestStore()\n\n  const finalStageController = new StagedRenderingController()\n\n  // We've filled the caches, so now we can render as usual,\n  // without any cache-filling mechanics.\n  requestStore.prerenderResumeDataCache = null\n  requestStore.renderResumeDataCache = createRenderResumeDataCache(\n    prerenderResumeDataCache\n  )\n  requestStore.stagedRendering = finalStageController\n  requestStore.cacheSignal = null\n  requestStore.asyncApiPromises = createAsyncApiPromisesInDev(\n    finalStageController,\n    requestStore.cookies,\n    requestStore.mutableCookies,\n    requestStore.headers\n  )\n\n  // The initial render already wrote to its debug channel.\n  // We're not using it, so we need to create a new one.\n  debugChannel = setReactDebugChannel && createDebugChannel()\n\n  const finalRscPayload = await getPayload(requestStore)\n  const finalServerStream = await workUnitAsyncStorage.run(requestStore, () =>\n    pipelineInSequentialTasks(\n      () => {\n        // Static stage\n        return ComponentMod.renderToReadableStream(\n          finalRscPayload,\n          clientReferenceManifest.clientModules,\n          {\n            onError,\n            environmentName,\n            filterStackFrame,\n            debugChannel: debugChannel?.serverSide,\n          }\n        )\n      },\n      (stream) => {\n        // Runtime stage\n        finalStageController.advanceStage(RenderStage.Runtime)\n        return stream\n      },\n      (stream) => {\n        // Dynamic stage\n        finalStageController.advanceStage(RenderStage.Dynamic)\n        return stream\n      }\n    )\n  )\n\n  if (process.env.NODE_ENV === 'development' && setCacheStatus) {\n    setCacheStatus('filled', htmlRequestId, requestId)\n  }\n\n  return {\n    stream: finalServerStream,\n    debugChannel,\n    requestStore,\n  }\n}\n\nfunction createAsyncApiPromisesInDev(\n  stagedRendering: StagedRenderingController,\n  cookies: RequestStore['cookies'],\n  mutableCookies: RequestStore['mutableCookies'],\n  headers: RequestStore['headers']\n): NonNullable<RequestStore['asyncApiPromises']> {\n  return {\n    // Runtime APIs\n    cookies: stagedRendering.delayUntilStage(\n      RenderStage.Runtime,\n      'cookies',\n      cookies\n    ),\n    mutableCookies: stagedRendering.delayUntilStage(\n      RenderStage.Runtime,\n      'cookies',\n      mutableCookies as RequestStore['cookies']\n    ),\n    headers: stagedRendering.delayUntilStage(\n      RenderStage.Runtime,\n      'headers',\n      headers\n    ),\n    // These are not used directly, but we chain other `params`/`searchParams` promises off of them.\n    sharedParamsParent: stagedRendering.delayUntilStage(\n      RenderStage.Runtime,\n      undefined,\n      '<internal params>'\n    ),\n    sharedSearchParamsParent: stagedRendering.delayUntilStage(\n      RenderStage.Runtime,\n      undefined,\n      '<internal searchParams>'\n    ),\n    connection: stagedRendering.delayUntilStage(\n      RenderStage.Dynamic,\n      'connection',\n      undefined\n    ),\n  }\n}\n\ntype DebugChannelPair = {\n  serverSide: DebugChannelServer\n  clientSide: DebugChannelClient\n}\n\ntype DebugChannelServer = {\n  readable?: ReadableStream<Uint8Array>\n  writable: WritableStream<Uint8Array>\n}\ntype DebugChannelClient = {\n  readable: ReadableStream<Uint8Array>\n  writable?: WritableStream<Uint8Array>\n}\n\nfunction createDebugChannel(): DebugChannelPair | undefined {\n  if (process.env.NODE_ENV === 'production') {\n    return undefined\n  }\n\n  let readableController: ReadableStreamDefaultController | undefined\n\n  const clientSideReadable = new ReadableStream<Uint8Array>({\n    start(controller) {\n      readableController = controller\n    },\n  })\n\n  return {\n    serverSide: {\n      writable: new WritableStream<Uint8Array>({\n        write(chunk) {\n          readableController?.enqueue(chunk)\n        },\n        close() {\n          readableController?.close()\n        },\n        abort(err) {\n          readableController?.error(err)\n        },\n      }),\n    },\n    clientSide: {\n      readable: clientSideReadable,\n    },\n  }\n}\n\nfunction createValidationOutlet() {\n  let resolveValidation: (value: ReactNode) => void\n  let outlet = new Promise<ReactNode>((resolve) => {\n    resolveValidation = resolve\n  })\n  return [resolveValidation!, outlet] as const\n}\n\n/**\n * This function is a fork of prerenderToStream cacheComponents branch.\n * While it doesn't return a stream we want it to have identical\n * prerender semantics to prerenderToStream and should update it\n * in conjunction with any changes to that function.\n */\nasync function spawnDynamicValidationInDev(\n  resolveValidation: (validatingElement: ReactNode) => void,\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  isNotFound: boolean,\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>,\n  requestStore: RequestStore,\n  fallbackRouteParams: OpaqueFallbackRouteParams | null\n): Promise<void> {\n  const {\n    componentMod: ComponentMod,\n    getDynamicParamFromSegment,\n    implicitTags,\n    nonce,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const { allowEmptyStaticShell = false } = renderOpts\n\n  // These values are placeholder values for this validating render\n  // that are provided during the actual prerenderToStream.\n  const preinitScripts = () => {}\n  const { ServerInsertedHTMLProvider } = createServerInsertedHTML()\n\n  const rootParams = getRootParams(\n    ComponentMod.routeModule.userland.loaderTree,\n    getDynamicParamFromSegment\n  )\n\n  const hmrRefreshHash = requestStore.cookies.get(\n    NEXT_HMR_REFRESH_HASH_COOKIE\n  )?.value\n\n  // The prerender controller represents the lifetime of the prerender. It will\n  // be aborted when a task is complete or a synchronously aborting API is\n  // called. Notably, during prospective prerenders, this does not actually\n  // terminate the prerender itself, which will continue until all caches are\n  // filled.\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller is used to abort the React prerender.\n  const initialServerReactController = new AbortController()\n\n  // This controller represents the lifetime of the React prerender. Its signal\n  // can be used for any I/O operation to abort the I/O and/or to reject, when\n  // prerendering aborts. This includes our own hanging promises for accessing\n  // request data, and for fetch calls. It might be replaced in the future by\n  // React.cacheSignal(). It's aborted after the React controller, so that no\n  // pending I/O can register abort listeners that are called before React's\n  // abort listener is called. This ensures that pending I/O is not rejected too\n  // early when aborting the prerender. Notably, during the prospective\n  // prerender, it is different from the prerender controller because we don't\n  // want to end the React prerender until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  // The cacheSignal helps us track whether caches are still filling or we are\n  // ready to cut the render off.\n  const cacheSignal = new CacheSignal()\n\n  const captureOwnerStackClient = ReactClient.captureOwnerStack\n  const { captureOwnerStack: captureOwnerStackServer, createElement } =\n    ComponentMod\n\n  // The resume data cache here should use a fresh instance as it's\n  // performing a fresh prerender. If we get to implementing the\n  // prerendering of an already prerendered page, we should use the passed\n  // resume data cache instead.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  const initialServerPayloadPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    // While this render signal isn't going to be used to abort a React render while getting the RSC payload\n    // various request data APIs bind to this controller to reject after completion.\n    renderSignal: initialServerRenderController.signal,\n    // When we generate the RSC payload we might abort this controller due to sync IO\n    // but we don't actually care about sync IO in this phase so we use a throw away controller\n    // that isn't connected to anything\n    controller: new AbortController(),\n    // During the initial prerender we need to track all cache reads to ensure\n    // we render long enough to fill every cache it is possible to visit during\n    // the final prerender.\n    cacheSignal,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const initialServerPayload = await workUnitAsyncStorage.run(\n    initialServerPayloadPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const initialServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    // During the initial prerender we need to track all cache reads to ensure\n    // we render long enough to fill every cache it is possible to visit during\n    // the final prerender.\n    cacheSignal,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  const pendingInitialServerResult = workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    ComponentMod.prerender,\n    initialServerPayload,\n    clientReferenceManifest.clientModules,\n    {\n      filterStackFrame,\n      onError: (err) => {\n        const digest = getDigestForWellKnownError(err)\n\n        if (digest) {\n          return digest\n        }\n\n        if (isReactLargeShellError(err)) {\n          // TODO: Aggregate\n          console.error(err)\n          return undefined\n        }\n\n        if (initialServerPrerenderController.signal.aborted) {\n          // The render aborted before this error was handled which indicates\n          // the error is caused by unfinished components within the render\n          return\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      },\n      // we don't care to track postpones during the prospective render because we need\n      // to always do a final render anyway\n      onPostpone: undefined,\n      // We don't want to stop rendering until the cacheSignal is complete so we pass\n      // a different signal to this render call than is used by dynamic APIs to signify\n      // transitioning out of the prerender environment\n      signal: initialServerReactController.signal,\n    }\n  )\n\n  // The listener to abort our own render controller must be added after React\n  // has added its listener, to ensure that pending I/O is not aborted/rejected\n  // too early.\n  initialServerReactController.signal.addEventListener(\n    'abort',\n    () => {\n      initialServerRenderController.abort()\n    },\n    { once: true }\n  )\n\n  // Wait for all caches to be finished filling and for async imports to resolve\n  trackPendingModules(cacheSignal)\n  await cacheSignal.cacheReady()\n\n  initialServerReactController.abort()\n\n  // We don't need to continue the prerender process if we already\n  // detected invalid dynamic usage in the initial prerender phase.\n  const { invalidDynamicUsageError } = workStore\n  if (invalidDynamicUsageError) {\n    resolveValidation(\n      createElement(LogSafely, {\n        fn: () => {\n          console.error(invalidDynamicUsageError)\n        },\n      })\n    )\n    return\n  }\n\n  let initialServerResult\n  try {\n    initialServerResult = await createReactServerPrerenderResult(\n      pendingInitialServerResult\n    )\n  } catch (err) {\n    if (\n      initialServerReactController.signal.aborted ||\n      initialServerPrerenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, workStore.route)\n    }\n  }\n\n  if (initialServerResult) {\n    const initialClientPrerenderController = new AbortController()\n    const initialClientReactController = new AbortController()\n    const initialClientRenderController = new AbortController()\n\n    const initialClientPrerenderStore: PrerenderStore = {\n      type: 'prerender-client',\n      phase: 'render',\n      rootParams,\n      fallbackRouteParams,\n      implicitTags,\n      renderSignal: initialClientRenderController.signal,\n      controller: initialClientPrerenderController,\n      // For HTML Generation the only cache tracked activity\n      // is module loading, which has it's own cache signal\n      cacheSignal: null,\n      dynamicTracking: null,\n      allowEmptyStaticShell,\n      revalidate: INFINITE_CACHE,\n      expire: INFINITE_CACHE,\n      stale: INFINITE_CACHE,\n      tags: [...implicitTags.tags],\n      prerenderResumeDataCache,\n      renderResumeDataCache: null,\n      hmrRefreshHash: undefined,\n      captureOwnerStack: captureOwnerStackClient,\n    }\n\n    const prerender = (\n      require('react-dom/static') as typeof import('react-dom/static')\n    ).prerender\n    const pendingInitialClientResult = workUnitAsyncStorage.run(\n      initialClientPrerenderStore,\n      prerender,\n      // eslint-disable-next-line @next/internal/no-ambiguous-jsx -- React Client\n      <App\n        reactServerStream={initialServerResult.asUnclosingStream()}\n        reactDebugStream={undefined}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        nonce={nonce}\n        images={ctx.renderOpts.images}\n      />,\n      {\n        signal: initialClientReactController.signal,\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (isReactLargeShellError(err)) {\n            // TODO: Aggregate\n            console.error(err)\n            return undefined\n          }\n\n          if (initialClientReactController.signal.aborted) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        },\n        // We don't need bootstrap scripts in this prerender\n        // bootstrapScripts: [bootstrapScript],\n      }\n    )\n\n    // The listener to abort our own render controller must be added after React\n    // has added its listener, to ensure that pending I/O is not\n    // aborted/rejected too early.\n    initialClientReactController.signal.addEventListener(\n      'abort',\n      () => {\n        initialClientRenderController.abort()\n      },\n      { once: true }\n    )\n\n    pendingInitialClientResult.catch((err) => {\n      if (\n        initialClientReactController.signal.aborted ||\n        isPrerenderInterruptedError(err)\n      ) {\n        // These are expected errors that might error the prerender. we ignore them.\n      } else if (\n        process.env.NEXT_DEBUG_BUILD ||\n        process.env.__NEXT_VERBOSE_LOGGING\n      ) {\n        // We don't normally log these errors because we are going to retry anyway but\n        // it can be useful for debugging Next.js itself to get visibility here when needed\n        printDebugThrownValueForProspectiveRender(err, workStore.route)\n      }\n    })\n\n    // This is mostly needed for dynamic `import()`s in client components.\n    // Promises passed to client were already awaited above (assuming that they came from cached functions)\n    trackPendingModules(cacheSignal)\n    await cacheSignal.cacheReady()\n    initialClientReactController.abort()\n  }\n\n  const finalServerReactController = new AbortController()\n  const finalServerRenderController = new AbortController()\n\n  const finalServerPayloadPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    // While this render signal isn't going to be used to abort a React render while getting the RSC payload\n    // various request data APIs bind to this controller to reject after completion.\n    renderSignal: finalServerRenderController.signal,\n    // When we generate the RSC payload we might abort this controller due to sync IO\n    // but we don't actually care about sync IO in this phase so we use a throw away controller\n    // that isn't connected to anything\n    controller: new AbortController(),\n    // All caches we could read must already be filled so no tracking is necessary\n    cacheSignal: null,\n    dynamicTracking: null,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n    finalServerPayloadPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const serverDynamicTracking = createDynamicTrackingState(\n    false // isDebugDynamicAccesses\n  )\n\n  const finalServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    renderSignal: finalServerRenderController.signal,\n    controller: finalServerReactController,\n    // All caches we could read must already be filled so no tracking is necessary\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackServer,\n  }\n\n  const reactServerResult = await createReactServerPrerenderResult(\n    prerenderAndAbortInSequentialTasks(\n      async () => {\n        const pendingPrerenderResult = workUnitAsyncStorage.run(\n          // The store to scope\n          finalServerPrerenderStore,\n          // The function to run\n          ComponentMod.prerender,\n          // ... the arguments for the function to run\n          finalAttemptRSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            filterStackFrame,\n            onError: (err: unknown) => {\n              if (\n                finalServerReactController.signal.aborted &&\n                isPrerenderInterruptedError(err)\n              ) {\n                return err.digest\n              }\n\n              if (isReactLargeShellError(err)) {\n                // TODO: Aggregate\n                console.error(err)\n                return undefined\n              }\n\n              return getDigestForWellKnownError(err)\n            },\n            signal: finalServerReactController.signal,\n          }\n        )\n\n        // The listener to abort our own render controller must be added after\n        // React has added its listener, to ensure that pending I/O is not\n        // aborted/rejected too early.\n        finalServerReactController.signal.addEventListener(\n          'abort',\n          () => {\n            finalServerRenderController.abort()\n          },\n          { once: true }\n        )\n\n        return pendingPrerenderResult\n      },\n      () => {\n        finalServerReactController.abort()\n      }\n    )\n  )\n\n  const clientDynamicTracking = createDynamicTrackingState(\n    false //isDebugDynamicAccesses\n  )\n  const finalClientReactController = new AbortController()\n  const finalClientRenderController = new AbortController()\n\n  const finalClientPrerenderStore: PrerenderStore = {\n    type: 'prerender-client',\n    phase: 'render',\n    rootParams,\n    fallbackRouteParams,\n    implicitTags,\n    renderSignal: finalClientRenderController.signal,\n    controller: finalClientReactController,\n    // No APIs require a cacheSignal through the workUnitStore during the HTML prerender\n    cacheSignal: null,\n    dynamicTracking: clientDynamicTracking,\n    allowEmptyStaticShell,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [...implicitTags.tags],\n    prerenderResumeDataCache,\n    renderResumeDataCache: null,\n    hmrRefreshHash,\n    captureOwnerStack: captureOwnerStackClient,\n  }\n\n  let dynamicValidation = createDynamicValidationState()\n\n  try {\n    const prerender = (\n      require('react-dom/static') as typeof import('react-dom/static')\n    ).prerender\n    let { prelude: unprocessedPrelude } =\n      await prerenderAndAbortInSequentialTasks(\n        () => {\n          const pendingFinalClientResult = workUnitAsyncStorage.run(\n            finalClientPrerenderStore,\n            prerender,\n            // eslint-disable-next-line @next/internal/no-ambiguous-jsx -- React Client\n            <App\n              reactServerStream={reactServerResult.asUnclosingStream()}\n              reactDebugStream={undefined}\n              preinitScripts={preinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              nonce={nonce}\n              images={ctx.renderOpts.images}\n            />,\n            {\n              signal: finalClientReactController.signal,\n              onError: (err: unknown, errorInfo: ErrorInfo) => {\n                if (\n                  isPrerenderInterruptedError(err) ||\n                  finalClientReactController.signal.aborted\n                ) {\n                  const componentStack = errorInfo.componentStack\n                  if (typeof componentStack === 'string') {\n                    trackAllowedDynamicAccess(\n                      workStore,\n                      componentStack,\n                      dynamicValidation,\n                      clientDynamicTracking\n                    )\n                  }\n                  return\n                }\n\n                if (isReactLargeShellError(err)) {\n                  // TODO: Aggregate\n                  console.error(err)\n                  return undefined\n                }\n\n                return getDigestForWellKnownError(err)\n              },\n              // We don't need bootstrap scripts in this prerender\n              // bootstrapScripts: [bootstrapScript],\n            }\n          )\n\n          // The listener to abort our own render controller must be added after\n          // React has added its listener, to ensure that pending I/O is not\n          // aborted/rejected too early.\n          finalClientReactController.signal.addEventListener(\n            'abort',\n            () => {\n              finalClientRenderController.abort()\n            },\n            { once: true }\n          )\n\n          return pendingFinalClientResult\n        },\n        () => {\n          finalClientReactController.abort()\n        }\n      )\n\n    const { preludeIsEmpty } = await processPrelude(unprocessedPrelude)\n    resolveValidation(\n      createElement(LogSafely, {\n        fn: throwIfDisallowedDynamic.bind(\n          null,\n          workStore,\n          preludeIsEmpty ? PreludeState.Empty : PreludeState.Full,\n          dynamicValidation,\n          serverDynamicTracking\n        ),\n      })\n    )\n  } catch (thrownValue) {\n    // Even if the root errors we still want to report any cache components errors\n    // that were discovered before the root errored.\n\n    let loggingFunction = throwIfDisallowedDynamic.bind(\n      null,\n      workStore,\n      PreludeState.Errored,\n      dynamicValidation,\n      serverDynamicTracking\n    )\n\n    if (process.env.NEXT_DEBUG_BUILD || process.env.__NEXT_VERBOSE_LOGGING) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      const originalLoggingFunction = loggingFunction\n      loggingFunction = () => {\n        console.error(\n          'During dynamic validation the root of the page errored. The next logged error is the thrown value. It may be a duplicate of errors reported during the normal development mode render.'\n        )\n        console.error(thrownValue)\n        originalLoggingFunction()\n      }\n    }\n\n    resolveValidation(\n      createElement(LogSafely, {\n        fn: loggingFunction,\n      })\n    )\n  }\n}\n\nasync function LogSafely({ fn }: { fn: () => unknown }) {\n  try {\n    await fn()\n  } catch {}\n  return null\n}\n\ntype PrerenderToStreamResult = {\n  stream: ReadableStream<Uint8Array>\n  digestErrorsMap: Map<string, DigestedError>\n  ssrErrors: Array<unknown>\n  dynamicAccess?: null | Array<DynamicAccess>\n  collectedRevalidate: number\n  collectedExpire: number\n  collectedStale: number\n  collectedTags: null | string[]\n  renderResumeDataCache?: RenderResumeDataCache\n}\n\n/**\n * Determines whether we should generate static flight data.\n */\nfunction shouldGenerateStaticFlightData(workStore: WorkStore): boolean {\n  const { isStaticGeneration } = workStore\n  if (!isStaticGeneration) return false\n\n  return true\n}\n\nasync function prerenderToStream(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  metadata: AppPageRenderResultMetadata,\n  tree: LoaderTree,\n  fallbackRouteParams: OpaqueFallbackRouteParams | null\n): Promise<PrerenderToStreamResult> {\n  // When prerendering formState is always null. We still include it\n  // because some shared APIs expect a formState value and this is slightly\n  // more explicit than making it an optional function argument\n  const formState = null\n\n  const {\n    assetPrefix,\n    getDynamicParamFromSegment,\n    implicitTags,\n    nonce,\n    pagePath,\n    renderOpts,\n    workStore,\n  } = ctx\n\n  const {\n    allowEmptyStaticShell = false,\n    basePath,\n    buildManifest,\n    clientReferenceManifest,\n    ComponentMod,\n    crossOrigin,\n    dev = false,\n    experimental,\n    isDebugDynamicAccesses,\n    nextExport = false,\n    onInstrumentationRequestError,\n    page,\n    reactMaxHeadersLength,\n    subresourceIntegrityManifest,\n    cacheComponents,\n  } = renderOpts\n\n  assertClientReferenceManifest(clientReferenceManifest)\n\n  const rootParams = getRootParams(tree, getDynamicParamFromSegment)\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const getServerInsertedMetadata = createServerInsertedMetadata(nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: subresourceIntegrityManifest?.[polyfill],\n        crossOrigin,\n        noModule: true,\n        nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    assetPrefix,\n    crossOrigin,\n    subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    nonce,\n    page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  // We don't report errors during prerendering through our instrumentation hooks\n  const silenceLogger = !!experimental.isRoutePPREnabled\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    dev,\n    nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerPrerenderResult: null | ReactServerPrerenderResult = null\n  const setMetadataHeader = (name: string) => {\n    metadata.headers ??= {}\n    metadata.headers[name] = res.getHeader(name)\n  }\n  const setHeader = (name: string, value: string | string[]) => {\n    res.setHeader(name, value)\n    setMetadataHeader(name)\n    return res\n  }\n  const appendHeader = (name: string, value: string | string[]) => {\n    if (Array.isArray(value)) {\n      value.forEach((item) => {\n        res.appendHeader(name, item)\n      })\n    } else {\n      res.appendHeader(name, value)\n    }\n    setMetadataHeader(name)\n  }\n\n  const selectStaleTime = createSelectStaleTime(experimental)\n\n  let prerenderStore: PrerenderStore | null = null\n\n  try {\n    if (cacheComponents) {\n      /**\n       * cacheComponents with PPR\n       *\n       * The general approach is to render the RSC stream first allowing any cache reads to resolve.\n       * Once we have settled all cache reads we restart the render and abort after a single Task.\n       *\n       * Unlike with the non PPR case we can't synchronously abort the render when a dynamic API is used\n       * during the initial render because we need to ensure all caches can be filled as part of the initial Task\n       * and a synchronous abort might prevent us from filling all caches.\n       *\n       * Once the render is complete we allow the SSR render to finish and use a combination of the postponed state\n       * and the reactServerIsDynamic value to determine how to treat the resulting render\n       */\n\n      // The prerender controller represents the lifetime of the prerender. It\n      // will be aborted when a task is complete or a synchronously aborting API\n      // is called. Notably, during prospective prerenders, this does not\n      // actually terminate the prerender itself, which will continue until all\n      // caches are filled.\n      const initialServerPrerenderController = new AbortController()\n\n      // This controller is used to abort the React prerender.\n      const initialServerReactController = new AbortController()\n\n      // This controller represents the lifetime of the React prerender. Its\n      // signal can be used for any I/O operation to abort the I/O and/or to\n      // reject, when prerendering aborts. This includes our own hanging\n      // promises for accessing request data, and for fetch calls. It might be\n      // replaced in the future by React.cacheSignal(). It's aborted after the\n      // React controller, so that no pending I/O can register abort listeners\n      // that are called before React's abort listener is called. This ensures\n      // that pending I/O is not rejected too early when aborting the prerender.\n      // Notably, during the prospective prerender, it is different from the\n      // prerender controller because we don't want to end the React prerender\n      // until all caches are filled.\n      const initialServerRenderController = new AbortController()\n\n      // The cacheSignal helps us track whether caches are still filling or we are ready\n      // to cut the render off.\n      const cacheSignal = new CacheSignal()\n\n      let resumeDataCache: RenderResumeDataCache | PrerenderResumeDataCache\n      let renderResumeDataCache: RenderResumeDataCache | null = null\n      let prerenderResumeDataCache: PrerenderResumeDataCache | null = null\n\n      if (renderOpts.renderResumeDataCache) {\n        // If a prefilled immutable render resume data cache is provided, e.g.\n        // when prerendering an optional fallback shell after having prerendered\n        // pages with defined params, we use this instead of a prerender resume\n        // data cache.\n        resumeDataCache = renderResumeDataCache =\n          renderOpts.renderResumeDataCache\n      } else {\n        // Otherwise we create a new mutable prerender resume data cache.\n        resumeDataCache = prerenderResumeDataCache =\n          createPrerenderResumeDataCache()\n      }\n\n      const initialServerPayloadPrerenderStore: PrerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        // While this render signal isn't going to be used to abort a React render while getting the RSC payload\n        // various request data APIs bind to this controller to reject after completion.\n        renderSignal: initialServerRenderController.signal,\n        // When we generate the RSC payload we might abort this controller due to sync IO\n        // but we don't actually care about sync IO in this phase so we use a throw away controller\n        // that isn't connected to anything\n        controller: new AbortController(),\n        // During the initial prerender we need to track all cache reads to ensure\n        // we render long enough to fill every cache it is possible to visit during\n        // the final prerender.\n        cacheSignal,\n        dynamicTracking: null,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      }\n\n      // We're not going to use the result of this render because the only time it could be used\n      // is if it completes in a microtask and that's likely very rare for any non-trivial app\n      const initialServerPayload = await workUnitAsyncStorage.run(\n        initialServerPayloadPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        renderSignal: initialServerRenderController.signal,\n        controller: initialServerPrerenderController,\n        // During the initial prerender we need to track all cache reads to ensure\n        // we render long enough to fill every cache it is possible to visit during\n        // the final prerender.\n        cacheSignal,\n        dynamicTracking: null,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      })\n\n      const pendingInitialServerResult = workUnitAsyncStorage.run(\n        initialServerPrerenderStore,\n        ComponentMod.prerender,\n        initialServerPayload,\n        clientReferenceManifest.clientModules,\n        {\n          filterStackFrame,\n          onError: (err) => {\n            const digest = getDigestForWellKnownError(err)\n\n            if (digest) {\n              return digest\n            }\n\n            if (isReactLargeShellError(err)) {\n              // TODO: Aggregate\n              console.error(err)\n              return undefined\n            }\n\n            if (initialServerPrerenderController.signal.aborted) {\n              // The render aborted before this error was handled which indicates\n              // the error is caused by unfinished components within the render\n              return\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          },\n          // we don't care to track postpones during the prospective render because we need\n          // to always do a final render anyway\n          onPostpone: undefined,\n          // We don't want to stop rendering until the cacheSignal is complete so we pass\n          // a different signal to this render call than is used by dynamic APIs to signify\n          // transitioning out of the prerender environment\n          signal: initialServerReactController.signal,\n        }\n      )\n\n      // The listener to abort our own render controller must be added after\n      // React has added its listener, to ensure that pending I/O is not\n      // aborted/rejected too early.\n      initialServerReactController.signal.addEventListener(\n        'abort',\n        () => {\n          initialServerRenderController.abort()\n          initialServerPrerenderController.abort()\n        },\n        { once: true }\n      )\n\n      // Wait for all caches to be finished filling and for async imports to resolve\n      trackPendingModules(cacheSignal)\n      await cacheSignal.cacheReady()\n\n      initialServerReactController.abort()\n\n      // We don't need to continue the prerender process if we already\n      // detected invalid dynamic usage in the initial prerender phase.\n      if (workStore.invalidDynamicUsageError) {\n        logDisallowedDynamicError(workStore, workStore.invalidDynamicUsageError)\n        throw new StaticGenBailoutError()\n      }\n\n      let initialServerResult\n      try {\n        initialServerResult = await createReactServerPrerenderResult(\n          pendingInitialServerResult\n        )\n      } catch (err) {\n        if (\n          initialServerReactController.signal.aborted ||\n          initialServerPrerenderController.signal.aborted\n        ) {\n          // These are expected errors that might error the prerender. we ignore them.\n        } else if (\n          process.env.NEXT_DEBUG_BUILD ||\n          process.env.__NEXT_VERBOSE_LOGGING\n        ) {\n          // We don't normally log these errors because we are going to retry anyway but\n          // it can be useful for debugging Next.js itself to get visibility here when needed\n          printDebugThrownValueForProspectiveRender(err, workStore.route)\n        }\n      }\n\n      if (initialServerResult) {\n        const initialClientPrerenderController = new AbortController()\n        const initialClientReactController = new AbortController()\n        const initialClientRenderController = new AbortController()\n\n        const initialClientPrerenderStore: PrerenderStore = {\n          type: 'prerender-client',\n          phase: 'render',\n          rootParams,\n          fallbackRouteParams,\n          implicitTags,\n          renderSignal: initialClientRenderController.signal,\n          controller: initialClientPrerenderController,\n          // For HTML Generation the only cache tracked activity\n          // is module loading, which has it's own cache signal\n          cacheSignal: null,\n          dynamicTracking: null,\n          allowEmptyStaticShell,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags.tags],\n          prerenderResumeDataCache,\n          renderResumeDataCache,\n          hmrRefreshHash: undefined,\n          captureOwnerStack: undefined, // Not available in production.\n        }\n\n        const prerender = (\n          require('react-dom/static') as typeof import('react-dom/static')\n        ).prerender\n        const pendingInitialClientResult = workUnitAsyncStorage.run(\n          initialClientPrerenderStore,\n          prerender,\n          // eslint-disable-next-line @next/internal/no-ambiguous-jsx\n          <App\n            reactServerStream={initialServerResult.asUnclosingStream()}\n            reactDebugStream={undefined}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={nonce}\n            images={ctx.renderOpts.images}\n          />,\n          {\n            signal: initialClientReactController.signal,\n            onError: (err) => {\n              const digest = getDigestForWellKnownError(err)\n\n              if (digest) {\n                return digest\n              }\n\n              if (isReactLargeShellError(err)) {\n                // TODO: Aggregate\n                console.error(err)\n                return undefined\n              }\n\n              if (initialClientReactController.signal.aborted) {\n                // These are expected errors that might error the prerender. we ignore them.\n              } else if (\n                process.env.NEXT_DEBUG_BUILD ||\n                process.env.__NEXT_VERBOSE_LOGGING\n              ) {\n                // We don't normally log these errors because we are going to retry anyway but\n                // it can be useful for debugging Next.js itself to get visibility here when needed\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            },\n            bootstrapScripts: [bootstrapScript],\n          }\n        )\n\n        // The listener to abort our own render controller must be added after\n        // React has added its listener, to ensure that pending I/O is not\n        // aborted/rejected too early.\n        initialClientReactController.signal.addEventListener(\n          'abort',\n          () => {\n            initialClientRenderController.abort()\n          },\n          { once: true }\n        )\n\n        pendingInitialClientResult.catch((err) => {\n          if (\n            initialClientReactController.signal.aborted ||\n            isPrerenderInterruptedError(err)\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        })\n\n        // This is mostly needed for dynamic `import()`s in client components.\n        // Promises passed to client were already awaited above (assuming that they came from cached functions)\n        trackPendingModules(cacheSignal)\n        await cacheSignal.cacheReady()\n        initialClientReactController.abort()\n      }\n\n      const finalServerReactController = new AbortController()\n      const finalServerRenderController = new AbortController()\n\n      const finalServerPayloadPrerenderStore: PrerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        // While this render signal isn't going to be used to abort a React render while getting the RSC payload\n        // various request data APIs bind to this controller to reject after completion.\n        renderSignal: finalServerRenderController.signal,\n        // When we generate the RSC payload we might abort this controller due to sync IO\n        // but we don't actually care about sync IO in this phase so we use a throw away controller\n        // that isn't connected to anything\n        controller: new AbortController(),\n        // All caches we could read must already be filled so no tracking is necessary\n        cacheSignal: null,\n        dynamicTracking: null,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      }\n\n      const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n        finalServerPayloadPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const serverDynamicTracking = createDynamicTrackingState(\n        isDebugDynamicAccesses\n      )\n      let serverIsDynamic = false\n\n      const finalServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        renderSignal: finalServerRenderController.signal,\n        controller: finalServerReactController,\n        // All caches we could read must already be filled so no tracking is necessary\n        cacheSignal: null,\n        dynamicTracking: serverDynamicTracking,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      })\n\n      let prerenderIsPending = true\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResult(\n          prerenderAndAbortInSequentialTasks(\n            async () => {\n              const pendingPrerenderResult = workUnitAsyncStorage.run(\n                // The store to scope\n                finalServerPrerenderStore,\n                // The function to run\n                ComponentMod.prerender,\n                // ... the arguments for the function to run\n                finalAttemptRSCPayload,\n                clientReferenceManifest.clientModules,\n                {\n                  filterStackFrame,\n                  onError: (err: unknown) => {\n                    return serverComponentsErrorHandler(err)\n                  },\n                  signal: finalServerReactController.signal,\n                }\n              )\n\n              // The listener to abort our own render controller must be added\n              // after React has added its listener, to ensure that pending I/O\n              // is not aborted/rejected too early.\n              finalServerReactController.signal.addEventListener(\n                'abort',\n                () => {\n                  finalServerRenderController.abort()\n                },\n                { once: true }\n              )\n\n              const prerenderResult = await pendingPrerenderResult\n              prerenderIsPending = false\n\n              return prerenderResult\n            },\n            () => {\n              if (finalServerReactController.signal.aborted) {\n                // If the server controller is already aborted we must have called something\n                // that required aborting the prerender synchronously such as with new Date()\n                serverIsDynamic = true\n                return\n              }\n\n              if (prerenderIsPending) {\n                // If prerenderIsPending then we have blocked for longer than a Task and we assume\n                // there is something unfinished.\n                serverIsDynamic = true\n              }\n\n              finalServerReactController.abort()\n            }\n          )\n        ))\n\n      const clientDynamicTracking = createDynamicTrackingState(\n        isDebugDynamicAccesses\n      )\n\n      const finalClientReactController = new AbortController()\n      const finalClientRenderController = new AbortController()\n\n      const finalClientPrerenderStore: PrerenderStore = {\n        type: 'prerender-client',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        renderSignal: finalClientRenderController.signal,\n        controller: finalClientReactController,\n        // No APIs require a cacheSignal through the workUnitStore during the HTML prerender\n        cacheSignal: null,\n        dynamicTracking: clientDynamicTracking,\n        allowEmptyStaticShell,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n        renderResumeDataCache,\n        hmrRefreshHash: undefined,\n        captureOwnerStack: undefined, // Not available in production.\n      }\n\n      let dynamicValidation = createDynamicValidationState()\n\n      const prerender = (\n        require('react-dom/static') as typeof import('react-dom/static')\n      ).prerender\n      let { prelude: unprocessedPrelude, postponed } =\n        await prerenderAndAbortInSequentialTasks(\n          () => {\n            const pendingFinalClientResult = workUnitAsyncStorage.run(\n              finalClientPrerenderStore,\n              prerender,\n              // eslint-disable-next-line @next/internal/no-ambiguous-jsx\n              <App\n                reactServerStream={reactServerResult.asUnclosingStream()}\n                reactDebugStream={undefined}\n                preinitScripts={preinitScripts}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                nonce={nonce}\n                images={ctx.renderOpts.images}\n              />,\n              {\n                signal: finalClientReactController.signal,\n                onError: (err: unknown, errorInfo: ErrorInfo) => {\n                  if (\n                    isPrerenderInterruptedError(err) ||\n                    finalClientReactController.signal.aborted\n                  ) {\n                    const componentStack: string | undefined = (\n                      errorInfo as any\n                    ).componentStack\n                    if (typeof componentStack === 'string') {\n                      trackAllowedDynamicAccess(\n                        workStore,\n                        componentStack,\n                        dynamicValidation,\n                        clientDynamicTracking\n                      )\n                    }\n                    return\n                  }\n\n                  return htmlRendererErrorHandler(err, errorInfo)\n                },\n                onHeaders: (headers: Headers) => {\n                  headers.forEach((value, key) => {\n                    appendHeader(key, value)\n                  })\n                },\n                maxHeadersLength: reactMaxHeadersLength,\n                bootstrapScripts: [bootstrapScript],\n              }\n            )\n\n            // The listener to abort our own render controller must be added\n            // after React has added its listener, to ensure that pending I/O is\n            // not aborted/rejected too early.\n            finalClientReactController.signal.addEventListener(\n              'abort',\n              () => {\n                finalClientRenderController.abort()\n              },\n              { once: true }\n            )\n\n            return pendingFinalClientResult\n          },\n          () => {\n            finalClientReactController.abort()\n          }\n        )\n\n      const { prelude, preludeIsEmpty } =\n        await processPrelude(unprocessedPrelude)\n\n      // If we've disabled throwing on empty static shell, then we don't need to\n      // track any dynamic access that occurs above the suspense boundary because\n      // we'll do so in the route shell.\n      if (!allowEmptyStaticShell) {\n        throwIfDisallowedDynamic(\n          workStore,\n          preludeIsEmpty ? PreludeState.Empty : PreludeState.Full,\n          dynamicValidation,\n          serverDynamicTracking\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n      metadata.flightData = flightData\n      metadata.segmentData = await collectSegmentData(\n        flightData,\n        finalServerPrerenderStore,\n        ComponentMod,\n        renderOpts\n      )\n\n      if (serverIsDynamic) {\n        // Dynamic case\n        // We will always need to perform a \"resume\" render of some kind when this route is accessed\n        // because the RSC data itself is dynamic. We determine if there are any HTML holes or not\n        // but generally this is a \"partial\" prerender in that there will be a per-request compute\n        // concatenated to the static shell.\n        if (postponed != null) {\n          // Dynamic HTML case\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            preludeIsEmpty\n              ? DynamicHTMLPreludeState.Empty\n              : DynamicHTMLPreludeState.Full,\n            fallbackRouteParams,\n            resumeDataCache,\n            cacheComponents\n          )\n        } else {\n          // Dynamic Data case\n          metadata.postponed = await getDynamicDataPostponedState(\n            resumeDataCache,\n            cacheComponents\n          )\n        }\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n          collectedTags: finalServerPrerenderStore.tags,\n          renderResumeDataCache: createRenderResumeDataCache(resumeDataCache),\n        }\n      } else {\n        // Static case\n        // We will not perform resumption per request. The result can be served statically to the requestor\n        // and if there was anything dynamic it will only be rendered in the browser.\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = (\n            require('react-dom/server') as typeof import('react-dom/server')\n          ).resume\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            // eslint-disable-next-line @next/internal/no-ambiguous-jsx\n            <App\n              reactServerStream={foreverStream}\n              reactDebugStream={undefined}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              nonce={nonce}\n              images={ctx.renderOpts.images}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createRenderInBrowserAbortSignal(),\n              onError: htmlRendererErrorHandler,\n              nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        let finalStream\n        const hasFallbackRouteParams =\n          fallbackRouteParams && fallbackRouteParams.size > 0\n        if (hasFallbackRouteParams) {\n          // This is a \"static fallback\" prerender: although the page didn't\n          // access any runtime params in a Server Component, it may have\n          // accessed a runtime param in a client segment.\n          //\n          // TODO: If there were no client segments, we can use the fully static\n          // path instead.\n          //\n          // Rather than use a dynamic server resume to fill in the params,\n          // we can rely on the client to parse the params from the URL and use\n          // that to hydrate the page.\n          //\n          // Send an empty InitialRSCPayload to the server component renderer\n          // The data will be fetched by the client instead.\n          // TODO: In the future, rather than defer the entire hydration payload\n          // to be fetched by the client, we should only defer the client\n          // segments, since those are the only ones whose data is not complete.\n          const emptyReactServerResult =\n            await createReactServerPrerenderResultFromRender(\n              ComponentMod.renderToReadableStream(\n                [],\n                clientReferenceManifest.clientModules,\n                {\n                  filterStackFrame,\n                  onError: serverComponentsErrorHandler,\n                }\n              )\n            )\n          finalStream = await continueStaticFallbackPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              emptyReactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            isBuildTimePrerendering:\n              ctx.workStore.isBuildTimePrerendering === true,\n            buildId: ctx.workStore.buildId,\n          })\n        } else {\n          // Normal static prerender case, no fallback param handling needed\n          finalStream = await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            isBuildTimePrerendering:\n              ctx.workStore.isBuildTimePrerendering === true,\n            buildId: ctx.workStore.buildId,\n          })\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: finalStream,\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(finalServerPrerenderStore.stale),\n          collectedTags: finalServerPrerenderStore.tags,\n          renderResumeDataCache: createRenderResumeDataCache(resumeDataCache),\n        }\n      }\n    } else if (experimental.isRoutePPREnabled) {\n      // We're statically generating with PPR and need to do dynamic tracking\n      let dynamicTracking = createDynamicTrackingState(isDebugDynamicAccesses)\n\n      const prerenderResumeDataCache = createPrerenderResumeDataCache()\n      const reactServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      })\n      const RSCPayload = await workUnitAsyncStorage.run(\n        reactServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            reactServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            // ... the arguments for the function to run\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              filterStackFrame,\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const ssrPrerenderStore: PrerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        fallbackRouteParams,\n        implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n        prerenderResumeDataCache,\n      }\n      const prerender = (\n        require('react-dom/static') as typeof import('react-dom/static')\n      ).prerender\n      const { prelude: unprocessedPrelude, postponed } =\n        await workUnitAsyncStorage.run(\n          ssrPrerenderStore,\n          prerender,\n          // eslint-disable-next-line @next/internal/no-ambiguous-jsx\n          <App\n            reactServerStream={reactServerResult.asUnclosingStream()}\n            reactDebugStream={undefined}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            nonce={nonce}\n            images={ctx.renderOpts.images}\n          />,\n          {\n            onError: htmlRendererErrorHandler,\n            onHeaders: (headers: Headers) => {\n              headers.forEach((value, key) => {\n                appendHeader(key, value)\n              })\n            },\n            maxHeadersLength: reactMaxHeadersLength,\n            bootstrapScripts: [bootstrapScript],\n          }\n        )\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      // After awaiting here we've waited for the entire RSC render to complete. Crucially this means\n      // that when we detect whether we've used dynamic APIs below we know we'll have picked up even\n      // parts of the React Server render that might not be used in the SSR render.\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          ssrPrerenderStore,\n          ComponentMod,\n          renderOpts\n        )\n      }\n\n      const { prelude, preludeIsEmpty } =\n        await processPrelude(unprocessedPrelude)\n\n      /**\n       * When prerendering there are three outcomes to consider\n       *\n       *   Dynamic HTML:      The prerender has dynamic holes (caused by using Next.js Dynamic Rendering APIs)\n       *                      We will need to resume this result when requests are handled and we don't include\n       *                      any server inserted HTML or inlined flight data in the static HTML\n       *\n       *   Dynamic Data:      The prerender has no dynamic holes but dynamic APIs were used. We will not\n       *                      resume this render when requests are handled but we will generate new inlined\n       *                      flight data since it is dynamic and differences may end up reconciling on the client\n       *\n       *   Static:            The prerender has no dynamic holes and no dynamic APIs were used. We statically encode\n       *                      all server inserted HTML and flight data\n       */\n      // First we check if we have any dynamic holes in our HTML prerender\n      if (accessedDynamicData(dynamicTracking.dynamicAccesses)) {\n        if (postponed != null) {\n          // Dynamic HTML case.\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            preludeIsEmpty\n              ? DynamicHTMLPreludeState.Empty\n              : DynamicHTMLPreludeState.Full,\n            fallbackRouteParams,\n            prerenderResumeDataCache,\n            cacheComponents\n          )\n        } else {\n          // Dynamic Data case.\n          metadata.postponed = await getDynamicDataPostponedState(\n            prerenderResumeDataCache,\n            cacheComponents\n          )\n        }\n        // Regardless of whether this is the Dynamic HTML or Dynamic Data case we need to ensure we include\n        // server inserted html in the static response because the html that is part of the prerender may depend on it\n        // It is possible in the set of stream transforms for Dynamic HTML vs Dynamic Data may differ but currently both states\n        // require the same set so we unify the code path here\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else if (fallbackRouteParams && fallbackRouteParams.size > 0) {\n        // Rendering the fallback case.\n        metadata.postponed = await getDynamicDataPostponedState(\n          prerenderResumeDataCache,\n          cacheComponents\n        )\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else {\n        // Static case\n        // We still have not used any dynamic APIs. At this point we can produce an entirely static prerender response\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = (\n            require('react-dom/server') as typeof import('react-dom/server')\n          ).resume\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            // eslint-disable-next-line @next/internal/no-ambiguous-jsx\n            <App\n              reactServerStream={foreverStream}\n              reactDebugStream={undefined}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              nonce={nonce}\n              images={ctx.renderOpts.images}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createRenderInBrowserAbortSignal(),\n              onError: htmlRendererErrorHandler,\n              nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            isBuildTimePrerendering:\n              ctx.workStore.isBuildTimePrerendering === true,\n            buildId: ctx.workStore.buildId,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: selectStaleTime(reactServerPrerenderStore.stale),\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      }\n    } else {\n      const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-legacy',\n        phase: 'render',\n        rootParams,\n        implicitTags,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags.tags],\n      })\n      // This is a regular static generation. We don't do dynamic tracking because we rely on\n      // the old-school dynamic error handling to bail out of static generation\n      const RSCPayload = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            prerenderLegacyStore,\n            ComponentMod.renderToReadableStream,\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              filterStackFrame,\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const renderToReadableStream = (\n        require('react-dom/server') as typeof import('react-dom/server')\n      ).renderToReadableStream\n      const htmlStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToReadableStream,\n        // eslint-disable-next-line @next/internal/no-ambiguous-jsx\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          reactDebugStream={undefined}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          nonce={nonce}\n          images={ctx.renderOpts.images}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          nonce,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath,\n        tracingMetadata: tracingMetadata,\n      })\n      return {\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consumeAsStream(),\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          isBuildTimePrerendering:\n            ctx.workStore.isBuildTimePrerendering === true,\n          buildId: ctx.workStore.buildId,\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        }),\n        // TODO: Should this include the SSR pass?\n        collectedRevalidate: prerenderLegacyStore.revalidate,\n        collectedExpire: prerenderLegacyStore.expire,\n        collectedStale: selectStaleTime(prerenderLegacyStore.stale),\n        collectedTags: prerenderLegacyStore.tags,\n      }\n    }\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If this is a static generation error, we need to throw it so that it\n    // can be handled by the caller if we're in static generation mode.\n    if (isDynamicServerError(err)) {\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    // If we errored when we did not have an RSC stream to read from. This is\n    // not just a render error, we need to throw early.\n    if (reactServerPrerenderResult === null) {\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      metadata.statusCode = res.statusCode\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n      metadata.statusCode = res.statusCode\n\n      const redirectUrl = addPathPrefix(getURLFromRedirectError(err), basePath)\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n      metadata.statusCode = res.statusCode\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      buildManifest,\n      assetPrefix,\n      crossOrigin,\n      subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      nonce,\n      '/_not-found/page'\n    )\n\n    const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n      type: 'prerender-legacy',\n      phase: 'render',\n      rootParams,\n      implicitTags: implicitTags,\n      revalidate:\n        typeof prerenderStore?.revalidate !== 'undefined'\n          ? prerenderStore.revalidate\n          : INFINITE_CACHE,\n      expire:\n        typeof prerenderStore?.expire !== 'undefined'\n          ? prerenderStore.expire\n          : INFINITE_CACHE,\n      stale:\n        typeof prerenderStore?.stale !== 'undefined'\n          ? prerenderStore.stale\n          : INFINITE_CACHE,\n      tags: [...(prerenderStore?.tags || implicitTags.tags)],\n    })\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? undefined : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        filterStackFrame,\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    try {\n      // TODO we should use the same prerender semantics that we initially rendered\n      // with in this case too. The only reason why this is ok atm is because it's essentially\n      // an empty page and no user code runs.\n      const fizzStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer:\n            require('react-dom/server') as typeof import('react-dom/server'),\n          element: (\n            // eslint-disable-next-line @next/internal/no-ambiguous-jsx\n            <ErrorApp\n              reactServerStream={errorServerStream}\n              reactDebugStream={undefined}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              nonce={nonce}\n              images={ctx.renderOpts.images}\n            />\n          ),\n          streamOptions: {\n            nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(\n          reactServerPrerenderResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts\n        )\n      }\n\n      // This is intentionally using the readable datastream from the main\n      // render rather than the flight data from the error page render\n      const flightStream = reactServerPrerenderResult.consumeAsStream()\n\n      return {\n        // Returning the error that was thrown so it can be used to handle\n        // the response in the caller.\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(fizzStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            flightStream,\n            nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          isBuildTimePrerendering:\n            ctx.workStore.isBuildTimePrerendering === true,\n          buildId: ctx.workStore.buildId,\n          getServerInsertedHTML: makeGetServerInsertedHTML({\n            polyfills,\n            renderServerInsertedHTML,\n            serverCapturedErrors: [],\n            basePath,\n            tracingMetadata: tracingMetadata,\n          }),\n          getServerInsertedMetadata,\n          validateRootLayout: dev,\n        }),\n        dynamicAccess: null,\n        collectedRevalidate:\n          prerenderStore !== null ? prerenderStore.revalidate : INFINITE_CACHE,\n        collectedExpire:\n          prerenderStore !== null ? prerenderStore.expire : INFINITE_CACHE,\n        collectedStale: selectStaleTime(\n          prerenderStore !== null ? prerenderStore.stale : INFINITE_CACHE\n        ),\n        collectedTags: prerenderStore !== null ? prerenderStore.tags : null,\n      }\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nconst getGlobalErrorStyles = async (\n  tree: LoaderTree,\n  ctx: AppRenderContext\n): Promise<{\n  GlobalError: GlobalErrorComponent\n  styles: ReactNode | undefined\n}> => {\n  const {\n    modules: { 'global-error': globalErrorModule },\n  } = parseLoaderTree(tree)\n\n  const {\n    componentMod: { createElement },\n  } = ctx\n  const GlobalErrorComponent: GlobalErrorComponent =\n    ctx.componentMod.GlobalError\n  let globalErrorStyles\n  if (globalErrorModule) {\n    const [, styles] = await createComponentStylesAndScripts({\n      ctx,\n      filePath: globalErrorModule[1],\n      getComponent: globalErrorModule[0],\n      injectedCSS: new Set(),\n      injectedJS: new Set(),\n    })\n    globalErrorStyles = styles\n  }\n  if (ctx.renderOpts.dev) {\n    const dir =\n      (process.env.NEXT_RUNTIME === 'edge'\n        ? process.env.__NEXT_EDGE_PROJECT_DIR\n        : ctx.renderOpts.dir) || ''\n\n    const globalErrorModulePath = normalizeConventionFilePath(\n      dir,\n      globalErrorModule?.[1]\n    )\n    if (globalErrorModulePath) {\n      const SegmentViewNode = ctx.componentMod.SegmentViewNode\n      globalErrorStyles =\n        // This will be rendered next to GlobalError component under ErrorBoundary,\n        // it requires a key to avoid React warning about duplicate keys.\n        createElement(\n          SegmentViewNode,\n          {\n            key: 'ge-svn',\n            type: 'global-error',\n            pagePath: globalErrorModulePath,\n          },\n          globalErrorStyles\n        )\n    }\n  }\n\n  return {\n    GlobalError: GlobalErrorComponent,\n    styles: globalErrorStyles,\n  }\n}\n\nfunction createSelectStaleTime(experimental: ExperimentalConfig) {\n  return (stale: number) =>\n    stale === INFINITE_CACHE &&\n    typeof experimental.staleTimes?.static === 'number'\n      ? experimental.staleTimes.static\n      : stale\n}\n\nasync function collectSegmentData(\n  fullPageDataBuffer: Buffer,\n  prerenderStore: PrerenderStore,\n  ComponentMod: AppPageModule,\n  renderOpts: RenderOpts\n): Promise<Map<string, Buffer> | undefined> {\n  // Per-segment prefetch data\n  //\n  // All of the segments for a page are generated simultaneously, including\n  // during revalidations. This is to ensure consistency, because it's\n  // possible for a mismatch between a layout and page segment can cause the\n  // client to error during rendering. We want to preserve the ability of the\n  // client to recover from such a mismatch by re-requesting all the segments\n  // to get a consistent view of the page.\n  //\n  // For performance, we reuse the Flight output that was created when\n  // generating the initial page HTML. The Flight stream for the whole page is\n  // decomposed into a separate stream per segment.\n\n  const clientReferenceManifest = renderOpts.clientReferenceManifest\n  if (\n    !clientReferenceManifest ||\n    // Do not generate per-segment data unless the experimental Segment Cache\n    // flag is enabled.\n    //\n    // We also skip generating segment data if flag is set to \"client-only\",\n    // rather than true. (The \"client-only\" option only affects the behavior of\n    // the client-side implementation; per-segment prefetches are intentionally\n    // disabled in that configuration).\n    renderOpts.experimental.clientSegmentCache !== true\n  ) {\n    return\n  }\n\n  // Manifest passed to the Flight client for reading the full-page Flight\n  // stream. Based off similar code in use-cache-wrapper.ts.\n  const isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n  const serverConsumerManifest = {\n    // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n    // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n    // which themselves will handle the preloading.\n    moduleLoading: null,\n    moduleMap: isEdgeRuntime\n      ? clientReferenceManifest.edgeRscModuleMapping\n      : clientReferenceManifest.rscModuleMapping,\n    serverModuleMap: getServerModuleMap(),\n  }\n\n  const selectStaleTime = createSelectStaleTime(renderOpts.experimental)\n  const staleTime = selectStaleTime(prerenderStore.stale)\n  return await ComponentMod.collectSegmentData(\n    renderOpts.cacheComponents,\n    fullPageDataBuffer,\n    staleTime,\n    clientReferenceManifest.clientModules as ManifestNode,\n    serverConsumerManifest\n  )\n}\n\nfunction isBypassingCachesInDev(\n  renderOpts: RenderOpts,\n  requestStore: RequestStore\n): boolean {\n  return (\n    process.env.NODE_ENV === 'development' &&\n    !!renderOpts.dev &&\n    requestStore.headers.get('cache-control') === 'no-cache'\n  )\n}\n\nfunction WarnForBypassCachesInDev({ route }: { route: string }) {\n  warnOnce(\n    `Route ${route} is rendering with server caches disabled. For this navigation, Component Metadata in React DevTools will not accurately reflect what is statically prerenderable and runtime prefetchable. See more info here: https://nextjs.org/docs/messages/cache-bypass-in-dev`\n  )\n  return null\n}\n"], "names": ["renderToHTMLOrFlight", "flightDataPathHeadKey", "getFlightViewportKey", "requestId", "getFlightMetadataKey", "filterStackFrame", "process", "env", "NODE_ENV", "require", "filterStackFrameDEV", "undefined", "parseRequestHeaders", "headers", "options", "isPrefetchRequest", "NEXT_ROUTER_PREFETCH_HEADER", "isRuntimePrefetchRequest", "isHmrRefresh", "NEXT_HMR_REFRESH_HEADER", "isRSCRequest", "RSC_HEADER", "shouldProvideFlightRouterState", "isRoutePPREnabled", "flightRouterState", "parseAndValidateFlightRouterState", "NEXT_ROUTER_STATE_TREE_HEADER", "isRouteTreePrefetchRequest", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "csp", "nonce", "getScriptNonceFromHeader", "previouslyRevalidatedTags", "getPreviouslyRevalidatedTags", "previewModeId", "htmlRequestId", "NEXT_REQUEST_ID_HEADER", "NEXT_HTML_REQUEST_ID_HEADER", "createNotFoundLoaderTree", "loaderTree", "components", "hasGlobalNotFound", "notFoundTreeComponents", "layout", "page", "children", "PAGE_SEGMENT_KEY", "makeGetDynamicParamFromSegment", "interpolatedParams", "fallbackRouteParams", "getDynamicParamFromSegment", "segment", "segmentParam", "getSegmentParam", "segmentKey", "param", "dynamicParamType", "dynamicParamTypes", "type", "getDynamicParam", "NonIndex", "createElement", "pagePath", "statusCode", "isPossibleServerAction", "is404Page", "isInvalidStatusCode", "name", "content", "generateDynamicRSCPayload", "ctx", "flightData", "componentMod", "routeModule", "userland", "createMetadataComponents", "Fragment", "query", "workStore", "url", "serveStreamingMetadata", "renderOpts", "skipFlight", "preloadCallbacks", "Viewport", "<PERSON><PERSON><PERSON>", "MetadataOutlet", "tree", "parsed<PERSON><PERSON><PERSON>", "pathname", "metadataContext", "createMetadataContext", "walkTreeWithFlightRouterState", "loaderTreeToFilter", "parentParams", "rscHead", "key", "res", "injectedCSS", "Set", "injectedJS", "injectedFontPreloadTags", "rootLayoutIncluded", "map", "path", "slice", "actionResult", "a", "f", "b", "sharedContext", "buildId", "S", "isStaticGeneration", "createErrorContext", "renderSource", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason", "generateDynamicFlightRenderResult", "req", "requestStore", "clientReferenceManifest", "renderToReadableStream", "dev", "onInstrumentationRequestError", "setReactDebugChannel", "onFlightDataRenderError", "err", "onError", "createFlightReactServerErrorHandler", "debugChannel", "createDebugChannel", "clientSide", "rscPayload", "workUnitAsyncStorage", "run", "flightReadableStream", "clientModules", "temporaryReferences", "serverSide", "FlightRenderResult", "fetchMetrics", "stagedRenderToReadableStreamWithoutCachesInDev", "getPayload", "stageController", "StagedRenderingController", "environmentName", "currentStage", "RenderStage", "Static", "Runtime", "Dynamic", "InvariantError", "stagedRendering", "asyncApiPromises", "createAsyncApiPromisesInDev", "cookies", "mutableCookies", "scheduleInSequentialTasks", "advanceStage", "generateDynamicFlightRenderResultWithStagesInDev", "initialRequestStore", "createRequestStore", "setCacheStatus", "payload", "isBypassingCachesInDev", "_bypassCachesInDev", "WarnForBypassCachesInDev", "route", "stream", "result", "renderWithRestartOnCacheMissInDev", "assertClientReferenceManifest", "generateRuntimePrefetchResult", "metadata", "generatePayload", "rootParams", "getRootParams", "prerenderResumeDataCache", "createPrerenderResumeDataCache", "renderResumeDataCache", "prospectiveRuntimeServerPrerender", "draftMode", "response", "finalRuntimeServerPrerender", "applyMetadataFromPrerenderResult", "isPartial", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_DID_POSTPONE_HEADER", "prelude", "implicitTags", "ComponentMod", "initialServerPrerenderController", "AbortController", "initialServerRenderController", "cacheSignal", "CacheSignal", "initialServerPrerenderStore", "phase", "renderSignal", "signal", "controller", "dynamicTracking", "revalidate", "expire", "stale", "INFINITE_CACHE", "tags", "hmrRefreshHash", "captureOwnerStack", "runtimeStagePromise", "initialServerPayload", "pendingInitialServerResult", "prerender", "digest", "getDigestForWellKnownError", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "printDebugThrownValueForProspectiveRender", "onPostpone", "trackPendingModules", "cacheReady", "abort", "invalidDynamicUsageError", "createReactServerPrerenderResult", "experimental", "isDebugDynamicAccesses", "selectStaleTime", "createSelectStaleTime", "serverIsDynamic", "finalServerController", "serverDynamicTracking", "createDynamicTrackingState", "promise", "resolve", "resolveBlockedRuntimeAPIs", "createPromiseWithResolvers", "finalServerPrerenderStore", "finalRSCPayload", "prerenderIsPending", "prerenderAndAbortInSequentialTasksWithStages", "prerenderResult", "dynamicAccess", "collectedRevalidate", "collectedExpire", "collectedStale", "collectedTags", "prepareInitialCanonicalUrl", "search", "split", "getRenderedSearch", "pairs", "value", "Array", "isArray", "v", "push", "encodeURIComponent", "String", "length", "join", "getRSCPayload", "is404", "missingSlots", "appUsingSizeAdjustment", "initialTree", "createFlightRouterStateFromLoaderTree", "errorType", "seedData", "createComponentTree", "authInterrupts", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "couldBeIntercepted", "includes", "NEXT_URL", "initialHead", "GlobalError", "styles", "globalErrorStyles", "getGlobalErrorStyles", "isPossiblyPartialHead", "P", "Preloads", "c", "q", "i", "m", "G", "s", "postponed", "for<PERSON>ach", "preloadFn", "getErrorRSCPayload", "ssrError", "isError", "Error", "id", "message", "stack", "App", "reactServerStream", "reactDebugStream", "preinitScripts", "ServerInsertedHTMLProvider", "images", "ReactClient", "use", "useFlightStream", "initialState", "createInitialRouterState", "navigatedAt", "initialFlightData", "initialCanonicalUrlParts", "initialRenderedSearch", "initialParallelRoutes", "Map", "location", "actionQueue", "createMutableActionQueue", "HeadManagerContext", "Provider", "appDir", "ImageConfigContext", "imageConfigDefault", "AppRouter", "globalErrorState", "ErrorApp", "renderToHTMLOrFlightImpl", "parsedRequestHeaders", "postponedState", "serverComponentsHmrCache", "isNotFoundPath", "requestTimestamp", "Date", "now", "serverActionsManifest", "nextFontManifest", "serverActions", "assetPrefix", "enableTainting", "cacheComponents", "__next_app__", "instrumented", "wrapClientComponentLoader", "shouldTrackModuleLoading", "workUnitStore", "getStore", "__next_require__", "args", "exportsOrPromise", "trackPendingImport", "globalThis", "__next_chunk_load__", "loadingChunk", "loadChunk", "trackPendingChunkLoad", "setIsrStatus", "URL", "NEXT_RUNTIME", "isNodeNextRequest", "onClose", "shouldTrackFetchMetrics", "originalRequest", "on", "metrics", "getClientComponentLoaderMetrics", "reset", "getTracer", "startSpan", "NextNodeServerSpan", "clientComponentLoading", "startTime", "clientComponentLoadStart", "attributes", "clientComponentLoadCount", "end", "clientComponentLoadTimes", "appUsingSizeAdjust", "serverModuleMap", "createServerModuleMap", "setReferenceManifestsSingleton", "patchFetch", "taintObjectReference", "stripInternalQueries", "<PERSON><PERSON><PERSON>", "from", "crypto", "subtle", "toString", "randomUUID", "nanoid", "isPossibleActionRequest", "getIsPossibleServerAction", "getImplicitTags", "isPrefetch", "setRootSpanAttribute", "prerenderToStreamWithTracing", "wrap", "AppRenderSpan", "getBodyResult", "spanName", "prerenderToStream", "accessedDynamicData", "warn", "access", "formatDynamicAPIAccesses", "logDisallowedDynamicError", "StaticGenBailoutError", "digestErrorsMap", "size", "buildFailingError", "values", "next", "ssrErrors", "find", "isUserLandError", "contentType", "HTML_CONTENT_TYPE_HEADER", "pendingRevalidates", "pendingRevalidateWrites", "pendingRevalidatedTags", "pendingPromise", "executeRevalidates", "finally", "NEXT_PRIVATE_DEBUG_CACHE", "console", "log", "waitUntil", "RenderResult", "streamToString", "devValidatingFallbackParams", "getRequestMeta", "createRequestStoreForRender", "bind", "onUpdateCookies", "previewProps", "isStatic", "usedDynamic", "forceDynamic", "renderToStreamWithTracing", "renderToStream", "didExecuteServerAction", "formState", "actionRequestResult", "handleAction", "generateFlight", "notFoundLoaderTree", "assignMetadata", "parseRelativeUrl", "interpolateParallelRouteParams", "params", "parsePostponedState", "createWorkStore", "definition", "workAsyncStorage", "fetchTags", "staleHeader", "NEXT_ROUTER_STALE_TIME_HEADER", "forceStatic", "cacheControl", "staticBailoutInfo", "description", "dynamicUsageDescription", "dynamicUsageStack", "basePath", "buildManifest", "serverRenderToReadableStream", "crossOrigin", "nextExport", "reactMaxHeadersLength", "shouldWaitOnAllReady", "subresourceIntegrityManifest", "supportsDynamicResponse", "renderServerInsertedHTML", "createServerInsertedHTML", "getServerInsertedMetadata", "createServerInsertedMetadata", "tracingMetadata", "getTracedMetadata", "getTracePropagationData", "clientTraceMetadata", "polyfills", "polyfillFiles", "filter", "polyfill", "endsWith", "src", "getAssetQueryString", "integrity", "noModule", "bootstrapScript", "getRequiredScripts", "bootstrapScriptContent", "JSON", "stringify", "reactServerErrorsByDigest", "silenceLogger", "onHTMLRenderRSCError", "serverComponentsErrorHandler", "createHTMLReactServerErrorHandler", "onHTMLRenderSSRError", "allCapturedErrors", "htmlRendererErrorHandler", "createHTMLErrorHandler", "reactServerResult", "append<PERSON><PERSON>er", "resolveValidation", "validationOutlet", "createValidationOutlet", "_validation", "serverStream", "returnedDebugChannel", "finalRequestStore", "ReactServerResult", "readableSsr", "readableBrowser", "readable", "tee", "consoleAsyncStorage", "dim", "spawnDynamicValidationInDev", "RSCPayload", "waitAtLeastOneReactRenderTask", "DynamicState", "DATA", "inlinedReactServerDataStream", "createInlinedDataReadableStream", "chainStreams", "createDocumentClosingStream", "preludeState", "getPostponedFromState", "resume", "htmlStream", "getServerInsertedHTML", "makeGetServerInsertedHTML", "serverCapturedErrors", "continueDynamicHTMLResume", "delayDataUntilFirstHtmlChunk", "DynamicHTMLPreludeState", "Empty", "inlinedDataStream", "consume", "onHeaders", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bootstrapScripts", "generateStaticHTML", "continueFizzStream", "isBuildTimePrerendering", "validateRootLayout", "isStaticGenBailoutError", "shouldBailoutToCSR", "isBailoutToCSRError", "getStackWithoutErrorMessage", "error", "reason", "isHTTPAccessFallbackError", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isRedirectError", "getRedirectStatusCodeFromError", "redirectUrl", "addPathPrefix", "getURLFromRedirectError", "Headers", "appendMutableCookies", "errorPreinitScripts", "errorBootstrapScript", "errorRSCPayload", "has", "errorServerStream", "fizzStream", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "finalErr", "bailOnRootNotFound", "hasRuntimePrefetch", "anySegmentHasRuntimePrefetchEnabled", "initialReactController", "initialDataController", "initialStageController", "initialRscPayload", "maybeInitialServerStream", "pipelineInSequentialTasks", "addEventListener", "hasPendingReads", "maybeStream", "finalStageController", "createRenderResumeDataCache", "finalRscPayload", "finalServerStream", "delayUntilStage", "sharedParamsParent", "sharedSearchParamsParent", "connection", "readableController", "clientSideReadable", "ReadableStream", "start", "writable", "WritableStream", "write", "chunk", "enqueue", "close", "outlet", "Promise", "isNotFound", "allowEmptyStaticShell", "get", "NEXT_HMR_REFRESH_HASH_COOKIE", "initialServerReactController", "captureOwnerStackClient", "captureOwnerStackServer", "initialServerPayloadPrerenderStore", "isReactLargeShellError", "once", "LogSafely", "fn", "initialServerResult", "initialClientPrerenderController", "initialClientReactController", "initialClientRenderController", "initialClientPrerenderStore", "pendingInitialClientResult", "asUnclosingStream", "catch", "isPrerenderInterruptedError", "finalServerReactController", "finalServerRenderController", "finalServerPayloadPrerenderStore", "finalAttemptRSCPayload", "prerenderAndAbortInSequentialTasks", "pendingPrerenderResult", "clientDynamicTracking", "finalClientReactController", "finalClientRenderController", "finalClientPrerenderStore", "dynamicValidation", "createDynamicValidationState", "unprocessedPrelude", "pendingFinalClientResult", "errorInfo", "componentStack", "trackAllowedDynamicAccess", "preludeIsEmpty", "processPrelude", "throwIfDisallowedDynamic", "PreludeState", "Full", "thrownValue", "loggingFunction", "Errored", "originalLoggingFunction", "shouldGenerateStaticFlightData", "reactServerPrerenderResult", "setMetadataHeader", "item", "prerenderStore", "resumeDataCache", "streamToBuffer", "asStream", "segmentData", "collectSegmentData", "getDynamicHTMLPostponedState", "getDynamicDataPostponedState", "continueDynamicPrerender", "consumeDynamicAccess", "foreverStream", "resumeStream", "parse", "createRenderInBrowserAbortSignal", "finalStream", "hasFallbackRouteParams", "emptyReactServerResult", "createReactServerPrerenderResultFromRender", "continueStaticFallbackPrerender", "consumeAsStream", "continueStaticP<PERSON><PERSON>", "reactServerPrerenderStore", "ssrPrerenderStore", "dynamicAccesses", "prerenderLegacyStore", "isDynamicServerError", "flightStream", "modules", "globalErrorModule", "parseLoaderTree", "GlobalErrorComponent", "createComponentStylesAndScripts", "filePath", "getComponent", "dir", "__NEXT_EDGE_PROJECT_DIR", "globalErrorModulePath", "normalizeConventionFilePath", "SegmentViewNode", "staleTimes", "static", "fullPageDataBuffer", "clientSegmentCache", "isEdgeRuntime", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeRscModuleMapping", "rscModuleMapping", "getServerModuleMap", "staleTime", "warnOnce"], "mappings": ";;;;+BAinEaA;;;eAAAA;;;;0CAjmEN;+DAesB;qEAKtB;sCAYA;+BAC8B;kCAa9B;iCAC+B;8BACM;2BACZ;oCAKzB;0BAIA;+BACyB;8BACmB;2BACD;wBACxB;oCACS;oCAQ5B;0CAC2B;iCACF;0CACS;mDACS;uDACI;+BACzB;8BACO;qBACR;gCACS;oCACI;iCACN;+BACL;2CACY;+CACI;qCACK;qCACf;iCAI7B;gCAMA;oCAM8B;mCAI9B;yCAIA;mCACqC;kCAcrC;+CAIA;6BAC+B;yBACJ;kCACD;kEACX;yCAGoB;0CACD;mCACA;uBACL;yBACH;yCAK1B;wCAQmD;sCAInD;2BACuC;8CAIvC;6CAC6B;6BACR;wBACM;gCACH;4BAE0B;iDACT;iCAChB;iCAMzB;gEAEa;8CACyB;6BACA;mCACV;4CAK5B;sCACgC;qCAEK;6BACb;iCAIxB;sCAGoC;iDACR;6BACA;iCACoB;kCACH;0BAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuDzB,MAAMC,wBAAwB;AAC9B,MAAMC,uBAAuB,CAACC,YAAsBA,YAAY;AAChE,MAAMC,uBAAuB,CAACD,YAAsBA,YAAY;AAEhE,MAAME,mBACJC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNC,mBAAmB,GACtBC;AAqBN,SAASC,oBACPC,OAA4B,EAC5BC,OAAmC;IAEnC,mEAAmE;IACnE,4EAA4E;IAC5E,MAAMC,oBAAoBF,OAAO,CAACG,6CAA2B,CAAC,KAAK;IAEnE,MAAMC,2BAA2BJ,OAAO,CAACG,6CAA2B,CAAC,KAAK;IAE1E,MAAME,eAAeL,OAAO,CAACM,yCAAuB,CAAC,KAAKR;IAE1D,MAAMS,eAAeP,OAAO,CAACQ,4BAAU,CAAC,KAAKV;IAE7C,MAAMW,iCACJF,gBAAiB,CAAA,CAACL,qBAAqB,CAACD,QAAQS,iBAAiB,AAAD;IAElE,MAAMC,oBAAoBF,iCACtBG,IAAAA,oEAAiC,EAACZ,OAAO,CAACa,+CAA6B,CAAC,IACxEf;IAEJ,sEAAsE;IACtE,MAAMgB,6BACJd,OAAO,CAACe,qDAAmC,CAAC,KAAK;IAEnD,MAAMC,MACJhB,OAAO,CAAC,0BAA0B,IAClCA,OAAO,CAAC,sCAAsC;IAEhD,MAAMiB,QACJ,OAAOD,QAAQ,WAAWE,IAAAA,kDAAwB,EAACF,OAAOlB;IAE5D,MAAMqB,4BAA4BC,IAAAA,yCAA4B,EAC5DpB,SACAC,QAAQoB,aAAa;IAGvB,IAAI/B;IACJ,IAAIgC;IAEJ,IAAI7B,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,kEAAkE;QAClE,wEAAwE;QACxE,sEAAsE;QACtE,oEAAoE;QAEpEL,YACE,OAAOU,OAAO,CAACuB,wCAAsB,CAAC,KAAK,WACvCvB,OAAO,CAACuB,wCAAsB,CAAC,GAC/BzB;QAENwB,gBACE,OAAOtB,OAAO,CAACwB,6CAA2B,CAAC,KAAK,WAC5CxB,OAAO,CAACwB,6CAA2B,CAAC,GACpC1B;IACR;IAEA,OAAO;QACLa;QACAT;QACAE;QACAU;QACAT;QACAE;QACAU;QACAE;QACA7B;QACAgC;IACF;AACF;AAEA,SAASG,yBAAyBC,UAAsB;IACtD,MAAMC,aAAaD,UAAU,CAAC,EAAE;IAChC,MAAME,oBAAoB,CAAC,CAACD,UAAU,CAAC,mBAAmB;IAC1D,MAAME,yBAAwCD,oBAC1C;QACEE,QAAQH,UAAU,CAAC,mBAAmB;QACtCI,MAAM;YAAC,IAAM;YAAM;SAAiD;IACtE,IACA;QACEA,MAAMJ,UAAU,CAAC,YAAY;IAC/B;IAEJ,OAAO;QACL;QACA;YACEK,UAAU;gBAACC,yBAAgB;gBAAE,CAAC;gBAAGJ;aAAuB;QAC1D;QACA,gEAAgE;QAChED,oBAAoBD,aAAa,CAAC;KACnC;AACH;AAEA;;CAEC,GACD,SAASO,+BACPC,kBAA0B,EAC1BC,mBAAqD;IAErD,OAAO,SAASC,2BACd,gCAAgC;IAChCC,OAAe;QAEf,MAAMC,eAAeC,IAAAA,gCAAe,EAACF;QACrC,IAAI,CAACC,cAAc;YACjB,OAAO;QACT;QACA,MAAME,aAAaF,aAAaG,KAAK;QACrC,MAAMC,mBAAmBC,2CAAiB,CAACL,aAAaM,IAAI,CAAC;QAC7D,OAAOC,IAAAA,gCAAe,EACpBX,oBACAM,YACAE,kBACAP;IAEJ;AACF;AAEA,SAASW,SAAS,EAChBC,aAAa,EACbC,QAAQ,EACRC,UAAU,EACVC,sBAAsB,EAMvB;IACC,MAAMC,YAAYH,aAAa;IAC/B,MAAMI,sBAAsB,OAAOH,eAAe,YAAYA,aAAa;IAE3E,gEAAgE;IAChE,yEAAyE;IACzE,IAAI,CAACC,0BAA2BC,CAAAA,aAAaC,mBAAkB,GAAI;QACjE,OAAOL,cAAc,QAAQ;YAC3BM,MAAM;YACNC,SAAS;QACX;IACF;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,eAAeC,0BACbC,GAAqB,EACrBxD,OAGC;IAED,yDAAyD;IACzD,0GAA0G;IAE1G,gGAAgG;IAChG,mGAAmG;IACnG,0GAA0G;IAC1G,mFAAmF;IACnF,IAAIyD,aAAyB;IAE7B,MAAM,EACJC,cAAc,EACZC,aAAa,EACXC,UAAU,EAAEnC,UAAU,EAAE,EACzB,EACDsB,aAAa,EACbc,wBAAwB,EACxBC,QAAQ,EACT,EACD1B,0BAA0B,EAC1B2B,KAAK,EACL1E,SAAS,EACTqB,iBAAiB,EACjBsD,SAAS,EACTC,GAAG,EACJ,GAAGT;IAEJ,MAAMU,yBAAyB,CAAC,CAACV,IAAIW,UAAU,CAACD,sBAAsB;IAEtE,IAAI,EAAClE,2BAAAA,QAASoE,UAAU,GAAE;QACxB,MAAMC,mBAAqC,EAAE;QAE7C,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAE,GAAGX,yBAAyB;YACtEY,MAAMhD;YACNiD,aAAaX;YACbY,UAAUV,IAAIU,QAAQ;YACtBC,iBAAiBC,IAAAA,sCAAqB,EAACrB,IAAIW,UAAU;YACrD/B;YACA4B;YACAE;QACF;QAEAT,aAAa,AACX,CAAA,MAAMqB,IAAAA,4DAA6B,EAAC;YAClCtB;YACAuB,oBAAoBtD;YACpBuD,cAAc,CAAC;YACftE;YACA,+CAA+C;YAC/CuE,SAASlC,cACPe,UACA;gBACEoB,KAAK/F;YACP,GACA4D,cAAcD,UAAU;gBACtBC;gBACAC,UAAUQ,IAAIR,QAAQ;gBACtBC,YAAYO,IAAI2B,GAAG,CAAClC,UAAU;gBAC9BC,wBAAwBM,IAAIN,sBAAsB;YACpD,IACAH,cAAcuB,UAAU;gBACtBY,KAAK9F,qBAAqBC;YAC5B,IACA0D,cAAcwB,UAAU;gBACtBW,KAAK5F,qBAAqBD;YAC5B;YAEF+F,aAAa,IAAIC;YACjBC,YAAY,IAAID;YAChBE,yBAAyB,IAAIF;YAC7BG,oBAAoB;YACpBnB;YACAG;QACF,EAAC,EACDiB,GAAG,CAAC,CAACC,OAASA,KAAKC,KAAK,CAAC,IAAI,+BAA+B;;IAChE;IAEA,sEAAsE;IACtE,+EAA+E;IAC/E,wBAAwB;IACxB,IAAI3F,2BAAAA,QAAS4F,YAAY,EAAE;QACzB,OAAO;YACLC,GAAG7F,QAAQ4F,YAAY;YACvBE,GAAGrC;YACHsC,GAAGvC,IAAIwC,aAAa,CAACC,OAAO;QAC9B;IACF;IAEA,0CAA0C;IAC1C,OAAO;QACLF,GAAGvC,IAAIwC,aAAa,CAACC,OAAO;QAC5BH,GAAGrC;QACHyC,GAAGlC,UAAUmC,kBAAkB;IACjC;AACF;AAEA,SAASC,mBACP5C,GAAqB,EACrB6C,YAAiD;IAEjD,OAAO;QACLC,YAAY;QACZC,WAAW/C,IAAIR,QAAQ;QACvB,yEAAyE;QACzEwD,WAAWhD,IAAIN,sBAAsB,GAAG,WAAW;QACnDmD;QACAI,kBAAkBC,IAAAA,0BAAmB,EAAClD,IAAIQ,SAAS;IACrD;AACF;AAEA;;;CAGC,GACD,eAAe2C,kCACbC,GAAoB,EACpBpD,GAAqB,EACrBqD,YAA0B,EAC1B7G,OAMC;IAED,MAAM,EACJ8G,uBAAuB,EACvBpD,cAAc,EAAEqD,sBAAsB,EAAE,EACxC1F,aAAa,EACb8C,UAAU,EACV9E,SAAS,EACT2E,SAAS,EACV,GAAGR;IAEJ,MAAM,EACJwD,MAAM,KAAK,EACXC,6BAA6B,EAC7BC,oBAAoB,EACrB,GAAG/C;IAEJ,SAASgD,wBAAwBC,GAAkB;QACjD,OAAOH,iDAAAA,8BACLG,KACAR,KACAR,mBAAmB5C,KAAK;IAE5B;IACA,MAAM6D,UAAUC,IAAAA,uDAAmC,EACjDN,KACAG;IAGF,MAAMI,eAAeL,wBAAwBM;IAE7C,IAAID,cAAc;QAChBL,qBAAqBK,aAAaE,UAAU,EAAEpG,eAAehC;IAC/D;IAEA,0FAA0F;IAC1F,mCAAmC;IACnC,MAAMqI,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/Cf,cACAtD,2BACAC,KACAxD;IAGF,MAAM6H,uBAAuBF,kDAAoB,CAACC,GAAG,CACnDf,cACAE,wBACAW,YACAZ,wBAAwBgB,aAAa,EACrC;QACET;QACAU,mBAAmB,EAAE/H,2BAAAA,QAAS+H,mBAAmB;QACjDxI;QACAgI,YAAY,EAAEA,gCAAAA,aAAcS,UAAU;IACxC;IAGF,OAAO,IAAIC,sCAAkB,CAACJ,sBAAsB;QAClDK,cAAclE,UAAUkE,YAAY;IACtC;AACF;AAQA,eAAeC,+CACb3E,GAAqB,EACrBqD,YAA0B,EAC1BuB,UAA+D,EAC/DtB,uBAA2E,EAC3E9G,OAAqE;IAErE,MAAM,EACJ0D,cAAc,EAAEqD,sBAAsB,EAAE,EACzC,GAAGvD;IACJ,0CAA0C;IAC1C,wDAAwD;IACxD,2EAA2E;IAC3E,gEAAgE;IAChE,yDAAyD;IAEzD,MAAM6E,kBAAkB,IAAIC,0CAAyB;IACrD,MAAMC,kBAAkB;QACtB,MAAMC,eAAeH,gBAAgBG,YAAY;QACjD,OAAQA;YACN,KAAKC,4BAAW,CAACC,MAAM;gBACrB,OAAO;YACT,KAAKD,4BAAW,CAACE,OAAO;YACxB,KAAKF,4BAAW,CAACG,OAAO;gBACtB,OAAO;YACT;gBACEJ;gBACA,MAAM,qBAA2D,CAA3D,IAAIK,8BAAc,CAAC,CAAC,sBAAsB,EAAEL,cAAc,GAA1D,qBAAA;2BAAA;gCAAA;kCAAA;gBAA0D;QACpE;IACF;IAEA3B,aAAaiC,eAAe,GAAGT;IAC/BxB,aAAakC,gBAAgB,GAAGC,4BAC9BX,iBACAxB,aAAaoC,OAAO,EACpBpC,aAAaqC,cAAc,EAC3BrC,aAAa9G,OAAO;IAGtB,MAAM2H,aAAa,MAAMU,WAAWvB;IAEpC,OAAO,MAAMc,kDAAoB,CAACC,GAAG,CACnCf,cACAsC,+CAAyB,EACzB;QACE,OAAOpC,uBACLW,YACAZ,wBAAwBgB,aAAa,EACrC;YACE,GAAG9H,OAAO;YACVuI;QACF;IAEJ,GACA;QACEF,gBAAgBe,YAAY,CAACX,4BAAW,CAACG,OAAO;IAClD;AAEJ;AAEA;;;CAGC,GACD,eAAeS,iDACbzC,GAAoB,EACpBpD,GAAqB,EACrB8F,mBAAiC,EACjCC,kBAAoD;IAEpD,MAAM,EACJlI,aAAa,EACb8C,UAAU,EACV9E,SAAS,EACT2E,SAAS,EACTN,cAAc,EAAEX,aAAa,EAAE,EAChC,GAAGS;IAEJ,MAAM,EACJwD,MAAM,KAAK,EACXC,6BAA6B,EAC7BC,oBAAoB,EACpBsC,cAAc,EACd1C,uBAAuB,EACxB,GAAG3C;IAEJ,SAASgD,wBAAwBC,GAAkB;QACjD,OAAOH,iDAAAA,8BACLG,KACAR,KACAR,mBAAmB5C,KAAK;IAE5B;IACA,MAAM6D,UAAUC,IAAAA,uDAAmC,EACjDN,KACAG;IAGF,MAAMiB,aAAa,OAAOvB;QACxB,MAAM4C,UACJ,MAAM9B,kDAAoB,CAACC,GAAG,CAC5Bf,cACAtD,2BACAC,KACA3D;QAGJ,IAAI6J,uBAAuBvF,YAAY0C,eAAe;YACpD,qEAAqE;YACrE,wEAAwE;YACxE4C,QAAQE,kBAAkB,GAAG5G,cAAc6G,0BAA0B;gBACnEC,OAAO7F,UAAU6F,KAAK;YACxB;QACF;QAEA,OAAOJ;IACT;IAEA,IAAIlC;IACJ,IAAIuC;IAEJ,IACE,wEAAwE;IACxE,sDAAsD;IACtDP,sBACA,kEAAkE;IAClE,4EAA4E;IAC5E,CAACG,uBAAuBvF,YAAYmF,sBACpC;QACA,oFAAoF;QACpF,gDAAgD;QAChD,IAAIE,gBAAgB;YAClBA,eAAe,SAASnI,eAAehC;QACzC;QAEA,MAAM0K,SAAS,MAAMC,kCACnBxG,KACA8F,qBACAC,oBACAnB,YACAf;QAEFE,eAAewC,OAAOxC,YAAY;QAClCuC,SAASC,OAAOD,MAAM;IACxB,OAAO;QACL,gEAAgE;QAChE,4DAA4D;QAE5DG,8BAA8BnD;QAE9B,uEAAuE;QACvE,IAAI0C,gBAAgB;YAClBA,eAAe,UAAUnI,eAAehC;QAC1C;QAEAkI,eAAeL,wBAAwBM;QAEvCsC,SAAS,MAAM3B,+CACb3E,KACA8F,qBACAlB,YACAtB,yBACA;YACEO,SAASA;YACT9H;YACAgI,YAAY,EAAEA,gCAAAA,aAAcS,UAAU;QACxC;IAEJ;IAEA,IAAIT,gBAAgBL,sBAAsB;QACxCA,qBAAqBK,aAAaE,UAAU,EAAEpG,eAAehC;IAC/D;IAEA,OAAO,IAAI4I,sCAAkB,CAAC6B,QAAQ;QACpC5B,cAAclE,UAAUkE,YAAY;IACtC;AACF;AAEA,eAAegC,8BACbtD,GAAoB,EACpBzB,GAAqB,EACrB3B,GAAqB,EACrBqD,YAA0B;IAE1B,MAAM,EAAE7C,SAAS,EAAE,GAAGR;IACtB,MAAMW,aAAaX,IAAIW,UAAU;IAEjC,SAASgD,wBAAwBC,GAAkB;QACjD,OAAOjD,WAAW8C,6BAA6B,oBAAxC9C,WAAW8C,6BAA6B,MAAxC9C,YACLiD,KACAR,KACA,sDAAsD;QACtDR,mBAAmB5C,KAAK;IAE5B;IACA,MAAM6D,UAAUC,IAAAA,uDAAmC,EACjD,OACAH;IAGF,MAAMgD,WAAwC,CAAC;IAE/C,MAAMC,kBAAkB,IAAM7G,0BAA0BC,KAAK3D;IAE7D,MAAM,EACJ6D,cAAc,EACZC,aAAa,EACXC,UAAU,EAAEnC,UAAU,EAAE,EACzB,EACF,EACDW,0BAA0B,EAC3B,GAAGoB;IACJ,MAAM6G,aAAaC,IAAAA,kCAAa,EAAC7I,YAAYW;IAE7C,qFAAqF;IACrF,gDAAgD;IAChD,MAAMmI,2BAA2BC,IAAAA,+CAA8B;IAC/D,yCAAyC;IACzC,MAAMC,wBAAwB;IAE9B,MAAMC,kCACJlH,KACA4G,iBACAG,0BACAE,uBACAJ,YACAxD,aAAa9G,OAAO,EACpB8G,aAAaoC,OAAO,EACpBpC,aAAa8D,SAAS;IAGxB,MAAMC,WAAW,MAAMC,4BACrBrH,KACA4G,iBACAG,0BACAE,uBACAJ,YACAxD,aAAa9G,OAAO,EACpB8G,aAAaoC,OAAO,EACpBpC,aAAa8D,SAAS,EACtBtD;IAGFyD,iCAAiCF,UAAUT,UAAUnG;IACrDmG,SAASjC,YAAY,GAAG1E,IAAIQ,SAAS,CAACkE,YAAY;IAElD,IAAI0C,SAASG,SAAS,EAAE;QACtB5F,IAAI6F,SAAS,CAACC,0CAAwB,EAAE;IAC1C;IAEA,OAAO,IAAIhD,sCAAkB,CAAC2C,SAASb,MAAM,CAACmB,OAAO,EAAEf;AACzD;AAEA,eAAeO,kCACblH,GAAqB,EACrB4E,UAAqB,EACrBmC,wBAAyD,EACzDE,qBAAmD,EACnDJ,UAAkB,EAClBtK,OAA+C,EAC/CkJ,OAA+C,EAC/C0B,SAAmD;IAEnD,MAAM,EAAEQ,YAAY,EAAEhH,UAAU,EAAEH,SAAS,EAAE,GAAGR;IAEhD,MAAM,EAAEsD,uBAAuB,EAAEsE,YAAY,EAAE,GAAGjH;IAElD8F,8BAA8BnD;IAE9B,iEAAiE;IACjE,yEAAyE;IACzE,6EAA6E;IAC7E,8EAA8E;IAC9E,MAAMuE,mCAAmC,IAAIC;IAE7C,4EAA4E;IAC5E,gFAAgF;IAChF,6EAA6E;IAC7E,MAAMC,gCAAgC,IAAID;IAE1C,kFAAkF;IAClF,yBAAyB;IACzB,MAAME,cAAc,IAAIC,wBAAW;IAEnC,MAAMC,8BAA2D;QAC/D9I,MAAM;QACN+I,OAAO;QACPtB;QACAc;QACAS,cAAcL,8BAA8BM,MAAM;QAClDC,YAAYT;QACZ,0EAA0E;QAC1E,2EAA2E;QAC3E,uBAAuB;QACvBG;QACA,qEAAqE;QACrEO,iBAAiB;QACjB,qEAAqE;QACrE,4EAA4E;QAC5EC,YAAY;QACZC,QAAQ;QACRC,OAAOC,0BAAc;QACrBC,MAAM;eAAIjB,aAAaiB,IAAI;SAAC;QAC5B3B;QACAF;QACA8B,gBAAgBxM;QAChByM,mBAAmBzM;QACnB,uDAAuD;QACvD0M,qBAAqB;QACrB,mFAAmF;QACnFxM;QACAkJ;QACA0B;IACF;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAM6B,uBAAuB,MAAM7E,kDAAoB,CAACC,GAAG,CACzD8D,6BACAtD;IAGF,MAAMqE,6BAA6B9E,kDAAoB,CAACC,GAAG,CACzD8D,6BACAN,aAAasB,SAAS,EACtBF,sBACA1F,wBAAwBgB,aAAa,EACrC;QACEvI;QACA8H,SAAS,CAACD;YACR,MAAMuF,SAASC,IAAAA,8CAA0B,EAACxF;YAE1C,IAAIuF,QAAQ;gBACV,OAAOA;YACT;YAEA,IAAItB,iCAAiCQ,MAAM,CAACgB,OAAO,EAAE;gBACnD,mEAAmE;gBACnE,iEAAiE;gBACjE;YACF,OAAO,IACLrN,QAAQC,GAAG,CAACqN,gBAAgB,IAC5BtN,QAAQC,GAAG,CAACsN,sBAAsB,EAClC;gBACAC,IAAAA,iEAAyC,EAAC5F,KAAKpD,UAAU6F,KAAK;YAChE;QACF;QACA,iFAAiF;QACjF,qCAAqC;QACrCoD,YAAYpN;QACZ,+EAA+E;QAC/E,iFAAiF;QACjF,iDAAiD;QACjDgM,QAAQN,8BAA8BM,MAAM;IAC9C;IAGF,8EAA8E;IAC9EqB,IAAAA,+CAAmB,EAAC1B;IACpB,MAAMA,YAAY2B,UAAU;IAE5B5B,8BAA8B6B,KAAK;IACnC/B,iCAAiC+B,KAAK;IAEtC,gEAAgE;IAChE,iEAAiE;IACjE,IAAIpJ,UAAUqJ,wBAAwB,EAAE;QACtC,MAAMrJ,UAAUqJ,wBAAwB;IAC1C;IAEA,IAAI;QACF,OAAO,MAAMC,IAAAA,yDAAgC,EAACb;IAChD,EAAE,OAAOrF,KAAK;QACZ,IACEmE,8BAA8BM,MAAM,CAACgB,OAAO,IAC5CxB,iCAAiCQ,MAAM,CAACgB,OAAO,EAC/C;QACA,4EAA4E;QAC9E,OAAO,IACLrN,QAAQC,GAAG,CAACqN,gBAAgB,IAC5BtN,QAAQC,GAAG,CAACsN,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnFC,IAAAA,iEAAyC,EAAC5F,KAAKpD,UAAU6F,KAAK;QAChE;QACA,OAAO;IACT;AACF;AAEA,eAAegB,4BACbrH,GAAqB,EACrB4E,UAAqB,EACrBmC,wBAAyD,EACzDE,qBAAmD,EACnDJ,UAAkB,EAClBtK,OAA+C,EAC/CkJ,OAA+C,EAC/C0B,SAAmD,EACnDtD,OAA6C;IAE7C,MAAM,EAAE8D,YAAY,EAAEhH,UAAU,EAAE,GAAGX;IAErC,MAAM,EACJsD,uBAAuB,EACvBsE,YAAY,EACZmC,YAAY,EACZC,sBAAsB,EACvB,GAAGrJ;IAEJ8F,8BAA8BnD;IAE9B,MAAM2G,kBAAkBC,sBAAsBH;IAE9C,IAAII,kBAAkB;IACtB,MAAMC,wBAAwB,IAAItC;IAElC,MAAMuC,wBAAwBC,IAAAA,4CAA0B,EACtDN;IAGF,MAAM,EAAEO,SAASxB,mBAAmB,EAAEyB,SAASC,yBAAyB,EAAE,GACxEC,IAAAA,gDAA0B;IAE5B,MAAMC,4BAAyD;QAC7DvL,MAAM;QACN+I,OAAO;QACPtB;QACAc;QACAS,cAAcgC,sBAAsB/B,MAAM;QAC1CC,YAAY8B;QACZ,8EAA8E;QAC9EpC,aAAa;QACbO,iBAAiB8B;QACjB,qEAAqE;QACrE,4EAA4E;QAC5E7B,YAAY;QACZC,QAAQ;QACRC,OAAOC,0BAAc;QACrBC,MAAM;eAAIjB,aAAaiB,IAAI;SAAC;QAC5B7B;QACAE;QACA4B,gBAAgBxM;QAChByM,mBAAmBzM;QACnB,gEAAgE;QAChE0M;QACA,mFAAmF;QACnFxM;QACAkJ;QACA0B;IACF;IAEA,MAAMyD,kBAAkB,MAAMzG,kDAAoB,CAACC,GAAG,CACpDuG,2BACA/F;IAGF,IAAIiG,qBAAqB;IACzB,MAAMtE,SAAS,MAAMuE,IAAAA,qEAA4C,EAC/D;QACE,eAAe;QACf,MAAMC,kBAAkB,MAAM5G,kDAAoB,CAACC,GAAG,CACpDuG,2BACA/C,aAAasB,SAAS,EACtB0B,iBACAtH,wBAAwBgB,aAAa,EACrC;YACEvI;YACA8H;YACAwE,QAAQ+B,sBAAsB/B,MAAM;QACtC;QAEFwC,qBAAqB;QACrB,OAAOE;IACT,GACA;QACE,gCAAgC;QAChC,EAAE;QACF,0GAA0G;QAC1G,kHAAkH;QAClH,mGAAmG;QACnG,+FAA+F;QAC/F,sFAAsF;QACtFN;IACF,GACA;QACE,SAAS;QACT,IAAIL,sBAAsB/B,MAAM,CAACgB,OAAO,EAAE;YACxC,4EAA4E;YAC5E,6EAA6E;YAC7Ec,kBAAkB;YAClB;QACF;QAEA,IAAIU,oBAAoB;YACtB,kFAAkF;YAClF,iCAAiC;YACjCV,kBAAkB;QACpB;QACAC,sBAAsBR,KAAK;IAC7B;IAGF,OAAO;QACLrD;QACA,8DAA8D;QAC9D,wBAAwB;QACxByE,eAAeX;QACf9C,WAAW4C;QACXc,qBAAqBN,0BAA0BnC,UAAU;QACzD0C,iBAAiBP,0BAA0BlC,MAAM;QACjD0C,gBAAgBlB,gBAAgBU,0BAA0BjC,KAAK;QAC/D0C,eAAeT,0BAA0B/B,IAAI;IAC/C;AACF;AAEA;;;;;CAKC,GACD,SAASyC,2BAA2B5K,GAAwB;IAC1D,OAAO,AAACA,CAAAA,IAAIU,QAAQ,GAAGV,IAAI6K,MAAM,AAAD,EAAGC,KAAK,CAAC;AAC3C;AAEA,SAASC,kBAAkBjL,KAAyB;IAClD,0EAA0E;IAC1E,oBAAoB;IACpB,MAAMkL,QAAQ,EAAE;IAChB,IAAK,MAAM/J,OAAOnB,MAAO;QACvB,MAAMmL,QAAQnL,KAAK,CAACmB,IAAI;QACxB,IAAIgK,SAAS,MAAM;QACnB,IAAIC,MAAMC,OAAO,CAACF,QAAQ;YACxB,KAAK,MAAMG,KAAKH,MAAO;gBACrBD,MAAMK,IAAI,CACR,GAAGC,mBAAmBrK,KAAK,CAAC,EAAEqK,mBAAmBC,OAAOH,KAAK;YAEjE;QACF,OAAO;YACLJ,MAAMK,IAAI,CACR,GAAGC,mBAAmBrK,KAAK,CAAC,EAAEqK,mBAAmBC,OAAON,SAAS;QAErE;IACF;IAEA,6EAA6E;IAC7E,4DAA4D;IAC5D,0EAA0E;IAC1E,uEAAuE;IACvE,gBAAgB;IAChB,IAAID,MAAMQ,MAAM,KAAK,GAAG;QACtB,yDAAyD;QACzD,OAAO;IACT;IACA,2CAA2C;IAC3C,OAAO,MAAMR,MAAMS,IAAI,CAAC;AAC1B;AAEA,wFAAwF;AACxF,eAAeC,cACblL,IAAgB,EAChBjB,GAAqB,EACrBoM,KAAc;IAEd,MAAMxK,cAAc,IAAIC;IACxB,MAAMC,aAAa,IAAID;IACvB,MAAME,0BAA0B,IAAIF;IACpC,IAAIwK;IAEJ,sDAAsD;IACtD,IAAIrQ,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CmQ,eAAe,IAAIxK;IACrB;IAEA,MAAM,EACJjD,0BAA0B,EAC1B2B,KAAK,EACL+L,sBAAsB,EACtBpM,cAAc,EAAEG,wBAAwB,EAAEd,aAAa,EAAEe,QAAQ,EAAE,EACnEG,GAAG,EACHD,SAAS,EACV,GAAGR;IAEJ,MAAMuM,cAAcC,IAAAA,4EAAqC,EACvDvL,MACArC,4BACA2B;IAEF,MAAMG,yBAAyB,CAAC,CAACV,IAAIW,UAAU,CAACD,sBAAsB;IACtE,MAAMvC,oBAAoB,CAAC,CAAC8C,IAAI,CAAC,EAAE,CAAC,mBAAmB;IAEvD,MAAM,EAAEH,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAE,GAAGX,yBAAyB;QACtEY;QACA,6FAA6F;QAC7F,0BAA0B;QAC1B,wFAAwF;QACxF,2CAA2C;QAC3C,yFAAyF;QACzFwL,WAAWL,SAAS,CAACjO,oBAAoB,cAAc9B;QACvD6E,aAAaX;QACbY,UAAUV,IAAIU,QAAQ;QACtBC,iBAAiBC,IAAAA,sCAAqB,EAACrB,IAAIW,UAAU;QACrD/B;QACA4B;QACAE;IACF;IAEA,MAAMG,mBAAqC,EAAE;IAE7C,MAAM6L,WAAW,MAAMC,IAAAA,wCAAmB,EAAC;QACzC3M;QACA/B,YAAYgD;QACZO,cAAc,CAAC;QACfI;QACAE;QACAC;QACAC,oBAAoB;QACpBqK;QACAxL;QACA+L,gBAAgB5M,IAAIW,UAAU,CAACoJ,YAAY,CAAC6C,cAAc;QAC1D5L;IACF;IAEA,0FAA0F;IAC1F,6FAA6F;IAC7F,2FAA2F;IAC3F,MAAM6L,aAAa7M,IAAI2B,GAAG,CAACmL,SAAS,CAAC;IACrC,MAAMC,qBACJ,OAAOF,eAAe,YAAYA,WAAWG,QAAQ,CAACC,0BAAQ;IAEhE,MAAMC,cAAc3N,cAClBe,UACA;QACEoB,KAAK/F;IACP,GACA4D,cAAcD,UAAU;QACtBC;QACAC,UAAUQ,IAAIR,QAAQ;QACtBC,YAAYO,IAAI2B,GAAG,CAAClC,UAAU;QAC9BC,wBAAwBM,IAAIN,sBAAsB;IACpD,IACAH,cAAcuB,UAAU,OACxBvB,cAAcwB,UAAU,OACxBuL,yBACI/M,cAAc,QAAQ;QACpBM,MAAM;QACNC,SAAS;IACX,KACA;IAGN,MAAM,EAAEqN,WAAW,EAAEC,QAAQC,iBAAiB,EAAE,GAAG,MAAMC,qBACvDrM,MACAjB;IAGF,uEAAuE;IACvE,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,qEAAqE;IACrE,MAAMuN,wBACJ/M,UAAUmC,kBAAkB,IAC5B3C,IAAIW,UAAU,CAACoJ,YAAY,CAAC9M,iBAAiB,KAAK;IAEpD,OAAO;QACL,6FAA6F;QAC7FuQ,GAAGjO,cAAckO,UAAU;YACzB5M,kBAAkBA;QACpB;QACA0B,GAAGvC,IAAIwC,aAAa,CAACC,OAAO;QAC5BiL,GAAGrC,2BAA2B5K;QAC9BkN,GAAGnC,kBAAkBjL;QACrBqN,GAAG,CAAC,CAACb;QACLzK,GAAG;YACD;gBACEiK;gBACAG;gBACAQ;gBACAK;aACD;SACF;QACDM,GAAGxB;QACHyB,GAAG;YAACX;YAAaE;SAAkB;QACnCU,GAAG,OAAO/N,IAAIW,UAAU,CAACqN,SAAS,KAAK;QACvCtL,GAAGlC,UAAUmC,kBAAkB;IACjC;AACF;AAEA;;;;;CAKC,GACD,SAAS8K,SAAS,EAAE5M,gBAAgB,EAAoC;IACtEA,iBAAiBoN,OAAO,CAAC,CAACC,YAAcA;IACxC,OAAO;AACT;AAEA,sFAAsF;AACtF,eAAeC,mBACblN,IAAgB,EAChBjB,GAAqB,EACrBoO,QAAiB,EACjB3B,SAAqD;IAErD,MAAM,EACJ7N,0BAA0B,EAC1B2B,KAAK,EACLL,cAAc,EAAEG,wBAAwB,EAAEd,aAAa,EAAEe,QAAQ,EAAE,EACnEG,GAAG,EACHD,SAAS,EACV,GAAGR;IAEJ,MAAMU,yBAAyB,CAAC,CAACV,IAAIW,UAAU,CAACD,sBAAsB;IACtE,MAAM,EAAEI,QAAQ,EAAEC,QAAQ,EAAE,GAAGV,yBAAyB;QACtDY;QACAC,aAAaX;QACbY,UAAUV,IAAIU,QAAQ;QACtBC,iBAAiBC,IAAAA,sCAAqB,EAACrB,IAAIW,UAAU;QACrD8L;QACA7N;QACA4B;QACAE,wBAAwBA;IAC1B;IAEA,MAAMwM,cAAc3N,cAClBe,UACA;QACEoB,KAAK/F;IACP,GACA4D,cAAcD,UAAU;QACtBC;QACAC,UAAUQ,IAAIR,QAAQ;QACtBC,YAAYO,IAAI2B,GAAG,CAAClC,UAAU;QAC9BC,wBAAwBM,IAAIN,sBAAsB;IACpD,IACAH,cAAcuB,UAAU,OACxB9E,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACvBqD,cAAc,QAAQ;QACpBM,MAAM;QACNC,SAAS;IACX,IACFP,cAAcwB,UAAU;IAG1B,MAAMwL,cAAcC,IAAAA,4EAAqC,EACvDvL,MACArC,4BACA2B;IAGF,IAAIqD,MAAyBvH;IAC7B,IAAI+R,UAAU;QACZxK,MAAMyK,IAAAA,gBAAO,EAACD,YAAYA,WAAW,qBAAwB,CAAxB,IAAIE,MAAMF,WAAW,KAArB,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC9D;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/C,MAAM1B,WAA8B;QAClCnN,cACE,QACA;YACEgP,IAAI;QACN,GACAhP,cAAc,QAAQ,OACtBA,cACE,QACA,MACAvD,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB0H,MACrCrE,cAAc,YAAY;YACxB,2BAA2BqE,IAAI4K,OAAO;YACtC,0BAA0B,YAAY5K,MAAMA,IAAIuF,MAAM,GAAG;YACzD,yBAAyBvF,IAAI6K,KAAK;QACpC,KACA;QAGR,CAAC;QACD;QACA;QACA;KACD;IAED,MAAM,EAAEtB,WAAW,EAAEC,QAAQC,iBAAiB,EAAE,GAAG,MAAMC,qBACvDrM,MACAjB;IAGF,MAAMuN,wBACJ/M,UAAUmC,kBAAkB,IAC5B3C,IAAIW,UAAU,CAACoJ,YAAY,CAAC9M,iBAAiB,KAAK;IAEpD,OAAO;QACLsF,GAAGvC,IAAIwC,aAAa,CAACC,OAAO;QAC5BiL,GAAGrC,2BAA2B5K;QAC9BkN,GAAGnC,kBAAkBjL;QACrBsN,GAAGxR;QACHuR,GAAG;QACHtL,GAAG;YACD;gBACEiK;gBACAG;gBACAQ;gBACAK;aACD;SACF;QACDO,GAAG;YAACX;YAAaE;SAAkB;QACnCU,GAAG,OAAO/N,IAAIW,UAAU,CAACqN,SAAS,KAAK;QACvCtL,GAAGlC,UAAUmC,kBAAkB;IACjC;AACF;AAEA,SAAS8D,8BACPnD,uBAA8D;IAI9D,IAAI,CAACA,yBAAyB;QAC5B,MAAM,qBAAqE,CAArE,IAAI+B,8BAAc,CAAC,oDAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAoE;IAC5E;AACF;AAEA,mFAAmF;AACnF,SAASqJ,IAAO,EACdC,iBAAiB,EACjBC,gBAAgB,EAChBC,cAAc,EACdvL,uBAAuB,EACvBwL,0BAA0B,EAC1BtR,KAAK,EACLuR,MAAM,EAYP;IACCF;IACA,MAAMzH,WAAW4H,OAAYC,GAAG,CAC9BC,IAAAA,kCAAe,EACbP,mBACAC,kBACAtL,yBACA9F;IAIJ,MAAM2R,eAAeC,IAAAA,kDAAwB,EAAC;QAC5C,gEAAgE;QAChE,kBAAkB;QAClBC,aAAa,CAAC;QACdC,mBAAmBlI,SAAS9E,CAAC;QAC7BiN,0BAA0BnI,SAASsG,CAAC;QACpC8B,uBAAuBpI,SAASuG,CAAC;QACjC8B,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;IACZ;IAEA,MAAMC,cAAcC,IAAAA,2CAAwB,EAACV,cAAc;IAE3D,MAAM,EAAEW,kBAAkB,EAAE,GAC1B3T,QAAQ;IAEV,qBACE,qBAAC2T,mBAAmBC,QAAQ;QAC1BrE,OAAO;YACLsE,QAAQ;YACRxS;QACF;kBAEA,cAAA,qBAACyS,mDAAkB,CAACF,QAAQ;YAACrE,OAAOqD,UAAUmB,+BAAkB;sBAC9D,cAAA,qBAACpB;0BACC,cAAA,qBAACqB,kBAAS;oBAACP,aAAaA;oBAAaQ,kBAAkBhJ,SAAS0G,CAAC;;;;;AAKzE,iEAAiE,GACnE;AAEA,oGAAoG;AACpG,uGAAuG;AACvG,sBAAsB;AACtB,SAASuC,SAAY,EACnB1B,iBAAiB,EACjBC,gBAAgB,EAChBC,cAAc,EACdvL,uBAAuB,EACvBwL,0BAA0B,EAC1BtR,KAAK,EACLuR,MAAM,EAWP;IACC,kEAAkE,GAClEF;IACA,MAAMzH,WAAW4H,OAAYC,GAAG,CAC9BC,IAAAA,kCAAe,EACbP,mBACAC,kBACAtL,yBACA9F;IAIJ,MAAM2R,eAAeC,IAAAA,kDAAwB,EAAC;QAC5C,gEAAgE;QAChE,kBAAkB;QAClBC,aAAa,CAAC;QACdC,mBAAmBlI,SAAS9E,CAAC;QAC7BiN,0BAA0BnI,SAASsG,CAAC;QACpC8B,uBAAuBpI,SAASuG,CAAC;QACjC8B,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;IACZ;IAEA,MAAMC,cAAcC,IAAAA,2CAAwB,EAACV,cAAc;IAE3D,qBACE,qBAACc,mDAAkB,CAACF,QAAQ;QAACrE,OAAOqD,UAAUmB,+BAAkB;kBAC9D,cAAA,qBAACpB;sBACC,cAAA,qBAACqB,kBAAS;gBAACP,aAAaA;gBAAaQ,kBAAkBhJ,SAAS0G,CAAC;;;;AAIvE,iEAAiE,GACnE;AASA,eAAewC,yBACblN,GAAoB,EACpBzB,GAAqB,EACrBlB,GAAwC,EACxCjB,QAAgB,EAChBe,KAAyB,EACzBI,UAAsB,EACtBH,SAAoB,EACpB+P,oBAA0C,EAC1CC,cAAqC,EACrCC,wBAA8D,EAC9DjO,aAA+B,EAC/B9D,kBAA0B,EAC1BC,mBAAqD;IAErD,MAAM+R,iBAAiBlR,aAAa;IACpC,IAAIkR,gBAAgB;QAClB/O,IAAIlC,UAAU,GAAG;IACnB;IAEA,qEAAqE;IACrE,wEAAwE;IACxE,6EAA6E;IAC7E,+EAA+E;IAC/E,MAAMkR,mBAAmBC,KAAKC,GAAG;IAEjC,MAAM,EACJvN,uBAAuB,EACvBwN,qBAAqB,EACrBlJ,YAAY,EACZmJ,gBAAgB,EAChBC,aAAa,EACbC,cAAc,EAAE,EAChBC,cAAc,EACdC,eAAe,EAChB,GAAGxQ;IAEJ,2DAA2D;IAC3D,uEAAuE;IACvE,IAAIiH,aAAawJ,YAAY,EAAE;QAC7B,MAAMC,eAAeC,IAAAA,wDAAyB,EAAC1J;QAE/C,kEAAkE;QAClE,0EAA0E;QAC1E,+EAA+E;QAC/E,8DAA8D;QAE9D,MAAM2J,2BAA2B;YAC/B,IAAI,CAACJ,iBAAiB;gBACpB,OAAO;YACT;YACA,IAAIxQ,WAAW6C,GAAG,EAAE;gBAClB,OAAO;YACT;YACA,MAAMgO,gBAAgBrN,kDAAoB,CAACsN,QAAQ;YAEnD,IAAI,CAACD,eAAe;gBAClB,OAAO;YACT;YAEA,OAAQA,cAAcpS,IAAI;gBACxB,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,OAAO;gBACT,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,OAAO;gBACT;oBACEoS;YACJ;QACF;QAEA,MAAME,mBAAgD,CAAC,GAAGC;YACxD,MAAMC,mBAAmBP,aAAalV,OAAO,IAAIwV;YACjD,IAAIJ,4BAA4B;gBAC9B,+CAA+C;gBAC/CM,IAAAA,8CAAkB,EAACD;YACrB;YACA,OAAOA;QACT;QACA,mBAAmB;QACnBE,WAAWJ,gBAAgB,GAAGA;QAE9B,MAAMK,sBAAqD,CAAC,GAAGJ;YAC7D,MAAMK,eAAeX,aAAaY,SAAS,IAAIN;YAC/C,IAAIJ,4BAA4B;gBAC9BW,IAAAA,iDAAqB,EAACF;YACxB;YACA,OAAOA;QACT;QACA,mBAAmB;QACnBF,WAAWC,mBAAmB,GAAGA;IACnC;IAEA,IACE/V,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzByE,WAAWwR,YAAY,IACvB,CAAChB,iBACD;QACA,4CAA4C;QAC5C,MAAM,EAAEhQ,QAAQ,EAAE,GAAG,IAAIiR,IAAIhP,IAAI3C,GAAG,IAAI,KAAK;QAC7CE,WAAWwR,YAAY,CACrBhR,UACA,yEAAyE;QACzEnF,QAAQC,GAAG,CAACoW,YAAY,KAAK,SAAS,QAAQhW;IAElD;IAEA,IACE,qEAAqE;IACrE,6DAA6D;IAC7DL,QAAQC,GAAG,CAACoW,YAAY,KAAK,UAC7BC,IAAAA,0BAAiB,EAAClP,MAClB;QACAzB,IAAI4Q,OAAO,CAAC;YACV,oEAAoE;YACpE,4BAA4B;YAC5B/R,UAAUgS,uBAAuB,GAAG;QACtC;QAEApP,IAAIqP,eAAe,CAACC,EAAE,CAAC,OAAO;YAC5B,IAAI,iBAAiBZ,YAAY;gBAC/B,MAAMa,UAAUC,IAAAA,8DAA+B,EAAC;oBAAEC,OAAO;gBAAK;gBAC9D,IAAIF,SAAS;oBACXG,IAAAA,iBAAS,IACNC,SAAS,CAACC,6BAAkB,CAACC,sBAAsB,EAAE;wBACpDC,WAAWP,QAAQQ,wBAAwB;wBAC3CC,YAAY;4BACV,iCACET,QAAQU,wBAAwB;4BAClC,kBAAkBL,6BAAkB,CAACC,sBAAsB;wBAC7D;oBACF,GACCK,GAAG,CACFX,QAAQQ,wBAAwB,GAC9BR,QAAQY,wBAAwB;gBAExC;YACF;QACF;IACF;IAEA,MAAM5M,WAAwC;QAC5ClH,YAAYiR,iBAAiB,MAAMrU;IACrC;IAEA,MAAMiQ,yBAAyB,CAAC,EAACyE,oCAAAA,iBAAkByC,kBAAkB;IAErE/M,8BAA8BnD;IAE9B,MAAMmQ,kBAAkBC,IAAAA,kCAAqB,EAAC;QAAE5C;IAAsB;IAEtE6C,IAAAA,+CAA8B,EAAC;QAC7BrV,MAAMkC,UAAUlC,IAAI;QACpBgF;QACAwN;QACA2C;IACF;IAEA7L,aAAagM,UAAU;IAEvB,oDAAoD;IACpD,MAAM,EACJzT,aAAa,EACXC,UAAU,EAAEnC,UAAU,EAAE,EACzB,EACD4V,oBAAoB,EACrB,GAAGjM;IACJ,IAAIsJ,gBAAgB;QAClB2C,qBACE,kFACA7X,QAAQC,GAAG;IAEf;IAEAuE,UAAUkE,YAAY,GAAG,EAAE;IAC3BiC,SAASjC,YAAY,GAAGlE,UAAUkE,YAAY;IAE9C,qCAAqC;IACrCnE,QAAQ;QAAE,GAAGA,KAAK;IAAC;IACnBuT,IAAAA,mCAAoB,EAACvT;IAErB,MAAM,EAAEoC,kBAAkB,EAAE,GAAGnC;IAE/B,IAAI3E;IACJ,IAAIgC;IAEJ,MAAM,EACJX,iBAAiB,EACjBT,iBAAiB,EACjBE,wBAAwB,EACxBG,YAAY,EACZF,YAAY,EACZY,KAAK,EACN,GAAG+S;IAEJ,IAAIA,qBAAqB1U,SAAS,EAAE;QAClC,4EAA4E;QAC5EA,YAAY0U,qBAAqB1U,SAAS;IAC5C,OAAO;QACL,0CAA0C;QAC1C,IAAI8G,oBAAoB;YACtB9G,YAAYkY,OAAOC,IAAI,CACrB,MAAMC,OAAOC,MAAM,CAAC/K,MAAM,CAAC,SAAS4K,OAAOC,IAAI,CAAC5Q,IAAI3C,GAAG,IACvD0T,QAAQ,CAAC;QACb,OAAO,IAAInY,QAAQC,GAAG,CAACoW,YAAY,KAAK,QAAQ;YAC9CxW,YAAYoY,OAAOG,UAAU;QAC/B,OAAO;YACLvY,YAAY,AACVM,QAAQ,6BACRkY,MAAM;QACV;IACF;IAEA,4EAA4E;IAC5E,4EAA4E;IAC5E,6EAA6E;IAC7E,6EAA6E;IAC7E,mBAAmB;IACnBxW,gBAAgB0S,qBAAqB1S,aAAa,IAAIhC;IAEtD,MAAM+C,6BAA6BH,+BACjCC,oBACAC;IAGF,MAAM2V,0BAA0BC,IAAAA,kDAAyB,EAACnR;IAE1D,MAAMuE,eAAe,MAAM6M,IAAAA,6BAAe,EACxChU,UAAUlC,IAAI,EACdmC,KACA9B;IAGF,MAAMqB,MAAwB;QAC5BE,cAAc0H;QACdnH;QACAE;QACAH;QACA+P;QACA3R;QACA2B;QACAkU,YAAYhY;QACZiD,wBAAwB4U;QACxB3D;QACArE;QACApP;QACArB;QACAgC;QACA2B;QACA8D;QACA2N;QACAP;QACAlT;QACAmE;QACAa;QACAmF;IACF;IAEAmL,IAAAA,iBAAS,IAAG4B,oBAAoB,CAAC,cAAclV;IAE/C,IAAImD,oBAAoB;QACtB,mEAAmE;QACnE,4CAA4C;QAC5C,MAAMgS,+BAA+B7B,IAAAA,iBAAS,IAAG8B,IAAI,CACnDC,wBAAa,CAACC,aAAa,EAC3B;YACEC,UAAU,CAAC,sBAAsB,EAAEvV,UAAU;YAC7C4T,YAAY;gBACV,cAAc5T;YAChB;QACF,GACAwV;QAGF,MAAM5N,WAAW,MAAMuN,6BACrBvR,KACAzB,KACA3B,KACA2G,UACA1I,YACAU;QAGF,8EAA8E;QAC9E,mCAAmC;QACnC,0CAA0C;QAC1C,IACEyI,SAAS4D,aAAa,IACtBiK,IAAAA,qCAAmB,EAAC7N,SAAS4D,aAAa,KAC1CrK,WAAWqJ,sBAAsB,EACjC;YACAkL,IAAAA,SAAI,EAAC;YACL,KAAK,MAAMC,UAAUC,IAAAA,0CAAwB,EAAChO,SAAS4D,aAAa,EAAG;gBACrEkK,IAAAA,SAAI,EAACC;YACP;QACF;QAEA,mEAAmE;QACnE,oCAAoC;QACpC,IAAI3U,UAAUqJ,wBAAwB,EAAE;YACtCwL,IAAAA,2CAAyB,EAAC7U,WAAWA,UAAUqJ,wBAAwB;YACvE,MAAM,IAAIyL,8CAAqB;QACjC;QACA,IAAIlO,SAASmO,eAAe,CAACC,IAAI,EAAE;YACjC,MAAMC,oBAAoBrO,SAASmO,eAAe,CAACG,MAAM,GAAGC,IAAI,GAAGjK,KAAK;YACxE,IAAI+J,mBAAmB,MAAMA;QAC/B;QACA,gEAAgE;QAChE,IAAIrO,SAASwO,SAAS,CAAC3J,MAAM,EAAE;YAC7B,MAAMwJ,oBAAoBrO,SAASwO,SAAS,CAACC,IAAI,CAAC,CAACjS,MACjDkS,IAAAA,mCAAe,EAAClS;YAElB,IAAI6R,mBAAmB,MAAMA;QAC/B;QAEA,MAAMjZ,UAA+B;YACnCmK;YACAoP,aAAaC,oCAAwB;QACvC;QACA,oEAAoE;QACpE,IACExV,UAAUyV,kBAAkB,IAC5BzV,UAAU0V,uBAAuB,IACjC1V,UAAU2V,sBAAsB,EAChC;YACA,MAAMC,iBAAiBC,IAAAA,qCAAkB,EAAC7V,WAAW8V,OAAO,CAAC;gBAC3D,IAAIta,QAAQC,GAAG,CAACsa,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6ChW;gBAC3D;YACF;YAEA,IAAIE,WAAW+V,SAAS,EAAE;gBACxB/V,WAAW+V,SAAS,CAACN;YACvB,OAAO;gBACL5Z,QAAQka,SAAS,GAAGN;YACtB;QACF;QAEA9O,iCAAiCF,UAAUT,UAAUnG;QAErD,IAAI4G,SAASH,qBAAqB,EAAE;YAClCN,SAASM,qBAAqB,GAAGG,SAASH,qBAAqB;QACjE;QAEA,OAAO,IAAI0P,qBAAY,CAAC,MAAMC,IAAAA,oCAAc,EAACxP,SAASd,MAAM,GAAG9J;IACjE,OAAO;QACL,8BAA8B;QAC9B,MAAMyK,wBACJtG,WAAWsG,qBAAqB,KAChCuJ,kCAAAA,eAAgBvJ,qBAAqB,KACrC;QAEF,MAAMJ,aAAaC,IAAAA,kCAAa,EAAC7I,YAAY+B,IAAIpB,0BAA0B;QAC3E,MAAMiY,8BACJC,IAAAA,2BAAc,EAAC1T,KAAK,kCAAkC;QAExD,MAAM2C,qBAAqBgR,yCAA2B,CAACC,IAAI,CACzD,MACA5T,KACAzB,KACAlB,KACAoG,YACAc,cACAhH,WAAWsW,eAAe,EAC1BtW,WAAWuW,YAAY,EACvBta,cACA6T,0BACAxJ,uBACA4P;QAEF,MAAMxT,eAAe0C;QAErB,IACE/J,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzByE,WAAWwR,YAAY,IACvB,CAAChB,mBACD,oEAAoE;QACpE,+BAA+B;QAC/B,qEAAqE;QACrE,6DAA6D;QAC7DnV,QAAQC,GAAG,CAACoW,YAAY,KAAK,UAC7BC,IAAAA,0BAAiB,EAAClP,MAClB;YACA,MAAM+O,eAAexR,WAAWwR,YAAY;YAC5C/O,IAAIqP,eAAe,CAACC,EAAE,CAAC,OAAO;gBAC5B,MAAM,EAAEvR,QAAQ,EAAE,GAAG,IAAIiR,IAAIhP,IAAI3C,GAAG,IAAI,KAAK;gBAC7C,MAAM0W,WAAW,CAAC9T,aAAa+T,WAAW,IAAI,CAAC5W,UAAU6W,YAAY;gBACrElF,aAAahR,UAAUgW;YACzB;QACF;QAEA,IAAIra,cAAc;YAChB,IAAIH,0BAA0B;gBAC5B,OAAO+J,8BAA8BtD,KAAKzB,KAAK3B,KAAKqD;YACtD,OAAO;gBACL,IACErH,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBF,QAAQC,GAAG,CAACoW,YAAY,KAAK,UAC7BlB,iBACA;oBACA,OAAOtL,iDACLzC,KACApD,KACAqD,cACA0C;gBAEJ,OAAO;oBACL,OAAO5C,kCAAkCC,KAAKpD,KAAKqD;gBACrD;YACF;QACF;QAEA,MAAMiU,4BAA4BxE,IAAAA,iBAAS,IAAG8B,IAAI,CAChDC,wBAAa,CAACC,aAAa,EAC3B;YACEC,UAAU,CAAC,mBAAmB,EAAEvV,UAAU;YAC1C4T,YAAY;gBACV,cAAc5T;YAChB;QACF,GACA+X;QAGF,IAAIC,yBAAyB;QAC7B,IAAIC,YAAwB;QAC5B,IAAInD,yBAAyB;YAC3B,mEAAmE;YACnEjR,aAAa4D,qBAAqB,GAAG;YAErC,gFAAgF;YAChF,MAAMyQ,sBAAsB,MAAMC,IAAAA,2BAAY,EAAC;gBAC7CvU;gBACAzB;gBACAiG;gBACA6L;gBACAmE,gBAAgBzU;gBAChB3C;gBACA6C;gBACA2N;gBACAhR;gBACA2G;YACF;YAEA,IAAI+Q,qBAAqB;gBACvB,IAAIA,oBAAoBtY,IAAI,KAAK,aAAa;oBAC5C,MAAMyY,qBAAqB7Z,yBAAyBC;oBACpD0D,IAAIlC,UAAU,GAAG;oBACjBkH,SAASlH,UAAU,GAAG;oBACtB,MAAM6G,SAAS,MAAMgR,0BACnBjU,cACAD,KACAzB,KACA3B,KACA6X,oBACAJ,WACAjH,gBACA7J,UACAtK,WACAwa;oBAGF,OAAO,IAAIF,qBAAY,CAACrQ,QAAQ;wBAC9BK;wBACAoP,aAAaC,oCAAwB;oBACvC;gBACF,OAAO,IAAI0B,oBAAoBtY,IAAI,KAAK,QAAQ;oBAC9C,IAAIsY,oBAAoBnR,MAAM,EAAE;wBAC9BmR,oBAAoBnR,MAAM,CAACuR,cAAc,CAACnR;wBAC1C,OAAO+Q,oBAAoBnR,MAAM;oBACnC,OAAO,IAAImR,oBAAoBD,SAAS,EAAE;wBACxCA,YAAYC,oBAAoBD,SAAS;oBAC3C;gBACF;YACF;YAEAD,yBAAyB;YACzB,gCAAgC;YAChCnU,aAAa4D,qBAAqB,GAAGA;QACvC;QAEA,MAAMzK,UAA+B;YACnCmK;YACAoP,aAAaC,oCAAwB;QACvC;QAEA,MAAM1P,SAAS,MAAMgR,0BACnB,oGAAoG;QACpG,2CAA2C;QAC3CjU,cACAD,KACAzB,KACA3B,KACA/B,YACAwZ,WACAjH,gBACA7J,UACA,qFAAqF;QACrF,4DAA4D;QAC5D,0FAA0F;QAC1F,yEAAyE;QACzE,+CAA+C;QAC/C6Q,yBAAyBnb,YAAY0J,oBACrC8Q;QAGF,uEAAuE;QACvE,kDAAkD;QAClD,6GAA6G;QAC7G,IAAIrW,UAAUqJ,wBAAwB,IAAIrJ,UAAUgD,GAAG,EAAE;YACvD,MAAMhD,UAAUqJ,wBAAwB;QAC1C;QAEA,oEAAoE;QACpE,IACErJ,UAAUyV,kBAAkB,IAC5BzV,UAAU0V,uBAAuB,IACjC1V,UAAU2V,sBAAsB,EAChC;YACA,MAAMC,iBAAiBC,IAAAA,qCAAkB,EAAC7V,WAAW8V,OAAO,CAAC;gBAC3D,IAAIta,QAAQC,GAAG,CAACsa,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6ChW;gBAC3D;YACF;YAEA,IAAIE,WAAW+V,SAAS,EAAE;gBACxB/V,WAAW+V,SAAS,CAACN;YACvB,OAAO;gBACL5Z,QAAQka,SAAS,GAAGN;YACtB;QACF;QAEA,iDAAiD;QACjD,OAAO,IAAIO,qBAAY,CAACrQ,QAAQ9J;IAClC;AACF;AAaO,MAAMd,uBAAsC,CACjD0H,KACAzB,KACAnC,UACAe,OACA5B,qBACAgC,YACA8P,0BACAjO;QAYiB7B;IAVjB,IAAI,CAACyC,IAAI3C,GAAG,EAAE;QACZ,MAAM,qBAAwB,CAAxB,IAAI6N,MAAM,gBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC/B;IAEA,MAAM7N,MAAMsX,IAAAA,kCAAgB,EAAC3U,IAAI3C,GAAG,EAAEpE,WAAW;IAEjD,qEAAqE;IACrE,wEAAwE;IACxE,MAAMkU,uBAAuBjU,oBAAoB8G,IAAI7G,OAAO,EAAE;QAC5DU,mBAAmB0D,WAAWoJ,YAAY,CAAC9M,iBAAiB,KAAK;QACjEW,aAAa,GAAE+C,2BAAAA,WAAWuW,YAAY,qBAAvBvW,yBAAyB/C,aAAa;IACvD;IAEA,MAAM,EAAEnB,iBAAiB,EAAEiB,yBAAyB,EAAEF,KAAK,EAAE,GAC3D+S;IAEF,IAAI7R;IACJ,IAAI8R,iBAAwC;IAE5C,4EAA4E;IAC5E,SAAS;IACT,IAAI,OAAO7P,WAAWqN,SAAS,KAAK,UAAU;QAC5C,IAAIrP,qBAAqB;YACvB,MAAM,qBAEL,CAFK,IAAI0G,8BAAc,CACtB,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA3G,qBAAqBsZ,IAAAA,+CAA8B,EACjDrX,WAAWiH,YAAY,CAACzH,WAAW,CAACC,QAAQ,CAACnC,UAAU,EACvD0C,WAAWsX,MAAM,IAAI,CAAC,GACtBzY,UACAb;QAGF6R,iBAAiB0H,IAAAA,mCAAmB,EAClCvX,WAAWqN,SAAS,EACpBtP;IAEJ,OAAO;QACLA,qBAAqBsZ,IAAAA,+CAA8B,EACjDrX,WAAWiH,YAAY,CAACzH,WAAW,CAACC,QAAQ,CAACnC,UAAU,EACvD0C,WAAWsX,MAAM,IAAI,CAAC,GACtBzY,UACAb;IAEJ;IAEA,IACE6R,CAAAA,kCAAAA,eAAgBvJ,qBAAqB,KACrCtG,WAAWsG,qBAAqB,EAChC;QACA,MAAM,qBAEL,CAFK,IAAI5B,8BAAc,CACtB,+FADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAM7E,YAAY2X,IAAAA,0BAAe,EAAC;QAChC7Z,MAAMqC,WAAWR,WAAW,CAACiY,UAAU,CAAC9Z,IAAI;QAC5CqC;QACA,8CAA8C;QAC9ClE;QACAgG,SAASD,cAAcC,OAAO;QAC9B/E;QACAF;IACF;IAEA,OAAO6a,0CAAgB,CAACjU,GAAG,CACzB5D,WACA,sBAAsB;IACtB8P,0BACA,mBAAmB;IACnBlN,KACAzB,KACAlB,KACAjB,UACAe,OACAI,YACAH,WACA+P,sBACAC,gBACAC,0BACAjO,eACA9D,oBACAC;AAEJ;AAEA,SAAS2I,iCACPF,QAMC,EACDT,QAAqC,EACrCnG,SAAoB;IAEpB,IAAI4G,SAASgE,aAAa,EAAE;QAC1BzE,SAAS2R,SAAS,GAAGlR,SAASgE,aAAa,CAACc,IAAI,CAAC;IACnD;IAEA,uEAAuE;IACvE,MAAMqM,cAAcvM,OAAO5E,SAAS+D,cAAc;IAClDxE,SAASpK,OAAO,KAAK,CAAC;IACtBoK,SAASpK,OAAO,CAACic,+CAA6B,CAAC,GAAGD;IAElD,yEAAyE;IACzE,YAAY;IACZ,IAAI/X,UAAUiY,WAAW,KAAK,SAASrR,SAAS6D,mBAAmB,KAAK,GAAG;QACzEtE,SAAS+R,YAAY,GAAG;YAAElQ,YAAY;YAAGC,QAAQpM;QAAU;IAC7D,OAAO;QACL,gEAAgE;QAChEsK,SAAS+R,YAAY,GAAG;YACtBlQ,YACEpB,SAAS6D,mBAAmB,IAAItC,0BAAc,GAC1C,QACAvB,SAAS6D,mBAAmB;YAClCxC,QACErB,SAAS8D,eAAe,IAAIvC,0BAAc,GACtCtM,YACA+K,SAAS8D,eAAe;QAChC;IACF;IAEA,qCAAqC;IACrC,IAAIvE,SAAS+R,YAAY,CAAClQ,UAAU,KAAK,GAAG;QAC1C7B,SAASgS,iBAAiB,GAAG;YAC3BC,aAAapY,UAAUqY,uBAAuB;YAC9CpK,OAAOjO,UAAUsY,iBAAiB;QACpC;IACF;AACF;AAQA,eAAevB,eACblU,YAA0B,EAC1BD,GAAoB,EACpBzB,GAAqB,EACrB3B,GAAqB,EACrBiB,IAAgB,EAChBwW,SAAc,EACdjH,cAAqC,EACrC7J,QAAqC,EACrCZ,kBAAoD,EACpD8Q,2BAA6D;IAE7D,kEAAkE,GAClE,MAAM,EACJ5F,WAAW,EACXpT,aAAa,EACbL,KAAK,EACLgC,QAAQ,EACRmB,UAAU,EACV9E,SAAS,EACT2E,SAAS,EACV,GAAGR;IAEJ,MAAM,EACJ+Y,QAAQ,EACRC,aAAa,EACb1V,uBAAuB,EACvBsE,cAAc,EACZrI,aAAa,EACbgE,wBAAwB0V,4BAA4B,EACrD,EACDC,WAAW,EACX1V,MAAM,KAAK,EACXuG,YAAY,EACZoP,aAAa,KAAK,EAClB1V,6BAA6B,EAC7BnF,IAAI,EACJ8a,qBAAqB,EACrB1V,oBAAoB,EACpB2V,oBAAoB,EACpBC,4BAA4B,EAC5BC,uBAAuB,EACvBpI,eAAe,EAChB,GAAGxQ;IAEJ8F,8BAA8BnD;IAE9B,MAAM,EAAEwL,0BAA0B,EAAE0K,wBAAwB,EAAE,GAC5DC,IAAAA,4CAAwB;IAC1B,MAAMC,4BAA4BC,IAAAA,0DAA4B,EAACnc;IAE/D,MAAMoc,kBAAkBC,IAAAA,yBAAiB,EACvC/G,IAAAA,iBAAS,IAAGgH,uBAAuB,IACnC/P,aAAagQ,mBAAmB;IAGlC,MAAMC,YACJhB,cAAciB,aAAa,CACxBC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDnY,GAAG,CAAC,CAACkY,WAAc,CAAA;YAClBE,KAAK,GAAGpJ,YAAY,OAAO,EAAEkJ,WAAWG,IAAAA,wCAAmB,EACzDta,KACA,QACC;YACHua,SAAS,EAAEjB,gDAAAA,4BAA8B,CAACa,SAAS;YACnDjB;YACAsB,UAAU;YACVhd;QACF,CAAA;IAEJ,MAAM,CAACqR,gBAAgB4L,gBAAgB,GAAGC,IAAAA,mCAAkB,EAC1D1B,eACA,6CAA6C;IAC7C,8EAA8E;IAC9E/H,aACAiI,aACAI,8BACAgB,IAAAA,wCAAmB,EAACta,KAAK,OACzBxC,OACAc;IAGF,2EAA2E;IAC3E,sEAAsE;IACtE,MAAMqc,yBACJ3e,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACrB,CAAC,cAAc,EAAE0e,KAAKC,SAAS,CAAChf,YAAY,GAC5CQ;IAEN,MAAMye,4BAAwD,IAAIpL;IAClE,MAAMqL,gBAAgB;IACtB,SAASC,qBAAqBpX,GAAkB;QAC9C,OAAOH,iDAAAA,8BACLG,KACAR,KACAR,mBAAmB5C,KAAK;IAE5B;IACA,MAAMib,+BAA+BC,IAAAA,qDAAiC,EACpE1X,KACA2V,YACA2B,2BACAC,eACAC;IAGF,SAASG,qBAAqBvX,GAAkB;QAC9C,OAAOH,iDAAAA,8BACLG,KACAR,KACAR,mBAAmB5C,KAAK;IAE5B;IAEA,MAAMob,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2BC,IAAAA,0CAAsB,EACrD9X,KACA2V,YACA2B,2BACAM,mBACAL,eACAI;IAGF,IAAII,oBAA8C;IAClD,IAAI3M;IAEJ,MAAMpH,YAAY7F,IAAI6F,SAAS,CAACwP,IAAI,CAACrV;IACrC,MAAM6Z,eAAe7Z,IAAI6Z,YAAY,CAACxE,IAAI,CAACrV;IAE3C,IAAI;QACF,IACE,uEAAuE;QACvE3F,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,qDAAqD;QACrDsH,OACA,oGAAoG;QACpGxH,QAAQC,GAAG,CAACoW,YAAY,KAAK,UAC7B,+EAA+E;QAC/ElB,iBACA;YACA,MAAM,CAACsK,mBAAmBC,iBAAiB,GAAGC;YAC9C,IAAI5X;YACJ,MAAMa,aAAa,OACjB,wDAAwD;YACxDvB;gBAEA,MAAM4C,UACJ,MAAM9B,kDAAoB,CAACC,GAAG,CAC5Bf,cACA8I,eACAlL,MACAjB,KACA2B,IAAIlC,UAAU,KAAK;gBAEvB,uDAAuD;gBACvD,wDAAwD;gBACxD,8DAA8D;gBAC9D,gDAAgD;gBAChDwG,QAAQ2V,WAAW,GAAGF;gBAEtB,IAAIxV,uBAAuBvF,YAAY0C,eAAe;oBACpD,qEAAqE;oBACrE,wEAAwE;oBACxE,IAAI1C,WAAWqF,cAAc,EAAE;wBAC7B,uFAAuF;wBACvFrF,WAAWqF,cAAc,CAAC,UAAUnI,eAAehC;oBACrD;oBACAoK,QAAQE,kBAAkB,GAAG5G,cAAc6G,0BAA0B;wBACnEC,OAAO7F,UAAU6F,KAAK;oBACxB;gBACF;gBAEA,OAAOJ;YACT;YAEA,IACE,wEAAwE;YACxE,sDAAsD;YACtDF,sBACA,kEAAkE;YAClE,4EAA4E;YAC5E,CAACG,uBAAuBvF,YAAY0C,eACpC;gBACA,MAAM,EACJiD,QAAQuV,YAAY,EACpB9X,cAAc+X,oBAAoB,EAClCzY,cAAc0Y,iBAAiB,EAChC,GAAG,MAAMvV,kCACRxG,KACAqD,cACA0C,oBACAnB,YACAqW;gBAGFM,oBAAoB,IAAIS,0CAAiB,CAACH;gBAC1CxY,eAAe0Y;gBACfhY,eAAe+X;YACjB,OAAO;gBACL,gEAAgE;gBAChE,4DAA4D;gBAE5D/X,eAAeL,wBAAwBM;gBAEvC,MAAM6X,eACJ,MAAMlX,+CACJ3E,KACAqD,cACAuB,YACAtB,yBACA;oBACEO,SAASoX;oBACTlf;oBACAgI,YAAY,EAAEA,gCAAAA,aAAcS,UAAU;gBACxC;gBAEJ+W,oBAAoB,IAAIS,0CAAiB,CAACH;YAC5C;YAEA,IAAI9X,gBAAgBL,sBAAsB;gBACxC,MAAM,CAACuY,aAAaC,gBAAgB,GAClCnY,aAAaE,UAAU,CAACkY,QAAQ,CAACC,GAAG;gBAEtCxN,mBAAmBqN;gBAEnBvY,qBACE;oBAAEyY,UAAUD;gBAAgB,GAC5Bre,eACAhC;YAEJ;YAEA,+BAA+B;YAC/B,kDAAkD;YAClD,4DAA4D;YAC5DwgB,gDAAmB,CAACjY,GAAG,CACrB;gBAAEkY,KAAK;YAAK,GACZC,6BACAd,mBACAxa,MACAjB,KACA2B,IAAIlC,UAAU,KAAK,KACnB6D,yBACAD,cACAwT;QAEJ,OAAO;YACL,wFAAwF;YACxF,MAAM2F,aACJ,MAAMrY,kDAAoB,CAACC,GAAG,CAC5Bf,cACA8I,eACAlL,MACAjB,KACA2B,IAAIlC,UAAU,KAAK;YAGvB,MAAMsE,eAAeL,wBAAwBM;YAE7C,IAAID,cAAc;gBAChB,MAAM,CAACkY,aAAaC,gBAAgB,GAClCnY,aAAaE,UAAU,CAACkY,QAAQ,CAACC,GAAG;gBAEtCxN,mBAAmBqN;gBAEnBvY,qBACE;oBAAEyY,UAAUD;gBAAgB,GAC5Bre,eACAhC;YAEJ;YAEA0f,oBAAoB,IAAIS,0CAAiB,CACvC7X,kDAAoB,CAACC,GAAG,CACtBf,cACA4V,8BACAuD,YACAlZ,wBAAwBgB,aAAa,EACrC;gBACEvI;gBACA8H,SAASoX;gBACTlX,YAAY,EAAEA,gCAAAA,aAAcS,UAAU;YACxC;QAGN;QAEA,mGAAmG;QACnG,oGAAoG;QACpG,6BAA6B;QAC7B,MAAMiY,IAAAA,wCAA6B;QAEnC,wEAAwE;QACxE,qBAAqB;QACrB,IAAI,OAAO9b,WAAWqN,SAAS,KAAK,UAAU;YAC5C,IAAIwC,CAAAA,kCAAAA,eAAgBpR,IAAI,MAAKsd,4BAAY,CAACC,IAAI,EAAE;gBAC9C,mEAAmE;gBACnE,4EAA4E;gBAC5E,yBAAyB;gBACzB,MAAMC,+BAA+BC,IAAAA,kDAA+B,EAClEtB,kBAAkBa,GAAG,IACrB5e,OACAia;gBAGF,OAAOqF,IAAAA,kCAAY,EACjBF,8BACAG,IAAAA,iDAA2B;YAE/B,OAAO,IAAIvM,gBAAgB;gBACzB,uEAAuE;gBACvE,MAAM,EAAExC,SAAS,EAAEgP,YAAY,EAAE,GAC/BC,IAAAA,qCAAqB,EAACzM;gBACxB,MAAM0M,SAAS,AACb/gB,QAAQ,oBACR+gB,MAAM;gBAER,MAAMC,aAAa,MAAMhZ,kDAAoB,CAACC,GAAG,CAC/Cf,cACA6Z,sBACA,qBAACxO;oBACCC,mBAAmB4M,kBAAkBa,GAAG;oBACxCxN,kBAAkBA;oBAClBC,gBAAgBA;oBAChBvL,yBAAyBA;oBACzBwL,4BAA4BA;oBAC5BtR,OAAOA;oBACPuR,QAAQ/O,IAAIW,UAAU,CAACoO,MAAM;oBAE/Bf,WACA;oBAAEnK,SAASwX;oBAA0B7d;gBAAM;gBAG7C,MAAM4f,wBAAwBC,IAAAA,oDAAyB,EAAC;oBACtDrD;oBACAR;oBACA8D,sBAAsBlC;oBACtBrC;oBACAa,iBAAiBA;gBACnB;gBACA,OAAO,MAAM2D,IAAAA,+CAAyB,EAACJ,YAAY;oBACjD,oGAAoG;oBACpG,yCAAyC;oBACzC,qGAAqG;oBACrG,2FAA2F;oBAC3FK,8BACER,iBAAiBS,uCAAuB,CAACC,KAAK;oBAChDC,mBAAmBd,IAAAA,kDAA+B,EAChDtB,kBAAkBqC,OAAO,IACzBpgB,OACAia;oBAEF2F;oBACA1D;gBACF;YACF;QACF;QAEA,mCAAmC;QACnC,MAAMnW,yBAAyB,AAC7BpH,QAAQ,oBACRoH,sBAAsB;QAExB,MAAM4Z,aAAa,MAAMhZ,kDAAoB,CAACC,GAAG,CAC/Cf,cACAE,sCACA,qBAACmL;YACCC,mBAAmB4M,kBAAkBa,GAAG;YACxCxN,kBAAkBA;YAClBC,gBAAgBA;YAChBvL,yBAAyBA;YACzBwL,4BAA4BA;YAC5BtR,OAAOA;YACPuR,QAAQ/O,IAAIW,UAAU,CAACoO,MAAM;YAE/B;YACElL,SAASwX;YACT7d;YACAqgB,WAAW,CAACthB;gBACVA,QAAQ0R,OAAO,CAAC,CAACvC,OAAOhK;oBACtB8Z,aAAa9Z,KAAKgK;gBACpB;YACF;YACAoS,kBAAkB1E;YAClBuB;YACAoD,kBAAkB;gBAACtD;aAAgB;YACnChD;QACF;QAGF,MAAM2F,wBAAwBC,IAAAA,oDAAyB,EAAC;YACtDrD;YACAR;YACA8D,sBAAsBlC;YACtBrC;YACAa,iBAAiBA;QACnB;QACA;;;;;;;;;;;;;;;;KAgBC,GACD,MAAMoE,qBACJzE,4BAA4B,QAAQ,CAAC,CAACF;QAExC,OAAO,MAAM4E,IAAAA,wCAAkB,EAACd,YAAY;YAC1CQ,mBAAmBd,IAAAA,kDAA+B,EAChDtB,kBAAkBqC,OAAO,IACzBpgB,OACAia;YAEF9U,oBAAoBqb;YACpBE,yBAAyBle,IAAIQ,SAAS,CAAC0d,uBAAuB,KAAK;YACnEzb,SAASzC,IAAIQ,SAAS,CAACiC,OAAO;YAC9B2a;YACA1D;YACAyE,oBAAoB3a;QACtB;IACF,EAAE,OAAOI,KAAK;QACZ,IACEwa,IAAAA,gDAAuB,EAACxa,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAI4K,OAAO,KAAK,YACvB5K,IAAI4K,OAAO,CAACxB,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMpJ;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAMya,qBAAqBC,IAAAA,iCAAmB,EAAC1a;QAC/C,IAAIya,oBAAoB;YACtB,MAAM5P,QAAQ8P,IAAAA,8CAA2B,EAAC3a;YAC1C4a,IAAAA,UAAK,EACH,GAAG5a,IAAI6a,MAAM,CAAC,mDAAmD,EAAEjf,SAAS,kFAAkF,EAAEiP,OAAO;YAGzK,MAAM7K;QACR;QAEA,IAAI6I;QAEJ,IAAIiS,IAAAA,6CAAyB,EAAC9a,MAAM;YAClCjC,IAAIlC,UAAU,GAAGkf,IAAAA,+CAA2B,EAAC/a;YAC7C+C,SAASlH,UAAU,GAAGkC,IAAIlC,UAAU;YACpCgN,YAAYmS,IAAAA,sDAAkC,EAACjd,IAAIlC,UAAU;QAC/D,OAAO,IAAIof,IAAAA,8BAAe,EAACjb,MAAM;YAC/B6I,YAAY;YACZ9K,IAAIlC,UAAU,GAAGqf,IAAAA,wCAA8B,EAAClb;YAChD+C,SAASlH,UAAU,GAAGkC,IAAIlC,UAAU;YAEpC,MAAMsf,cAAcC,IAAAA,4BAAa,EAACC,IAAAA,iCAAuB,EAACrb,MAAMmV;YAEhE,gEAAgE;YAChE,YAAY;YACZ,MAAMxc,UAAU,IAAI2iB;YACpB,IAAIC,IAAAA,oCAAoB,EAAC5iB,SAAS8G,aAAaqC,cAAc,GAAG;gBAC9D8B,UAAU,cAAcmE,MAAMqI,IAAI,CAACzX,QAAQmZ,MAAM;YACnD;YAEAlO,UAAU,YAAYuX;QACxB,OAAO,IAAI,CAACV,oBAAoB;YAC9B1c,IAAIlC,UAAU,GAAG;YACjBkH,SAASlH,UAAU,GAAGkC,IAAIlC,UAAU;QACtC;QAEA,MAAM,CAAC2f,qBAAqBC,qBAAqB,GAAG3E,IAAAA,mCAAkB,EACpE1B,eACA/H,aACAiI,aACAI,8BACAgB,IAAAA,wCAAmB,EAACta,KAAK,QACzBxC,OACA;QAGF,MAAM8hB,kBAAkB,MAAMnb,kDAAoB,CAACC,GAAG,CACpDf,cACA8K,oBACAlN,MACAjB,KACA8a,0BAA0ByE,GAAG,CAAC,AAAC3b,IAAYuF,MAAM,IAAI,OAAOvF,KAC5D6I;QAGF,MAAM+S,oBAAoBrb,kDAAoB,CAACC,GAAG,CAChDf,cACA4V,8BACAqG,iBACAhc,wBAAwBgB,aAAa,EACrC;YACEvI;YACA8H,SAASoX;QACX;QAGF,IAAIM,sBAAsB,MAAM;YAC9B,wFAAwF;YACxF,gCAAgC;YAChC,MAAM3X;QACR;QAEA,IAAI;YACF,MAAM6b,aAAa,MAAMtb,kDAAoB,CAACC,GAAG,CAC/Cf,cACAqc,+CAAyB,EACzB;gBACEC,gBACExjB,QAAQ;gBACVyjB,uBACE,qBAACvP;oBACC1B,mBAAmB6Q;oBACnB5Q,kBAAkBvS;oBAClByS,4BAA4BA;oBAC5BD,gBAAgBuQ;oBAChB9b,yBAAyBA;oBACzB9F,OAAOA;oBACPuR,QAAQ/O,IAAIW,UAAU,CAACoO,MAAM;;gBAGjC8Q,eAAe;oBACbriB;oBACAmd;oBACA,wCAAwC;oBACxCoD,kBAAkB;wBAACsB;qBAAqB;oBACxC5H;gBACF;YACF;YAGF;;;;;;;;;;;;;;;OAeC,GACD,MAAMuG,qBACJzE,4BAA4B,QAAQ,CAAC,CAACF;YACxC,OAAO,MAAM4E,IAAAA,wCAAkB,EAACwB,YAAY;gBAC1C9B,mBAAmBd,IAAAA,kDAA+B,EAChD,+DAA+D;gBAC/D,8DAA8D;gBAC9D,SAAS;gBACTtB,kBAAkBqC,OAAO,IACzBpgB,OACAia;gBAEF9U,oBAAoBqb;gBACpBE,yBAAyBle,IAAIQ,SAAS,CAAC0d,uBAAuB,KAAK;gBACnEzb,SAASzC,IAAIQ,SAAS,CAACiC,OAAO;gBAC9B2a,uBAAuBC,IAAAA,oDAAyB,EAAC;oBAC/CrD;oBACAR;oBACA8D,sBAAsB,EAAE;oBACxBvE;oBACAa,iBAAiBA;gBACnB;gBACAF;gBACAyE,oBAAoB3a;YACtB;QACF,EAAE,OAAOsc,UAAe;YACtB,IACE9jB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBwiB,IAAAA,6CAAyB,EAACoB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1B5jB,QAAQ;gBACV4jB;YACF;YACA,MAAMD;QACR;IACF;AACA,iDAAiD,GACnD;AAEA,eAAetZ,kCACbxG,GAAqB,EACrB8F,mBAAiC,EACjCC,kBAAsC,EACtCnB,UAA+D,EAC/Df,OAAiC;IAEjC,MAAM,EACJhG,aAAa,EACb8C,UAAU,EACV9E,SAAS,EACTqE,cAAc,EACZC,aAAa,EACXC,UAAU,EAAEnC,UAAU,EAAE,EACzB,EACF,EACF,GAAG+B;IACJ,MAAM,EACJsD,uBAAuB,EACvBsE,YAAY,EACZ5B,cAAc,EACdtC,oBAAoB,EACrB,GAAG/C;IACJ8F,8BAA8BnD;IAE9B,MAAM0c,qBACJ,MAAMC,IAAAA,qDAAmC,EAAChiB;IAE5C,mEAAmE;IACnE,IAAIoF,eAA6ByC;IAEjC,MAAMf,kBAAkB;QACtB,MAAMC,eAAe3B,aAAaiC,eAAe,CAAEN,YAAY;QAC/D,OAAQA;YACN,KAAKC,4BAAW,CAACC,MAAM;gBACrB,OAAO;YACT,KAAKD,4BAAW,CAACE,OAAO;gBACtB,OAAO6a,qBAAqB,aAAa;YAC3C,KAAK/a,4BAAW,CAACG,OAAO;gBACtB,OAAO;YACT;gBACEJ;gBACA,MAAM,qBAA2D,CAA3D,IAAIK,8BAAc,CAAC,CAAC,sBAAsB,EAAEL,cAAc,GAA1D,qBAAA;2BAAA;gCAAA;kCAAA;gBAA0D;QACpE;IACF;IAEA,iDAAiD;IACjD,iBAAiB;IACjB,iDAAiD;IAEjD,8DAA8D;IAC9D,kEAAkE;IAElE,yFAAyF;IACzF,8CAA8C;IAC9C,MAAMgD,cAAc,IAAIC,wBAAW;IAEnC,kFAAkF;IAClF,oGAAoG;IACpG,2EAA2E;IAC3EyB,IAAAA,+CAAmB,EAAC1B;IAEpB,MAAMjB,2BAA2BC,IAAAA,+CAA8B;IAE/D,MAAMkZ,yBAAyB,IAAIpY;IACnC,MAAMqY,wBAAwB,IAAIrY,kBAAkB,sCAAsC;;IAC1F,MAAMsY,yBAAyB,IAAItb,0CAAyB,CAC1Dqb,sBAAsB9X,MAAM;IAG9BhF,aAAa0D,wBAAwB,GAAGA;IACxC,4GAA4G;IAC5G,mGAAmG;IACnG1D,aAAa4D,qBAAqB,GAAG;IACrC5D,aAAaiC,eAAe,GAAG8a;IAC/B/c,aAAakC,gBAAgB,GAAGC,4BAC9B4a,wBACA/c,aAAaoC,OAAO,EACpBpC,aAAaqC,cAAc,EAC3BrC,aAAa9G,OAAO;IAEtB8G,aAAa2E,WAAW,GAAGA;IAE3B,IAAIjE,eAAeL,wBAAwBM;IAE3C,MAAMqc,oBAAoB,MAAMzb,WAAWvB;IAC3C,MAAMid,2BAA2B,MAAMnc,kDAAoB,CAACC,GAAG,CAC7Df,cACA,IACEkd,IAAAA,+CAAyB,EACvB;YACE,eAAe;YACf,MAAMja,SAASsB,aAAarE,sBAAsB,CAChD8c,mBACA/c,wBAAwBgB,aAAa,EACrC;gBACET;gBACAkB;gBACAhJ;gBACAgI,YAAY,EAAEA,gCAAAA,aAAcS,UAAU;gBACtC6D,QAAQ6X,uBAAuB7X,MAAM;YACvC;YAEF,kFAAkF;YAClF,yEAAyE;YACzE,kEAAkE;YAClE6X,uBAAuB7X,MAAM,CAACmY,gBAAgB,CAAC,SAAS;gBACtDL,sBAAsBvW,KAAK,CAACsW,uBAAuB7X,MAAM,CAACoW,MAAM;YAClE;YACA,OAAOnY;QACT,GACA,CAACA;YACC,gBAAgB;YAChB8Z,uBAAuBxa,YAAY,CAACX,4BAAW,CAACE,OAAO;YAEvD,+EAA+E;YAC/E,6CAA6C;YAC7C,IAAI6C,YAAYyY,eAAe,IAAI;gBACjC,OAAO;YACT;YAEA,wDAAwD;YACxD,4DAA4D;YAC5D,OAAOna;QACT,GACA,OAAOoa;YACL,gBAAgB;YAEhB,2DAA2D;YAC3D,sDAAsD;YACtD,mEAAmE;YACnE,+DAA+D;YAC/D,IAAIA,gBAAgB,QAAQ1Y,YAAYyY,eAAe,IAAI;gBACzD,OAAO;YACT;YAEA,6FAA6F;YAC7FL,uBAAuBxa,YAAY,CAACX,4BAAW,CAACG,OAAO;YACvD,OAAOsb;QACT;IAIN,IAAIJ,6BAA6B,MAAM;QACrC,gDAAgD;QAChD,OAAO;YACLha,QAAQga;YACRvc;YACAV;QACF;IACF;IAEA,IAAIrH,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB8J,gBAAgB;QAC5DA,eAAe,WAAWnI,eAAehC;IAC3C;IAEA,qFAAqF;IACrF,8CAA8C;IAE9C,+BAA+B;IAC/B,qEAAqE;IACrE,qFAAqF;IACrF,yEAAyE;IACzE,wFAAwF;IAExF,MAAMmM,YAAY2B,UAAU;IAC5BuW,uBAAuBtW,KAAK;IAE5B,iDAAiD;IACjD,2BAA2B;IAC3B,iDAAiD;IAEjD,uEAAuE;IACvEvG,eAAe0C;IAEf,MAAM4a,uBAAuB,IAAI7b,0CAAyB;IAE1D,0DAA0D;IAC1D,uCAAuC;IACvCzB,aAAa0D,wBAAwB,GAAG;IACxC1D,aAAa4D,qBAAqB,GAAG2Z,IAAAA,4CAA2B,EAC9D7Z;IAEF1D,aAAaiC,eAAe,GAAGqb;IAC/Btd,aAAa2E,WAAW,GAAG;IAC3B3E,aAAakC,gBAAgB,GAAGC,4BAC9Bmb,sBACAtd,aAAaoC,OAAO,EACpBpC,aAAaqC,cAAc,EAC3BrC,aAAa9G,OAAO;IAGtB,yDAAyD;IACzD,sDAAsD;IACtDwH,eAAeL,wBAAwBM;IAEvC,MAAM6c,kBAAkB,MAAMjc,WAAWvB;IACzC,MAAMyd,oBAAoB,MAAM3c,kDAAoB,CAACC,GAAG,CAACf,cAAc,IACrEkd,IAAAA,+CAAyB,EACvB;YACE,eAAe;YACf,OAAO3Y,aAAarE,sBAAsB,CACxCsd,iBACAvd,wBAAwBgB,aAAa,EACrC;gBACET;gBACAkB;gBACAhJ;gBACAgI,YAAY,EAAEA,gCAAAA,aAAcS,UAAU;YACxC;QAEJ,GACA,CAAC8B;YACC,gBAAgB;YAChBqa,qBAAqB/a,YAAY,CAACX,4BAAW,CAACE,OAAO;YACrD,OAAOmB;QACT,GACA,CAACA;YACC,gBAAgB;YAChBqa,qBAAqB/a,YAAY,CAACX,4BAAW,CAACG,OAAO;YACrD,OAAOkB;QACT;IAIJ,IAAItK,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB8J,gBAAgB;QAC5DA,eAAe,UAAUnI,eAAehC;IAC1C;IAEA,OAAO;QACLyK,QAAQwa;QACR/c;QACAV;IACF;AACF;AAEA,SAASmC,4BACPF,eAA0C,EAC1CG,OAAgC,EAChCC,cAA8C,EAC9CnJ,OAAgC;IAEhC,OAAO;QACL,eAAe;QACfkJ,SAASH,gBAAgByb,eAAe,CACtC9b,4BAAW,CAACE,OAAO,EACnB,WACAM;QAEFC,gBAAgBJ,gBAAgByb,eAAe,CAC7C9b,4BAAW,CAACE,OAAO,EACnB,WACAO;QAEFnJ,SAAS+I,gBAAgByb,eAAe,CACtC9b,4BAAW,CAACE,OAAO,EACnB,WACA5I;QAEF,gGAAgG;QAChGykB,oBAAoB1b,gBAAgByb,eAAe,CACjD9b,4BAAW,CAACE,OAAO,EACnB9I,WACA;QAEF4kB,0BAA0B3b,gBAAgByb,eAAe,CACvD9b,4BAAW,CAACE,OAAO,EACnB9I,WACA;QAEF6kB,YAAY5b,gBAAgByb,eAAe,CACzC9b,4BAAW,CAACG,OAAO,EACnB,cACA/I;IAEJ;AACF;AAgBA,SAAS2H;IACP,IAAIhI,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,OAAOG;IACT;IAEA,IAAI8kB;IAEJ,MAAMC,qBAAqB,IAAIC,eAA2B;QACxDC,OAAMhZ,UAAU;YACd6Y,qBAAqB7Y;QACvB;IACF;IAEA,OAAO;QACL9D,YAAY;YACV+c,UAAU,IAAIC,eAA2B;gBACvCC,OAAMC,KAAK;oBACTP,sCAAAA,mBAAoBQ,OAAO,CAACD;gBAC9B;gBACAE;oBACET,sCAAAA,mBAAoBS,KAAK;gBAC3B;gBACAhY,OAAMhG,GAAG;oBACPud,sCAAAA,mBAAoB3C,KAAK,CAAC5a;gBAC5B;YACF;QACF;QACAK,YAAY;YACVkY,UAAUiF;QACZ;IACF;AACF;AAEA,SAASzF;IACP,IAAIF;IACJ,IAAIoG,SAAS,IAAIC,QAAmB,CAACtX;QACnCiR,oBAAoBjR;IACtB;IACA,OAAO;QAACiR;QAAoBoG;KAAO;AACrC;AAEA;;;;;CAKC,GACD,eAAetF,4BACbd,iBAAyD,EACzDxa,IAAgB,EAChBjB,GAAqB,EACrB+hB,UAAmB,EACnBze,uBAA2E,EAC3ED,YAA0B,EAC1B1E,mBAAqD;QAuB9B0E;IArBvB,MAAM,EACJnD,cAAc0H,YAAY,EAC1BhJ,0BAA0B,EAC1B+I,YAAY,EACZnK,KAAK,EACLmD,UAAU,EACVH,SAAS,EACV,GAAGR;IAEJ,MAAM,EAAEgiB,wBAAwB,KAAK,EAAE,GAAGrhB;IAE1C,iEAAiE;IACjE,yDAAyD;IACzD,MAAMkO,iBAAiB,KAAO;IAC9B,MAAM,EAAEC,0BAA0B,EAAE,GAAG2K,IAAAA,4CAAwB;IAE/D,MAAM5S,aAAaC,IAAAA,kCAAa,EAC9Bc,aAAazH,WAAW,CAACC,QAAQ,CAACnC,UAAU,EAC5CW;IAGF,MAAMiK,kBAAiBxF,4BAAAA,aAAaoC,OAAO,CAACwc,GAAG,CAC7CC,8CAA4B,sBADP7e,0BAEpBqI,KAAK;IAER,6EAA6E;IAC7E,wEAAwE;IACxE,yEAAyE;IACzE,2EAA2E;IAC3E,UAAU;IACV,MAAM7D,mCAAmC,IAAIC;IAE7C,wDAAwD;IACxD,MAAMqa,+BAA+B,IAAIra;IAEzC,6EAA6E;IAC7E,4EAA4E;IAC5E,4EAA4E;IAC5E,2EAA2E;IAC3E,2EAA2E;IAC3E,0EAA0E;IAC1E,8EAA8E;IAC9E,qEAAqE;IACrE,4EAA4E;IAC5E,+DAA+D;IAC/D,MAAMC,gCAAgC,IAAID;IAE1C,4EAA4E;IAC5E,+BAA+B;IAC/B,MAAME,cAAc,IAAIC,wBAAW;IAEnC,MAAMma,0BAA0BpT,OAAYlG,iBAAiB;IAC7D,MAAM,EAAEA,mBAAmBuZ,uBAAuB,EAAE9iB,aAAa,EAAE,GACjEqI;IAEF,iEAAiE;IACjE,8DAA8D;IAC9D,wEAAwE;IACxE,6BAA6B;IAC7B,MAAMb,2BAA2BC,IAAAA,+CAA8B;IAC/D,MAAMsb,qCAAqD;QACzDljB,MAAM;QACN+I,OAAO;QACPtB;QACAlI;QACAgJ;QACA,wGAAwG;QACxG,gFAAgF;QAChFS,cAAcL,8BAA8BM,MAAM;QAClD,iFAAiF;QACjF,2FAA2F;QAC3F,mCAAmC;QACnCC,YAAY,IAAIR;QAChB,0EAA0E;QAC1E,2EAA2E;QAC3E,uBAAuB;QACvBE;QACAO,iBAAiB;QACjByZ;QACAxZ,YAAYG,0BAAc;QAC1BF,QAAQE,0BAAc;QACtBD,OAAOC,0BAAc;QACrBC,MAAM;eAAIjB,aAAaiB,IAAI;SAAC;QAC5B7B;QACAE,uBAAuB;QACvB4B;QACAC,mBAAmBuZ;IACrB;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAMrZ,uBAAuB,MAAM7E,kDAAoB,CAACC,GAAG,CACzDke,oCACAnW,eACAlL,MACAjB,KACA+hB;IAGF,MAAM7Z,8BAA8C;QAClD9I,MAAM;QACN+I,OAAO;QACPtB;QACAlI;QACAgJ;QACAS,cAAcL,8BAA8BM,MAAM;QAClDC,YAAYT;QACZ,0EAA0E;QAC1E,2EAA2E;QAC3E,uBAAuB;QACvBG;QACAO,iBAAiB;QACjByZ;QACAxZ,YAAYG,0BAAc;QAC1BF,QAAQE,0BAAc;QACtBD,OAAOC,0BAAc;QACrBC,MAAM;eAAIjB,aAAaiB,IAAI;SAAC;QAC5B7B;QACAE,uBAAuB;QACvB4B;QACAC,mBAAmBuZ;IACrB;IAEA,MAAMpZ,6BAA6B9E,kDAAoB,CAACC,GAAG,CACzD8D,6BACAN,aAAasB,SAAS,EACtBF,sBACA1F,wBAAwBgB,aAAa,EACrC;QACEvI;QACA8H,SAAS,CAACD;YACR,MAAMuF,SAASC,IAAAA,8CAA0B,EAACxF;YAE1C,IAAIuF,QAAQ;gBACV,OAAOA;YACT;YAEA,IAAIoZ,IAAAA,4CAAsB,EAAC3e,MAAM;gBAC/B,kBAAkB;gBAClB4S,QAAQgI,KAAK,CAAC5a;gBACd,OAAOvH;YACT;YAEA,IAAIwL,iCAAiCQ,MAAM,CAACgB,OAAO,EAAE;gBACnD,mEAAmE;gBACnE,iEAAiE;gBACjE;YACF,OAAO,IACLrN,QAAQC,GAAG,CAACqN,gBAAgB,IAC5BtN,QAAQC,GAAG,CAACsN,sBAAsB,EAClC;gBACAC,IAAAA,iEAAyC,EAAC5F,KAAKpD,UAAU6F,KAAK;YAChE;QACF;QACA,iFAAiF;QACjF,qCAAqC;QACrCoD,YAAYpN;QACZ,+EAA+E;QAC/E,iFAAiF;QACjF,iDAAiD;QACjDgM,QAAQ8Z,6BAA6B9Z,MAAM;IAC7C;IAGF,4EAA4E;IAC5E,6EAA6E;IAC7E,aAAa;IACb8Z,6BAA6B9Z,MAAM,CAACmY,gBAAgB,CAClD,SACA;QACEzY,8BAA8B6B,KAAK;IACrC,GACA;QAAE4Y,MAAM;IAAK;IAGf,8EAA8E;IAC9E9Y,IAAAA,+CAAmB,EAAC1B;IACpB,MAAMA,YAAY2B,UAAU;IAE5BwY,6BAA6BvY,KAAK;IAElC,gEAAgE;IAChE,iEAAiE;IACjE,MAAM,EAAEC,wBAAwB,EAAE,GAAGrJ;IACrC,IAAIqJ,0BAA0B;QAC5B4R,kBACElc,cAAckjB,WAAW;YACvBC,IAAI;gBACFlM,QAAQgI,KAAK,CAAC3U;YAChB;QACF;QAEF;IACF;IAEA,IAAI8Y;IACJ,IAAI;QACFA,sBAAsB,MAAM7Y,IAAAA,yDAAgC,EAC1Db;IAEJ,EAAE,OAAOrF,KAAK;QACZ,IACEue,6BAA6B9Z,MAAM,CAACgB,OAAO,IAC3CxB,iCAAiCQ,MAAM,CAACgB,OAAO,EAC/C;QACA,4EAA4E;QAC9E,OAAO,IACLrN,QAAQC,GAAG,CAACqN,gBAAgB,IAC5BtN,QAAQC,GAAG,CAACsN,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnFC,IAAAA,iEAAyC,EAAC5F,KAAKpD,UAAU6F,KAAK;QAChE;IACF;IAEA,IAAIsc,qBAAqB;QACvB,MAAMC,mCAAmC,IAAI9a;QAC7C,MAAM+a,+BAA+B,IAAI/a;QACzC,MAAMgb,gCAAgC,IAAIhb;QAE1C,MAAMib,8BAA8C;YAClD3jB,MAAM;YACN+I,OAAO;YACPtB;YACAlI;YACAgJ;YACAS,cAAc0a,8BAA8Bza,MAAM;YAClDC,YAAYsa;YACZ,sDAAsD;YACtD,qDAAqD;YACrD5a,aAAa;YACbO,iBAAiB;YACjByZ;YACAxZ,YAAYG,0BAAc;YAC1BF,QAAQE,0BAAc;YACtBD,OAAOC,0BAAc;YACrBC,MAAM;mBAAIjB,aAAaiB,IAAI;aAAC;YAC5B7B;YACAE,uBAAuB;YACvB4B,gBAAgBxM;YAChByM,mBAAmBsZ;QACrB;QAEA,MAAMlZ,YAAY,AAChB/M,QAAQ,oBACR+M,SAAS;QACX,MAAM8Z,6BAA6B7e,kDAAoB,CAACC,GAAG,CACzD2e,6BACA7Z,WACA,2EAA2E;sBAC3E,qBAACwF;YACCC,mBAAmBgU,oBAAoBM,iBAAiB;YACxDrU,kBAAkBvS;YAClBwS,gBAAgBA;YAChBvL,yBAAyBA;YACzBwL,4BAA4BA;YAC5BtR,OAAOA;YACPuR,QAAQ/O,IAAIW,UAAU,CAACoO,MAAM;YAE/B;YACE1G,QAAQwa,6BAA6Bxa,MAAM;YAC3CxE,SAAS,CAACD;gBACR,MAAMuF,SAASC,IAAAA,8CAA0B,EAACxF;gBAE1C,IAAIuF,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IAAIoZ,IAAAA,4CAAsB,EAAC3e,MAAM;oBAC/B,kBAAkB;oBAClB4S,QAAQgI,KAAK,CAAC5a;oBACd,OAAOvH;gBACT;gBAEA,IAAIwmB,6BAA6Bxa,MAAM,CAACgB,OAAO,EAAE;gBAC/C,4EAA4E;gBAC9E,OAAO,IACLrN,QAAQC,GAAG,CAACqN,gBAAgB,IAC5BtN,QAAQC,GAAG,CAACsN,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnFC,IAAAA,iEAAyC,EAAC5F,KAAKpD,UAAU6F,KAAK;gBAChE;YACF;QAGF;QAGF,4EAA4E;QAC5E,4DAA4D;QAC5D,8BAA8B;QAC9Bwc,6BAA6Bxa,MAAM,CAACmY,gBAAgB,CAClD,SACA;YACEsC,8BAA8BlZ,KAAK;QACrC,GACA;YAAE4Y,MAAM;QAAK;QAGfQ,2BAA2BE,KAAK,CAAC,CAACtf;YAChC,IACEif,6BAA6Bxa,MAAM,CAACgB,OAAO,IAC3C8Z,IAAAA,6CAA2B,EAACvf,MAC5B;YACA,4EAA4E;YAC9E,OAAO,IACL5H,QAAQC,GAAG,CAACqN,gBAAgB,IAC5BtN,QAAQC,GAAG,CAACsN,sBAAsB,EAClC;gBACA,8EAA8E;gBAC9E,mFAAmF;gBACnFC,IAAAA,iEAAyC,EAAC5F,KAAKpD,UAAU6F,KAAK;YAChE;QACF;QAEA,sEAAsE;QACtE,uGAAuG;QACvGqD,IAAAA,+CAAmB,EAAC1B;QACpB,MAAMA,YAAY2B,UAAU;QAC5BkZ,6BAA6BjZ,KAAK;IACpC;IAEA,MAAMwZ,6BAA6B,IAAItb;IACvC,MAAMub,8BAA8B,IAAIvb;IAExC,MAAMwb,mCAAmD;QACvDlkB,MAAM;QACN+I,OAAO;QACPtB;QACAlI;QACAgJ;QACA,wGAAwG;QACxG,gFAAgF;QAChFS,cAAcib,4BAA4Bhb,MAAM;QAChD,iFAAiF;QACjF,2FAA2F;QAC3F,mCAAmC;QACnCC,YAAY,IAAIR;QAChB,8EAA8E;QAC9EE,aAAa;QACbO,iBAAiB;QACjByZ;QACAxZ,YAAYG,0BAAc;QAC1BF,QAAQE,0BAAc;QACtBD,OAAOC,0BAAc;QACrBC,MAAM;eAAIjB,aAAaiB,IAAI;SAAC;QAC5B7B;QACAE,uBAAuB;QACvB4B;QACAC,mBAAmBuZ;IACrB;IAEA,MAAMkB,yBAAyB,MAAMpf,kDAAoB,CAACC,GAAG,CAC3Dkf,kCACAnX,eACAlL,MACAjB,KACA+hB;IAGF,MAAM1X,wBAAwBC,IAAAA,4CAA0B,EACtD,MAAM,yBAAyB;;IAGjC,MAAMK,4BAA4C;QAChDvL,MAAM;QACN+I,OAAO;QACPtB;QACAlI;QACAgJ;QACAS,cAAcib,4BAA4Bhb,MAAM;QAChDC,YAAY8a;QACZ,8EAA8E;QAC9Epb,aAAa;QACbO,iBAAiB8B;QACjB2X;QACAxZ,YAAYG,0BAAc;QAC1BF,QAAQE,0BAAc;QACtBD,OAAOC,0BAAc;QACrBC,MAAM;eAAIjB,aAAaiB,IAAI;SAAC;QAC5B7B;QACAE,uBAAuB;QACvB4B;QACAC,mBAAmBuZ;IACrB;IAEA,MAAM9G,oBAAoB,MAAMzR,IAAAA,yDAAgC,EAC9D0Z,IAAAA,2DAAkC,EAChC;QACE,MAAMC,yBAAyBtf,kDAAoB,CAACC,GAAG,CACrD,qBAAqB;QACrBuG,2BACA,sBAAsB;QACtB/C,aAAasB,SAAS,EACtB,4CAA4C;QAC5Cqa,wBACAjgB,wBAAwBgB,aAAa,EACrC;YACEvI;YACA8H,SAAS,CAACD;gBACR,IACEwf,2BAA2B/a,MAAM,CAACgB,OAAO,IACzC8Z,IAAAA,6CAA2B,EAACvf,MAC5B;oBACA,OAAOA,IAAIuF,MAAM;gBACnB;gBAEA,IAAIoZ,IAAAA,4CAAsB,EAAC3e,MAAM;oBAC/B,kBAAkB;oBAClB4S,QAAQgI,KAAK,CAAC5a;oBACd,OAAOvH;gBACT;gBAEA,OAAO+M,IAAAA,8CAA0B,EAACxF;YACpC;YACAyE,QAAQ+a,2BAA2B/a,MAAM;QAC3C;QAGF,sEAAsE;QACtE,kEAAkE;QAClE,8BAA8B;QAC9B+a,2BAA2B/a,MAAM,CAACmY,gBAAgB,CAChD,SACA;YACE6C,4BAA4BzZ,KAAK;QACnC,GACA;YAAE4Y,MAAM;QAAK;QAGf,OAAOiB;IACT,GACA;QACEL,2BAA2BxZ,KAAK;IAClC;IAIJ,MAAM8Z,wBAAwBpZ,IAAAA,4CAA0B,EACtD,MAAM,wBAAwB;;IAEhC,MAAMqZ,6BAA6B,IAAI7b;IACvC,MAAM8b,8BAA8B,IAAI9b;IAExC,MAAM+b,4BAA4C;QAChDzkB,MAAM;QACN+I,OAAO;QACPtB;QACAlI;QACAgJ;QACAS,cAAcwb,4BAA4Bvb,MAAM;QAChDC,YAAYqb;QACZ,oFAAoF;QACpF3b,aAAa;QACbO,iBAAiBmb;QACjB1B;QACAxZ,YAAYG,0BAAc;QAC1BF,QAAQE,0BAAc;QACtBD,OAAOC,0BAAc;QACrBC,MAAM;eAAIjB,aAAaiB,IAAI;SAAC;QAC5B7B;QACAE,uBAAuB;QACvB4B;QACAC,mBAAmBsZ;IACrB;IAEA,IAAI0B,oBAAoBC,IAAAA,8CAA4B;IAEpD,IAAI;QACF,MAAM7a,YAAY,AAChB/M,QAAQ,oBACR+M,SAAS;QACX,IAAI,EAAExB,SAASsc,kBAAkB,EAAE,GACjC,MAAMR,IAAAA,2DAAkC,EACtC;YACE,MAAMS,2BAA2B9f,kDAAoB,CAACC,GAAG,CACvDyf,2BACA3a,WACA,2EAA2E;0BAC3E,qBAACwF;gBACCC,mBAAmB4M,kBAAkB0H,iBAAiB;gBACtDrU,kBAAkBvS;gBAClBwS,gBAAgBA;gBAChBvL,yBAAyBA;gBACzBwL,4BAA4BA;gBAC5BtR,OAAOA;gBACPuR,QAAQ/O,IAAIW,UAAU,CAACoO,MAAM;gBAE/B;gBACE1G,QAAQsb,2BAA2Btb,MAAM;gBACzCxE,SAAS,CAACD,KAAcsgB;oBACtB,IACEf,IAAAA,6CAA2B,EAACvf,QAC5B+f,2BAA2Btb,MAAM,CAACgB,OAAO,EACzC;wBACA,MAAM8a,iBAAiBD,UAAUC,cAAc;wBAC/C,IAAI,OAAOA,mBAAmB,UAAU;4BACtCC,IAAAA,2CAAyB,EACvB5jB,WACA2jB,gBACAL,mBACAJ;wBAEJ;wBACA;oBACF;oBAEA,IAAInB,IAAAA,4CAAsB,EAAC3e,MAAM;wBAC/B,kBAAkB;wBAClB4S,QAAQgI,KAAK,CAAC5a;wBACd,OAAOvH;oBACT;oBAEA,OAAO+M,IAAAA,8CAA0B,EAACxF;gBACpC;YAGF;YAGF,sEAAsE;YACtE,kEAAkE;YAClE,8BAA8B;YAC9B+f,2BAA2Btb,MAAM,CAACmY,gBAAgB,CAChD,SACA;gBACEoD,4BAA4Bha,KAAK;YACnC,GACA;gBAAE4Y,MAAM;YAAK;YAGf,OAAOyB;QACT,GACA;YACEN,2BAA2B/Z,KAAK;QAClC;QAGJ,MAAM,EAAEya,cAAc,EAAE,GAAG,MAAMC,IAAAA,uCAAc,EAACN;QAChDvI,kBACElc,cAAckjB,WAAW;YACvBC,IAAI6B,0CAAwB,CAACvN,IAAI,CAC/B,MACAxW,WACA6jB,iBAAiBG,8BAAY,CAAC9G,KAAK,GAAG8G,8BAAY,CAACC,IAAI,EACvDX,mBACAzZ;QAEJ;IAEJ,EAAE,OAAOqa,aAAa;QACpB,8EAA8E;QAC9E,gDAAgD;QAEhD,IAAIC,kBAAkBJ,0CAAwB,CAACvN,IAAI,CACjD,MACAxW,WACAgkB,8BAAY,CAACI,OAAO,EACpBd,mBACAzZ;QAGF,IAAIrO,QAAQC,GAAG,CAACqN,gBAAgB,IAAItN,QAAQC,GAAG,CAACsN,sBAAsB,EAAE;YACtE,8EAA8E;YAC9E,mFAAmF;YACnF,MAAMsb,0BAA0BF;YAChCA,kBAAkB;gBAChBnO,QAAQgI,KAAK,CACX;gBAEFhI,QAAQgI,KAAK,CAACkG;gBACdG;YACF;QACF;QAEApJ,kBACElc,cAAckjB,WAAW;YACvBC,IAAIiC;QACN;IAEJ;AACF;AAEA,eAAelC,UAAU,EAAEC,EAAE,EAAyB;IACpD,IAAI;QACF,MAAMA;IACR,EAAE,OAAM,CAAC;IACT,OAAO;AACT;AAcA;;CAEC,GACD,SAASoC,+BAA+BtkB,SAAoB;IAC1D,MAAM,EAAEmC,kBAAkB,EAAE,GAAGnC;IAC/B,IAAI,CAACmC,oBAAoB,OAAO;IAEhC,OAAO;AACT;AAEA,eAAeqS,kBACb5R,GAAoB,EACpBzB,GAAqB,EACrB3B,GAAqB,EACrB2G,QAAqC,EACrC1F,IAAgB,EAChBtC,mBAAqD;IAErD,kEAAkE;IAClE,yEAAyE;IACzE,6DAA6D;IAC7D,MAAM8Y,YAAY;IAElB,MAAM,EACJxG,WAAW,EACXrS,0BAA0B,EAC1B+I,YAAY,EACZnK,KAAK,EACLgC,QAAQ,EACRmB,UAAU,EACVH,SAAS,EACV,GAAGR;IAEJ,MAAM,EACJgiB,wBAAwB,KAAK,EAC7BjJ,QAAQ,EACRC,aAAa,EACb1V,uBAAuB,EACvBsE,YAAY,EACZsR,WAAW,EACX1V,MAAM,KAAK,EACXuG,YAAY,EACZC,sBAAsB,EACtBmP,aAAa,KAAK,EAClB1V,6BAA6B,EAC7BnF,IAAI,EACJ8a,qBAAqB,EACrBE,4BAA4B,EAC5BnI,eAAe,EAChB,GAAGxQ;IAEJ8F,8BAA8BnD;IAE9B,MAAMuD,aAAaC,IAAAA,kCAAa,EAAC7F,MAAMrC;IAEvC,MAAM,EAAEkQ,0BAA0B,EAAE0K,wBAAwB,EAAE,GAC5DC,IAAAA,4CAAwB;IAC1B,MAAMC,4BAA4BC,IAAAA,0DAA4B,EAACnc;IAE/D,MAAMoc,kBAAkBC,IAAAA,yBAAiB,EACvC/G,IAAAA,iBAAS,IAAGgH,uBAAuB,IACnC/P,aAAagQ,mBAAmB;IAGlC,MAAMC,YACJhB,cAAciB,aAAa,CACxBC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDnY,GAAG,CAAC,CAACkY,WAAc,CAAA;YAClBE,KAAK,GAAGpJ,YAAY,OAAO,EAAEkJ,WAAWG,IAAAA,wCAAmB,EACzDta,KACA,QACC;YACHua,SAAS,EAAEjB,gDAAAA,4BAA8B,CAACa,SAAS;YACnDjB;YACAsB,UAAU;YACVhd;QACF,CAAA;IAEJ,MAAM,CAACqR,gBAAgB4L,gBAAgB,GAAGC,IAAAA,mCAAkB,EAC1D1B,eACA,6CAA6C;IAC7C,8EAA8E;IAC9E/H,aACAiI,aACAI,8BACAgB,IAAAA,wCAAmB,EAACta,KAAK,OACzBxC,OACAc;IAGF,MAAMwc,4BAAwD,IAAIpL;IAClE,+EAA+E;IAC/E,MAAMqL,gBAAgB,CAAC,CAAChR,aAAa9M,iBAAiB;IACtD,SAAS+d,qBAAqBpX,GAAkB;QAC9C,OAAOH,iDAAAA,8BACLG,KACAR,KACAR,mBAAmB5C,KAAK;IAE5B;IACA,MAAMib,+BAA+BC,IAAAA,qDAAiC,EACpE1X,KACA2V,YACA2B,2BACAC,eACAC;IAGF,SAASG,qBAAqBvX,GAAkB;QAC9C,OAAOH,iDAAAA,8BACLG,KACAR,KACAR,mBAAmB5C,KAAK;IAE5B;IACA,MAAMob,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2BC,IAAAA,0CAAsB,EACrD9X,KACA2V,YACA2B,2BACAM,mBACAL,eACAI;IAGF,IAAI4J,6BAAgE;IACpE,MAAMC,oBAAoB,CAACnlB;QACzB8G,SAASpK,OAAO,KAAK,CAAC;QACtBoK,SAASpK,OAAO,CAACsD,KAAK,GAAG8B,IAAImL,SAAS,CAACjN;IACzC;IACA,MAAM2H,YAAY,CAAC3H,MAAc6L;QAC/B/J,IAAI6F,SAAS,CAAC3H,MAAM6L;QACpBsZ,kBAAkBnlB;QAClB,OAAO8B;IACT;IACA,MAAM6Z,eAAe,CAAC3b,MAAc6L;QAClC,IAAIC,MAAMC,OAAO,CAACF,QAAQ;YACxBA,MAAMuC,OAAO,CAAC,CAACgX;gBACbtjB,IAAI6Z,YAAY,CAAC3b,MAAMolB;YACzB;QACF,OAAO;YACLtjB,IAAI6Z,YAAY,CAAC3b,MAAM6L;QACzB;QACAsZ,kBAAkBnlB;IACpB;IAEA,MAAMoK,kBAAkBC,sBAAsBH;IAE9C,IAAImb,iBAAwC;IAE5C,IAAI;QACF,IAAI/T,iBAAiB;YACnB;;;;;;;;;;;;OAYC,GAED,wEAAwE;YACxE,0EAA0E;YAC1E,mEAAmE;YACnE,yEAAyE;YACzE,qBAAqB;YACrB,MAAMtJ,mCAAmC,IAAIC;YAE7C,wDAAwD;YACxD,MAAMqa,+BAA+B,IAAIra;YAEzC,sEAAsE;YACtE,sEAAsE;YACtE,kEAAkE;YAClE,wEAAwE;YACxE,wEAAwE;YACxE,wEAAwE;YACxE,wEAAwE;YACxE,0EAA0E;YAC1E,sEAAsE;YACtE,wEAAwE;YACxE,+BAA+B;YAC/B,MAAMC,gCAAgC,IAAID;YAE1C,kFAAkF;YAClF,yBAAyB;YACzB,MAAME,cAAc,IAAIC,wBAAW;YAEnC,IAAIkd;YACJ,IAAIle,wBAAsD;YAC1D,IAAIF,2BAA4D;YAEhE,IAAIpG,WAAWsG,qBAAqB,EAAE;gBACpC,sEAAsE;gBACtE,wEAAwE;gBACxE,uEAAuE;gBACvE,cAAc;gBACdke,kBAAkBle,wBAChBtG,WAAWsG,qBAAqB;YACpC,OAAO;gBACL,iEAAiE;gBACjEke,kBAAkBpe,2BAChBC,IAAAA,+CAA8B;YAClC;YAEA,MAAMsb,qCAAqD;gBACzDljB,MAAM;gBACN+I,OAAO;gBACPtB;gBACAlI;gBACAgJ;gBACA,wGAAwG;gBACxG,gFAAgF;gBAChFS,cAAcL,8BAA8BM,MAAM;gBAClD,iFAAiF;gBACjF,2FAA2F;gBAC3F,mCAAmC;gBACnCC,YAAY,IAAIR;gBAChB,0EAA0E;gBAC1E,2EAA2E;gBAC3E,uBAAuB;gBACvBE;gBACAO,iBAAiB;gBACjByZ;gBACAxZ,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;gBAC5B7B;gBACAE;gBACA4B,gBAAgBxM;gBAChByM,mBAAmBzM;YACrB;YAEA,0FAA0F;YAC1F,wFAAwF;YACxF,MAAM2M,uBAAuB,MAAM7E,kDAAoB,CAACC,GAAG,CACzDke,oCACAnW,eACAlL,MACAjB,KACA2B,IAAIlC,UAAU,KAAK;YAGrB,MAAMyI,8BAA+Cgd,iBAAiB;gBACpE9lB,MAAM;gBACN+I,OAAO;gBACPtB;gBACAlI;gBACAgJ;gBACAS,cAAcL,8BAA8BM,MAAM;gBAClDC,YAAYT;gBACZ,0EAA0E;gBAC1E,2EAA2E;gBAC3E,uBAAuB;gBACvBG;gBACAO,iBAAiB;gBACjByZ;gBACAxZ,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;gBAC5B7B;gBACAE;gBACA4B,gBAAgBxM;gBAChByM,mBAAmBzM;YACrB;YAEA,MAAM4M,6BAA6B9E,kDAAoB,CAACC,GAAG,CACzD8D,6BACAN,aAAasB,SAAS,EACtBF,sBACA1F,wBAAwBgB,aAAa,EACrC;gBACEvI;gBACA8H,SAAS,CAACD;oBACR,MAAMuF,SAASC,IAAAA,8CAA0B,EAACxF;oBAE1C,IAAIuF,QAAQ;wBACV,OAAOA;oBACT;oBAEA,IAAIoZ,IAAAA,4CAAsB,EAAC3e,MAAM;wBAC/B,kBAAkB;wBAClB4S,QAAQgI,KAAK,CAAC5a;wBACd,OAAOvH;oBACT;oBAEA,IAAIwL,iCAAiCQ,MAAM,CAACgB,OAAO,EAAE;wBACnD,mEAAmE;wBACnE,iEAAiE;wBACjE;oBACF,OAAO,IACLrN,QAAQC,GAAG,CAACqN,gBAAgB,IAC5BtN,QAAQC,GAAG,CAACsN,sBAAsB,EAClC;wBACAC,IAAAA,iEAAyC,EAAC5F,KAAKpD,UAAU6F,KAAK;oBAChE;gBACF;gBACA,iFAAiF;gBACjF,qCAAqC;gBACrCoD,YAAYpN;gBACZ,+EAA+E;gBAC/E,iFAAiF;gBACjF,iDAAiD;gBACjDgM,QAAQ8Z,6BAA6B9Z,MAAM;YAC7C;YAGF,sEAAsE;YACtE,kEAAkE;YAClE,8BAA8B;YAC9B8Z,6BAA6B9Z,MAAM,CAACmY,gBAAgB,CAClD,SACA;gBACEzY,8BAA8B6B,KAAK;gBACnC/B,iCAAiC+B,KAAK;YACxC,GACA;gBAAE4Y,MAAM;YAAK;YAGf,8EAA8E;YAC9E9Y,IAAAA,+CAAmB,EAAC1B;YACpB,MAAMA,YAAY2B,UAAU;YAE5BwY,6BAA6BvY,KAAK;YAElC,gEAAgE;YAChE,iEAAiE;YACjE,IAAIpJ,UAAUqJ,wBAAwB,EAAE;gBACtCwL,IAAAA,2CAAyB,EAAC7U,WAAWA,UAAUqJ,wBAAwB;gBACvE,MAAM,IAAIyL,8CAAqB;YACjC;YAEA,IAAIqN;YACJ,IAAI;gBACFA,sBAAsB,MAAM7Y,IAAAA,yDAAgC,EAC1Db;YAEJ,EAAE,OAAOrF,KAAK;gBACZ,IACEue,6BAA6B9Z,MAAM,CAACgB,OAAO,IAC3CxB,iCAAiCQ,MAAM,CAACgB,OAAO,EAC/C;gBACA,4EAA4E;gBAC9E,OAAO,IACLrN,QAAQC,GAAG,CAACqN,gBAAgB,IAC5BtN,QAAQC,GAAG,CAACsN,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnFC,IAAAA,iEAAyC,EAAC5F,KAAKpD,UAAU6F,KAAK;gBAChE;YACF;YAEA,IAAIsc,qBAAqB;gBACvB,MAAMC,mCAAmC,IAAI9a;gBAC7C,MAAM+a,+BAA+B,IAAI/a;gBACzC,MAAMgb,gCAAgC,IAAIhb;gBAE1C,MAAMib,8BAA8C;oBAClD3jB,MAAM;oBACN+I,OAAO;oBACPtB;oBACAlI;oBACAgJ;oBACAS,cAAc0a,8BAA8Bza,MAAM;oBAClDC,YAAYsa;oBACZ,sDAAsD;oBACtD,qDAAqD;oBACrD5a,aAAa;oBACbO,iBAAiB;oBACjByZ;oBACAxZ,YAAYG,0BAAc;oBAC1BF,QAAQE,0BAAc;oBACtBD,OAAOC,0BAAc;oBACrBC,MAAM;2BAAIjB,aAAaiB,IAAI;qBAAC;oBAC5B7B;oBACAE;oBACA4B,gBAAgBxM;oBAChByM,mBAAmBzM;gBACrB;gBAEA,MAAM6M,YAAY,AAChB/M,QAAQ,oBACR+M,SAAS;gBACX,MAAM8Z,6BAA6B7e,kDAAoB,CAACC,GAAG,CACzD2e,6BACA7Z,WACA,2DAA2D;8BAC3D,qBAACwF;oBACCC,mBAAmBgU,oBAAoBM,iBAAiB;oBACxDrU,kBAAkBvS;oBAClBwS,gBAAgBA;oBAChBvL,yBAAyBA;oBACzBwL,4BAA4BA;oBAC5BtR,OAAOA;oBACPuR,QAAQ/O,IAAIW,UAAU,CAACoO,MAAM;oBAE/B;oBACE1G,QAAQwa,6BAA6Bxa,MAAM;oBAC3CxE,SAAS,CAACD;wBACR,MAAMuF,SAASC,IAAAA,8CAA0B,EAACxF;wBAE1C,IAAIuF,QAAQ;4BACV,OAAOA;wBACT;wBAEA,IAAIoZ,IAAAA,4CAAsB,EAAC3e,MAAM;4BAC/B,kBAAkB;4BAClB4S,QAAQgI,KAAK,CAAC5a;4BACd,OAAOvH;wBACT;wBAEA,IAAIwmB,6BAA6Bxa,MAAM,CAACgB,OAAO,EAAE;wBAC/C,4EAA4E;wBAC9E,OAAO,IACLrN,QAAQC,GAAG,CAACqN,gBAAgB,IAC5BtN,QAAQC,GAAG,CAACsN,sBAAsB,EAClC;4BACA,8EAA8E;4BAC9E,mFAAmF;4BACnFC,IAAAA,iEAAyC,EAAC5F,KAAKpD,UAAU6F,KAAK;wBAChE;oBACF;oBACA0X,kBAAkB;wBAACtD;qBAAgB;gBACrC;gBAGF,sEAAsE;gBACtE,kEAAkE;gBAClE,8BAA8B;gBAC9BoI,6BAA6Bxa,MAAM,CAACmY,gBAAgB,CAClD,SACA;oBACEsC,8BAA8BlZ,KAAK;gBACrC,GACA;oBAAE4Y,MAAM;gBAAK;gBAGfQ,2BAA2BE,KAAK,CAAC,CAACtf;oBAChC,IACEif,6BAA6Bxa,MAAM,CAACgB,OAAO,IAC3C8Z,IAAAA,6CAA2B,EAACvf,MAC5B;oBACA,4EAA4E;oBAC9E,OAAO,IACL5H,QAAQC,GAAG,CAACqN,gBAAgB,IAC5BtN,QAAQC,GAAG,CAACsN,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnFC,IAAAA,iEAAyC,EAAC5F,KAAKpD,UAAU6F,KAAK;oBAChE;gBACF;gBAEA,sEAAsE;gBACtE,uGAAuG;gBACvGqD,IAAAA,+CAAmB,EAAC1B;gBACpB,MAAMA,YAAY2B,UAAU;gBAC5BkZ,6BAA6BjZ,KAAK;YACpC;YAEA,MAAMwZ,6BAA6B,IAAItb;YACvC,MAAMub,8BAA8B,IAAIvb;YAExC,MAAMwb,mCAAmD;gBACvDlkB,MAAM;gBACN+I,OAAO;gBACPtB;gBACAlI;gBACAgJ;gBACA,wGAAwG;gBACxG,gFAAgF;gBAChFS,cAAcib,4BAA4Bhb,MAAM;gBAChD,iFAAiF;gBACjF,2FAA2F;gBAC3F,mCAAmC;gBACnCC,YAAY,IAAIR;gBAChB,8EAA8E;gBAC9EE,aAAa;gBACbO,iBAAiB;gBACjByZ;gBACAxZ,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;gBAC5B7B;gBACAE;gBACA4B,gBAAgBxM;gBAChByM,mBAAmBzM;YACrB;YAEA,MAAMknB,yBAAyB,MAAMpf,kDAAoB,CAACC,GAAG,CAC3Dkf,kCACAnX,eACAlL,MACAjB,KACA2B,IAAIlC,UAAU,KAAK;YAGrB,MAAM4K,wBAAwBC,IAAAA,4CAA0B,EACtDN;YAEF,IAAIG,kBAAkB;YAEtB,MAAMQ,4BAA6Cua,iBAAiB;gBAClE9lB,MAAM;gBACN+I,OAAO;gBACPtB;gBACAlI;gBACAgJ;gBACAS,cAAcib,4BAA4Bhb,MAAM;gBAChDC,YAAY8a;gBACZ,8EAA8E;gBAC9Epb,aAAa;gBACbO,iBAAiB8B;gBACjB2X;gBACAxZ,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;gBAC5B7B;gBACAE;gBACA4B,gBAAgBxM;gBAChByM,mBAAmBzM;YACrB;YAEA,IAAIwO,qBAAqB;YACzB,MAAM0Q,oBAAqBwJ,6BACzB,MAAMjb,IAAAA,yDAAgC,EACpC0Z,IAAAA,2DAAkC,EAChC;gBACE,MAAMC,yBAAyBtf,kDAAoB,CAACC,GAAG,CACrD,qBAAqB;gBACrBuG,2BACA,sBAAsB;gBACtB/C,aAAasB,SAAS,EACtB,4CAA4C;gBAC5Cqa,wBACAjgB,wBAAwBgB,aAAa,EACrC;oBACEvI;oBACA8H,SAAS,CAACD;wBACR,OAAOqX,6BAA6BrX;oBACtC;oBACAyE,QAAQ+a,2BAA2B/a,MAAM;gBAC3C;gBAGF,gEAAgE;gBAChE,iEAAiE;gBACjE,qCAAqC;gBACrC+a,2BAA2B/a,MAAM,CAACmY,gBAAgB,CAChD,SACA;oBACE6C,4BAA4BzZ,KAAK;gBACnC,GACA;oBAAE4Y,MAAM;gBAAK;gBAGf,MAAMzX,kBAAkB,MAAM0Y;gBAC9B5Y,qBAAqB;gBAErB,OAAOE;YACT,GACA;gBACE,IAAIqY,2BAA2B/a,MAAM,CAACgB,OAAO,EAAE;oBAC7C,4EAA4E;oBAC5E,6EAA6E;oBAC7Ec,kBAAkB;oBAClB;gBACF;gBAEA,IAAIU,oBAAoB;oBACtB,kFAAkF;oBAClF,iCAAiC;oBACjCV,kBAAkB;gBACpB;gBAEAiZ,2BAA2BxZ,KAAK;YAClC;YAIN,MAAM8Z,wBAAwBpZ,IAAAA,4CAA0B,EACtDN;YAGF,MAAM2Z,6BAA6B,IAAI7b;YACvC,MAAM8b,8BAA8B,IAAI9b;YAExC,MAAM+b,4BAA4C;gBAChDzkB,MAAM;gBACN+I,OAAO;gBACPtB;gBACAlI;gBACAgJ;gBACAS,cAAcwb,4BAA4Bvb,MAAM;gBAChDC,YAAYqb;gBACZ,oFAAoF;gBACpF3b,aAAa;gBACbO,iBAAiBmb;gBACjB1B;gBACAxZ,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;gBAC5B7B;gBACAE;gBACA4B,gBAAgBxM;gBAChByM,mBAAmBzM;YACrB;YAEA,IAAIynB,oBAAoBC,IAAAA,8CAA4B;YAEpD,MAAM7a,YAAY,AAChB/M,QAAQ,oBACR+M,SAAS;YACX,IAAI,EAAExB,SAASsc,kBAAkB,EAAEhW,SAAS,EAAE,GAC5C,MAAMwV,IAAAA,2DAAkC,EACtC;gBACE,MAAMS,2BAA2B9f,kDAAoB,CAACC,GAAG,CACvDyf,2BACA3a,WACA,2DAA2D;8BAC3D,qBAACwF;oBACCC,mBAAmB4M,kBAAkB0H,iBAAiB;oBACtDrU,kBAAkBvS;oBAClBwS,gBAAgBA;oBAChBvL,yBAAyBA;oBACzBwL,4BAA4BA;oBAC5BtR,OAAOA;oBACPuR,QAAQ/O,IAAIW,UAAU,CAACoO,MAAM;oBAE/B;oBACE1G,QAAQsb,2BAA2Btb,MAAM;oBACzCxE,SAAS,CAACD,KAAcsgB;wBACtB,IACEf,IAAAA,6CAA2B,EAACvf,QAC5B+f,2BAA2Btb,MAAM,CAACgB,OAAO,EACzC;4BACA,MAAM8a,iBAAqC,AACzCD,UACAC,cAAc;4BAChB,IAAI,OAAOA,mBAAmB,UAAU;gCACtCC,IAAAA,2CAAyB,EACvB5jB,WACA2jB,gBACAL,mBACAJ;4BAEJ;4BACA;wBACF;wBAEA,OAAOrI,yBAAyBzX,KAAKsgB;oBACvC;oBACArG,WAAW,CAACthB;wBACVA,QAAQ0R,OAAO,CAAC,CAACvC,OAAOhK;4BACtB8Z,aAAa9Z,KAAKgK;wBACpB;oBACF;oBACAoS,kBAAkB1E;oBAClB2E,kBAAkB;wBAACtD;qBAAgB;gBACrC;gBAGF,gEAAgE;gBAChE,oEAAoE;gBACpE,kCAAkC;gBAClCkJ,2BAA2Btb,MAAM,CAACmY,gBAAgB,CAChD,SACA;oBACEoD,4BAA4Bha,KAAK;gBACnC,GACA;oBAAE4Y,MAAM;gBAAK;gBAGf,OAAOyB;YACT,GACA;gBACEN,2BAA2B/Z,KAAK;YAClC;YAGJ,MAAM,EAAElC,OAAO,EAAE2c,cAAc,EAAE,GAC/B,MAAMC,IAAAA,uCAAc,EAACN;YAEvB,0EAA0E;YAC1E,2EAA2E;YAC3E,kCAAkC;YAClC,IAAI,CAAChC,uBAAuB;gBAC1BuC,IAAAA,0CAAwB,EACtB/jB,WACA6jB,iBAAiBG,8BAAY,CAAC9G,KAAK,GAAG8G,8BAAY,CAACC,IAAI,EACvDX,mBACAzZ;YAEJ;YAEA,MAAM+S,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtDrD;gBACAR;gBACA8D,sBAAsBlC;gBACtBrC;gBACAa,iBAAiBA;YACnB;YAEA,MAAM3Z,aAAa,MAAMmlB,IAAAA,oCAAc,EAAC7J,kBAAkB8J,QAAQ;YAClE1e,SAAS1G,UAAU,GAAGA;YACtB0G,SAAS2e,WAAW,GAAG,MAAMC,mBAC3BtlB,YACA0K,2BACA/C,cACAjH;YAGF,IAAIwJ,iBAAiB;gBACnB,eAAe;gBACf,4FAA4F;gBAC5F,0FAA0F;gBAC1F,0FAA0F;gBAC1F,oCAAoC;gBACpC,IAAI6D,aAAa,MAAM;oBACrB,oBAAoB;oBACpBrH,SAASqH,SAAS,GAAG,MAAMwX,IAAAA,4CAA4B,EACrDxX,WACAqW,iBACI5G,uCAAuB,CAACC,KAAK,GAC7BD,uCAAuB,CAACgH,IAAI,EAChC9lB,qBACAwmB,iBACAhU;gBAEJ,OAAO;oBACL,oBAAoB;oBACpBxK,SAASqH,SAAS,GAAG,MAAMyX,IAAAA,4CAA4B,EACrDN,iBACAhU;gBAEJ;gBACAoK,kBAAkBqC,OAAO;gBACzB,OAAO;oBACLrI,iBAAiBuF;oBACjBlF,WAAWwF;oBACX9U,QAAQ,MAAMof,IAAAA,8CAAwB,EAAChe,SAAS;wBAC9C0V;wBACA1D;oBACF;oBACA1O,eAAe2a,IAAAA,sCAAoB,EACjCtb,uBACAqZ;oBAEF,0CAA0C;oBAC1CzY,qBAAqBN,0BAA0BnC,UAAU;oBACzD0C,iBAAiBP,0BAA0BlC,MAAM;oBACjD0C,gBAAgBlB,gBAAgBU,0BAA0BjC,KAAK;oBAC/D0C,eAAeT,0BAA0B/B,IAAI;oBAC7C3B,uBAAuB2Z,IAAAA,4CAA2B,EAACuE;gBACrD;YACF,OAAO;gBACL,cAAc;gBACd,mGAAmG;gBACnG,6EAA6E;gBAC7E,IAAI3kB,UAAU6W,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAI/B,8CAAqB,CAC7B,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAI6H,aAAazV;gBACjB,IAAIsG,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAMkP,SAAS,AACb/gB,QAAQ,oBACR+gB,MAAM;oBAER,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAM0I,gBAAgB,IAAIvE;oBAE1B,MAAMwE,eAAe,MAAM3I,OACzB,2DAA2D;kCAC3D,qBAACxO;wBACCC,mBAAmBiX;wBACnBhX,kBAAkBvS;wBAClBwS,gBAAgB,KAAO;wBACvBvL,yBAAyBA;wBACzBwL,4BAA4BA;wBAC5BtR,OAAOA;wBACPuR,QAAQ/O,IAAIW,UAAU,CAACoO,MAAM;wBAE/B6L,KAAKkL,KAAK,CAAClL,KAAKC,SAAS,CAAC7M,aAC1B;wBACE3F,QAAQ0d,IAAAA,kDAAgC;wBACxCliB,SAASwX;wBACT7d;oBACF;oBAGF,wGAAwG;oBACxG2f,aAAaL,IAAAA,kCAAY,EAACpV,SAASme;gBACrC;gBAEA,IAAIG;gBACJ,MAAMC,yBACJtnB,uBAAuBA,oBAAoB6W,IAAI,GAAG;gBACpD,IAAIyQ,wBAAwB;oBAC1B,kEAAkE;oBAClE,+DAA+D;oBAC/D,gDAAgD;oBAChD,EAAE;oBACF,sEAAsE;oBACtE,gBAAgB;oBAChB,EAAE;oBACF,iEAAiE;oBACjE,qEAAqE;oBACrE,4BAA4B;oBAC5B,EAAE;oBACF,mEAAmE;oBACnE,kDAAkD;oBAClD,sEAAsE;oBACtE,+DAA+D;oBAC/D,sEAAsE;oBACtE,MAAMC,yBACJ,MAAMC,IAAAA,mEAA0C,EAC9Cve,aAAarE,sBAAsB,CACjC,EAAE,EACFD,wBAAwBgB,aAAa,EACrC;wBACEvI;wBACA8H,SAASoX;oBACX;oBAGN+K,cAAc,MAAMI,IAAAA,qDAA+B,EAACjJ,YAAY;wBAC9DQ,mBAAmBd,IAAAA,kDAA+B,EAChDqJ,uBAAuBG,eAAe,IACtC7oB,OACAia;wBAEF2F;wBACA1D;wBACAwE,yBACEle,IAAIQ,SAAS,CAAC0d,uBAAuB,KAAK;wBAC5Czb,SAASzC,IAAIQ,SAAS,CAACiC,OAAO;oBAChC;gBACF,OAAO;oBACL,kEAAkE;oBAClEujB,cAAc,MAAMM,IAAAA,6CAAuB,EAACnJ,YAAY;wBACtDQ,mBAAmBd,IAAAA,kDAA+B,EAChDtB,kBAAkB8K,eAAe,IACjC7oB,OACAia;wBAEF2F;wBACA1D;wBACAwE,yBACEle,IAAIQ,SAAS,CAAC0d,uBAAuB,KAAK;wBAC5Czb,SAASzC,IAAIQ,SAAS,CAACiC,OAAO;oBAChC;gBACF;gBAEA,OAAO;oBACL8S,iBAAiBuF;oBACjBlF,WAAWwF;oBACX9U,QAAQ0f;oBACRhb,eAAe2a,IAAAA,sCAAoB,EACjCtb,uBACAqZ;oBAEF,0CAA0C;oBAC1CzY,qBAAqBN,0BAA0BnC,UAAU;oBACzD0C,iBAAiBP,0BAA0BlC,MAAM;oBACjD0C,gBAAgBlB,gBAAgBU,0BAA0BjC,KAAK;oBAC/D0C,eAAeT,0BAA0B/B,IAAI;oBAC7C3B,uBAAuB2Z,IAAAA,4CAA2B,EAACuE;gBACrD;YACF;QACF,OAAO,IAAIpb,aAAa9M,iBAAiB,EAAE;YACzC,uEAAuE;YACvE,IAAIsL,kBAAkB+B,IAAAA,4CAA0B,EAACN;YAEjD,MAAMjD,2BAA2BC,IAAAA,+CAA8B;YAC/D,MAAMuf,4BAA6CrB,iBAAiB;gBAClE9lB,MAAM;gBACN+I,OAAO;gBACPtB;gBACAlI;gBACAgJ;gBACAY;gBACAC,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;gBAC5B7B;YACF;YACA,MAAMyV,aAAa,MAAMrY,kDAAoB,CAACC,GAAG,CAC/CmiB,2BACApa,eACAlL,MACAjB,KACA2B,IAAIlC,UAAU,KAAK;YAErB,MAAM8b,oBAAqBwJ,6BACzB,MAAMoB,IAAAA,mEAA0C,EAC9ChiB,kDAAoB,CAACC,GAAG,CACtBmiB,2BACA3e,aAAarE,sBAAsB,EACnC,4CAA4C;YAC5CiZ,YACAlZ,wBAAwBgB,aAAa,EACrC;gBACEvI;gBACA8H,SAASoX;YACX;YAIN,MAAMuL,oBAAoC;gBACxCpnB,MAAM;gBACN+I,OAAO;gBACPtB;gBACAlI;gBACAgJ;gBACAY;gBACAC,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;gBAC5B7B;YACF;YACA,MAAMmC,YAAY,AAChB/M,QAAQ,oBACR+M,SAAS;YACX,MAAM,EAAExB,SAASsc,kBAAkB,EAAEhW,SAAS,EAAE,GAC9C,MAAM7J,kDAAoB,CAACC,GAAG,CAC5BoiB,mBACAtd,WACA,2DAA2D;0BAC3D,qBAACwF;gBACCC,mBAAmB4M,kBAAkB0H,iBAAiB;gBACtDrU,kBAAkBvS;gBAClBwS,gBAAgBA;gBAChBvL,yBAAyBA;gBACzBwL,4BAA4BA;gBAC5BtR,OAAOA;gBACPuR,QAAQ/O,IAAIW,UAAU,CAACoO,MAAM;gBAE/B;gBACElL,SAASwX;gBACTwC,WAAW,CAACthB;oBACVA,QAAQ0R,OAAO,CAAC,CAACvC,OAAOhK;wBACtB8Z,aAAa9Z,KAAKgK;oBACpB;gBACF;gBACAoS,kBAAkB1E;gBAClB2E,kBAAkB;oBAACtD;iBAAgB;YACrC;YAEJ,MAAM2C,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtDrD;gBACAR;gBACA8D,sBAAsBlC;gBACtBrC;gBACAa,iBAAiBA;YACnB;YAEA,+FAA+F;YAC/F,8FAA8F;YAC9F,6EAA6E;YAC7E,MAAM3Z,aAAa,MAAMmlB,IAAAA,oCAAc,EAAC7J,kBAAkB8J,QAAQ;YAElE,IAAIP,+BAA+BtkB,YAAY;gBAC7CmG,SAAS1G,UAAU,GAAGA;gBACtB0G,SAAS2e,WAAW,GAAG,MAAMC,mBAC3BtlB,YACAumB,mBACA5e,cACAjH;YAEJ;YAEA,MAAM,EAAE+G,OAAO,EAAE2c,cAAc,EAAE,GAC/B,MAAMC,IAAAA,uCAAc,EAACN;YAEvB;;;;;;;;;;;;;OAaC,GACD,oEAAoE;YACpE,IAAI/O,IAAAA,qCAAmB,EAAC1M,gBAAgBke,eAAe,GAAG;gBACxD,IAAIzY,aAAa,MAAM;oBACrB,qBAAqB;oBACrBrH,SAASqH,SAAS,GAAG,MAAMwX,IAAAA,4CAA4B,EACrDxX,WACAqW,iBACI5G,uCAAuB,CAACC,KAAK,GAC7BD,uCAAuB,CAACgH,IAAI,EAChC9lB,qBACAoI,0BACAoK;gBAEJ,OAAO;oBACL,qBAAqB;oBACrBxK,SAASqH,SAAS,GAAG,MAAMyX,IAAAA,4CAA4B,EACrD1e,0BACAoK;gBAEJ;gBACA,mGAAmG;gBACnG,8GAA8G;gBAC9G,uHAAuH;gBACvH,sDAAsD;gBACtDoK,kBAAkBqC,OAAO;gBACzB,OAAO;oBACLrI,iBAAiBuF;oBACjBlF,WAAWwF;oBACX9U,QAAQ,MAAMof,IAAAA,8CAAwB,EAAChe,SAAS;wBAC9C0V;wBACA1D;oBACF;oBACA1O,eAAezC,gBAAgBke,eAAe;oBAC9C,0CAA0C;oBAC1Cxb,qBAAqBsb,0BAA0B/d,UAAU;oBACzD0C,iBAAiBqb,0BAA0B9d,MAAM;oBACjD0C,gBAAgBlB,gBAAgBsc,0BAA0B7d,KAAK;oBAC/D0C,eAAemb,0BAA0B3d,IAAI;gBAC/C;YACF,OAAO,IAAIjK,uBAAuBA,oBAAoB6W,IAAI,GAAG,GAAG;gBAC9D,+BAA+B;gBAC/B7O,SAASqH,SAAS,GAAG,MAAMyX,IAAAA,4CAA4B,EACrD1e,0BACAoK;gBAGF,OAAO;oBACLoE,iBAAiBuF;oBACjBlF,WAAWwF;oBACX9U,QAAQ,MAAMof,IAAAA,8CAAwB,EAAChe,SAAS;wBAC9C0V;wBACA1D;oBACF;oBACA1O,eAAezC,gBAAgBke,eAAe;oBAC9C,0CAA0C;oBAC1Cxb,qBAAqBsb,0BAA0B/d,UAAU;oBACzD0C,iBAAiBqb,0BAA0B9d,MAAM;oBACjD0C,gBAAgBlB,gBAAgBsc,0BAA0B7d,KAAK;oBAC/D0C,eAAemb,0BAA0B3d,IAAI;gBAC/C;YACF,OAAO;gBACL,cAAc;gBACd,8GAA8G;gBAC9G,IAAIpI,UAAU6W,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAI/B,8CAAqB,CAC7B,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAI6H,aAAazV;gBACjB,IAAIsG,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAMkP,SAAS,AACb/gB,QAAQ,oBACR+gB,MAAM;oBAER,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAM0I,gBAAgB,IAAIvE;oBAE1B,MAAMwE,eAAe,MAAM3I,OACzB,2DAA2D;kCAC3D,qBAACxO;wBACCC,mBAAmBiX;wBACnBhX,kBAAkBvS;wBAClBwS,gBAAgB,KAAO;wBACvBvL,yBAAyBA;wBACzBwL,4BAA4BA;wBAC5BtR,OAAOA;wBACPuR,QAAQ/O,IAAIW,UAAU,CAACoO,MAAM;wBAE/B6L,KAAKkL,KAAK,CAAClL,KAAKC,SAAS,CAAC7M,aAC1B;wBACE3F,QAAQ0d,IAAAA,kDAAgC;wBACxCliB,SAASwX;wBACT7d;oBACF;oBAGF,wGAAwG;oBACxG2f,aAAaL,IAAAA,kCAAY,EAACpV,SAASme;gBACrC;gBAEA,OAAO;oBACLtQ,iBAAiBuF;oBACjBlF,WAAWwF;oBACX9U,QAAQ,MAAMggB,IAAAA,6CAAuB,EAACnJ,YAAY;wBAChDQ,mBAAmBd,IAAAA,kDAA+B,EAChDtB,kBAAkB8K,eAAe,IACjC7oB,OACAia;wBAEF2F;wBACA1D;wBACAwE,yBACEle,IAAIQ,SAAS,CAAC0d,uBAAuB,KAAK;wBAC5Czb,SAASzC,IAAIQ,SAAS,CAACiC,OAAO;oBAChC;oBACAuI,eAAezC,gBAAgBke,eAAe;oBAC9C,0CAA0C;oBAC1Cxb,qBAAqBsb,0BAA0B/d,UAAU;oBACzD0C,iBAAiBqb,0BAA0B9d,MAAM;oBACjD0C,gBAAgBlB,gBAAgBsc,0BAA0B7d,KAAK;oBAC/D0C,eAAemb,0BAA0B3d,IAAI;gBAC/C;YACF;QACF,OAAO;YACL,MAAM8d,uBAAwCxB,iBAAiB;gBAC7D9lB,MAAM;gBACN+I,OAAO;gBACPtB;gBACAc;gBACAa,YAAYG,0BAAc;gBAC1BF,QAAQE,0BAAc;gBACtBD,OAAOC,0BAAc;gBACrBC,MAAM;uBAAIjB,aAAaiB,IAAI;iBAAC;YAC9B;YACA,uFAAuF;YACvF,yEAAyE;YACzE,MAAM4T,aAAa,MAAMrY,kDAAoB,CAACC,GAAG,CAC/CsiB,sBACAva,eACAlL,MACAjB,KACA2B,IAAIlC,UAAU,KAAK;YAGrB,MAAM8b,oBAAqBwJ,6BACzB,MAAMoB,IAAAA,mEAA0C,EAC9ChiB,kDAAoB,CAACC,GAAG,CACtBsiB,sBACA9e,aAAarE,sBAAsB,EACnCiZ,YACAlZ,wBAAwBgB,aAAa,EACrC;gBACEvI;gBACA8H,SAASoX;YACX;YAIN,MAAM1X,yBAAyB,AAC7BpH,QAAQ,oBACRoH,sBAAsB;YACxB,MAAM4Z,aAAa,MAAMhZ,kDAAoB,CAACC,GAAG,CAC/CsiB,sBACAnjB,wBACA,2DAA2D;0BAC3D,qBAACmL;gBACCC,mBAAmB4M,kBAAkB0H,iBAAiB;gBACtDrU,kBAAkBvS;gBAClBwS,gBAAgBA;gBAChBvL,yBAAyBA;gBACzBwL,4BAA4BA;gBAC5BtR,OAAOA;gBACPuR,QAAQ/O,IAAIW,UAAU,CAACoO,MAAM;gBAE/B;gBACElL,SAASwX;gBACT7d;gBACAugB,kBAAkB;oBAACtD;iBAAgB;YACrC;YAGF,IAAIqK,+BAA+BtkB,YAAY;gBAC7C,MAAMP,aAAa,MAAMmlB,IAAAA,oCAAc,EAAC7J,kBAAkB8J,QAAQ;gBAClE1e,SAAS1G,UAAU,GAAGA;gBACtB0G,SAAS2e,WAAW,GAAG,MAAMC,mBAC3BtlB,YACAymB,sBACA9e,cACAjH;YAEJ;YAEA,MAAMyc,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtDrD;gBACAR;gBACA8D,sBAAsBlC;gBACtBrC;gBACAa,iBAAiBA;YACnB;YACA,OAAO;gBACLrE,iBAAiBuF;gBACjBlF,WAAWwF;gBACX9U,QAAQ,MAAM2X,IAAAA,wCAAkB,EAACd,YAAY;oBAC3CQ,mBAAmBd,IAAAA,kDAA+B,EAChDtB,kBAAkB8K,eAAe,IACjC7oB,OACAia;oBAEF9U,oBAAoB;oBACpBub,yBACEle,IAAIQ,SAAS,CAAC0d,uBAAuB,KAAK;oBAC5Czb,SAASzC,IAAIQ,SAAS,CAACiC,OAAO;oBAC9B2a;oBACA1D;gBACF;gBACA,0CAA0C;gBAC1CzO,qBAAqByb,qBAAqBle,UAAU;gBACpD0C,iBAAiBwb,qBAAqBje,MAAM;gBAC5C0C,gBAAgBlB,gBAAgByc,qBAAqBhe,KAAK;gBAC1D0C,eAAesb,qBAAqB9d,IAAI;YAC1C;QACF;IACF,EAAE,OAAOhF,KAAK;QACZ,IACEwa,IAAAA,gDAAuB,EAACxa,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAI4K,OAAO,KAAK,YACvB5K,IAAI4K,OAAO,CAACxB,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMpJ;QACR;QAEA,uEAAuE;QACvE,mEAAmE;QACnE,IAAI+iB,IAAAA,wCAAoB,EAAC/iB,MAAM;YAC7B,MAAMA;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAMya,qBAAqBC,IAAAA,iCAAmB,EAAC1a;QAC/C,IAAIya,oBAAoB;YACtB,MAAM5P,QAAQ8P,IAAAA,8CAA2B,EAAC3a;YAC1C4a,IAAAA,UAAK,EACH,GAAG5a,IAAI6a,MAAM,CAAC,mDAAmD,EAAEjf,SAAS,kFAAkF,EAAEiP,OAAO;YAGzK,MAAM7K;QACR;QAEA,yEAAyE;QACzE,mDAAmD;QACnD,IAAImhB,+BAA+B,MAAM;YACvC,MAAMnhB;QACR;QAEA,IAAI6I;QAEJ,IAAIiS,IAAAA,6CAAyB,EAAC9a,MAAM;YAClCjC,IAAIlC,UAAU,GAAGkf,IAAAA,+CAA2B,EAAC/a;YAC7C+C,SAASlH,UAAU,GAAGkC,IAAIlC,UAAU;YACpCgN,YAAYmS,IAAAA,sDAAkC,EAACjd,IAAIlC,UAAU;QAC/D,OAAO,IAAIof,IAAAA,8BAAe,EAACjb,MAAM;YAC/B6I,YAAY;YACZ9K,IAAIlC,UAAU,GAAGqf,IAAAA,wCAA8B,EAAClb;YAChD+C,SAASlH,UAAU,GAAGkC,IAAIlC,UAAU;YAEpC,MAAMsf,cAAcC,IAAAA,4BAAa,EAACC,IAAAA,iCAAuB,EAACrb,MAAMmV;YAEhEvR,UAAU,YAAYuX;QACxB,OAAO,IAAI,CAACV,oBAAoB;YAC9B1c,IAAIlC,UAAU,GAAG;YACjBkH,SAASlH,UAAU,GAAGkC,IAAIlC,UAAU;QACtC;QAEA,MAAM,CAAC2f,qBAAqBC,qBAAqB,GAAG3E,IAAAA,mCAAkB,EACpE1B,eACA/H,aACAiI,aACAI,8BACAgB,IAAAA,wCAAmB,EAACta,KAAK,QACzBxC,OACA;QAGF,MAAMkpB,uBAAwCxB,iBAAiB;YAC7D9lB,MAAM;YACN+I,OAAO;YACPtB;YACAc,cAAcA;YACda,YACE,QAAO0c,kCAAAA,eAAgB1c,UAAU,MAAK,cAClC0c,eAAe1c,UAAU,GACzBG,0BAAc;YACpBF,QACE,QAAOyc,kCAAAA,eAAgBzc,MAAM,MAAK,cAC9Byc,eAAezc,MAAM,GACrBE,0BAAc;YACpBD,OACE,QAAOwc,kCAAAA,eAAgBxc,KAAK,MAAK,cAC7Bwc,eAAexc,KAAK,GACpBC,0BAAc;YACpBC,MAAM;mBAAKsc,CAAAA,kCAAAA,eAAgBtc,IAAI,KAAIjB,aAAaiB,IAAI;aAAE;QACxD;QACA,MAAM0W,kBAAkB,MAAMnb,kDAAoB,CAACC,GAAG,CACpDsiB,sBACAvY,oBACAlN,MACAjB,KACA8a,0BAA0ByE,GAAG,CAAC,AAAC3b,IAAYuF,MAAM,IAAI9M,YAAYuH,KACjE6I;QAGF,MAAM+S,oBAAoBrb,kDAAoB,CAACC,GAAG,CAChDsiB,sBACA9e,aAAarE,sBAAsB,EACnC+b,iBACAhc,wBAAwBgB,aAAa,EACrC;YACEvI;YACA8H,SAASoX;QACX;QAGF,IAAI;YACF,6EAA6E;YAC7E,wFAAwF;YACxF,uCAAuC;YACvC,MAAMwE,aAAa,MAAMtb,kDAAoB,CAACC,GAAG,CAC/CsiB,sBACAhH,+CAAyB,EACzB;gBACEC,gBACExjB,QAAQ;gBACVyjB,SACE,2DAA2D;8BAC3D,qBAACvP;oBACC1B,mBAAmB6Q;oBACnB5Q,kBAAkBvS;oBAClByS,4BAA4BA;oBAC5BD,gBAAgBuQ;oBAChB9b,yBAAyBA;oBACzB9F,OAAOA;oBACPuR,QAAQ/O,IAAIW,UAAU,CAACoO,MAAM;;gBAGjC8Q,eAAe;oBACbriB;oBACA,wCAAwC;oBACxCugB,kBAAkB;wBAACsB;qBAAqB;oBACxC5H;gBACF;YACF;YAGF,IAAIqN,+BAA+BtkB,YAAY;gBAC7C,MAAMP,aAAa,MAAMmlB,IAAAA,oCAAc,EACrCL,2BAA2BM,QAAQ;gBAErC1e,SAAS1G,UAAU,GAAGA;gBACtB0G,SAAS2e,WAAW,GAAG,MAAMC,mBAC3BtlB,YACAymB,sBACA9e,cACAjH;YAEJ;YAEA,oEAAoE;YACpE,gEAAgE;YAChE,MAAMimB,eAAe7B,2BAA2BsB,eAAe;YAE/D,OAAO;gBACL,kEAAkE;gBAClE,8BAA8B;gBAC9B9Q,iBAAiBuF;gBACjBlF,WAAWwF;gBACX9U,QAAQ,MAAM2X,IAAAA,wCAAkB,EAACwB,YAAY;oBAC3C9B,mBAAmBd,IAAAA,kDAA+B,EAChD+J,cACAppB,OACAia;oBAEF9U,oBAAoB;oBACpBub,yBACEle,IAAIQ,SAAS,CAAC0d,uBAAuB,KAAK;oBAC5Czb,SAASzC,IAAIQ,SAAS,CAACiC,OAAO;oBAC9B2a,uBAAuBC,IAAAA,oDAAyB,EAAC;wBAC/CrD;wBACAR;wBACA8D,sBAAsB,EAAE;wBACxBvE;wBACAa,iBAAiBA;oBACnB;oBACAF;oBACAyE,oBAAoB3a;gBACtB;gBACAwH,eAAe;gBACfC,qBACEia,mBAAmB,OAAOA,eAAe1c,UAAU,GAAGG,0BAAc;gBACtEuC,iBACEga,mBAAmB,OAAOA,eAAezc,MAAM,GAAGE,0BAAc;gBAClEwC,gBAAgBlB,gBACdib,mBAAmB,OAAOA,eAAexc,KAAK,GAAGC,0BAAc;gBAEjEyC,eAAe8Z,mBAAmB,OAAOA,eAAetc,IAAI,GAAG;YACjE;QACF,EAAE,OAAOkX,UAAe;YACtB,IACE9jB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBwiB,IAAAA,6CAAyB,EAACoB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1B5jB,QAAQ;gBACV4jB;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,MAAMxS,uBAAuB,OAC3BrM,MACAjB;IAKA,MAAM,EACJ6mB,SAAS,EAAE,gBAAgBC,iBAAiB,EAAE,EAC/C,GAAGC,IAAAA,gCAAe,EAAC9lB;IAEpB,MAAM,EACJf,cAAc,EAAEX,aAAa,EAAE,EAChC,GAAGS;IACJ,MAAMgnB,uBACJhnB,IAAIE,YAAY,CAACiN,WAAW;IAC9B,IAAIE;IACJ,IAAIyZ,mBAAmB;QACrB,MAAM,GAAG1Z,OAAO,GAAG,MAAM6Z,IAAAA,gEAA+B,EAAC;YACvDjnB;YACAknB,UAAUJ,iBAAiB,CAAC,EAAE;YAC9BK,cAAcL,iBAAiB,CAAC,EAAE;YAClCllB,aAAa,IAAIC;YACjBC,YAAY,IAAID;QAClB;QACAwL,oBAAoBD;IACtB;IACA,IAAIpN,IAAIW,UAAU,CAAC6C,GAAG,EAAE;QACtB,MAAM4jB,MACJ,AAACprB,CAAAA,QAAQC,GAAG,CAACoW,YAAY,KAAK,SAC1BrW,QAAQC,GAAG,CAACorB,uBAAuB,GACnCrnB,IAAIW,UAAU,CAACymB,GAAG,AAAD,KAAM;QAE7B,MAAME,wBAAwBC,IAAAA,gDAA2B,EACvDH,KACAN,qCAAAA,iBAAmB,CAAC,EAAE;QAExB,IAAIQ,uBAAuB;YACzB,MAAME,kBAAkBxnB,IAAIE,YAAY,CAACsnB,eAAe;YACxDna,oBACE,2EAA2E;YAC3E,iEAAiE;YACjE9N,cACEioB,iBACA;gBACE9lB,KAAK;gBACLtC,MAAM;gBACNI,UAAU8nB;YACZ,GACAja;QAEN;IACF;IAEA,OAAO;QACLF,aAAa6Z;QACb5Z,QAAQC;IACV;AACF;AAEA,SAASnD,sBAAsBH,YAAgC;IAC7D,OAAO,CAACrB;YAECqB;eADPrB,UAAUC,0BAAc,IACxB,SAAOoB,2BAAAA,aAAa0d,UAAU,qBAAvB1d,yBAAyB2d,MAAM,MAAK,WACvC3d,aAAa0d,UAAU,CAACC,MAAM,GAC9Bhf;;AACR;AAEA,eAAe6c,mBACboC,kBAA0B,EAC1BzC,cAA8B,EAC9Btd,YAA2B,EAC3BjH,UAAsB;IAEtB,4BAA4B;IAC5B,EAAE;IACF,yEAAyE;IACzE,oEAAoE;IACpE,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,wCAAwC;IACxC,EAAE;IACF,oEAAoE;IACpE,4EAA4E;IAC5E,iDAAiD;IAEjD,MAAM2C,0BAA0B3C,WAAW2C,uBAAuB;IAClE,IACE,CAACA,2BACD,yEAAyE;IACzE,mBAAmB;IACnB,EAAE;IACF,wEAAwE;IACxE,2EAA2E;IAC3E,2EAA2E;IAC3E,mCAAmC;IACnC3C,WAAWoJ,YAAY,CAAC6d,kBAAkB,KAAK,MAC/C;QACA;IACF;IAEA,wEAAwE;IACxE,0DAA0D;IAC1D,MAAMC,gBAAgB7rB,QAAQC,GAAG,CAACoW,YAAY,KAAK;IACnD,MAAMyV,yBAAyB;QAC7B,2FAA2F;QAC3F,yFAAyF;QACzF,+CAA+C;QAC/CC,eAAe;QACfC,WAAWH,gBACPvkB,wBAAwB2kB,oBAAoB,GAC5C3kB,wBAAwB4kB,gBAAgB;QAC5CzU,iBAAiB0U,IAAAA,mCAAkB;IACrC;IAEA,MAAMle,kBAAkBC,sBAAsBvJ,WAAWoJ,YAAY;IACrE,MAAMqe,YAAYne,gBAAgBib,eAAexc,KAAK;IACtD,OAAO,MAAMd,aAAa2d,kBAAkB,CAC1C5kB,WAAWwQ,eAAe,EAC1BwW,oBACAS,WACA9kB,wBAAwBgB,aAAa,EACrCwjB;AAEJ;AAEA,SAAS5hB,uBACPvF,UAAsB,EACtB0C,YAA0B;IAE1B,OACErH,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,CAAC,CAACyE,WAAW6C,GAAG,IAChBH,aAAa9G,OAAO,CAAC0lB,GAAG,CAAC,qBAAqB;AAElD;AAEA,SAAS7b,yBAAyB,EAAEC,KAAK,EAAqB;IAC5DgiB,IAAAA,kBAAQ,EACN,CAAC,MAAM,EAAEhiB,MAAM,oQAAoQ,CAAC;IAEtR,OAAO;AACT", "ignoreList": [0]}