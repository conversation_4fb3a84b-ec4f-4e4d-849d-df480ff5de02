import type { FlightRouterState } from '../../shared/lib/app-router-types';
export declare function parseAndValidateFlightRouterState(stateHeader: string | string[]): FlightRouterState;
export declare function parseAndValidateFlightRouterState(stateHeader: undefined): undefined;
export declare function parseAndValidateFlightRouterState(stateHeader: string | string[] | undefined): FlightRouterState | undefined;
