{"version": 3, "sources": ["../../../src/server/app-render/make-get-server-inserted-html.tsx"], "sourcesContent": ["/* eslint-disable @next/internal/no-ambiguous-jsx -- whole module is used in React Client */\nimport React, { type JSX } from 'react'\nimport { isHTTPAccessFallbackError } from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { renderToReadableStream } from 'react-dom/server'\nimport { streamToString } from '../stream-utils/node-web-streams-helper'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport type { ClientTraceDataEntry } from '../lib/trace/tracer'\n\nexport function makeGetServerInsertedHTML({\n  polyfills,\n  renderServerInsertedHTML,\n  serverCapturedErrors,\n  tracingMetadata,\n  basePath,\n}: {\n  polyfills: JSX.IntrinsicElements['script'][]\n  renderServerInsertedHTML: () => React.ReactNode\n  tracingMetadata: ClientTraceDataEntry[] | undefined\n  serverCapturedErrors: Array<unknown>\n  basePath: string\n}) {\n  let flushedErrorMetaTagsUntilIndex = 0\n\n  // These only need to be rendered once, they'll be set to empty arrays once flushed.\n  let polyfillTags = polyfills.map((polyfill) => {\n    return <script key={polyfill.src} {...polyfill} />\n  })\n  let traceMetaTags = (tracingMetadata || []).map(({ key, value }, index) => (\n    <meta key={`next-trace-data-${index}`} name={key} content={value} />\n  ))\n\n  return async function getServerInsertedHTML() {\n    // Loop through all the errors that have been captured but not yet\n    // flushed.\n    const errorMetaTags = []\n    while (flushedErrorMetaTagsUntilIndex < serverCapturedErrors.length) {\n      const error = serverCapturedErrors[flushedErrorMetaTagsUntilIndex]\n      flushedErrorMetaTagsUntilIndex++\n\n      if (isHTTPAccessFallbackError(error)) {\n        errorMetaTags.push(\n          <meta name=\"robots\" content=\"noindex\" key={error.digest} />,\n          process.env.NODE_ENV === 'development' ? (\n            <meta name=\"next-error\" content=\"not-found\" key=\"next-error\" />\n          ) : null\n        )\n      } else if (isRedirectError(error)) {\n        const redirectUrl = addPathPrefix(\n          getURLFromRedirectError(error),\n          basePath\n        )\n        const statusCode = getRedirectStatusCodeFromError(error)\n        const isPermanent =\n          statusCode === RedirectStatusCode.PermanentRedirect ? true : false\n        if (redirectUrl) {\n          errorMetaTags.push(\n            <meta\n              id=\"__next-page-redirect\"\n              httpEquiv=\"refresh\"\n              content={`${isPermanent ? 0 : 1};url=${redirectUrl}`}\n              key={error.digest}\n            />\n          )\n        }\n      }\n    }\n\n    const serverInsertedHTML = renderServerInsertedHTML()\n\n    // Skip React rendering if we know the content is empty.\n    if (\n      polyfillTags.length === 0 &&\n      traceMetaTags.length === 0 &&\n      errorMetaTags.length === 0 &&\n      Array.isArray(serverInsertedHTML) &&\n      serverInsertedHTML.length === 0\n    ) {\n      return ''\n    }\n\n    const stream = await renderToReadableStream(\n      <>\n        {polyfillTags}\n        {serverInsertedHTML}\n        {traceMetaTags}\n        {errorMetaTags}\n      </>,\n      {\n        // Larger chunk because this isn't sent over the network.\n        // Let's set it to 1MB.\n        progressiveChunkSize: 1024 * 1024,\n      }\n    )\n\n    // The polyfills and trace metadata have been flushed, so they don't need to be rendered again\n    polyfillTags = []\n    traceMetaTags = []\n\n    // There's no need to wait for the stream to be ready\n    // e.g. calling `await stream.allReady` because `streamToString` will\n    // wait and decode the stream progressively with better parallelism.\n    return streamToString(stream)\n  }\n}\n"], "names": ["makeGetServerInsertedHTML", "polyfills", "renderServerInsertedHTML", "serverCapturedErrors", "tracingMetadata", "basePath", "flushedErrorMetaTagsUntilIndex", "polyfillTags", "map", "polyfill", "script", "src", "traceMetaTags", "key", "value", "index", "meta", "name", "content", "getServerInsertedHTML", "errorMetaTags", "length", "error", "isHTTPAccessFallbackError", "push", "digest", "process", "env", "NODE_ENV", "isRedirectError", "redirectUrl", "addPathPrefix", "getURLFromRedirectError", "statusCode", "getRedirectStatusCodeFromError", "isPermanent", "RedirectStatusCode", "PermanentRedirect", "id", "httpEquiv", "serverInsertedHTML", "Array", "isArray", "stream", "renderToReadableStream", "progressiveChunkSize", "streamToString"], "mappings": "AAAA,0FAA0F;;;;+BAc1EA;;;eAAAA;;;;8DAbgB;oCACU;0BAInC;+BACyB;wBACO;sCACR;oCACI;+BACL;;;;;;AAGvB,SAASA,0BAA0B,EACxCC,SAAS,EACTC,wBAAwB,EACxBC,oBAAoB,EACpBC,eAAe,EACfC,QAAQ,EAOT;IACC,IAAIC,iCAAiC;IAErC,oFAAoF;IACpF,IAAIC,eAAeN,UAAUO,GAAG,CAAC,CAACC;QAChC,qBAAO,qBAACC;YAA2B,GAAGD,QAAQ;WAA1BA,SAASE,GAAG;IAClC;IACA,IAAIC,gBAAgB,AAACR,CAAAA,mBAAmB,EAAE,AAAD,EAAGI,GAAG,CAAC,CAAC,EAAEK,GAAG,EAAEC,KAAK,EAAE,EAAEC,sBAC/D,qBAACC;YAAsCC,MAAMJ;YAAKK,SAASJ;WAAhD,CAAC,gBAAgB,EAAEC,OAAO;IAGvC,OAAO,eAAeI;QACpB,kEAAkE;QAClE,WAAW;QACX,MAAMC,gBAAgB,EAAE;QACxB,MAAOd,iCAAiCH,qBAAqBkB,MAAM,CAAE;YACnE,MAAMC,QAAQnB,oBAAoB,CAACG,+BAA+B;YAClEA;YAEA,IAAIiB,IAAAA,6CAAyB,EAACD,QAAQ;gBACpCF,cAAcI,IAAI,eAChB,qBAACR;oBAAKC,MAAK;oBAASC,SAAQ;mBAAeI,MAAMG,MAAM,GACvDC,QAAQC,GAAG,CAACC,QAAQ,KAAK,8BACvB,qBAACZ;oBAAKC,MAAK;oBAAaC,SAAQ;mBAAgB,gBAC9C;YAER,OAAO,IAAIW,IAAAA,8BAAe,EAACP,QAAQ;gBACjC,MAAMQ,cAAcC,IAAAA,4BAAa,EAC/BC,IAAAA,iCAAuB,EAACV,QACxBjB;gBAEF,MAAM4B,aAAaC,IAAAA,wCAA8B,EAACZ;gBAClD,MAAMa,cACJF,eAAeG,sCAAkB,CAACC,iBAAiB,GAAG,OAAO;gBAC/D,IAAIP,aAAa;oBACfV,cAAcI,IAAI,eAChB,qBAACR;wBACCsB,IAAG;wBACHC,WAAU;wBACVrB,SAAS,GAAGiB,cAAc,IAAI,EAAE,KAAK,EAAEL,aAAa;uBAC/CR,MAAMG,MAAM;gBAGvB;YACF;QACF;QAEA,MAAMe,qBAAqBtC;QAE3B,wDAAwD;QACxD,IACEK,aAAac,MAAM,KAAK,KACxBT,cAAcS,MAAM,KAAK,KACzBD,cAAcC,MAAM,KAAK,KACzBoB,MAAMC,OAAO,CAACF,uBACdA,mBAAmBnB,MAAM,KAAK,GAC9B;YACA,OAAO;QACT;QAEA,MAAMsB,SAAS,MAAMC,IAAAA,8BAAsB,gBACzC;;gBACGrC;gBACAiC;gBACA5B;gBACAQ;;YAEH;YACE,yDAAyD;YACzD,uBAAuB;YACvByB,sBAAsB,OAAO;QAC/B;QAGF,8FAA8F;QAC9FtC,eAAe,EAAE;QACjBK,gBAAgB,EAAE;QAElB,qDAAqD;QACrD,qEAAqE;QACrE,oEAAoE;QACpE,OAAOkC,IAAAA,oCAAc,EAACH;IACxB;AACF", "ignoreList": [0]}