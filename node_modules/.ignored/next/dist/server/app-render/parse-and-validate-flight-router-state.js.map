{"version": 3, "sources": ["../../../src/server/app-render/parse-and-validate-flight-router-state.tsx"], "sourcesContent": ["import type { FlightRouterState } from '../../shared/lib/app-router-types'\nimport { flightRouterStateSchema } from './types'\nimport { assert } from 'next/dist/compiled/superstruct'\n\nexport function parseAndValidateFlightRouterState(\n  stateHeader: string | string[]\n): FlightRouterState\nexport function parseAndValidateFlightRouterState(\n  stateHeader: undefined\n): undefined\nexport function parseAndValidateFlightRouterState(\n  stateHeader: string | string[] | undefined\n): FlightRouterState | undefined\nexport function parseAndValidateFlightRouterState(\n  stateHeader: string | string[] | undefined\n): FlightRouterState | undefined {\n  if (typeof stateHeader === 'undefined') {\n    return undefined\n  }\n  if (Array.isArray(stateHeader)) {\n    throw new Error(\n      'Multiple router state headers were sent. This is not allowed.'\n    )\n  }\n\n  // We limit the size of the router state header to ~40kb. This is to prevent\n  // a malicious user from sending a very large header and slowing down the\n  // resolving of the router state.\n  // This is around 2,000 nested or parallel route segment states:\n  // '{\"children\":[\"\",{}]}'.length === 20.\n  if (stateHeader.length > 20 * 2000) {\n    throw new Error('The router state header was too large.')\n  }\n\n  try {\n    const state = JSON.parse(decodeURIComponent(stateHeader))\n    assert(state, flightRouterStateSchema)\n    return state\n  } catch {\n    throw new Error('The router state header was sent but could not be parsed.')\n  }\n}\n"], "names": ["parseAndValidateFlightRouterState", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "Array", "isArray", "Error", "length", "state", "JSON", "parse", "decodeURIComponent", "assert", "flightRouterStateSchema"], "mappings": ";;;;+BAagBA;;;eAAAA;;;uBAZwB;6BACjB;AAWhB,SAASA,kCACdC,WAA0C;IAE1C,IAAI,OAAOA,gBAAgB,aAAa;QACtC,OAAOC;IACT;IACA,IAAIC,MAAMC,OAAO,CAACH,cAAc;QAC9B,MAAM,qBAEL,CAFK,IAAII,MACR,kEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,4EAA4E;IAC5E,yEAAyE;IACzE,iCAAiC;IACjC,gEAAgE;IAChE,wCAAwC;IACxC,IAAIJ,YAAYK,MAAM,GAAG,KAAK,MAAM;QAClC,MAAM,qBAAmD,CAAnD,IAAID,MAAM,2CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkD;IAC1D;IAEA,IAAI;QACF,MAAME,QAAQC,KAAKC,KAAK,CAACC,mBAAmBT;QAC5CU,IAAAA,mBAAM,EAACJ,OAAOK,8BAAuB;QACrC,OAAOL;IACT,EAAE,OAAM;QACN,MAAM,qBAAsE,CAAtE,IAAIF,MAAM,8DAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAqE;IAC7E;AACF", "ignoreList": [0]}