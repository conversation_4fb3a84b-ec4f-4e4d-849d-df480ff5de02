{"version": 3, "sources": ["../../../src/server/app-render/app-render-render-utils.ts"], "sourcesContent": ["import { InvariantError } from '../../shared/lib/invariant-error'\n\n/**\n * This is a utility function to make scheduling sequential tasks that run back to back easier.\n * We schedule on the same queue (setTimeout) at the same time to ensure no other events can sneak in between.\n */\nexport function scheduleInSequentialTasks<R>(\n  render: () => R | Promise<R>,\n  followup: () => void\n): Promise<R> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    throw new InvariantError(\n      '`scheduleInSequentialTasks` should not be called in edge runtime.'\n    )\n  } else {\n    return new Promise((resolve, reject) => {\n      let pendingResult: R | Promise<R>\n      setTimeout(() => {\n        try {\n          pendingResult = render()\n        } catch (err) {\n          reject(err)\n        }\n      }, 0)\n      setTimeout(() => {\n        followup()\n        resolve(pendingResult)\n      }, 0)\n    })\n  }\n}\n\n/**\n * This is a utility function to make scheduling sequential tasks that run back to back easier.\n * We schedule on the same queue (setTimeout) at the same time to ensure no other events can sneak in between.\n * The function that runs in the second task gets access to the first tasks's result.\n */\nexport function pipelineInSequentialTasks<A, B, C>(\n  one: () => A,\n  two: (a: A) => B,\n  three: (b: B) => C | Promise<C>\n): Promise<C> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    throw new InvariantError(\n      '`pipelineInSequentialTasks` should not be called in edge runtime.'\n    )\n  } else {\n    return new Promise((resolve, reject) => {\n      let oneResult: A | undefined = undefined\n      setTimeout(() => {\n        try {\n          oneResult = one()\n        } catch (err) {\n          clearTimeout(twoId)\n          clearTimeout(threeId)\n          reject(err)\n        }\n      }, 0)\n\n      let twoResult: B | undefined = undefined\n      const twoId = setTimeout(() => {\n        // if `one` threw, then this timeout would've been cleared,\n        // so if we got here, we're guaranteed to have a value.\n        try {\n          twoResult = two(oneResult!)\n        } catch (err) {\n          clearTimeout(threeId)\n          reject(err)\n        }\n      }, 0)\n\n      const threeId = setTimeout(() => {\n        // if `two` threw, then this timeout would've been cleared,\n        // so if we got here, we're guaranteed to have a value.\n        try {\n          resolve(three(twoResult!))\n        } catch (err) {\n          reject(err)\n        }\n      }, 0)\n    })\n  }\n}\n"], "names": ["pipelineInSequentialTasks", "scheduleInSequentialTasks", "render", "followup", "process", "env", "NEXT_RUNTIME", "InvariantError", "Promise", "resolve", "reject", "pendingResult", "setTimeout", "err", "one", "two", "three", "oneResult", "undefined", "clearTimeout", "twoId", "threeId", "twoResult"], "mappings": ";;;;;;;;;;;;;;;IAqCgBA,yBAAyB;eAAzBA;;IA/BAC,yBAAyB;eAAzBA;;;gCANe;AAMxB,SAASA,0BACdC,MAA4B,EAC5BC,QAAoB;IAEpB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,sEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACL,OAAO,IAAIC,QAAQ,CAACC,SAASC;YAC3B,IAAIC;YACJC,WAAW;gBACT,IAAI;oBACFD,gBAAgBT;gBAClB,EAAE,OAAOW,KAAK;oBACZH,OAAOG;gBACT;YACF,GAAG;YACHD,WAAW;gBACTT;gBACAM,QAAQE;YACV,GAAG;QACL;IACF;AACF;AAOO,SAASX,0BACdc,GAAY,EACZC,GAAgB,EAChBC,KAA+B;IAE/B,IAAIZ,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,sEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF,OAAO;QACL,OAAO,IAAIC,QAAQ,CAACC,SAASC;YAC3B,IAAIO,YAA2BC;YAC/BN,WAAW;gBACT,IAAI;oBACFK,YAAYH;gBACd,EAAE,OAAOD,KAAK;oBACZM,aAAaC;oBACbD,aAAaE;oBACbX,OAAOG;gBACT;YACF,GAAG;YAEH,IAAIS,YAA2BJ;YAC/B,MAAME,QAAQR,WAAW;gBACvB,2DAA2D;gBAC3D,uDAAuD;gBACvD,IAAI;oBACFU,YAAYP,IAAIE;gBAClB,EAAE,OAAOJ,KAAK;oBACZM,aAAaE;oBACbX,OAAOG;gBACT;YACF,GAAG;YAEH,MAAMQ,UAAUT,WAAW;gBACzB,2DAA2D;gBAC3D,uDAAuD;gBACvD,IAAI;oBACFH,QAAQO,MAAMM;gBAChB,EAAE,OAAOT,KAAK;oBACZH,OAAOG;gBACT;YACF,GAAG;QACL;IACF;AACF", "ignoreList": [0]}