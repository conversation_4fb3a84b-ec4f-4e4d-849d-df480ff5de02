{"version": 3, "sources": ["../../../src/server/app-render/use-flight-response.tsx"], "sourcesContent": ["import type { ClientReferenceManifest } from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { BinaryStreamOf } from './app-render'\n\nimport { htmlEscapeJsonString } from '../htmlescape'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\nconst INLINE_FLIGHT_PAYLOAD_BOOTSTRAP = 0\nconst INLINE_FLIGHT_PAYLOAD_DATA = 1\nconst INLINE_FLIGHT_PAYLOAD_FORM_STATE = 2\nconst INLINE_FLIGHT_PAYLOAD_BINARY = 3\n\nconst flightResponses = new WeakMap<BinaryStreamOf<any>, Promise<any>>()\nconst encoder = new TextEncoder()\n\nconst findSourceMapURL =\n  process.env.NODE_ENV !== 'production'\n    ? (require('../lib/source-maps') as typeof import('../lib/source-maps'))\n        .findSourceMapURLDEV\n    : undefined\n\n/**\n * Render Flight stream.\n * This is only used for renderToHTML, the Flight response does not need additional wrappers.\n */\nexport function useFlightStream<T>(\n  flightStream: BinaryStreamOf<T>,\n  debugStream: ReadableStream<Uint8Array> | undefined,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>,\n  nonce: string | undefined\n): Promise<T> {\n  const response = flightResponses.get(flightStream)\n\n  if (response) {\n    return response\n  }\n\n  // react-server-dom-webpack/client.edge must not be hoisted for require cache clearing to work correctly\n  const { createFromReadableStream } =\n    // eslint-disable-next-line import/no-extraneous-dependencies\n    require('react-server-dom-webpack/client') as typeof import('react-server-dom-webpack/client')\n\n  const newResponse = createFromReadableStream<T>(flightStream, {\n    findSourceMapURL,\n    serverConsumerManifest: {\n      moduleLoading: clientReferenceManifest.moduleLoading,\n      moduleMap: isEdgeRuntime\n        ? clientReferenceManifest.edgeSSRModuleMapping\n        : clientReferenceManifest.ssrModuleMapping,\n      serverModuleMap: null,\n    },\n    nonce,\n    debugChannel: debugStream ? { readable: debugStream } : undefined,\n  })\n\n  // Edge pages are never prerendered so they necessarily cannot have a workUnitStore type\n  // that requires the nextTick behavior. This is why it is safe to access a node only API here\n  if (process.env.NEXT_RUNTIME !== 'edge') {\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    if (!workUnitStore) {\n      throw new InvariantError('Expected workUnitAsyncStorage to have a store.')\n    }\n\n    switch (workUnitStore.type) {\n      case 'prerender-client':\n        const responseOnNextTick = new Promise<T>((resolve) => {\n          process.nextTick(() => resolve(newResponse))\n        })\n        flightResponses.set(flightStream, responseOnNextTick)\n        return responseOnNextTick\n      case 'prerender':\n      case 'prerender-runtime':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'request':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  flightResponses.set(flightStream, newResponse)\n\n  return newResponse\n}\n\n/**\n * Creates a ReadableStream provides inline script tag chunks for writing hydration\n * data to the client outside the React render itself.\n *\n * @param flightStream The RSC render stream\n * @param nonce optionally a nonce used during this particular render\n * @param formState optionally the formState used with this particular render\n * @returns a ReadableStream without the complete property. This signifies a lazy ReadableStream\n */\nexport function createInlinedDataReadableStream(\n  flightStream: ReadableStream<Uint8Array>,\n  nonce: string | undefined,\n  formState: unknown | null\n): ReadableStream<Uint8Array> {\n  const startScriptTag = nonce\n    ? `<script nonce=${JSON.stringify(nonce)}>`\n    : '<script>'\n\n  const flightReader = flightStream.getReader()\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n\n  const readable = new ReadableStream({\n    type: 'bytes',\n    start(controller) {\n      try {\n        writeInitialInstructions(controller, startScriptTag, formState)\n      } catch (error) {\n        // during encoding or enqueueing forward the error downstream\n        controller.error(error)\n      }\n    },\n    async pull(controller) {\n      try {\n        const { done, value } = await flightReader.read()\n\n        if (value) {\n          try {\n            const decodedString = decoder.decode(value, { stream: !done })\n\n            // The chunk cannot be decoded as valid UTF-8 string as it might\n            // have arbitrary binary data.\n            writeFlightDataInstruction(\n              controller,\n              startScriptTag,\n              decodedString\n            )\n          } catch {\n            // The chunk cannot be decoded as valid UTF-8 string.\n            writeFlightDataInstruction(controller, startScriptTag, value)\n          }\n        }\n\n        if (done) {\n          controller.close()\n        }\n      } catch (error) {\n        // There was a problem in the upstream reader or during decoding or enqueuing\n        // forward the error downstream\n        controller.error(error)\n      }\n    },\n  })\n\n  return readable\n}\n\nfunction writeInitialInstructions(\n  controller: ReadableStreamDefaultController,\n  scriptStart: string,\n  formState: unknown | null\n) {\n  let scriptContents = `(self.__next_f=self.__next_f||[]).push(${htmlEscapeJsonString(\n    JSON.stringify([INLINE_FLIGHT_PAYLOAD_BOOTSTRAP])\n  )})`\n\n  if (formState != null) {\n    scriptContents += `;self.__next_f.push(${htmlEscapeJsonString(\n      JSON.stringify([INLINE_FLIGHT_PAYLOAD_FORM_STATE, formState])\n    )})`\n  }\n\n  controller.enqueue(encoder.encode(`${scriptStart}${scriptContents}</script>`))\n}\n\nfunction writeFlightDataInstruction(\n  controller: ReadableStreamDefaultController,\n  scriptStart: string,\n  chunk: string | Uint8Array\n) {\n  let htmlInlinedData: string\n\n  if (typeof chunk === 'string') {\n    htmlInlinedData = htmlEscapeJsonString(\n      JSON.stringify([INLINE_FLIGHT_PAYLOAD_DATA, chunk])\n    )\n  } else {\n    // The chunk cannot be embedded as a UTF-8 string in the script tag.\n    // Instead let's inline it in base64.\n    // Credits to Devon Govett (devongovett) for the technique.\n    // https://github.com/devongovett/rsc-html-stream\n    const base64 = btoa(String.fromCodePoint(...chunk))\n    htmlInlinedData = htmlEscapeJsonString(\n      JSON.stringify([INLINE_FLIGHT_PAYLOAD_BINARY, base64])\n    )\n  }\n\n  controller.enqueue(\n    encoder.encode(\n      `${scriptStart}self.__next_f.push(${htmlInlinedData})</script>`\n    )\n  )\n}\n"], "names": ["createInlinedDataReadableStream", "useFlightStream", "isEdgeRuntime", "process", "env", "NEXT_RUNTIME", "INLINE_FLIGHT_PAYLOAD_BOOTSTRAP", "INLINE_FLIGHT_PAYLOAD_DATA", "INLINE_FLIGHT_PAYLOAD_FORM_STATE", "INLINE_FLIGHT_PAYLOAD_BINARY", "flightResponses", "WeakMap", "encoder", "TextEncoder", "findSourceMapURL", "NODE_ENV", "require", "findSourceMapURLDEV", "undefined", "flightStream", "debugStream", "clientReferenceManifest", "nonce", "response", "get", "createFromReadableStream", "newResponse", "serverConsumerManifest", "moduleLoading", "moduleMap", "edgeSSRModuleMapping", "ssrModuleMapping", "serverModuleMap", "debugChannel", "readable", "workUnitStore", "workUnitAsyncStorage", "getStore", "InvariantError", "type", "responseOnNextTick", "Promise", "resolve", "nextTick", "set", "formState", "startScriptTag", "JSON", "stringify", "flightReader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "fatal", "ReadableStream", "start", "controller", "writeInitialInstructions", "error", "pull", "done", "value", "read", "decodedString", "decode", "stream", "writeFlightDataInstruction", "close", "scriptStart", "scriptContents", "htmlEscapeJsonString", "enqueue", "encode", "chunk", "htmlInlinedData", "base64", "btoa", "String", "fromCodePoint"], "mappings": ";;;;;;;;;;;;;;;IAsGgBA,+BAA+B;eAA/BA;;IA1EAC,eAAe;eAAfA;;;4BAzBqB;8CAEA;gCACN;AAE/B,MAAMC,gBAAgBC,QAAQC,GAAG,CAACC,YAAY,KAAK;AAEnD,MAAMC,kCAAkC;AACxC,MAAMC,6BAA6B;AACnC,MAAMC,mCAAmC;AACzC,MAAMC,+BAA+B;AAErC,MAAMC,kBAAkB,IAAIC;AAC5B,MAAMC,UAAU,IAAIC;AAEpB,MAAMC,mBACJX,QAAQC,GAAG,CAACW,QAAQ,KAAK,eACrB,AAACC,QAAQ,sBACNC,mBAAmB,GACtBC;AAMC,SAASjB,gBACdkB,YAA+B,EAC/BC,WAAmD,EACnDC,uBAA8D,EAC9DC,KAAyB;IAEzB,MAAMC,WAAWb,gBAAgBc,GAAG,CAACL;IAErC,IAAII,UAAU;QACZ,OAAOA;IACT;IAEA,wGAAwG;IACxG,MAAM,EAAEE,wBAAwB,EAAE,GAChC,6DAA6D;IAC7DT,QAAQ;IAEV,MAAMU,cAAcD,yBAA4BN,cAAc;QAC5DL;QACAa,wBAAwB;YACtBC,eAAeP,wBAAwBO,aAAa;YACpDC,WAAW3B,gBACPmB,wBAAwBS,oBAAoB,GAC5CT,wBAAwBU,gBAAgB;YAC5CC,iBAAiB;QACnB;QACAV;QACAW,cAAcb,cAAc;YAAEc,UAAUd;QAAY,IAAIF;IAC1D;IAEA,wFAAwF;IACxF,6FAA6F;IAC7F,IAAIf,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;QACvC,MAAM8B,gBAAgBC,kDAAoB,CAACC,QAAQ;QAEnD,IAAI,CAACF,eAAe;YAClB,MAAM,qBAAoE,CAApE,IAAIG,8BAAc,CAAC,mDAAnB,qBAAA;uBAAA;4BAAA;8BAAA;YAAmE;QAC3E;QAEA,OAAQH,cAAcI,IAAI;YACxB,KAAK;gBACH,MAAMC,qBAAqB,IAAIC,QAAW,CAACC;oBACzCvC,QAAQwC,QAAQ,CAAC,IAAMD,QAAQhB;gBACjC;gBACAhB,gBAAgBkC,GAAG,CAACzB,cAAcqB;gBAClC,OAAOA;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEL;QACJ;IACF;IAEAzB,gBAAgBkC,GAAG,CAACzB,cAAcO;IAElC,OAAOA;AACT;AAWO,SAAS1B,gCACdmB,YAAwC,EACxCG,KAAyB,EACzBuB,SAAyB;IAEzB,MAAMC,iBAAiBxB,QACnB,CAAC,cAAc,EAAEyB,KAAKC,SAAS,CAAC1B,OAAO,CAAC,CAAC,GACzC;IAEJ,MAAM2B,eAAe9B,aAAa+B,SAAS;IAC3C,MAAMC,UAAU,IAAIC,YAAY,SAAS;QAAEC,OAAO;IAAK;IAEvD,MAAMnB,WAAW,IAAIoB,eAAe;QAClCf,MAAM;QACNgB,OAAMC,UAAU;YACd,IAAI;gBACFC,yBAAyBD,YAAYV,gBAAgBD;YACvD,EAAE,OAAOa,OAAO;gBACd,6DAA6D;gBAC7DF,WAAWE,KAAK,CAACA;YACnB;QACF;QACA,MAAMC,MAAKH,UAAU;YACnB,IAAI;gBACF,MAAM,EAAEI,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMZ,aAAaa,IAAI;gBAE/C,IAAID,OAAO;oBACT,IAAI;wBACF,MAAME,gBAAgBZ,QAAQa,MAAM,CAACH,OAAO;4BAAEI,QAAQ,CAACL;wBAAK;wBAE5D,gEAAgE;wBAChE,8BAA8B;wBAC9BM,2BACEV,YACAV,gBACAiB;oBAEJ,EAAE,OAAM;wBACN,qDAAqD;wBACrDG,2BAA2BV,YAAYV,gBAAgBe;oBACzD;gBACF;gBAEA,IAAID,MAAM;oBACRJ,WAAWW,KAAK;gBAClB;YACF,EAAE,OAAOT,OAAO;gBACd,6EAA6E;gBAC7E,+BAA+B;gBAC/BF,WAAWE,KAAK,CAACA;YACnB;QACF;IACF;IAEA,OAAOxB;AACT;AAEA,SAASuB,yBACPD,UAA2C,EAC3CY,WAAmB,EACnBvB,SAAyB;IAEzB,IAAIwB,iBAAiB,CAAC,uCAAuC,EAAEC,IAAAA,gCAAoB,EACjFvB,KAAKC,SAAS,CAAC;QAAC1C;KAAgC,GAChD,CAAC,CAAC;IAEJ,IAAIuC,aAAa,MAAM;QACrBwB,kBAAkB,CAAC,oBAAoB,EAAEC,IAAAA,gCAAoB,EAC3DvB,KAAKC,SAAS,CAAC;YAACxC;YAAkCqC;SAAU,GAC5D,CAAC,CAAC;IACN;IAEAW,WAAWe,OAAO,CAAC3D,QAAQ4D,MAAM,CAAC,GAAGJ,cAAcC,eAAe,SAAS,CAAC;AAC9E;AAEA,SAASH,2BACPV,UAA2C,EAC3CY,WAAmB,EACnBK,KAA0B;IAE1B,IAAIC;IAEJ,IAAI,OAAOD,UAAU,UAAU;QAC7BC,kBAAkBJ,IAAAA,gCAAoB,EACpCvB,KAAKC,SAAS,CAAC;YAACzC;YAA4BkE;SAAM;IAEtD,OAAO;QACL,oEAAoE;QACpE,qCAAqC;QACrC,2DAA2D;QAC3D,iDAAiD;QACjD,MAAME,SAASC,KAAKC,OAAOC,aAAa,IAAIL;QAC5CC,kBAAkBJ,IAAAA,gCAAoB,EACpCvB,KAAKC,SAAS,CAAC;YAACvC;YAA8BkE;SAAO;IAEzD;IAEAnB,WAAWe,OAAO,CAChB3D,QAAQ4D,MAAM,CACZ,GAAGJ,YAAY,mBAAmB,EAAEM,gBAAgB,UAAU,CAAC;AAGrE", "ignoreList": [0]}