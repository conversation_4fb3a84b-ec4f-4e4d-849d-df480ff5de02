{"version": 3, "sources": ["../../../src/server/app-render/staged-validation.tsx"], "sourcesContent": ["import { getLayoutOrPageModule } from '../lib/app-dir-module'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport { parseLoaderTree } from '../../shared/lib/router/utils/parse-loader-tree'\nimport type { AppSegmentConfig } from '../../build/segment-config/app/app-segment-config'\n\nexport async function anySegmentHasRuntimePrefetchEnabled(\n  tree: LoaderTree\n): Promise<boolean> {\n  const { mod: layoutOrPageMod } = await getLayoutOrPageModule(tree)\n\n  // TODO(restart-on-cache-miss): Does this work correctly for client page/layout modules?\n  const prefetchConfig = layoutOrPageMod\n    ? (layoutOrPageMod as AppSegmentConfig).unstable_prefetch\n    : undefined\n  /** Whether this segment should use a runtime prefetch instead of a static prefetch. */\n  const hasRuntimePrefetch = prefetchConfig?.mode === 'runtime'\n  if (hasRuntimePrefetch) {\n    return true\n  }\n\n  const { parallelRoutes } = parseLoaderTree(tree)\n  for (const parallelRouteKey in parallelRoutes) {\n    const parallelRoute = parallelRoutes[parallelRouteKey]\n    const hasChildRuntimePrefetch =\n      await anySegmentHasRuntimePrefetchEnabled(parallelRoute)\n    if (hasChildRuntimePrefetch) {\n      return true\n    }\n  }\n\n  return false\n}\n"], "names": ["anySegmentHasRuntimePrefetchEnabled", "tree", "mod", "layoutOrPageMod", "getLayoutOrPageModule", "prefetchConfig", "unstable_prefetch", "undefined", "hasRuntimePrefetch", "mode", "parallelRoutes", "parseLoaderTree", "parallelRouteKey", "parallelRoute", "hasChildRuntimePrefetch"], "mappings": ";;;;+BAKsBA;;;eAAAA;;;8BALgB;iCAEN;AAGzB,eAAeA,oCACpBC,IAAgB;IAEhB,MAAM,EAAEC,KAAKC,eAAe,EAAE,GAAG,MAAMC,IAAAA,mCAAqB,EAACH;IAE7D,wFAAwF;IACxF,MAAMI,iBAAiBF,kBACnB,AAACA,gBAAqCG,iBAAiB,GACvDC;IACJ,qFAAqF,GACrF,MAAMC,qBAAqBH,CAAAA,kCAAAA,eAAgBI,IAAI,MAAK;IACpD,IAAID,oBAAoB;QACtB,OAAO;IACT;IAEA,MAAM,EAAEE,cAAc,EAAE,GAAGC,IAAAA,gCAAe,EAACV;IAC3C,IAAK,MAAMW,oBAAoBF,eAAgB;QAC7C,MAAMG,gBAAgBH,cAAc,CAACE,iBAAiB;QACtD,MAAME,0BACJ,MAAMd,oCAAoCa;QAC5C,IAAIC,yBAAyB;YAC3B,OAAO;QACT;IACF;IAEA,OAAO;AACT", "ignoreList": [0]}