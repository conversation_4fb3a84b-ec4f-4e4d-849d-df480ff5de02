{"version": 3, "sources": ["../../../src/server/request/draft-mode.ts"], "sourcesContent": ["import {\n  getDraftModeProviderForCacheScope,\n  throwForMissingRequestStore,\n} from '../app-render/work-unit-async-storage.external'\n\nimport type { DraftModeProvider } from '../async-storage/draft-mode-provider'\n\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  delayUntilRuntimeStage,\n  postponeWithTracking,\n  trackDynamicDataInDynamicRender,\n} from '../app-render/dynamic-rendering'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\n\nexport function draftMode(): Promise<DraftMode> {\n  const callingExpression = 'draftMode'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (!workStore || !workUnitStore) {\n    throwForMissingRequestStore(callingExpression)\n  }\n\n  switch (workUnitStore.type) {\n    case 'prerender-runtime':\n      // TODO(runtime-ppr): does it make sense to delay this? normally it's always microtasky\n      return delayUntilRuntimeStage(\n        workUnitStore,\n        createOrGetCachedDraftMode(workUnitStore.draftMode, workStore)\n      )\n    case 'request':\n      return createOrGetCachedDraftMode(workUnitStore.draftMode, workStore)\n\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n      // Inside of `\"use cache\"` or `unstable_cache`, draft mode is available if\n      // the outmost work unit store is a request store (or a runtime prerender),\n      // and if draft mode is enabled.\n      const draftModeProvider = getDraftModeProviderForCacheScope(\n        workStore,\n        workUnitStore\n      )\n\n      if (draftModeProvider) {\n        return createOrGetCachedDraftMode(draftModeProvider, workStore)\n      }\n\n    // Otherwise, we fall through to providing an empty draft mode.\n    // eslint-disable-next-line no-fallthrough\n    case 'prerender':\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // Return empty draft mode\n      return createOrGetCachedDraftMode(null, workStore)\n\n    default:\n      return workUnitStore satisfies never\n  }\n}\n\nfunction createOrGetCachedDraftMode(\n  draftModeProvider: DraftModeProvider | null,\n  workStore: WorkStore | undefined\n): Promise<DraftMode> {\n  const cacheKey = draftModeProvider ?? NullDraftMode\n  const cachedDraftMode = CachedDraftModes.get(cacheKey)\n\n  if (cachedDraftMode) {\n    return cachedDraftMode\n  }\n\n  if (process.env.NODE_ENV === 'development' && !workStore?.isPrefetchRequest) {\n    const route = workStore?.route\n    return createDraftModeWithDevWarnings(draftModeProvider, route)\n  } else {\n    return Promise.resolve(new DraftMode(draftModeProvider))\n  }\n}\n\ninterface CacheLifetime {}\nconst NullDraftMode = {}\nconst CachedDraftModes = new WeakMap<CacheLifetime, Promise<DraftMode>>()\n\nfunction createDraftModeWithDevWarnings(\n  underlyingProvider: null | DraftModeProvider,\n  route: undefined | string\n): Promise<DraftMode> {\n  const instance = new DraftMode(underlyingProvider)\n  const promise = Promise.resolve(instance)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case 'isEnabled':\n          warnForSyncAccess(route, `\\`draftMode().${prop}\\``)\n          break\n        case 'enable':\n        case 'disable': {\n          warnForSyncAccess(route, `\\`draftMode().${prop}()\\``)\n          break\n        }\n        default: {\n          // We only warn for well-defined properties of the draftMode object.\n        }\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  return proxiedPromise\n}\n\nclass DraftMode {\n  /**\n   * @internal - this declaration is stripped via `tsc --stripInternal`\n   */\n  private readonly _provider: null | DraftModeProvider\n\n  constructor(provider: null | DraftModeProvider) {\n    this._provider = provider\n  }\n  get isEnabled() {\n    if (this._provider !== null) {\n      return this._provider.isEnabled\n    }\n    return false\n  }\n  public enable() {\n    // We have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    trackDynamicDraftMode('draftMode().enable()', this.enable)\n    if (this._provider !== null) {\n      this._provider.enable()\n    }\n  }\n  public disable() {\n    trackDynamicDraftMode('draftMode().disable()', this.disable)\n    if (this._provider !== null) {\n      this._provider.disable()\n    }\n  }\n}\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createDraftModeAccessError\n)\n\nfunction createDraftModeAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`draftMode()\\` returns a Promise and must be unwrapped with \\`await\\` or \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction trackDynamicDraftMode(expression: string, constructorOpt: Function) {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    // We have a store we want to track dynamic data access to ensure we\n    // don't statically generate routes that manipulate draft mode.\n    if (workUnitStore?.phase === 'after') {\n      throw new Error(\n        `Route ${workStore.route} used \"${expression}\" inside \\`after()\\`. The enabled status of \\`draftMode()\\` can be read inside \\`after()\\` but you cannot enable or disable \\`draftMode()\\`. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'cache':\n        case 'private-cache': {\n          const error = new Error(\n            `Route ${workStore.route} used \"${expression}\" inside \"use cache\". The enabled status of \\`draftMode()\\` can be read in caches but you must not enable or disable \\`draftMode()\\` inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, constructorOpt)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'unstable-cache':\n          throw new Error(\n            `Route ${workStore.route} used \"${expression}\" inside a function cached with \\`unstable_cache()\\`. The enabled status of \\`draftMode()\\` can be read in caches but you must not enable or disable \\`draftMode()\\` inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n          )\n\n        case 'prerender':\n        case 'prerender-runtime': {\n          const error = new Error(\n            `Route ${workStore.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`\n          )\n          return abortAndThrowOnSynchronousRequestDataAccess(\n            workStore.route,\n            expression,\n            error,\n            workUnitStore\n          )\n        }\n        case 'prerender-client':\n          const exportName = '`draftMode`'\n          throw new InvariantError(\n            `${exportName} must not be used within a Client Component. Next.js should be preventing ${exportName} from being included in Client Components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          return postponeWithTracking(\n            workStore.route,\n            expression,\n            workUnitStore.dynamicTracking\n          )\n        case 'prerender-legacy':\n          workUnitStore.revalidate = 0\n\n          const err = new DynamicServerError(\n            `Route ${workStore.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n          )\n          workStore.dynamicUsageDescription = expression\n          workStore.dynamicUsageStack = err.stack\n\n          throw err\n        case 'request':\n          trackDynamicDataInDynamicRender(workUnitStore)\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n  }\n}\n"], "names": ["draftMode", "callingExpression", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "throwForMissingRequestStore", "type", "delayUntilRuntimeStage", "createOrGetCachedDraftMode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDraftModeProviderForCacheScope", "cache<PERSON>ey", "NullDraftMode", "cachedDraftMode", "CachedDraftModes", "get", "process", "env", "NODE_ENV", "isPrefetchRequest", "route", "createDraftModeWithDevWarnings", "Promise", "resolve", "DraftMode", "WeakMap", "underlyingProvider", "instance", "promise", "proxiedPromise", "Proxy", "target", "prop", "receiver", "warnForSyncAccess", "ReflectAdapter", "constructor", "provider", "_provider", "isEnabled", "enable", "trackDynamicDraftMode", "disable", "createDedupedByCallsiteServerErrorLoggerDev", "createDraftModeAccessError", "expression", "prefix", "Error", "constructorOpt", "phase", "dynamicShouldError", "StaticGenBailoutError", "error", "captureStackTrace", "invalidDynamicUsageError", "abortAndThrowOnSynchronousRequestDataAccess", "exportName", "InvariantError", "postponeWithTracking", "dynamicTracking", "revalidate", "err", "DynamicServerError", "dynamicUsageDescription", "dynamicUsageStack", "stack", "trackDynamicDataInDynamicRender"], "mappings": ";;;;+BAwBgBA;;;eAAAA;;;8CArBT;0CAOA;kCAOA;0DACqD;yCACtB;oCACH;gCACJ;yBACA;AAExB,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAI,CAACF,aAAa,CAACG,eAAe;QAChCE,IAAAA,yDAA2B,EAACN;IAC9B;IAEA,OAAQI,cAAcG,IAAI;QACxB,KAAK;YACH,uFAAuF;YACvF,OAAOC,IAAAA,wCAAsB,EAC3BJ,eACAK,2BAA2BL,cAAcL,SAAS,EAAEE;QAExD,KAAK;YACH,OAAOQ,2BAA2BL,cAAcL,SAAS,EAAEE;QAE7D,KAAK;QACL,KAAK;QACL,KAAK;YACH,0EAA0E;YAC1E,2EAA2E;YAC3E,gCAAgC;YAChC,MAAMS,oBAAoBC,IAAAA,+DAAiC,EACzDV,WACAG;YAGF,IAAIM,mBAAmB;gBACrB,OAAOD,2BAA2BC,mBAAmBT;YACvD;QAEF,+DAA+D;QAC/D,0CAA0C;QAC1C,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,0BAA0B;YAC1B,OAAOQ,2BAA2B,MAAMR;QAE1C;YACE,OAAOG;IACX;AACF;AAEA,SAASK,2BACPC,iBAA2C,EAC3CT,SAAgC;IAEhC,MAAMW,WAAWF,qBAAqBG;IACtC,MAAMC,kBAAkBC,iBAAiBC,GAAG,CAACJ;IAE7C,IAAIE,iBAAiB;QACnB,OAAOA;IACT;IAEA,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB,EAAClB,6BAAAA,UAAWmB,iBAAiB,GAAE;QAC3E,MAAMC,QAAQpB,6BAAAA,UAAWoB,KAAK;QAC9B,OAAOC,+BAA+BZ,mBAAmBW;IAC3D,OAAO;QACL,OAAOE,QAAQC,OAAO,CAAC,IAAIC,UAAUf;IACvC;AACF;AAGA,MAAMG,gBAAgB,CAAC;AACvB,MAAME,mBAAmB,IAAIW;AAE7B,SAASJ,+BACPK,kBAA4C,EAC5CN,KAAyB;IAEzB,MAAMO,WAAW,IAAIH,UAAUE;IAC/B,MAAME,UAAUN,QAAQC,OAAO,CAACI;IAEhC,MAAME,iBAAiB,IAAIC,MAAMF,SAAS;QACxCb,KAAIgB,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,OAAQD;gBACN,KAAK;oBACHE,kBAAkBd,OAAO,CAAC,cAAc,EAAEY,KAAK,EAAE,CAAC;oBAClD;gBACF,KAAK;gBACL,KAAK;oBAAW;wBACdE,kBAAkBd,OAAO,CAAC,cAAc,EAAEY,KAAK,IAAI,CAAC;wBACpD;oBACF;gBACA;oBAAS;oBACP,oEAAoE;oBACtE;YACF;YAEA,OAAOG,uBAAc,CAACpB,GAAG,CAACgB,QAAQC,MAAMC;QAC1C;IACF;IAEA,OAAOJ;AACT;AAEA,MAAML;IAMJY,YAAYC,QAAkC,CAAE;QAC9C,IAAI,CAACC,SAAS,GAAGD;IACnB;IACA,IAAIE,YAAY;QACd,IAAI,IAAI,CAACD,SAAS,KAAK,MAAM;YAC3B,OAAO,IAAI,CAACA,SAAS,CAACC,SAAS;QACjC;QACA,OAAO;IACT;IACOC,SAAS;QACd,oEAAoE;QACpE,+DAA+D;QAC/DC,sBAAsB,wBAAwB,IAAI,CAACD,MAAM;QACzD,IAAI,IAAI,CAACF,SAAS,KAAK,MAAM;YAC3B,IAAI,CAACA,SAAS,CAACE,MAAM;QACvB;IACF;IACOE,UAAU;QACfD,sBAAsB,yBAAyB,IAAI,CAACC,OAAO;QAC3D,IAAI,IAAI,CAACJ,SAAS,KAAK,MAAM;YAC3B,IAAI,CAACA,SAAS,CAACI,OAAO;QACxB;IACF;AACF;AACA,MAAMR,oBAAoBS,IAAAA,qFAA2C,EACnEC;AAGF,SAASA,2BACPxB,KAAyB,EACzByB,UAAkB;IAElB,MAAMC,SAAS1B,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAI2B,MACT,GAAGD,OAAO,KAAK,EAAED,WAAW,EAAE,CAAC,GAC7B,CAAC,2HAA2H,CAAC,GAC7H,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF;AAEA,SAASJ,sBAAsBI,UAAkB,EAAEG,cAAwB;IACzE,MAAMhD,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAIF,WAAW;QACb,oEAAoE;QACpE,+DAA+D;QAC/D,IAAIG,CAAAA,iCAAAA,cAAe8C,KAAK,MAAK,SAAS;YACpC,MAAM,qBAEL,CAFK,IAAIF,MACR,CAAC,MAAM,EAAE/C,UAAUoB,KAAK,CAAC,OAAO,EAAEyB,WAAW,0NAA0N,CAAC,GADpQ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI7C,UAAUkD,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAEnD,UAAUoB,KAAK,CAAC,8EAA8E,EAAEyB,WAAW,4HAA4H,CAAC,GAD7O,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI1C,eAAe;YACjB,OAAQA,cAAcG,IAAI;gBACxB,KAAK;gBACL,KAAK;oBAAiB;wBACpB,MAAM8C,QAAQ,qBAEb,CAFa,IAAIL,MAChB,CAAC,MAAM,EAAE/C,UAAUoB,KAAK,CAAC,OAAO,EAAEyB,WAAW,mOAAmO,CAAC,GADrQ,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACAE,MAAMM,iBAAiB,CAACD,OAAOJ;wBAC/BhD,UAAUsD,wBAAwB,KAAKF;wBACvC,MAAMA;oBACR;gBACA,KAAK;oBACH,MAAM,qBAEL,CAFK,IAAIL,MACR,CAAC,MAAM,EAAE/C,UAAUoB,KAAK,CAAC,OAAO,EAAEyB,WAAW,2QAA2Q,CAAC,GADrT,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBAEF,KAAK;gBACL,KAAK;oBAAqB;wBACxB,MAAMO,QAAQ,qBAEb,CAFa,IAAIL,MAChB,CAAC,MAAM,EAAE/C,UAAUoB,KAAK,CAAC,MAAM,EAAEyB,WAAW,+HAA+H,CAAC,GADhK,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACA,OAAOU,IAAAA,6DAA2C,EAChDvD,UAAUoB,KAAK,EACfyB,YACAO,OACAjD;oBAEJ;gBACA,KAAK;oBACH,MAAMqD,aAAa;oBACnB,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,GAAGD,WAAW,0EAA0E,EAAEA,WAAW,+EAA+E,CAAC,GADjL,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;oBACH,OAAOE,IAAAA,sCAAoB,EACzB1D,UAAUoB,KAAK,EACfyB,YACA1C,cAAcwD,eAAe;gBAEjC,KAAK;oBACHxD,cAAcyD,UAAU,GAAG;oBAE3B,MAAMC,MAAM,qBAEX,CAFW,IAAIC,sCAAkB,CAChC,CAAC,MAAM,EAAE9D,UAAUoB,KAAK,CAAC,mDAAmD,EAAEyB,WAAW,6EAA6E,CAAC,GAD7J,qBAAA;+BAAA;oCAAA;sCAAA;oBAEZ;oBACA7C,UAAU+D,uBAAuB,GAAGlB;oBACpC7C,UAAUgE,iBAAiB,GAAGH,IAAII,KAAK;oBAEvC,MAAMJ;gBACR,KAAK;oBACHK,IAAAA,iDAA+B,EAAC/D;oBAChC;gBACF;oBACEA;YACJ;QACF;IACF;AACF", "ignoreList": [0]}