import type { FallbackRouteParam } from '../../build/static-paths/types';
import type { DynamicParamTypesShort } from '../../shared/lib/app-router-types';
import type AppPageRouteModule from '../route-modules/app-page/module';
export type OpaqueFallbackRouteParamValue = [
    /**
     * The search value of the fallback route param. This is the opaque key
     * that will be used to replace the dynamic param in the postponed state.
     */
    searchValue: string,
    /**
     * The dynamic param type of the fallback route param. This is the type of
     * the dynamic param that will be used to replace the dynamic param in the
     * postponed state.
     */
    dynamicParamType: DynamicParamTypesShort
];
/**
 * An opaque fallback route params object. This is used to store the fallback
 * route params in a way that is not easily accessible to the client.
 */
export type OpaqueFallbackRouteParams = ReadonlyMap<string, OpaqueFallbackRouteParamValue>;
/**
 * The entries of the opaque fallback route params object.
 *
 * @param key the key of the fallback route param
 * @param value the value of the fallback route param
 */
export type OpaqueFallbackRouteParamEntries = ReturnType<OpaqueFallbackRouteParams['entries']> extends MapIterator<[
    infer K,
    infer V
]> ? ReadonlyArray<[K, V]> : never;
/**
 * Creates an opaque fallback route params object from the fallback route params.
 *
 * @param fallbackRouteParams the fallback route params
 * @returns the opaque fallback route params
 */
export declare function createOpaqueFallbackRouteParams(fallbackRouteParams: readonly FallbackRouteParam[]): OpaqueFallbackRouteParams | null;
/**
 * Gets the fallback route params for a given page. This is an expensive
 * operation because it requires parsing the loader tree to extract the fallback
 * route params.
 *
 * @param page the page
 * @param routeModule the route module
 * @returns the opaque fallback route params
 */
export declare function getFallbackRouteParams(page: string, routeModule: AppPageRouteModule): OpaqueFallbackRouteParams | null;
