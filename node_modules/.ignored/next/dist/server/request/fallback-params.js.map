{"version": 3, "sources": ["../../../src/server/request/fallback-params.ts"], "sourcesContent": ["import { collectFallbackRouteParams } from '../../build/segment-config/app/app-segments'\nimport type { FallbackRouteParam } from '../../build/static-paths/types'\nimport type { DynamicParamTypesShort } from '../../shared/lib/app-router-types'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { getRouteMatcher } from '../../shared/lib/router/utils/route-matcher'\nimport { getRouteRegex } from '../../shared/lib/router/utils/route-regex'\nimport { dynamicParamTypes } from '../app-render/get-short-dynamic-param-type'\nimport type AppPageRouteModule from '../route-modules/app-page/module'\n\nfunction getParamKeys(page: string) {\n  const pattern = getRouteRegex(page)\n  const matcher = getRouteMatcher(pattern)\n\n  // Get the default list of allowed params.\n  return Object.keys(matcher(page))\n}\n\nexport type OpaqueFallbackRouteParamValue = [\n  /**\n   * The search value of the fallback route param. This is the opaque key\n   * that will be used to replace the dynamic param in the postponed state.\n   */\n  searchValue: string,\n\n  /**\n   * The dynamic param type of the fallback route param. This is the type of\n   * the dynamic param that will be used to replace the dynamic param in the\n   * postponed state.\n   */\n  dynamicParamType: DynamicParamTypesShort,\n]\n\n/**\n * An opaque fallback route params object. This is used to store the fallback\n * route params in a way that is not easily accessible to the client.\n */\nexport type OpaqueFallbackRouteParams = ReadonlyMap<\n  string,\n  OpaqueFallbackRouteParamValue\n>\n\n/**\n * The entries of the opaque fallback route params object.\n *\n * @param key the key of the fallback route param\n * @param value the value of the fallback route param\n */\nexport type OpaqueFallbackRouteParamEntries =\n  ReturnType<OpaqueFallbackRouteParams['entries']> extends MapIterator<\n    [infer K, infer V]\n  >\n    ? ReadonlyArray<[K, V]>\n    : never\n\n/**\n * Creates an opaque fallback route params object from the fallback route params.\n *\n * @param fallbackRouteParams the fallback route params\n * @returns the opaque fallback route params\n */\nexport function createOpaqueFallbackRouteParams(\n  fallbackRouteParams: readonly FallbackRouteParam[]\n): OpaqueFallbackRouteParams | null {\n  // If there are no fallback route params, we can return early.\n  if (fallbackRouteParams.length === 0) return null\n\n  // As we're creating unique keys for each of the dynamic route params, we only\n  // need to generate a unique ID once per request because each of the keys will\n  // be also be unique.\n  const uniqueID = Math.random().toString(16).slice(2)\n\n  const keys = new Map<string, OpaqueFallbackRouteParamValue>()\n\n  // Generate a unique key for the fallback route param, if this key is found\n  // in the static output, it represents a bug in cache components.\n  for (const { paramName, paramType } of fallbackRouteParams) {\n    keys.set(paramName, [\n      `%%drp:${paramName}:${uniqueID}%%`,\n      dynamicParamTypes[paramType],\n    ])\n  }\n\n  return keys\n}\n\n/**\n * Gets the fallback route params for a given page. This is an expensive\n * operation because it requires parsing the loader tree to extract the fallback\n * route params.\n *\n * @param page the page\n * @param routeModule the route module\n * @returns the opaque fallback route params\n */\nexport function getFallbackRouteParams(\n  page: string,\n  routeModule: AppPageRouteModule\n) {\n  // First, get the fallback route params based on the provided page.\n  const unknownParamKeys = new Set(getParamKeys(page))\n\n  // Needed when processing fallback route params for catchall routes in\n  // parallel segments, derive from pathname. This is similar to\n  // getDynamicParam's pagePath parsing logic.\n  const pathSegments = page.split('/').filter(Boolean)\n\n  const collected = collectFallbackRouteParams(routeModule)\n\n  // Then, we have to get the fallback route params from the segments that are\n  // associated with parallel route segments.\n  const fallbackRouteParams: FallbackRouteParam[] = []\n  for (const fallbackRouteParam of collected) {\n    if (fallbackRouteParam.isParallelRouteParam) {\n      // Try to see if we can resolve this parameter from the page that was\n      // passed in.\n      if (unknownParamKeys.has(fallbackRouteParam.paramName)) {\n        // The parameter is known, we can skip adding it to the fallback route\n        // params.\n        continue\n      }\n\n      if (\n        fallbackRouteParam.paramType === 'optional-catchall' ||\n        fallbackRouteParam.paramType === 'catchall'\n      ) {\n        // If there are any fallback route segments then we can't use the\n        // pathname to derive the value because it's not complete. We can\n        // make this assumption because the routes are always resolved left\n        // to right and the catchall is always the last segment, so any\n        // route parameters that are unknown will always contribute to the\n        // pathname and therefore the catchall param too.\n        if (\n          collected.some(\n            (param) =>\n              !param.isParallelRouteParam &&\n              unknownParamKeys.has(param.paramName)\n          )\n        ) {\n          fallbackRouteParams.push(fallbackRouteParam)\n          continue\n        }\n\n        if (\n          pathSegments.length === 0 &&\n          fallbackRouteParam.paramType !== 'optional-catchall'\n        ) {\n          // We shouldn't be able to match a catchall segment without any path\n          // segments if it's not an optional catchall.\n          throw new InvariantError(\n            `Unexpected empty path segments match for a pathname \"${page}\" with param \"${fallbackRouteParam.paramName}\" of type \"${fallbackRouteParam.paramType}\"`\n          )\n        }\n\n        // The path segments are not empty, and the segments didn't contain any\n        // unknown params, so we know that this particular fallback route param\n        // route param is not actually unknown, and is known. We can skip adding\n        // it to the fallback route params.\n      } else {\n        // This is some other type of route param that shouldn't get resolved\n        // statically.\n        throw new InvariantError(\n          `Unexpected match for a pathname \"${page}\" with a param \"${fallbackRouteParam.paramName}\" of type \"${fallbackRouteParam.paramType}\"`\n        )\n      }\n    } else if (unknownParamKeys.has(fallbackRouteParam.paramName)) {\n      // As this is a non-parallel route segment, and it exists in the unknown\n      // param keys, we know it's a fallback route param.\n      fallbackRouteParams.push(fallbackRouteParam)\n    }\n  }\n\n  return createOpaqueFallbackRouteParams(fallbackRouteParams)\n}\n"], "names": ["createOpaqueFallbackRouteParams", "getFallbackRouteParams", "get<PERSON>ara<PERSON><PERSON><PERSON><PERSON>", "page", "pattern", "getRouteRegex", "matcher", "getRouteMatcher", "Object", "keys", "fallbackRouteParams", "length", "uniqueID", "Math", "random", "toString", "slice", "Map", "paramName", "paramType", "set", "dynamicParamTypes", "routeModule", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Set", "pathSegments", "split", "filter", "Boolean", "collected", "collectFallbackRouteParams", "fallbackRouteParam", "isParallelRouteParam", "has", "some", "param", "push", "InvariantError"], "mappings": ";;;;;;;;;;;;;;;IA4DgBA,+BAA+B;eAA/BA;;IAkCAC,sBAAsB;eAAtBA;;;6BA9F2B;gCAGZ;8BACC;4BACF;0CACI;AAGlC,SAASC,aAAaC,IAAY;IAChC,MAAMC,UAAUC,IAAAA,yBAAa,EAACF;IAC9B,MAAMG,UAAUC,IAAAA,6BAAe,EAACH;IAEhC,0CAA0C;IAC1C,OAAOI,OAAOC,IAAI,CAACH,QAAQH;AAC7B;AA6CO,SAASH,gCACdU,mBAAkD;IAElD,8DAA8D;IAC9D,IAAIA,oBAAoBC,MAAM,KAAK,GAAG,OAAO;IAE7C,8EAA8E;IAC9E,8EAA8E;IAC9E,qBAAqB;IACrB,MAAMC,WAAWC,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,KAAK,CAAC;IAElD,MAAMP,OAAO,IAAIQ;IAEjB,2EAA2E;IAC3E,iEAAiE;IACjE,KAAK,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAE,IAAIT,oBAAqB;QAC1DD,KAAKW,GAAG,CAACF,WAAW;YAClB,CAAC,MAAM,EAAEA,UAAU,CAAC,EAAEN,SAAS,EAAE,CAAC;YAClCS,2CAAiB,CAACF,UAAU;SAC7B;IACH;IAEA,OAAOV;AACT;AAWO,SAASR,uBACdE,IAAY,EACZmB,WAA+B;IAE/B,mEAAmE;IACnE,MAAMC,mBAAmB,IAAIC,IAAItB,aAAaC;IAE9C,sEAAsE;IACtE,8DAA8D;IAC9D,4CAA4C;IAC5C,MAAMsB,eAAetB,KAAKuB,KAAK,CAAC,KAAKC,MAAM,CAACC;IAE5C,MAAMC,YAAYC,IAAAA,uCAA0B,EAACR;IAE7C,4EAA4E;IAC5E,2CAA2C;IAC3C,MAAMZ,sBAA4C,EAAE;IACpD,KAAK,MAAMqB,sBAAsBF,UAAW;QAC1C,IAAIE,mBAAmBC,oBAAoB,EAAE;YAC3C,qEAAqE;YACrE,aAAa;YACb,IAAIT,iBAAiBU,GAAG,CAACF,mBAAmBb,SAAS,GAAG;gBAGtD;YACF;YAEA,IACEa,mBAAmBZ,SAAS,KAAK,uBACjCY,mBAAmBZ,SAAS,KAAK,YACjC;gBACA,iEAAiE;gBACjE,iEAAiE;gBACjE,mEAAmE;gBACnE,+DAA+D;gBAC/D,kEAAkE;gBAClE,iDAAiD;gBACjD,IACEU,UAAUK,IAAI,CACZ,CAACC,QACC,CAACA,MAAMH,oBAAoB,IAC3BT,iBAAiBU,GAAG,CAACE,MAAMjB,SAAS,IAExC;oBACAR,oBAAoB0B,IAAI,CAACL;oBACzB;gBACF;gBAEA,IACEN,aAAad,MAAM,KAAK,KACxBoB,mBAAmBZ,SAAS,KAAK,qBACjC;oBACA,oEAAoE;oBACpE,6CAA6C;oBAC7C,MAAM,qBAEL,CAFK,IAAIkB,8BAAc,CACtB,CAAC,qDAAqD,EAAElC,KAAK,cAAc,EAAE4B,mBAAmBb,SAAS,CAAC,WAAW,EAAEa,mBAAmBZ,SAAS,CAAC,CAAC,CAAC,GADlJ,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YAEA,uEAAuE;YACvE,uEAAuE;YACvE,wEAAwE;YACxE,mCAAmC;YACrC,OAAO;gBACL,qEAAqE;gBACrE,cAAc;gBACd,MAAM,qBAEL,CAFK,IAAIkB,8BAAc,CACtB,CAAC,iCAAiC,EAAElC,KAAK,gBAAgB,EAAE4B,mBAAmBb,SAAS,CAAC,WAAW,EAAEa,mBAAmBZ,SAAS,CAAC,CAAC,CAAC,GADhI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF,OAAO,IAAII,iBAAiBU,GAAG,CAACF,mBAAmBb,SAAS,GAAG;YAC7D,wEAAwE;YACxE,mDAAmD;YACnDR,oBAAoB0B,IAAI,CAACL;QAC3B;IACF;IAEA,OAAO/B,gCAAgCU;AACzC", "ignoreList": [0]}