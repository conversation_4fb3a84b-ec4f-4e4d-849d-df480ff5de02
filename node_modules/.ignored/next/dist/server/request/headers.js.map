{"version": 3, "sources": ["../../../src/server/request/headers.ts"], "sourcesContent": ["import {\n  Headers<PERSON>dapter,\n  type ReadonlyHeaders,\n} from '../web/spec-extension/adapters/headers'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport {\n  throwForMissingRequestStore,\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n  type RequestStore,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  delayUntilRuntimeStage,\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { isRequestAPICallableInsideAfter } from './utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { RenderStage } from '../app-render/staged-rendering'\n\n/**\n * This function allows you to read the HTTP incoming request headers in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers) and\n * [Middleware](https://nextjs.org/docs/app/building-your-application/routing/middleware).\n *\n * Read more: [Next.js Docs: `headers`](https://nextjs.org/docs/app/api-reference/functions/headers)\n */\nexport function headers(): Promise<ReadonlyHeaders> {\n  const callingExpression = 'headers'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        `Route ${workStore.route} used \\`headers()\\` inside \\`after()\\`. This is not supported. If you need this data inside an \\`after()\\` callback, use \\`headers()\\` outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // headers object without tracking\n      const underlyingHeaders = HeadersAdapter.seal(new Headers({}))\n      return makeUntrackedHeaders(underlyingHeaders)\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'cache': {\n          const error = new Error(\n            `Route ${workStore.route} used \\`headers()\\` inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \\`headers()\\` outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, headers)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        }\n        case 'unstable-cache':\n          throw new Error(\n            `Route ${workStore.route} used \\`headers()\\` inside a function cached with \\`unstable_cache()\\`. Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \\`headers()\\` outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n          )\n        case 'prerender':\n        case 'prerender-client':\n        case 'private-cache':\n        case 'prerender-runtime':\n        case 'prerender-ppr':\n        case 'prerender-legacy':\n        case 'request':\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`headers()\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'prerender':\n          return makeHangingHeaders(workStore, workUnitStore)\n        case 'prerender-client':\n          const exportName = '`headers`'\n          throw new InvariantError(\n            `${exportName} must not be used within a client component. Next.js should be preventing ${exportName} from being included in client components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          // PPR Prerender (no cacheComponents)\n          // We are prerendering with PPR. We need track dynamic access here eagerly\n          // to keep continuity with how headers has worked in PPR without cacheComponents.\n          // TODO consider switching the semantic to throw on property access instead\n          return postponeWithTracking(\n            workStore.route,\n            callingExpression,\n            workUnitStore.dynamicTracking\n          )\n        case 'prerender-legacy':\n          // Legacy Prerender\n          // We are in a legacy static generation mode while prerendering\n          // We track dynamic access here so we don't need to wrap the headers in\n          // individual property access tracking.\n          return throwToInterruptStaticGeneration(\n            callingExpression,\n            workStore,\n            workUnitStore\n          )\n        case 'prerender-runtime':\n          return delayUntilRuntimeStage(\n            workUnitStore,\n            makeUntrackedHeaders(workUnitStore.headers)\n          )\n        case 'private-cache':\n          // Private caches are delayed until the runtime stage in use-cache-wrapper,\n          // so we don't need an additional delay here.\n          return makeUntrackedHeaders(workUnitStore.headers)\n        case 'request':\n          trackDynamicDataInDynamicRender(workUnitStore)\n\n          if (process.env.NODE_ENV === 'development') {\n            // Semantically we only need the dev tracking when running in `next dev`\n            // but since you would never use next dev with production NODE_ENV we use this\n            // as a proxy so we can statically exclude this code from production builds.\n            return makeUntrackedHeadersWithDevWarnings(\n              workUnitStore.headers,\n              workStore?.route,\n              workUnitStore\n            )\n          } else {\n            return makeUntrackedHeaders(workUnitStore.headers)\n          }\n          break\n        default:\n          workUnitStore satisfies never\n      }\n    }\n  }\n\n  // If we end up here, there was no work store or work unit store present.\n  throwForMissingRequestStore(callingExpression)\n}\n\ninterface CacheLifetime {}\nconst CachedHeaders = new WeakMap<CacheLifetime, Promise<ReadonlyHeaders>>()\n\nfunction makeHangingHeaders(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(prerenderStore)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeHangingPromise<ReadonlyHeaders>(\n    prerenderStore.renderSignal,\n    workStore.route,\n    '`headers()`'\n  )\n  CachedHeaders.set(prerenderStore, promise)\n\n  return promise\n}\n\nfunction makeUntrackedHeaders(\n  underlyingHeaders: ReadonlyHeaders\n): Promise<ReadonlyHeaders> {\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = Promise.resolve(underlyingHeaders)\n  CachedHeaders.set(underlyingHeaders, promise)\n\n  return promise\n}\n\nfunction makeUntrackedHeadersWithDevWarnings(\n  underlyingHeaders: ReadonlyHeaders,\n  route: string | undefined,\n  requestStore: RequestStore\n): Promise<ReadonlyHeaders> {\n  if (requestStore.asyncApiPromises) {\n    const promise = requestStore.asyncApiPromises.headers\n    return instrumentHeadersPromiseWithDevWarnings(promise, route)\n  }\n\n  const cachedHeaders = CachedHeaders.get(underlyingHeaders)\n  if (cachedHeaders) {\n    return cachedHeaders\n  }\n\n  const promise = makeDevtoolsIOAwarePromise(\n    underlyingHeaders,\n    requestStore,\n    RenderStage.Runtime\n  )\n\n  const proxiedPromise = instrumentHeadersPromiseWithDevWarnings(promise, route)\n\n  CachedHeaders.set(underlyingHeaders, proxiedPromise)\n\n  return proxiedPromise\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createHeadersAccessError\n)\n\nfunction instrumentHeadersPromiseWithDevWarnings(\n  promise: Promise<ReadonlyHeaders>,\n  route: string | undefined\n) {\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: replaceableWarningDescriptorForSymbolIterator(\n      promise,\n      route\n    ),\n    append: replaceableWarningDescriptor(promise, 'append', route),\n    delete: replaceableWarningDescriptor(promise, 'delete', route),\n    get: replaceableWarningDescriptor(promise, 'get', route),\n    has: replaceableWarningDescriptor(promise, 'has', route),\n    set: replaceableWarningDescriptor(promise, 'set', route),\n    getSetCookie: replaceableWarningDescriptor(promise, 'getSetCookie', route),\n    forEach: replaceableWarningDescriptor(promise, 'forEach', route),\n    keys: replaceableWarningDescriptor(promise, 'keys', route),\n    values: replaceableWarningDescriptor(promise, 'values', route),\n    entries: replaceableWarningDescriptor(promise, 'entries', route),\n  })\n  return promise\n}\n\nfunction replaceableWarningDescriptor(\n  target: unknown,\n  prop: string,\n  route: string | undefined\n) {\n  return {\n    enumerable: false,\n    get() {\n      warnForSyncAccess(route, `\\`headers().${prop}\\``)\n      return undefined\n    },\n    set(value: unknown) {\n      Object.defineProperty(target, prop, {\n        value,\n        writable: true,\n        configurable: true,\n      })\n    },\n    configurable: true,\n  }\n}\n\nfunction replaceableWarningDescriptorForSymbolIterator(\n  target: unknown,\n  route: string | undefined\n) {\n  return {\n    enumerable: false,\n    get() {\n      warnForSyncAccess(route, '`...headers()` or similar iteration')\n      return undefined\n    },\n    set(value: unknown) {\n      Object.defineProperty(target, Symbol.iterator, {\n        value,\n        writable: true,\n        enumerable: true,\n        configurable: true,\n      })\n    },\n    configurable: true,\n  }\n}\n\nfunction createHeadersAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`headers()\\` returns a Promise and must be unwrapped with \\`await\\` or \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n"], "names": ["headers", "callingExpression", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "phase", "isRequestAPICallableInsideAfter", "Error", "route", "forceStatic", "underlyingHeaders", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seal", "Headers", "makeUntrackedHeaders", "type", "error", "captureStackTrace", "invalidDynamicUsageError", "dynamicShouldError", "StaticGenBailoutError", "makeHangingHeaders", "exportName", "InvariantError", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "delayUntilRuntimeStage", "trackDynamicDataInDynamicRender", "process", "env", "NODE_ENV", "makeUntrackedHeadersWithDevWarnings", "throwForMissingRequestStore", "CachedHeaders", "WeakMap", "prerenderStore", "cachedHeaders", "get", "promise", "makeHangingPromise", "renderSignal", "set", "Promise", "resolve", "requestStore", "asyncApiPromises", "instrumentHeadersPromiseWithDevWarnings", "makeDevtoolsIOAwarePromise", "RenderStage", "Runtime", "proxiedPromise", "warnForSyncAccess", "createDedupedByCallsiteServerErrorLoggerDev", "createHeadersAccessError", "Object", "defineProperties", "Symbol", "iterator", "replaceableWarningDescriptorForSymbolIterator", "append", "replaceableWarningDescriptor", "delete", "has", "getSetCookie", "for<PERSON>ach", "keys", "values", "entries", "target", "prop", "enumerable", "undefined", "value", "defineProperty", "writable", "configurable", "expression", "prefix"], "mappings": ";;;;+BAuCgBA;;;eAAAA;;;yBApCT;0CAIA;8CAMA;kCAMA;yCAC+B;uCAI/B;0DACqD;uBACZ;gCACjB;iCACH;AAWrB,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAIF,WAAW;QACb,IACEG,iBACAA,cAAcE,KAAK,KAAK,WACxB,CAACC,IAAAA,sCAA+B,KAChC;YACA,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,oPAAoP,CAAC,GAD1Q,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIR,UAAUS,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBC,uBAAc,CAACC,IAAI,CAAC,IAAIC,QAAQ,CAAC;YAC3D,OAAOC,qBAAqBJ;QAC9B;QAEA,IAAIP,eAAe;YACjB,OAAQA,cAAcY,IAAI;gBACxB,KAAK;oBAAS;wBACZ,MAAMC,QAAQ,qBAEb,CAFa,IAAIT,MAChB,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,kVAAkV,CAAC,GADhW,qBAAA;mCAAA;wCAAA;0CAAA;wBAEd;wBACAD,MAAMU,iBAAiB,CAACD,OAAOlB;wBAC/BE,UAAUkB,wBAAwB,KAAKF;wBACvC,MAAMA;oBACR;gBACA,KAAK;oBACH,MAAM,qBAEL,CAFK,IAAIT,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,0XAA0X,CAAC,GADhZ,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH;gBACF;oBACEL;YACJ;QACF;QAEA,IAAIH,UAAUmB,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAEpB,UAAUQ,KAAK,CAAC,mNAAmN,CAAC,GADzO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,eAAe;YACjB,OAAQA,cAAcY,IAAI;gBACxB,KAAK;oBACH,OAAOM,mBAAmBrB,WAAWG;gBACvC,KAAK;oBACH,MAAMmB,aAAa;oBACnB,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,GAAGD,WAAW,0EAA0E,EAAEA,WAAW,+EAA+E,CAAC,GADjL,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;oBACH,qCAAqC;oBACrC,0EAA0E;oBAC1E,iFAAiF;oBACjF,2EAA2E;oBAC3E,OAAOE,IAAAA,sCAAoB,EACzBxB,UAAUQ,KAAK,EACfT,mBACAI,cAAcsB,eAAe;gBAEjC,KAAK;oBACH,mBAAmB;oBACnB,+DAA+D;oBAC/D,uEAAuE;oBACvE,uCAAuC;oBACvC,OAAOC,IAAAA,kDAAgC,EACrC3B,mBACAC,WACAG;gBAEJ,KAAK;oBACH,OAAOwB,IAAAA,wCAAsB,EAC3BxB,eACAW,qBAAqBX,cAAcL,OAAO;gBAE9C,KAAK;oBACH,2EAA2E;oBAC3E,6CAA6C;oBAC7C,OAAOgB,qBAAqBX,cAAcL,OAAO;gBACnD,KAAK;oBACH8B,IAAAA,iDAA+B,EAACzB;oBAEhC,IAAI0B,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;wBAC1C,wEAAwE;wBACxE,8EAA8E;wBAC9E,4EAA4E;wBAC5E,OAAOC,oCACL7B,cAAcL,OAAO,EACrBE,6BAAAA,UAAWQ,KAAK,EAChBL;oBAEJ,OAAO;wBACL,OAAOW,qBAAqBX,cAAcL,OAAO;oBACnD;oBACA;gBACF;oBACEK;YACJ;QACF;IACF;IAEA,yEAAyE;IACzE8B,IAAAA,yDAA2B,EAAClC;AAC9B;AAGA,MAAMmC,gBAAgB,IAAIC;AAE1B,SAASd,mBACPrB,SAAoB,EACpBoC,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUC,IAAAA,yCAAkB,EAChCJ,eAAeK,YAAY,EAC3BzC,UAAUQ,KAAK,EACf;IAEF0B,cAAcQ,GAAG,CAACN,gBAAgBG;IAElC,OAAOA;AACT;AAEA,SAASzB,qBACPJ,iBAAkC;IAElC,MAAM2B,gBAAgBH,cAAcI,GAAG,CAAC5B;IACxC,IAAI2B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUI,QAAQC,OAAO,CAAClC;IAChCwB,cAAcQ,GAAG,CAAChC,mBAAmB6B;IAErC,OAAOA;AACT;AAEA,SAASP,oCACPtB,iBAAkC,EAClCF,KAAyB,EACzBqC,YAA0B;IAE1B,IAAIA,aAAaC,gBAAgB,EAAE;QACjC,MAAMP,UAAUM,aAAaC,gBAAgB,CAAChD,OAAO;QACrD,OAAOiD,wCAAwCR,SAAS/B;IAC1D;IAEA,MAAM6B,gBAAgBH,cAAcI,GAAG,CAAC5B;IACxC,IAAI2B,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUS,IAAAA,iDAA0B,EACxCtC,mBACAmC,cACAI,4BAAW,CAACC,OAAO;IAGrB,MAAMC,iBAAiBJ,wCAAwCR,SAAS/B;IAExE0B,cAAcQ,GAAG,CAAChC,mBAAmByC;IAErC,OAAOA;AACT;AAEA,MAAMC,oBAAoBC,IAAAA,qFAA2C,EACnEC;AAGF,SAASP,wCACPR,OAAiC,EACjC/B,KAAyB;IAEzB+C,OAAOC,gBAAgB,CAACjB,SAAS;QAC/B,CAACkB,OAAOC,QAAQ,CAAC,EAAEC,8CACjBpB,SACA/B;QAEFoD,QAAQC,6BAA6BtB,SAAS,UAAU/B;QACxDsD,QAAQD,6BAA6BtB,SAAS,UAAU/B;QACxD8B,KAAKuB,6BAA6BtB,SAAS,OAAO/B;QAClDuD,KAAKF,6BAA6BtB,SAAS,OAAO/B;QAClDkC,KAAKmB,6BAA6BtB,SAAS,OAAO/B;QAClDwD,cAAcH,6BAA6BtB,SAAS,gBAAgB/B;QACpEyD,SAASJ,6BAA6BtB,SAAS,WAAW/B;QAC1D0D,MAAML,6BAA6BtB,SAAS,QAAQ/B;QACpD2D,QAAQN,6BAA6BtB,SAAS,UAAU/B;QACxD4D,SAASP,6BAA6BtB,SAAS,WAAW/B;IAC5D;IACA,OAAO+B;AACT;AAEA,SAASsB,6BACPQ,MAAe,EACfC,IAAY,EACZ9D,KAAyB;IAEzB,OAAO;QACL+D,YAAY;QACZjC;YACEc,kBAAkB5C,OAAO,CAAC,YAAY,EAAE8D,KAAK,EAAE,CAAC;YAChD,OAAOE;QACT;QACA9B,KAAI+B,KAAc;YAChBlB,OAAOmB,cAAc,CAACL,QAAQC,MAAM;gBAClCG;gBACAE,UAAU;gBACVC,cAAc;YAChB;QACF;QACAA,cAAc;IAChB;AACF;AAEA,SAASjB,8CACPU,MAAe,EACf7D,KAAyB;IAEzB,OAAO;QACL+D,YAAY;QACZjC;YACEc,kBAAkB5C,OAAO;YACzB,OAAOgE;QACT;QACA9B,KAAI+B,KAAc;YAChBlB,OAAOmB,cAAc,CAACL,QAAQZ,OAAOC,QAAQ,EAAE;gBAC7Ce;gBACAE,UAAU;gBACVJ,YAAY;gBACZK,cAAc;YAChB;QACF;QACAA,cAAc;IAChB;AACF;AAEA,SAAStB,yBACP9C,KAAyB,EACzBqE,UAAkB;IAElB,MAAMC,SAAStE,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAID,MACT,GAAGuE,OAAO,KAAK,EAAED,WAAW,EAAE,CAAC,GAC7B,CAAC,yHAAyH,CAAC,GAC3H,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF", "ignoreList": [0]}