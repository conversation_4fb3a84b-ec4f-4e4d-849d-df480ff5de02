{"version": 3, "sources": ["../../../src/server/request/params.ts"], "sourcesContent": ["import {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type { OpaqueFallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  delayUntilRuntimeStage,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type StaticPrerenderStoreModern,\n  type StaticPrerenderStore,\n  throwInvariantForMissingStore,\n  type PrerenderStoreModernRuntime,\n  type RequestStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { dynamicAccessAsyncStorage } from '../app-render/dynamic-access-async-storage.external'\nimport { RenderStage } from '../app-render/staged-rendering'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createParamsFromClient should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createParamsFromClient should not be called in a runtime prerender.'\n        )\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore,\n            workUnitStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerParamsForRoute should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderParams(underlyingParams, workUnitStore)\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore,\n            workUnitStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerParamsForServerSegment should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderParams(underlyingParams, workUnitStore)\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore,\n            workUnitStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params\n): Promise<Params> {\n  const workStore = workAsyncStorage.getStore()\n  if (!workStore) {\n    throw new InvariantError(\n      'Missing workStore in createPrerenderParamsForClientSegment'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams) {\n          for (let key in underlyingParams) {\n            if (fallbackParams.has(key)) {\n              // This params object has one or more fallback params, so we need\n              // to consider the awaiting of this params object \"dynamic\". Since\n              // we are in cacheComponents mode we encode this as a promise that never\n              // resolves.\n              return makeHangingPromise(\n                workUnitStore.renderSignal,\n                workStore.route,\n                '`params`'\n              )\n            }\n          }\n        }\n        break\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createPrerenderParamsForClientSegment should not be called in cache contexts.'\n        )\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'prerender-runtime':\n      case 'request':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createStaticPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<Params> {\n  switch (prerenderStore.type) {\n    case 'prerender':\n    case 'prerender-client': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            // This params object has one or more fallback params, so we need\n            // to consider the awaiting of this params object \"dynamic\". Since\n            // we are in cacheComponents mode we encode this as a promise that never\n            // resolves.\n            return makeHangingParams(\n              underlyingParams,\n              workStore,\n              prerenderStore\n            )\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-ppr': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            return makeErroringParams(\n              underlyingParams,\n              fallbackParams,\n              workStore,\n              prerenderStore\n            )\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-legacy':\n      break\n    default:\n      prerenderStore satisfies never\n  }\n\n  return makeUntrackedParams(underlyingParams)\n}\n\nfunction createRuntimePrerenderParams(\n  underlyingParams: Params,\n  workUnitStore: PrerenderStoreModernRuntime\n): Promise<Params> {\n  return delayUntilRuntimeStage(\n    workUnitStore,\n    makeUntrackedParams(underlyingParams)\n  )\n}\n\nfunction createRenderParamsInProd(underlyingParams: Params): Promise<Params> {\n  return makeUntrackedParams(underlyingParams)\n}\n\nfunction createRenderParamsInDev(\n  underlyingParams: Params,\n  devFallbackParams: OpaqueFallbackRouteParams | null | undefined,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<Params> {\n  let hasFallbackParams = false\n  if (devFallbackParams) {\n    for (let key in underlyingParams) {\n      if (devFallbackParams.has(key)) {\n        hasFallbackParams = true\n        break\n      }\n    }\n  }\n\n  return makeDynamicallyTrackedParamsWithDevWarnings(\n    underlyingParams,\n    hasFallbackParams,\n    workStore,\n    requestStore\n  )\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nconst fallbackParamsProxyHandler: ProxyHandler<Promise<Params>> = {\n  get: function get(target, prop, receiver) {\n    if (prop === 'then' || prop === 'catch' || prop === 'finally') {\n      const originalMethod = ReflectAdapter.get(target, prop, receiver)\n\n      return {\n        [prop]: (...args: unknown[]) => {\n          const store = dynamicAccessAsyncStorage.getStore()\n\n          if (store) {\n            store.abortController.abort(\n              new Error(`Accessed fallback \\`params\\` during prerendering.`)\n            )\n          }\n\n          return new Proxy(\n            originalMethod.apply(target, args),\n            fallbackParamsProxyHandler\n          )\n        },\n      }[prop]\n    }\n\n    return ReflectAdapter.get(target, prop, receiver)\n  },\n}\n\nfunction makeHangingParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = new Proxy(\n    makeHangingPromise<Params>(\n      prerenderStore.renderSignal,\n      workStore.route,\n      '`params`'\n    ),\n    fallbackParamsProxyHandler\n  )\n\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeErroringParams(\n  underlyingParams: Params,\n  fallbackParams: OpaqueFallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when cacheComponents is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no cacheComponents)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedParamsWithDevWarnings(\n  underlyingParams: Params,\n  hasFallbackParams: boolean,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<Params> {\n  if (requestStore.asyncApiPromises && hasFallbackParams) {\n    // We wrap each instance of params in a `new Promise()`, because deduping\n    // them across requests doesn't work anyway and this let us show each\n    // await a different set of values. This is important when all awaits\n    // are in third party which would otherwise track all the way to the\n    // internal params.\n    const sharedParamsParent = requestStore.asyncApiPromises.sharedParamsParent\n    const promise: Promise<Params> = new Promise((resolve, reject) => {\n      sharedParamsParent.then(() => resolve(underlyingParams), reject)\n    })\n    // @ts-expect-error\n    promise.displayName = 'params'\n    return instrumentParamsPromiseWithDevWarnings(\n      underlyingParams,\n      promise,\n      workStore\n    )\n  }\n\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = hasFallbackParams\n    ? makeDevtoolsIOAwarePromise(\n        underlyingParams,\n        requestStore,\n        RenderStage.Runtime\n      )\n    : // We don't want to force an environment transition when this params is not part of the fallback params set\n      Promise.resolve(underlyingParams)\n\n  const proxiedPromise = instrumentParamsPromiseWithDevWarnings(\n    underlyingParams,\n    promise,\n    workStore\n  )\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction instrumentParamsPromiseWithDevWarnings(\n  underlyingParams: Params,\n  promise: Promise<Params>,\n  workStore: WorkStore\n): Promise<Params> {\n  // Track which properties we should warn for.\n  const proxiedProperties = new Set<string>()\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  return new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(workStore.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      warnForSyncAccess(workStore.route, expression)\n      return Reflect.ownKeys(target)\n    },\n  })\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` is a Promise and must be unwrapped with \\`await\\` or \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n"], "names": ["createParamsFromClient", "createPrerenderParamsForClientSegment", "createServerParamsForMetadata", "createServerParamsForRoute", "createServerParamsForServerSegment", "underlyingParams", "workStore", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "createStaticPrerenderParams", "InvariantError", "process", "env", "NODE_ENV", "devFallbackParams", "createRenderParamsInDev", "createRenderParamsInProd", "throwInvariantForMissingStore", "createRuntimePrerenderParams", "workAsyncStorage", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "key", "has", "makeHangingPromise", "renderSignal", "route", "Promise", "resolve", "prerenderStore", "makeHangingParams", "makeErroringParams", "makeUntrackedParams", "delayUntilRuntimeStage", "requestStore", "hasFallbackParams", "makeDynamicallyTrackedParamsWithDevWarnings", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "fallbackParamsProxyHandler", "get", "target", "prop", "receiver", "originalMethod", "ReflectAdapter", "args", "store", "dynamicAccessAsyncStorage", "abortController", "abort", "Error", "Proxy", "apply", "cachedParams", "promise", "set", "augmentedUnderlying", "Object", "keys", "for<PERSON>ach", "wellKnownProperties", "defineProperty", "expression", "describeStringPropertyAccess", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "enumerable", "asyncApiPromises", "sharedParamsParent", "reject", "then", "displayName", "instrumentParamsPromiseWithDevWarnings", "makeDevtoolsIOAwarePromise", "RenderStage", "Runtime", "proxiedPromise", "proxiedProperties", "Set", "add", "warnForSyncAccess", "value", "delete", "ownKeys", "Reflect", "createDedupedByCallsiteServerErrorLoggerDev", "createParamsAccessError", "prefix"], "mappings": ";;;;;;;;;;;;;;;;;;IAuCgBA,sBAAsB;eAAtBA;;IAiJAC,qCAAqC;eAArCA;;IA/FHC,6BAA6B;eAA7BA;;IAGGC,0BAA0B;eAA1BA;;IA8CAC,kCAAkC;eAAlCA;;;0CAvIT;yBAGwB;kCAKxB;8CAWA;gCACwB;8BAIxB;uCAIA;0DACqD;mDAClB;iCACd;AAKrB,SAASJ,uBACdK,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BACLN,kBACAC,WACAC;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIK,8BAAc,CACtB,mEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIA,8BAAc,CACtB,wEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;oBAC1C,wEAAwE;oBACxE,8EAA8E;oBAC9E,4EAA4E;oBAC5E,MAAMC,oBAAoBT,cAAcS,iBAAiB;oBACzD,OAAOC,wBACLZ,kBACAW,mBACAV,WACAC;gBAEJ,OAAO;oBACL,OAAOW,yBAAyBb;gBAClC;YACF;gBACEE;QACJ;IACF;IACAY,IAAAA,2DAA6B;AAC/B;AAIO,MAAMjB,gCAAgCE;AAGtC,SAASD,2BACdE,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BACLN,kBACAC,WACAC;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIK,8BAAc,CACtB,uEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOQ,6BAA6Bf,kBAAkBE;YACxD,KAAK;gBACH,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;oBAC1C,wEAAwE;oBACxE,8EAA8E;oBAC9E,4EAA4E;oBAC5E,MAAMC,oBAAoBT,cAAcS,iBAAiB;oBACzD,OAAOC,wBACLZ,kBACAW,mBACAV,WACAC;gBAEJ,OAAO;oBACL,OAAOW,yBAAyBb;gBAClC;YACF;gBACEE;QACJ;IACF;IACAY,IAAAA,2DAA6B;AAC/B;AAEO,SAASf,mCACdC,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BACLN,kBACAC,WACAC;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIK,8BAAc,CACtB,+EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOQ,6BAA6Bf,kBAAkBE;YACxD,KAAK;gBACH,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;oBAC1C,wEAAwE;oBACxE,8EAA8E;oBAC9E,4EAA4E;oBAC5E,MAAMC,oBAAoBT,cAAcS,iBAAiB;oBACzD,OAAOC,wBACLZ,kBACAW,mBACAV,WACAC;gBAEJ,OAAO;oBACL,OAAOW,yBAAyBb;gBAClC;YACF;gBACEE;QACJ;IACF;IACAY,IAAAA,2DAA6B;AAC/B;AAEO,SAASlB,sCACdI,gBAAwB;IAExB,MAAMC,YAAYe,0CAAgB,CAACZ,QAAQ;IAC3C,IAAI,CAACH,WAAW;QACd,MAAM,qBAEL,CAFK,IAAIM,8BAAc,CACtB,+DADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAML,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,MAAMY,iBAAiBf,cAAcgB,mBAAmB;gBACxD,IAAID,gBAAgB;oBAClB,IAAK,IAAIE,OAAOnB,iBAAkB;wBAChC,IAAIiB,eAAeG,GAAG,CAACD,MAAM;4BAC3B,iEAAiE;4BACjE,kEAAkE;4BAClE,wEAAwE;4BACxE,YAAY;4BACZ,OAAOE,IAAAA,yCAAkB,EACvBnB,cAAcoB,YAAY,EAC1BrB,UAAUsB,KAAK,EACf;wBAEJ;oBACF;gBACF;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,qBAEL,CAFK,IAAIhB,8BAAc,CACtB,kFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEL;QACJ;IACF;IACA,mFAAmF;IACnF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOsB,QAAQC,OAAO,CAACzB;AACzB;AAEA,SAASM,4BACPN,gBAAwB,EACxBC,SAAoB,EACpByB,cAAoC;IAEpC,OAAQA,eAAerB,IAAI;QACzB,KAAK;QACL,KAAK;YAAoB;gBACvB,MAAMY,iBAAiBS,eAAeR,mBAAmB;gBACzD,IAAID,gBAAgB;oBAClB,IAAK,MAAME,OAAOnB,iBAAkB;wBAClC,IAAIiB,eAAeG,GAAG,CAACD,MAAM;4BAC3B,iEAAiE;4BACjE,kEAAkE;4BAClE,wEAAwE;4BACxE,YAAY;4BACZ,OAAOQ,kBACL3B,kBACAC,WACAyB;wBAEJ;oBACF;gBACF;gBACA;YACF;QACA,KAAK;YAAiB;gBACpB,MAAMT,iBAAiBS,eAAeR,mBAAmB;gBACzD,IAAID,gBAAgB;oBAClB,IAAK,MAAME,OAAOnB,iBAAkB;wBAClC,IAAIiB,eAAeG,GAAG,CAACD,MAAM;4BAC3B,OAAOS,mBACL5B,kBACAiB,gBACAhB,WACAyB;wBAEJ;oBACF;gBACF;gBACA;YACF;QACA,KAAK;YACH;QACF;YACEA;IACJ;IAEA,OAAOG,oBAAoB7B;AAC7B;AAEA,SAASe,6BACPf,gBAAwB,EACxBE,aAA0C;IAE1C,OAAO4B,IAAAA,wCAAsB,EAC3B5B,eACA2B,oBAAoB7B;AAExB;AAEA,SAASa,yBAAyBb,gBAAwB;IACxD,OAAO6B,oBAAoB7B;AAC7B;AAEA,SAASY,wBACPZ,gBAAwB,EACxBW,iBAA+D,EAC/DV,SAAoB,EACpB8B,YAA0B;IAE1B,IAAIC,oBAAoB;IACxB,IAAIrB,mBAAmB;QACrB,IAAK,IAAIQ,OAAOnB,iBAAkB;YAChC,IAAIW,kBAAkBS,GAAG,CAACD,MAAM;gBAC9Ba,oBAAoB;gBACpB;YACF;QACF;IACF;IAEA,OAAOC,4CACLjC,kBACAgC,mBACA/B,WACA8B;AAEJ;AAGA,MAAMG,eAAe,IAAIC;AAEzB,MAAMC,6BAA4D;IAChEC,KAAK,SAASA,IAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;QACtC,IAAID,SAAS,UAAUA,SAAS,WAAWA,SAAS,WAAW;YAC7D,MAAME,iBAAiBC,uBAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;YAExD,OAAO,CAAA;gBACL,CAACD,KAAK,EAAE,CAAC,GAAGI;oBACV,MAAMC,QAAQC,4DAAyB,CAACzC,QAAQ;oBAEhD,IAAIwC,OAAO;wBACTA,MAAME,eAAe,CAACC,KAAK,CACzB,qBAA8D,CAA9D,IAAIC,MAAM,CAAC,iDAAiD,CAAC,GAA7D,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6D;oBAEjE;oBAEA,OAAO,IAAIC,MACTR,eAAeS,KAAK,CAACZ,QAAQK,OAC7BP;gBAEJ;YACF,CAAA,CAAC,CAACG,KAAK;QACT;QAEA,OAAOG,uBAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;IAC1C;AACF;AAEA,SAASb,kBACP3B,gBAAwB,EACxBC,SAAoB,EACpByB,cAA0C;IAE1C,MAAMyB,eAAejB,aAAaG,GAAG,CAACrC;IACtC,IAAImD,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMC,UAAU,IAAIH,MAClB5B,IAAAA,yCAAkB,EAChBK,eAAeJ,YAAY,EAC3BrB,UAAUsB,KAAK,EACf,aAEFa;IAGFF,aAAamB,GAAG,CAACrD,kBAAkBoD;IAEnC,OAAOA;AACT;AAEA,SAASxB,mBACP5B,gBAAwB,EACxBiB,cAAyC,EACzChB,SAAoB,EACpByB,cAAwD;IAExD,MAAMyB,eAAejB,aAAaG,GAAG,CAACrC;IACtC,IAAImD,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMG,sBAAsB;QAAE,GAAGtD,gBAAgB;IAAC;IAElD,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMoD,UAAU5B,QAAQC,OAAO,CAAC6B;IAChCpB,aAAamB,GAAG,CAACrD,kBAAkBoD;IAEnCG,OAAOC,IAAI,CAACxD,kBAAkByD,OAAO,CAAC,CAAClB;QACrC,IAAImB,iCAAmB,CAACtC,GAAG,CAACmB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL,IAAItB,eAAeG,GAAG,CAACmB,OAAO;gBAC5BgB,OAAOI,cAAc,CAACL,qBAAqBf,MAAM;oBAC/CF;wBACE,MAAMuB,aAAaC,IAAAA,0CAA4B,EAAC,UAAUtB;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,2EAA2E;wBAC3E,iCAAiC;wBACjC,IAAIb,eAAerB,IAAI,KAAK,iBAAiB;4BAC3C,qCAAqC;4BACrCyD,IAAAA,sCAAoB,EAClB7D,UAAUsB,KAAK,EACfqC,YACAlC,eAAeqC,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,IAAAA,kDAAgC,EAC9BJ,YACA3D,WACAyB;wBAEJ;oBACF;oBACAuC,YAAY;gBACd;YACF;QACF;IACF;IAEA,OAAOb;AACT;AAEA,SAASvB,oBAAoB7B,gBAAwB;IACnD,MAAMmD,eAAejB,aAAaG,GAAG,CAACrC;IACtC,IAAImD,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMC,UAAU5B,QAAQC,OAAO,CAACzB;IAChCkC,aAAamB,GAAG,CAACrD,kBAAkBoD;IAEnC,OAAOA;AACT;AAEA,SAASnB,4CACPjC,gBAAwB,EACxBgC,iBAA0B,EAC1B/B,SAAoB,EACpB8B,YAA0B;IAE1B,IAAIA,aAAamC,gBAAgB,IAAIlC,mBAAmB;QACtD,yEAAyE;QACzE,qEAAqE;QACrE,qEAAqE;QACrE,oEAAoE;QACpE,mBAAmB;QACnB,MAAMmC,qBAAqBpC,aAAamC,gBAAgB,CAACC,kBAAkB;QAC3E,MAAMf,UAA2B,IAAI5B,QAAQ,CAACC,SAAS2C;YACrDD,mBAAmBE,IAAI,CAAC,IAAM5C,QAAQzB,mBAAmBoE;QAC3D;QACA,mBAAmB;QACnBhB,QAAQkB,WAAW,GAAG;QACtB,OAAOC,uCACLvE,kBACAoD,SACAnD;IAEJ;IAEA,MAAMkD,eAAejB,aAAaG,GAAG,CAACrC;IACtC,IAAImD,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMC,UAAUpB,oBACZwC,IAAAA,iDAA0B,EACxBxE,kBACA+B,cACA0C,4BAAW,CAACC,OAAO,IAGrBlD,QAAQC,OAAO,CAACzB;IAEpB,MAAM2E,iBAAiBJ,uCACrBvE,kBACAoD,SACAnD;IAEFiC,aAAamB,GAAG,CAACrD,kBAAkB2E;IACnC,OAAOA;AACT;AAEA,SAASJ,uCACPvE,gBAAwB,EACxBoD,OAAwB,EACxBnD,SAAoB;IAEpB,6CAA6C;IAC7C,MAAM2E,oBAAoB,IAAIC;IAE9BtB,OAAOC,IAAI,CAACxD,kBAAkByD,OAAO,CAAC,CAAClB;QACrC,IAAImB,iCAAmB,CAACtC,GAAG,CAACmB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLqC,kBAAkBE,GAAG,CAACvC;QACxB;IACF;IAEA,OAAO,IAAIU,MAAMG,SAAS;QACxBf,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,uEAAuE;gBACvEqC,kBAAkBxD,GAAG,CAACmB,OACtB;oBACA,MAAMqB,aAAaC,IAAAA,0CAA4B,EAAC,UAAUtB;oBAC1DwC,kBAAkB9E,UAAUsB,KAAK,EAAEqC;gBACrC;YACF;YACA,OAAOlB,uBAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;QAC1C;QACAa,KAAIf,MAAM,EAAEC,IAAI,EAAEyC,KAAK,EAAExC,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5BqC,kBAAkBK,MAAM,CAAC1C;YAC3B;YACA,OAAOG,uBAAc,CAACW,GAAG,CAACf,QAAQC,MAAMyC,OAAOxC;QACjD;QACA0C,SAAQ5C,MAAM;YACZ,MAAMsB,aAAa;YACnBmB,kBAAkB9E,UAAUsB,KAAK,EAAEqC;YACnC,OAAOuB,QAAQD,OAAO,CAAC5C;QACzB;IACF;AACF;AAEA,MAAMyC,oBAAoBK,IAAAA,qFAA2C,EACnEC;AAGF,SAASA,wBACP9D,KAAyB,EACzBqC,UAAkB;IAElB,MAAM0B,SAAS/D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAIyB,MACT,GAAGsC,OAAO,KAAK,EAAE1B,WAAW,EAAE,CAAC,GAC7B,CAAC,iHAAiH,CAAC,GACnH,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF", "ignoreList": [0]}