{"version": 3, "sources": ["../../../src/server/request/cookies.ts"], "sourcesContent": ["import {\n  type ReadonlyRequestCookies,\n  areCookiesMutableInCurrentPhase,\n  RequestCookiesAdapter,\n} from '../web/spec-extension/adapters/request-cookies'\nimport { RequestCookies } from '../web/spec-extension/cookies'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport {\n  throwForMissingRequestStore,\n  workUnitAsyncStorage,\n  type PrerenderStoreModern,\n  type RequestStore,\n} from '../app-render/work-unit-async-storage.external'\nimport {\n  delayUntilRuntimeStage,\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { isRequestAPICallableInsideAfter } from './utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { RenderStage } from '../app-render/staged-rendering'\n\nexport function cookies(): Promise<ReadonlyRequestCookies> {\n  const callingExpression = 'cookies'\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        // TODO(after): clarify that this only applies to pages?\n        `Route ${workStore.route} used \\`cookies()\\` inside \\`after()\\`. This is not supported. If you need this data inside an \\`after()\\` callback, use \\`cookies()\\` outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // cookies object without tracking\n      const underlyingCookies = createEmptyCookies()\n      return makeUntrackedCookies(underlyingCookies)\n    }\n\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`cookies()\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      switch (workUnitStore.type) {\n        case 'cache':\n          const error = new Error(\n            `Route ${workStore.route} used \\`cookies()\\` inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \\`cookies()\\` outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n          )\n          Error.captureStackTrace(error, cookies)\n          workStore.invalidDynamicUsageError ??= error\n          throw error\n        case 'unstable-cache':\n          throw new Error(\n            `Route ${workStore.route} used \\`cookies()\\` inside a function cached with \\`unstable_cache()\\`. Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \\`cookies()\\` outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n          )\n        case 'prerender':\n          return makeHangingCookies(workStore, workUnitStore)\n        case 'prerender-client':\n          const exportName = '`cookies`'\n          throw new InvariantError(\n            `${exportName} must not be used within a Client Component. Next.js should be preventing ${exportName} from being included in Client Components statically, but did not in this case.`\n          )\n        case 'prerender-ppr':\n          // We need track dynamic access here eagerly to keep continuity with\n          // how cookies has worked in PPR without cacheComponents.\n          return postponeWithTracking(\n            workStore.route,\n            callingExpression,\n            workUnitStore.dynamicTracking\n          )\n        case 'prerender-legacy':\n          // We track dynamic access here so we don't need to wrap the cookies\n          // in individual property access tracking.\n          return throwToInterruptStaticGeneration(\n            callingExpression,\n            workStore,\n            workUnitStore\n          )\n        case 'prerender-runtime':\n          return delayUntilRuntimeStage(\n            workUnitStore,\n            makeUntrackedCookies(workUnitStore.cookies)\n          )\n        case 'private-cache':\n          // Private caches are delayed until the runtime stage in use-cache-wrapper,\n          // so we don't need an additional delay here.\n          return makeUntrackedCookies(workUnitStore.cookies)\n        case 'request':\n          trackDynamicDataInDynamicRender(workUnitStore)\n\n          let underlyingCookies: ReadonlyRequestCookies\n\n          if (areCookiesMutableInCurrentPhase(workUnitStore)) {\n            // We can't conditionally return different types here based on the context.\n            // To avoid confusion, we always return the readonly type here.\n            underlyingCookies =\n              workUnitStore.userspaceMutableCookies as unknown as ReadonlyRequestCookies\n          } else {\n            underlyingCookies = workUnitStore.cookies\n          }\n\n          if (process.env.NODE_ENV === 'development') {\n            // Semantically we only need the dev tracking when running in `next dev`\n            // but since you would never use next dev with production NODE_ENV we use this\n            // as a proxy so we can statically exclude this code from production builds.\n            return makeUntrackedCookiesWithDevWarnings(\n              workUnitStore,\n              underlyingCookies,\n              workStore?.route\n            )\n          } else {\n            return makeUntrackedCookies(underlyingCookies)\n          }\n        default:\n          workUnitStore satisfies never\n      }\n    }\n  }\n\n  // If we end up here, there was no work store or work unit store present.\n  throwForMissingRequestStore(callingExpression)\n}\n\nfunction createEmptyCookies(): ReadonlyRequestCookies {\n  return RequestCookiesAdapter.seal(new RequestCookies(new Headers({})))\n}\n\ninterface CacheLifetime {}\nconst CachedCookies = new WeakMap<\n  CacheLifetime,\n  Promise<ReadonlyRequestCookies>\n>()\n\nfunction makeHangingCookies(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreModern\n): Promise<ReadonlyRequestCookies> {\n  const cachedPromise = CachedCookies.get(prerenderStore)\n  if (cachedPromise) {\n    return cachedPromise\n  }\n\n  const promise = makeHangingPromise<ReadonlyRequestCookies>(\n    prerenderStore.renderSignal,\n    workStore.route,\n    '`cookies()`'\n  )\n  CachedCookies.set(prerenderStore, promise)\n\n  return promise\n}\n\nfunction makeUntrackedCookies(\n  underlyingCookies: ReadonlyRequestCookies\n): Promise<ReadonlyRequestCookies> {\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = Promise.resolve(underlyingCookies)\n  CachedCookies.set(underlyingCookies, promise)\n\n  return promise\n}\n\nfunction makeUntrackedCookiesWithDevWarnings(\n  requestStore: RequestStore,\n  underlyingCookies: ReadonlyRequestCookies,\n  route?: string\n): Promise<ReadonlyRequestCookies> {\n  if (requestStore.asyncApiPromises) {\n    let promise: Promise<ReadonlyRequestCookies>\n    if (underlyingCookies === requestStore.mutableCookies) {\n      promise = requestStore.asyncApiPromises.mutableCookies\n    } else if (underlyingCookies === requestStore.cookies) {\n      promise = requestStore.asyncApiPromises.cookies\n    } else {\n      throw new InvariantError(\n        'Received an underlying cookies object that does not match either `cookies` or `mutableCookies`'\n      )\n    }\n    return instrumentCookiesPromiseWithDevWarnings(promise, route)\n  }\n\n  const cachedCookies = CachedCookies.get(underlyingCookies)\n  if (cachedCookies) {\n    return cachedCookies\n  }\n\n  const promise = makeDevtoolsIOAwarePromise(\n    underlyingCookies,\n    requestStore,\n    RenderStage.Runtime\n  )\n\n  const proxiedPromise = instrumentCookiesPromiseWithDevWarnings(promise, route)\n\n  CachedCookies.set(underlyingCookies, proxiedPromise)\n\n  return proxiedPromise\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createCookiesAccessError\n)\n\nfunction instrumentCookiesPromiseWithDevWarnings(\n  promise: Promise<ReadonlyRequestCookies>,\n  route: string | undefined\n) {\n  Object.defineProperties(promise, {\n    [Symbol.iterator]: replaceableWarningDescriptorForSymbolIterator(\n      promise,\n      route\n    ),\n    size: replaceableWarningDescriptor(promise, 'size', route),\n    get: replaceableWarningDescriptor(promise, 'get', route),\n    getAll: replaceableWarningDescriptor(promise, 'getAll', route),\n    has: replaceableWarningDescriptor(promise, 'has', route),\n    set: replaceableWarningDescriptor(promise, 'set', route),\n    delete: replaceableWarningDescriptor(promise, 'delete', route),\n    clear: replaceableWarningDescriptor(promise, 'clear', route),\n    toString: replaceableWarningDescriptor(promise, 'toString', route),\n  })\n  return promise\n}\n\nfunction replaceableWarningDescriptor(\n  target: unknown,\n  prop: string,\n  route: string | undefined\n) {\n  return {\n    enumerable: false,\n    get() {\n      warnForSyncAccess(route, `\\`cookies().${prop}\\``)\n      return undefined\n    },\n    set(value: unknown) {\n      Object.defineProperty(target, prop, {\n        value,\n        writable: true,\n        configurable: true,\n      })\n    },\n    configurable: true,\n  }\n}\n\nfunction replaceableWarningDescriptorForSymbolIterator(\n  target: unknown,\n  route: string | undefined\n) {\n  return {\n    enumerable: false,\n    get() {\n      warnForSyncAccess(route, '`...cookies()` or similar iteration')\n      return undefined\n    },\n    set(value: unknown) {\n      Object.defineProperty(target, Symbol.iterator, {\n        value,\n        writable: true,\n        enumerable: true,\n        configurable: true,\n      })\n    },\n    configurable: true,\n  }\n}\n\nfunction createCookiesAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`cookies()\\` returns a Promise and must be unwrapped with \\`await\\` or \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n"], "names": ["cookies", "callingExpression", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "phase", "isRequestAPICallableInsideAfter", "Error", "route", "forceStatic", "underlyingCookies", "createEmptyCookies", "makeUntrackedCookies", "dynamicShouldError", "StaticGenBailoutError", "type", "error", "captureStackTrace", "invalidDynamicUsageError", "makeHangingCookies", "exportName", "InvariantError", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "delayUntilRuntimeStage", "trackDynamicDataInDynamicRender", "areCookiesMutableInCurrentPhase", "userspaceMutableCookies", "process", "env", "NODE_ENV", "makeUntrackedCookiesWithDevWarnings", "throwForMissingRequestStore", "RequestCookiesAdapter", "seal", "RequestCookies", "Headers", "CachedCookies", "WeakMap", "prerenderStore", "cachedPromise", "get", "promise", "makeHangingPromise", "renderSignal", "set", "cachedCookies", "Promise", "resolve", "requestStore", "asyncApiPromises", "mutableCookies", "instrumentCookiesPromiseWithDevWarnings", "makeDevtoolsIOAwarePromise", "RenderStage", "Runtime", "proxiedPromise", "warnForSyncAccess", "createDedupedByCallsiteServerErrorLoggerDev", "createCookiesAccessError", "Object", "defineProperties", "Symbol", "iterator", "replaceableWarningDescriptorForSymbolIterator", "size", "replaceableWarningDescriptor", "getAll", "has", "delete", "clear", "toString", "target", "prop", "enumerable", "undefined", "value", "defineProperty", "writable", "configurable", "expression", "prefix"], "mappings": ";;;;+BAgCgBA;;;eAAAA;;;gCA5BT;yBACwB;0CAIxB;8CAMA;kCAMA;yCAC+B;uCAI/B;0DACqD;uBACZ;gCACjB;iCACH;AAErB,SAASA;IACd,MAAMC,oBAAoB;IAC1B,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAIF,WAAW;QACb,IACEG,iBACAA,cAAcE,KAAK,KAAK,WACxB,CAACC,IAAAA,sCAA+B,KAChC;YACA,MAAM,qBAGL,CAHK,IAAIC,MACR,wDAAwD;YACxD,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,oPAAoP,CAAC,GAF1Q,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAIR,UAAUS,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,MAAMC,oBAAoBC;YAC1B,OAAOC,qBAAqBF;QAC9B;QAEA,IAAIV,UAAUa,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAEd,UAAUQ,KAAK,CAAC,mNAAmN,CAAC,GADzO,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,eAAe;YACjB,OAAQA,cAAcY,IAAI;gBACxB,KAAK;oBACH,MAAMC,QAAQ,qBAEb,CAFa,IAAIT,MAChB,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,kVAAkV,CAAC,GADhW,qBAAA;+BAAA;oCAAA;sCAAA;oBAEd;oBACAD,MAAMU,iBAAiB,CAACD,OAAOlB;oBAC/BE,UAAUkB,wBAAwB,KAAKF;oBACvC,MAAMA;gBACR,KAAK;oBACH,MAAM,qBAEL,CAFK,IAAIT,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,0XAA0X,CAAC,GADhZ,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;oBACH,OAAOW,mBAAmBnB,WAAWG;gBACvC,KAAK;oBACH,MAAMiB,aAAa;oBACnB,MAAM,qBAEL,CAFK,IAAIC,8BAAc,CACtB,GAAGD,WAAW,0EAA0E,EAAEA,WAAW,+EAA+E,CAAC,GADjL,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,KAAK;oBACH,oEAAoE;oBACpE,yDAAyD;oBACzD,OAAOE,IAAAA,sCAAoB,EACzBtB,UAAUQ,KAAK,EACfT,mBACAI,cAAcoB,eAAe;gBAEjC,KAAK;oBACH,oEAAoE;oBACpE,0CAA0C;oBAC1C,OAAOC,IAAAA,kDAAgC,EACrCzB,mBACAC,WACAG;gBAEJ,KAAK;oBACH,OAAOsB,IAAAA,wCAAsB,EAC3BtB,eACAS,qBAAqBT,cAAcL,OAAO;gBAE9C,KAAK;oBACH,2EAA2E;oBAC3E,6CAA6C;oBAC7C,OAAOc,qBAAqBT,cAAcL,OAAO;gBACnD,KAAK;oBACH4B,IAAAA,iDAA+B,EAACvB;oBAEhC,IAAIO;oBAEJ,IAAIiB,IAAAA,+CAA+B,EAACxB,gBAAgB;wBAClD,2EAA2E;wBAC3E,+DAA+D;wBAC/DO,oBACEP,cAAcyB,uBAAuB;oBACzC,OAAO;wBACLlB,oBAAoBP,cAAcL,OAAO;oBAC3C;oBAEA,IAAI+B,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;wBAC1C,wEAAwE;wBACxE,8EAA8E;wBAC9E,4EAA4E;wBAC5E,OAAOC,oCACL7B,eACAO,mBACAV,6BAAAA,UAAWQ,KAAK;oBAEpB,OAAO;wBACL,OAAOI,qBAAqBF;oBAC9B;gBACF;oBACEP;YACJ;QACF;IACF;IAEA,yEAAyE;IACzE8B,IAAAA,yDAA2B,EAAClC;AAC9B;AAEA,SAASY;IACP,OAAOuB,qCAAqB,CAACC,IAAI,CAAC,IAAIC,uBAAc,CAAC,IAAIC,QAAQ,CAAC;AACpE;AAGA,MAAMC,gBAAgB,IAAIC;AAK1B,SAASpB,mBACPnB,SAAoB,EACpBwC,cAAoC;IAEpC,MAAMC,gBAAgBH,cAAcI,GAAG,CAACF;IACxC,IAAIC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAME,UAAUC,IAAAA,yCAAkB,EAChCJ,eAAeK,YAAY,EAC3B7C,UAAUQ,KAAK,EACf;IAEF8B,cAAcQ,GAAG,CAACN,gBAAgBG;IAElC,OAAOA;AACT;AAEA,SAAS/B,qBACPF,iBAAyC;IAEzC,MAAMqC,gBAAgBT,cAAcI,GAAG,CAAChC;IACxC,IAAIqC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMJ,UAAUK,QAAQC,OAAO,CAACvC;IAChC4B,cAAcQ,GAAG,CAACpC,mBAAmBiC;IAErC,OAAOA;AACT;AAEA,SAASX,oCACPkB,YAA0B,EAC1BxC,iBAAyC,EACzCF,KAAc;IAEd,IAAI0C,aAAaC,gBAAgB,EAAE;QACjC,IAAIR;QACJ,IAAIjC,sBAAsBwC,aAAaE,cAAc,EAAE;YACrDT,UAAUO,aAAaC,gBAAgB,CAACC,cAAc;QACxD,OAAO,IAAI1C,sBAAsBwC,aAAapD,OAAO,EAAE;YACrD6C,UAAUO,aAAaC,gBAAgB,CAACrD,OAAO;QACjD,OAAO;YACL,MAAM,qBAEL,CAFK,IAAIuB,8BAAc,CACtB,mGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,OAAOgC,wCAAwCV,SAASnC;IAC1D;IAEA,MAAMuC,gBAAgBT,cAAcI,GAAG,CAAChC;IACxC,IAAIqC,eAAe;QACjB,OAAOA;IACT;IAEA,MAAMJ,UAAUW,IAAAA,iDAA0B,EACxC5C,mBACAwC,cACAK,4BAAW,CAACC,OAAO;IAGrB,MAAMC,iBAAiBJ,wCAAwCV,SAASnC;IAExE8B,cAAcQ,GAAG,CAACpC,mBAAmB+C;IAErC,OAAOA;AACT;AAEA,MAAMC,oBAAoBC,IAAAA,qFAA2C,EACnEC;AAGF,SAASP,wCACPV,OAAwC,EACxCnC,KAAyB;IAEzBqD,OAAOC,gBAAgB,CAACnB,SAAS;QAC/B,CAACoB,OAAOC,QAAQ,CAAC,EAAEC,8CACjBtB,SACAnC;QAEF0D,MAAMC,6BAA6BxB,SAAS,QAAQnC;QACpDkC,KAAKyB,6BAA6BxB,SAAS,OAAOnC;QAClD4D,QAAQD,6BAA6BxB,SAAS,UAAUnC;QACxD6D,KAAKF,6BAA6BxB,SAAS,OAAOnC;QAClDsC,KAAKqB,6BAA6BxB,SAAS,OAAOnC;QAClD8D,QAAQH,6BAA6BxB,SAAS,UAAUnC;QACxD+D,OAAOJ,6BAA6BxB,SAAS,SAASnC;QACtDgE,UAAUL,6BAA6BxB,SAAS,YAAYnC;IAC9D;IACA,OAAOmC;AACT;AAEA,SAASwB,6BACPM,MAAe,EACfC,IAAY,EACZlE,KAAyB;IAEzB,OAAO;QACLmE,YAAY;QACZjC;YACEgB,kBAAkBlD,OAAO,CAAC,YAAY,EAAEkE,KAAK,EAAE,CAAC;YAChD,OAAOE;QACT;QACA9B,KAAI+B,KAAc;YAChBhB,OAAOiB,cAAc,CAACL,QAAQC,MAAM;gBAClCG;gBACAE,UAAU;gBACVC,cAAc;YAChB;QACF;QACAA,cAAc;IAChB;AACF;AAEA,SAASf,8CACPQ,MAAe,EACfjE,KAAyB;IAEzB,OAAO;QACLmE,YAAY;QACZjC;YACEgB,kBAAkBlD,OAAO;YACzB,OAAOoE;QACT;QACA9B,KAAI+B,KAAc;YAChBhB,OAAOiB,cAAc,CAACL,QAAQV,OAAOC,QAAQ,EAAE;gBAC7Ca;gBACAE,UAAU;gBACVJ,YAAY;gBACZK,cAAc;YAChB;QACF;QACAA,cAAc;IAChB;AACF;AAEA,SAASpB,yBACPpD,KAAyB,EACzByE,UAAkB;IAElB,MAAMC,SAAS1E,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,qBAIN,CAJM,IAAID,MACT,GAAG2E,OAAO,KAAK,EAAED,WAAW,EAAE,CAAC,GAC7B,CAAC,yHAAyH,CAAC,GAC3H,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF", "ignoreList": [0]}