{"version": 3, "sources": ["../../../src/next-devtools/shared/webpack-module-path.ts"], "sourcesContent": ["const replacementRegExes = [\n  /^webpack-internal:\\/\\/\\/(\\([\\w-]+\\)\\/)?/,\n  /^(webpack:\\/\\/\\/|webpack:\\/\\/(_N_E\\/)?)(\\([\\w-]+\\)\\/)?/,\n]\n\nexport function isWebpackInternalResource(file: string) {\n  for (const regex of replacementRegExes) {\n    if (regex.test(file)) return true\n\n    file = file.replace(regex, '')\n  }\n\n  return false\n}\n\n/**\n * Format the webpack internal id to original file path\n *\n * webpack-internal:///./src/hello.tsx => ./src/hello.tsx\n * webpack://_N_E/./src/hello.tsx => ./src/hello.tsx\n * webpack://./src/hello.tsx => ./src/hello.tsx\n * webpack:///./src/hello.tsx => ./src/hello.tsx\n */\nexport function formatFrameSourceFile(file: string) {\n  for (const regex of replacementRegExes) {\n    file = file.replace(regex, '')\n  }\n\n  return file\n}\n"], "names": ["formatFrameSourceFile", "isWebpackInternalResource", "replacementRegExes", "file", "regex", "test", "replace"], "mappings": ";;;;;;;;;;;;;;;IAuBgBA,qBAAqB;eAArBA;;IAlBAC,yBAAyB;eAAzBA;;;AALhB,MAAMC,qBAAqB;IACzB;IACA;CACD;AAEM,SAASD,0BAA0BE,IAAY;IACpD,KAAK,MAAMC,SAASF,mBAAoB;QACtC,IAAIE,MAAMC,IAAI,CAACF,OAAO,OAAO;QAE7BA,OAAOA,KAAKG,OAAO,CAACF,OAAO;IAC7B;IAEA,OAAO;AACT;AAUO,SAASJ,sBAAsBG,IAAY;IAChD,KAAK,MAAMC,SAASF,mBAAoB;QACtCC,OAAOA,KAAKG,OAAO,CAACF,OAAO;IAC7B;IAEA,OAAOD;AACT", "ignoreList": [0]}