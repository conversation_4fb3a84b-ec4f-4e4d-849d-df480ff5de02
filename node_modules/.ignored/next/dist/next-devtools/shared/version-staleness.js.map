{"version": 3, "sources": ["../../../src/next-devtools/shared/version-staleness.ts"], "sourcesContent": ["import type { VersionInfo } from '../../server/dev/parse-version-info'\n\nexport function getStaleness({ installed, staleness, expected }: VersionInfo) {\n  let text = ''\n  let title = ''\n  let indicatorClass = ''\n  const versionLabel = `Next.js ${installed}`\n  switch (staleness) {\n    case 'newer-than-npm':\n    case 'fresh':\n      text = versionLabel\n      title = `Latest available version is detected (${installed}).`\n      indicatorClass = 'fresh'\n      break\n    case 'stale-patch':\n    case 'stale-minor':\n      text = `${versionLabel} (stale)`\n      title = `There is a newer version (${expected}) available, upgrade recommended! `\n      indicatorClass = 'stale'\n      break\n    case 'stale-major': {\n      text = `${versionLabel} (outdated)`\n      title = `An outdated version detected (latest is ${expected}), upgrade is highly recommended!`\n      indicatorClass = 'outdated'\n      break\n    }\n    case 'stale-prerelease': {\n      text = `${versionLabel} (stale)`\n      title = `There is a newer canary version (${expected}) available, please upgrade! `\n      indicatorClass = 'stale'\n      break\n    }\n    case 'unknown':\n      text = `${versionLabel} (unknown)`\n      title = 'No Next.js version data was found.'\n      indicatorClass = 'unknown'\n      break\n    default:\n      break\n  }\n  return { text, indicatorClass, title }\n}\n"], "names": ["getStaleness", "installed", "staleness", "expected", "text", "title", "indicatorClass", "versionLabel"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;AAAT,SAASA,aAAa,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAe;IAC1E,IAAIC,OAAO;IACX,IAAIC,QAAQ;IACZ,IAAIC,iBAAiB;IACrB,MAAMC,eAAe,CAAC,QAAQ,EAAEN,WAAW;IAC3C,OAAQC;QACN,KAAK;QACL,KAAK;YACHE,OAAOG;YACPF,QAAQ,CAAC,sCAAsC,EAAEJ,UAAU,EAAE,CAAC;YAC9DK,iBAAiB;YACjB;QACF,KAAK;QACL,KAAK;YACHF,OAAO,GAAGG,aAAa,QAAQ,CAAC;YAChCF,QAAQ,CAAC,0BAA0B,EAAEF,SAAS,kCAAkC,CAAC;YACjFG,iBAAiB;YACjB;QACF,KAAK;YAAe;gBAClBF,OAAO,GAAGG,aAAa,WAAW,CAAC;gBACnCF,QAAQ,CAAC,wCAAwC,EAAEF,SAAS,iCAAiC,CAAC;gBAC9FG,iBAAiB;gBACjB;YACF;QACA,KAAK;YAAoB;gBACvBF,OAAO,GAAGG,aAAa,QAAQ,CAAC;gBAChCF,QAAQ,CAAC,iCAAiC,EAAEF,SAAS,6BAA6B,CAAC;gBACnFG,iBAAiB;gBACjB;YACF;QACA,KAAK;YACHF,OAAO,GAAGG,aAAa,UAAU,CAAC;YAClCF,QAAQ;YACRC,iBAAiB;YACjB;QACF;YACE;IACJ;IACA,OAAO;QAAEF;QAAME;QAAgBD;IAAM;AACvC", "ignoreList": [0]}