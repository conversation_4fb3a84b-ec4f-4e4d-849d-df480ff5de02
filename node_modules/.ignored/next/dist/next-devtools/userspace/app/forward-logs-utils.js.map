{"version": 3, "sources": ["../../../../src/next-devtools/userspace/app/forward-logs-utils.ts"], "sourcesContent": ["import { configure } from 'next/dist/compiled/safe-stable-stringify'\nimport { getTerminalLoggingConfig } from './terminal-logging-config'\nimport { UNDEFINED_MARKER } from '../../shared/forward-logs-shared'\n\nconst terminalLoggingConfig = getTerminalLoggingConfig()\n\nconst PROMISE_MARKER = 'Promise {}'\nconst UNAVAILABLE_MARKER = '[Unable to view]'\n\nconst maximumDepth =\n  typeof terminalLoggingConfig === 'object' && terminalLoggingConfig.depthLimit\n    ? terminalLoggingConfig.depthLimit\n    : 5\nconst maximumBreadth =\n  typeof terminalLoggingConfig === 'object' && terminalLoggingConfig.edgeLimit\n    ? terminalLoggingConfig.edgeLimit\n    : 100\n\nexport const safeStringifyWithDepth = configure({\n  maximumDepth,\n  maximumBreadth,\n})\n\n/**\n * allows us to:\n * - revive the undefined log in the server as it would look in the browser\n * - not read/attempt to serialize promises (next will console error if you do that, and will cause this program to infinitely recurse)\n * - if we read a proxy that throws (no way to detect if something is a proxy), explain to the user we can't read this data\n */\nexport function preLogSerializationClone<T>(\n  value: T,\n  seen = new WeakMap()\n): any {\n  if (value === undefined) return UNDEFINED_MARKER\n  if (value === null || typeof value !== 'object') return value\n  if (seen.has(value as object)) return seen.get(value as object)\n\n  try {\n    Object.keys(value as object)\n  } catch {\n    return UNAVAILABLE_MARKER\n  }\n\n  try {\n    if (typeof (value as any).then === 'function') return PROMISE_MARKER\n  } catch {\n    return UNAVAILABLE_MARKER\n  }\n\n  if (Array.isArray(value)) {\n    const out: any[] = []\n    seen.set(value, out)\n    for (const item of value) {\n      try {\n        out.push(preLogSerializationClone(item, seen))\n      } catch {\n        out.push(UNAVAILABLE_MARKER)\n      }\n    }\n    return out\n  }\n\n  const proto = Object.getPrototypeOf(value)\n  if (proto === Object.prototype || proto === null) {\n    const out: Record<string, unknown> = {}\n    seen.set(value as object, out)\n    for (const key of Object.keys(value as object)) {\n      try {\n        out[key] = preLogSerializationClone((value as any)[key], seen)\n      } catch {\n        out[key] = UNAVAILABLE_MARKER\n      }\n    }\n    return out\n  }\n\n  return Object.prototype.toString.call(value)\n}\n\n// only safe if passed safeClone data\nexport const logStringify = (data: unknown): string => {\n  try {\n    const result = safeStringifyWithDepth(data)\n    return result ?? `\"${UNAVAILABLE_MARKER}\"`\n  } catch {\n    return `\"${UNAVAILABLE_MARKER}\"`\n  }\n}\n"], "names": ["logStringify", "preLogSerializationClone", "safeStringifyWithDepth", "terminalLoggingConfig", "getTerminalLoggingConfig", "PROMISE_MARKER", "UNAVAILABLE_MARKER", "maximumDepth", "depthLimit", "maximumBreadth", "edgeLimit", "configure", "value", "seen", "WeakMap", "undefined", "UNDEFINED_MARKER", "has", "get", "Object", "keys", "then", "Array", "isArray", "out", "set", "item", "push", "proto", "getPrototypeOf", "prototype", "key", "toString", "call", "data", "result"], "mappings": ";;;;;;;;;;;;;;;;IAgFaA,YAAY;eAAZA;;IAnDGC,wBAAwB;eAAxBA;;IAXHC,sBAAsB;eAAtBA;;;qCAlBa;uCACe;mCACR;AAEjC,MAAMC,wBAAwBC,IAAAA,+CAAwB;AAEtD,MAAMC,iBAAiB;AACvB,MAAMC,qBAAqB;AAE3B,MAAMC,eACJ,OAAOJ,0BAA0B,YAAYA,sBAAsBK,UAAU,GACzEL,sBAAsBK,UAAU,GAChC;AACN,MAAMC,iBACJ,OAAON,0BAA0B,YAAYA,sBAAsBO,SAAS,GACxEP,sBAAsBO,SAAS,GAC/B;AAEC,MAAMR,yBAAyBS,IAAAA,8BAAS,EAAC;IAC9CJ;IACAE;AACF;AAQO,SAASR,yBACdW,KAAQ,EACRC,OAAO,IAAIC,SAAS;IAEpB,IAAIF,UAAUG,WAAW,OAAOC,mCAAgB;IAChD,IAAIJ,UAAU,QAAQ,OAAOA,UAAU,UAAU,OAAOA;IACxD,IAAIC,KAAKI,GAAG,CAACL,QAAkB,OAAOC,KAAKK,GAAG,CAACN;IAE/C,IAAI;QACFO,OAAOC,IAAI,CAACR;IACd,EAAE,OAAM;QACN,OAAON;IACT;IAEA,IAAI;QACF,IAAI,OAAO,AAACM,MAAcS,IAAI,KAAK,YAAY,OAAOhB;IACxD,EAAE,OAAM;QACN,OAAOC;IACT;IAEA,IAAIgB,MAAMC,OAAO,CAACX,QAAQ;QACxB,MAAMY,MAAa,EAAE;QACrBX,KAAKY,GAAG,CAACb,OAAOY;QAChB,KAAK,MAAME,QAAQd,MAAO;YACxB,IAAI;gBACFY,IAAIG,IAAI,CAAC1B,yBAAyByB,MAAMb;YAC1C,EAAE,OAAM;gBACNW,IAAIG,IAAI,CAACrB;YACX;QACF;QACA,OAAOkB;IACT;IAEA,MAAMI,QAAQT,OAAOU,cAAc,CAACjB;IACpC,IAAIgB,UAAUT,OAAOW,SAAS,IAAIF,UAAU,MAAM;QAChD,MAAMJ,MAA+B,CAAC;QACtCX,KAAKY,GAAG,CAACb,OAAiBY;QAC1B,KAAK,MAAMO,OAAOZ,OAAOC,IAAI,CAACR,OAAkB;YAC9C,IAAI;gBACFY,GAAG,CAACO,IAAI,GAAG9B,yBAAyB,AAACW,KAAa,CAACmB,IAAI,EAAElB;YAC3D,EAAE,OAAM;gBACNW,GAAG,CAACO,IAAI,GAAGzB;YACb;QACF;QACA,OAAOkB;IACT;IAEA,OAAOL,OAAOW,SAAS,CAACE,QAAQ,CAACC,IAAI,CAACrB;AACxC;AAGO,MAAMZ,eAAe,CAACkC;IAC3B,IAAI;QACF,MAAMC,SAASjC,uBAAuBgC;QACtC,OAAOC,UAAU,CAAC,CAAC,EAAE7B,mBAAmB,CAAC,CAAC;IAC5C,EAAE,OAAM;QACN,OAAO,CAAC,CAAC,EAAEA,mBAAmB,CAAC,CAAC;IAClC;AACF", "ignoreList": [0]}