{"version": 3, "sources": ["../../../../src/next-devtools/userspace/app/app-dev-overlay-setup.ts"], "sourcesContent": ["import { patchConsoleError } from './errors/intercept-console-error'\nimport { handleGlobalErrors } from './errors/use-error-handler'\nimport { initializeDebugLogForwarding } from './forward-logs'\n\nhandleGlobalErrors()\npatchConsoleError()\n\ninitializeDebugLogForwarding('app')\n"], "names": ["handleGlobalErrors", "patchConsoleError", "initializeDebugLogForwarding"], "mappings": ";;;;uCAAkC;iCACC;6BACU;AAE7CA,IAAAA,mCAAkB;AAClBC,IAAAA,wCAAiB;AAEjBC,IAAAA,yCAA4B,EAAC", "ignoreList": [0]}