{"version": 3, "sources": ["../../../../src/next-devtools/userspace/app/client-entry.tsx"], "sourcesContent": ["import React from 'react'\nimport DefaultGlobalError from '../../../client/components/builtin/global-error'\nimport { AppDevOverlayErrorBoundary } from './app-dev-overlay-error-boundary'\n\n// If an error is thrown while rendering an RSC stream, this will catch it in\n// dev and show the error overlay.\nexport function RootLevelDevOverlayElement({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return (\n    <AppDevOverlayErrorBoundary globalError={[DefaultGlobalError, null]}>\n      {children}\n    </AppDevOverlayErrorBoundary>\n  )\n}\n"], "names": ["RootLevelDevOverlayElement", "children", "AppDevOverlayErrorBoundary", "globalError", "DefaultGlobalError"], "mappings": ";;;;+BAMgBA;;;eAAAA;;;;;gEANE;sEACa;4CACY;AAIpC,SAASA,2BAA2B,EACzCC,QAAQ,EAGT;IACC,qBACE,qBAACC,sDAA0B;QAACC,aAAa;YAACC,oBAAkB;YAAE;SAAK;kBAChEH;;AAGP", "ignoreList": [0]}