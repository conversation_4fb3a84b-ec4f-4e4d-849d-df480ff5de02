{"version": 3, "sources": ["../../../../src/next-devtools/userspace/app/forward-logs.ts"], "sourcesContent": ["import {\n  getOwnerStack,\n  setOwnerStackIfAvailable,\n} from './errors/stitched-error'\nimport { getErrorSource } from '../../../shared/lib/error-source'\nimport { getIsTerminalLoggingEnabled } from './terminal-logging-config'\nimport {\n  type ConsoleEntry,\n  type ConsoleErrorEntry,\n  type FormattedErrorEntry,\n  type ClientLogEntry,\n  type LogMethod,\n  patchConsoleMethod,\n} from '../../shared/forward-logs-shared'\nimport {\n  preLogSerializationClone,\n  logStringify,\n  safeStringifyWithDepth,\n} from './forward-logs-utils'\n\n// Client-side file logger for browser logs\nclass ClientFileLogger {\n  private logEntries: Array<{\n    timestamp: string\n    level: string // log level\n    message: string // log message\n  }> = []\n\n  private formatTimestamp(): string {\n    const now = new Date()\n    const hours = now.getHours().toString().padStart(2, '0')\n    const minutes = now.getMinutes().toString().padStart(2, '0')\n    const seconds = now.getSeconds().toString().padStart(2, '0')\n    const milliseconds = now.getMilliseconds().toString().padStart(3, '0')\n\n    return `${hours}:${minutes}:${seconds}.${milliseconds}`\n  }\n\n  log(level: string, args: any[]): void {\n    if (isReactServerReplayedLog(args)) {\n      return\n    }\n\n    // Format the args into a message string\n    const message = args\n      .map((arg) => {\n        if (typeof arg === 'string') return arg\n        if (typeof arg === 'number' || typeof arg === 'boolean')\n          return String(arg)\n        if (arg === null) return 'null'\n        if (arg === undefined) return 'undefined'\n        // Handle DOM nodes - only log the tag name to avoid React proxied elements\n        if (arg instanceof Element) {\n          return `<${arg.tagName.toLowerCase()}>`\n        }\n        return safeStringifyWithDepth(arg)\n      })\n      .join(' ')\n\n    const logEntry = {\n      timestamp: this.formatTimestamp(),\n      level: level.toUpperCase(),\n      message,\n    }\n    this.logEntries.push(logEntry)\n\n    // Schedule flush when new log is added\n    scheduleLogFlush()\n  }\n  getLogs(): Array<{ timestamp: string; level: string; message: string }> {\n    return [...this.logEntries]\n  }\n\n  clear(): void {\n    this.logEntries = []\n  }\n}\n\nconst clientFileLogger = new ClientFileLogger()\n\n// Set up flush-based sending of client file logs\nlet logFlushTimeout: NodeJS.Timeout | null = null\nlet heartbeatInterval: NodeJS.Timeout | null = null\n\nconst scheduleLogFlush = () => {\n  if (logFlushTimeout) {\n    clearTimeout(logFlushTimeout)\n  }\n\n  logFlushTimeout = setTimeout(() => {\n    sendClientFileLogs()\n    logFlushTimeout = null\n  }, 100) // Send after 100ms (much faster with debouncing)\n}\n\nconst cancelLogFlush = () => {\n  if (logFlushTimeout) {\n    clearTimeout(logFlushTimeout)\n    logFlushTimeout = null\n  }\n}\n\nconst startHeartbeat = () => {\n  if (heartbeatInterval) return\n\n  heartbeatInterval = setInterval(() => {\n    if (logQueue.socket && logQueue.socket.readyState === WebSocket.OPEN) {\n      try {\n        // Send a ping to keep the connection alive\n        logQueue.socket.send(JSON.stringify({ event: 'ping' }))\n      } catch (error) {\n        // Connection might be closed, stop heartbeat\n        stopHeartbeat()\n      }\n    } else {\n      stopHeartbeat()\n    }\n  }, 5000) // Send ping every 5 seconds\n}\n\nconst stopHeartbeat = () => {\n  if (heartbeatInterval) {\n    clearInterval(heartbeatInterval)\n    heartbeatInterval = null\n  }\n}\n\nconst isTerminalLoggingEnabled = getIsTerminalLoggingEnabled()\n\nconst methods: Array<LogMethod> = [\n  'log',\n  'info',\n  'warn',\n  'debug',\n  'table',\n  'assert',\n  'dir',\n  'dirxml',\n  'group',\n  'groupCollapsed',\n  'groupEnd',\n  'trace',\n]\n\nconst afterThisFrame = (cb: () => void) => {\n  let timeout: ReturnType<typeof setTimeout> | undefined\n\n  const rafId = requestAnimationFrame(() => {\n    timeout = setTimeout(() => {\n      cb()\n    })\n  })\n\n  return () => {\n    cancelAnimationFrame(rafId)\n    clearTimeout(timeout)\n  }\n}\n\nlet isPatched = false\n\nconst serializeEntries = (entries: Array<ClientLogEntry>) =>\n  entries.map((clientEntry) => {\n    switch (clientEntry.kind) {\n      case 'any-logged-error':\n      case 'console': {\n        return {\n          ...clientEntry,\n          args: clientEntry.args.map(stringifyUserArg),\n        }\n      }\n      case 'formatted-error': {\n        return clientEntry\n      }\n      default: {\n        return null!\n      }\n    }\n  })\n\n// Function to send client file logs to server\nconst sendClientFileLogs = () => {\n  if (!logQueue.socket || logQueue.socket.readyState !== WebSocket.OPEN) {\n    return\n  }\n\n  const logs = clientFileLogger.getLogs()\n  if (logs.length === 0) {\n    return\n  }\n\n  try {\n    const payload = JSON.stringify({\n      event: 'client-file-logs',\n      logs: logs,\n    })\n\n    logQueue.socket.send(payload)\n  } catch (error) {\n    console.error(error)\n  } finally {\n    // Clear logs regardless of send success to prevent memory leaks\n    clientFileLogger.clear()\n  }\n}\n\n// Combined state and public API\nexport const logQueue: {\n  entries: Array<ClientLogEntry>\n  onSocketReady: (socket: WebSocket) => void\n  flushScheduled: boolean\n  socket: WebSocket | null\n  cancelFlush: (() => void) | null\n  sourceType?: 'server' | 'edge-server'\n  router: 'app' | 'pages' | null\n  scheduleLogSend: (entry: ClientLogEntry) => void\n} = {\n  entries: [],\n  flushScheduled: false,\n  cancelFlush: null,\n  socket: null,\n  sourceType: undefined,\n  router: null,\n  scheduleLogSend: (entry: ClientLogEntry) => {\n    logQueue.entries.push(entry)\n    if (logQueue.flushScheduled) {\n      return\n    }\n    // safe to deref and use in setTimeout closure since we cancel on new socket\n    const socket = logQueue.socket\n    if (!socket) {\n      return\n    }\n\n    // we probably dont need this\n    logQueue.flushScheduled = true\n\n    // non blocking log flush, runs at most once per frame\n    logQueue.cancelFlush = afterThisFrame(() => {\n      logQueue.flushScheduled = false\n\n      // just incase\n      try {\n        const payload = JSON.stringify({\n          event: 'browser-logs',\n          entries: serializeEntries(logQueue.entries),\n          router: logQueue.router,\n          // needed for source mapping, we just assign the sourceType from the last error for the whole batch\n          sourceType: logQueue.sourceType,\n        })\n\n        socket.send(payload)\n        logQueue.entries = []\n        logQueue.sourceType = undefined\n\n        // Also send client file logs\n        sendClientFileLogs()\n      } catch {\n        // error (make sure u don't infinite loop)\n        /* noop */\n      }\n    })\n  },\n  onSocketReady: (socket: WebSocket) => {\n    // When MCP or terminal logging is enabled, we enable the socket connection,\n    // otherwise it will not proceed.\n    if (!isTerminalLoggingEnabled && !process.env.__NEXT_MCP_SERVER) {\n      return\n    }\n    if (socket.readyState !== WebSocket.OPEN) {\n      // invariant\n      return\n    }\n\n    // incase an existing timeout was going to run with a stale socket\n    logQueue.cancelFlush?.()\n    logQueue.socket = socket\n\n    // Add socket event listeners to track connection state\n    socket.addEventListener('close', () => {\n      cancelLogFlush()\n      stopHeartbeat()\n    })\n\n    // Only send terminal logs if enabled\n    if (isTerminalLoggingEnabled) {\n      try {\n        const payload = JSON.stringify({\n          event: 'browser-logs',\n          entries: serializeEntries(logQueue.entries),\n          router: logQueue.router,\n          sourceType: logQueue.sourceType,\n        })\n\n        socket.send(payload)\n        logQueue.entries = []\n        logQueue.sourceType = undefined\n      } catch {\n        /** noop just incase */\n      }\n    }\n\n    // Always send client file logs when socket is ready\n    sendClientFileLogs()\n\n    // Start heartbeat to keep connection alive\n    startHeartbeat()\n  },\n}\n\nconst stringifyUserArg = (\n  arg:\n    | {\n        kind: 'arg'\n        data: unknown\n      }\n    | {\n        kind: 'formatted-error-arg'\n      }\n) => {\n  if (arg.kind !== 'arg') {\n    return arg\n  }\n  return {\n    ...arg,\n    data: logStringify(arg.data),\n  }\n}\n\nconst createErrorArg = (error: Error) => {\n  const stack = stackWithOwners(error)\n  return {\n    kind: 'formatted-error-arg' as const,\n    prefix: error.message ? `${error.name}: ${error.message}` : `${error.name}`,\n    stack,\n  }\n}\n\nconst createLogEntry = (level: LogMethod, args: any[]) => {\n  // Always log to client file logger with args (formatting done inside log method)\n  clientFileLogger.log(level, args)\n\n  // Only forward to terminal if enabled\n  if (!isTerminalLoggingEnabled) {\n    return\n  }\n\n  // do not abstract this, it implicitly relies on which functions call it. forcing the inlined implementation makes you think about callers\n  // error capture stack trace maybe\n  const stack = stackWithOwners(new Error())\n  const stackLines = stack?.split('\\n')\n  const cleanStack = stackLines?.slice(3).join('\\n') // this is probably ignored anyways\n  const entry: ConsoleEntry<unknown> = {\n    kind: 'console',\n    consoleMethodStack: cleanStack ?? null, // depending on browser we might not have stack\n    method: level,\n    args: args.map((arg) => {\n      if (arg instanceof Error) {\n        return createErrorArg(arg)\n      }\n      return {\n        kind: 'arg',\n        data: preLogSerializationClone(arg),\n      }\n    }),\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nexport const forwardErrorLog = (args: any[]) => {\n  // Always log to client file logger with args (formatting done inside log method)\n  clientFileLogger.log('error', args)\n  // Only forward to terminal if enabled\n  if (!isTerminalLoggingEnabled) {\n    return\n  }\n\n  const errorObjects = args.filter((arg) => arg instanceof Error)\n  const first = errorObjects.at(0)\n  if (first) {\n    const source = getErrorSource(first)\n    if (source) {\n      logQueue.sourceType = source\n    }\n  }\n  /**\n   * browser shows stack regardless of type of data passed to console.error, so we should do the same\n   *\n   * do not abstract this, it implicitly relies on which functions call it. forcing the inlined implementation makes you think about callers\n   */\n  const stack = stackWithOwners(new Error())\n  const stackLines = stack?.split('\\n')\n  const cleanStack = stackLines?.slice(3).join('\\n')\n\n  const entry: ConsoleErrorEntry<unknown> = {\n    kind: 'any-logged-error',\n    method: 'error',\n    consoleErrorStack: cleanStack ?? '',\n    args: args.map((arg) => {\n      if (arg instanceof Error) {\n        return createErrorArg(arg)\n      }\n      return {\n        kind: 'arg',\n        data: preLogSerializationClone(arg),\n      }\n    }),\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst createUncaughtErrorEntry = (\n  errorName: string,\n  errorMessage: string,\n  fullStack: string\n) => {\n  const entry: FormattedErrorEntry = {\n    kind: 'formatted-error',\n    prefix: `Uncaught ${errorName}: ${errorMessage}`,\n    stack: fullStack,\n    method: 'error',\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst stackWithOwners = (error: Error) => {\n  let ownerStack = ''\n  setOwnerStackIfAvailable(error)\n  ownerStack = getOwnerStack(error) || ''\n  const stack = (error.stack || '') + ownerStack\n  return stack\n}\n\nexport function logUnhandledRejection(reason: unknown) {\n  // Always log to client file logger\n  const message =\n    reason instanceof Error\n      ? `${reason.name}: ${reason.message}`\n      : JSON.stringify(reason)\n  clientFileLogger.log('error', [`unhandledRejection: ${message}`])\n\n  // Only forward to terminal if enabled\n  if (!isTerminalLoggingEnabled) {\n    return\n  }\n\n  if (reason instanceof Error) {\n    createUnhandledRejectionErrorEntry(reason, stackWithOwners(reason))\n    return\n  }\n  createUnhandledRejectionNonErrorEntry(reason)\n}\n\nconst createUnhandledRejectionErrorEntry = (\n  error: Error,\n  fullStack: string\n) => {\n  const source = getErrorSource(error)\n  if (source) {\n    logQueue.sourceType = source\n  }\n\n  const entry: ClientLogEntry = {\n    kind: 'formatted-error',\n    prefix: `⨯ unhandledRejection: ${error.name}: ${error.message}`,\n    stack: fullStack,\n    method: 'error',\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst createUnhandledRejectionNonErrorEntry = (reason: unknown) => {\n  const entry: ClientLogEntry = {\n    kind: 'any-logged-error',\n    // we can't access the stack since the event is dispatched async and creating an inline error would be meaningless\n    consoleErrorStack: '',\n    method: 'error',\n    args: [\n      {\n        kind: 'arg',\n        data: `⨯ unhandledRejection:`,\n        isRejectionMessage: true,\n      },\n      {\n        kind: 'arg',\n        data: preLogSerializationClone(reason),\n      },\n    ],\n  }\n\n  logQueue.scheduleLogSend(entry)\n}\n\nconst isHMR = (args: any[]) => {\n  const firstArg = args[0]\n  if (typeof firstArg !== 'string') {\n    return false\n  }\n  if (firstArg.startsWith('[Fast Refresh]')) {\n    return true\n  }\n\n  if (firstArg.startsWith('[HMR]')) {\n    return true\n  }\n\n  return false\n}\n\n/**\n * Matches the format of logs arguments React replayed from the RSC.\n */\nconst isReactServerReplayedLog = (args: any[]) => {\n  if (args.length < 3) {\n    return false\n  }\n\n  const [format, styles, label] = args\n\n  if (\n    typeof format !== 'string' ||\n    typeof styles !== 'string' ||\n    typeof label !== 'string'\n  ) {\n    return false\n  }\n\n  return format.startsWith('%c%s%c') && styles.includes('background:')\n}\n\nexport function forwardUnhandledError(error: Error) {\n  // Always log to client file logger\n  clientFileLogger.log('error', [\n    `uncaughtError: ${error.name}: ${error.message}`,\n  ])\n\n  // Only forward to terminal if enabled\n  if (!isTerminalLoggingEnabled) {\n    return\n  }\n\n  createUncaughtErrorEntry(error.name, error.message, stackWithOwners(error))\n}\n\n// TODO: this router check is brittle, we need to update based on the current router the user is using\nexport const initializeDebugLogForwarding = (router: 'app' | 'pages'): void => {\n  // probably don't need this\n  if (isPatched) {\n    return\n  }\n  // TODO(rob): why does this break rendering on server, important to know incase the same bug appears in browser\n  if (typeof window === 'undefined') {\n    return\n  }\n\n  // better to be safe than sorry\n  try {\n    methods.forEach((method) =>\n      patchConsoleMethod(method, (_, ...args) => {\n        if (isHMR(args)) {\n          return\n        }\n        if (isReactServerReplayedLog(args)) {\n          return\n        }\n        createLogEntry(method, args)\n      })\n    )\n  } catch {}\n  logQueue.router = router\n  isPatched = true\n\n  // Cleanup on page unload\n  window.addEventListener('beforeunload', () => {\n    cancelLogFlush()\n    stopHeartbeat()\n    // Send any remaining logs before page unloads\n    sendClientFileLogs()\n  })\n}\n"], "names": ["forward<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardUnhandledError", "initializeDebugLogForwarding", "logQueue", "logUnhandledRejection", "ClientFileLogger", "formatTimestamp", "now", "Date", "hours", "getHours", "toString", "padStart", "minutes", "getMinutes", "seconds", "getSeconds", "milliseconds", "getMilliseconds", "log", "level", "args", "isReactServerReplayedLog", "message", "map", "arg", "String", "undefined", "Element", "tagName", "toLowerCase", "safeStringifyWithDepth", "join", "logEntry", "timestamp", "toUpperCase", "logEntries", "push", "scheduleLogFlush", "getLogs", "clear", "clientFileLogger", "logFlushTimeout", "heartbeatInterval", "clearTimeout", "setTimeout", "sendClientFileLogs", "cancelLogFlush", "startHeartbeat", "setInterval", "socket", "readyState", "WebSocket", "OPEN", "send", "JSON", "stringify", "event", "error", "stopHeartbeat", "clearInterval", "isTerminalLoggingEnabled", "getIsTerminalLoggingEnabled", "methods", "afterT<PERSON><PERSON><PERSON>e", "cb", "timeout", "rafId", "requestAnimationFrame", "cancelAnimationFrame", "isPatched", "serializeEntries", "entries", "clientEntry", "kind", "stringifyUserArg", "logs", "length", "payload", "console", "flushScheduled", "cancelFlush", "sourceType", "router", "scheduleLogSend", "entry", "onSocketReady", "process", "env", "__NEXT_MCP_SERVER", "addEventListener", "data", "logStringify", "createErrorArg", "stack", "stackWithOwners", "prefix", "name", "createLogEntry", "Error", "stackLines", "split", "cleanStack", "slice", "consoleMethodStack", "method", "preLogSerializationClone", "errorObjects", "filter", "first", "at", "source", "getErrorSource", "consoleErrorStack", "createUncaughtErrorEntry", "errorName", "errorMessage", "fullStack", "ownerStack", "setOwnerStackIfAvailable", "getOwnerStack", "reason", "createUnhandledRejectionErrorEntry", "createUnhandledRejectionNonErrorEntry", "isRejectionMessage", "isHMR", "firstArg", "startsWith", "format", "styles", "label", "includes", "window", "for<PERSON>ach", "patchConsoleMethod", "_"], "mappings": ";;;;;;;;;;;;;;;;;;IAkXaA,eAAe;eAAfA;;IAoKGC,qBAAqB;eAArBA;;IAeHC,4BAA4B;eAA5BA;;IAtVAC,QAAQ;eAARA;;IAqOGC,qBAAqB;eAArBA;;;+BAjbT;6BACwB;uCACa;mCAQrC;kCAKA;AAEP,2CAA2C;AAC3C,MAAMC;IAOIC,kBAA0B;QAChC,MAAMC,MAAM,IAAIC;QAChB,MAAMC,QAAQF,IAAIG,QAAQ,GAAGC,QAAQ,GAAGC,QAAQ,CAAC,GAAG;QACpD,MAAMC,UAAUN,IAAIO,UAAU,GAAGH,QAAQ,GAAGC,QAAQ,CAAC,GAAG;QACxD,MAAMG,UAAUR,IAAIS,UAAU,GAAGL,QAAQ,GAAGC,QAAQ,CAAC,GAAG;QACxD,MAAMK,eAAeV,IAAIW,eAAe,GAAGP,QAAQ,GAAGC,QAAQ,CAAC,GAAG;QAElE,OAAO,GAAGH,MAAM,CAAC,EAAEI,QAAQ,CAAC,EAAEE,QAAQ,CAAC,EAAEE,cAAc;IACzD;IAEAE,IAAIC,KAAa,EAAEC,IAAW,EAAQ;QACpC,IAAIC,yBAAyBD,OAAO;YAClC;QACF;QAEA,wCAAwC;QACxC,MAAME,UAAUF,KACbG,GAAG,CAAC,CAACC;YACJ,IAAI,OAAOA,QAAQ,UAAU,OAAOA;YACpC,IAAI,OAAOA,QAAQ,YAAY,OAAOA,QAAQ,WAC5C,OAAOC,OAAOD;YAChB,IAAIA,QAAQ,MAAM,OAAO;YACzB,IAAIA,QAAQE,WAAW,OAAO;YAC9B,2EAA2E;YAC3E,IAAIF,eAAeG,SAAS;gBAC1B,OAAO,CAAC,CAAC,EAAEH,IAAII,OAAO,CAACC,WAAW,GAAG,CAAC,CAAC;YACzC;YACA,OAAOC,IAAAA,wCAAsB,EAACN;QAChC,GACCO,IAAI,CAAC;QAER,MAAMC,WAAW;YACfC,WAAW,IAAI,CAAC5B,eAAe;YAC/Bc,OAAOA,MAAMe,WAAW;YACxBZ;QACF;QACA,IAAI,CAACa,UAAU,CAACC,IAAI,CAACJ;QAErB,uCAAuC;QACvCK;IACF;IACAC,UAAwE;QACtE,OAAO;eAAI,IAAI,CAACH,UAAU;SAAC;IAC7B;IAEAI,QAAc;QACZ,IAAI,CAACJ,UAAU,GAAG,EAAE;IACtB;;aArDQA,aAIH,EAAE;;AAkDT;AAEA,MAAMK,mBAAmB,IAAIpC;AAE7B,iDAAiD;AACjD,IAAIqC,kBAAyC;AAC7C,IAAIC,oBAA2C;AAE/C,MAAML,mBAAmB;IACvB,IAAII,iBAAiB;QACnBE,aAAaF;IACf;IAEAA,kBAAkBG,WAAW;QAC3BC;QACAJ,kBAAkB;IACpB,GAAG,KAAK,iDAAiD;;AAC3D;AAEA,MAAMK,iBAAiB;IACrB,IAAIL,iBAAiB;QACnBE,aAAaF;QACbA,kBAAkB;IACpB;AACF;AAEA,MAAMM,iBAAiB;IACrB,IAAIL,mBAAmB;IAEvBA,oBAAoBM,YAAY;QAC9B,IAAI9C,SAAS+C,MAAM,IAAI/C,SAAS+C,MAAM,CAACC,UAAU,KAAKC,UAAUC,IAAI,EAAE;YACpE,IAAI;gBACF,2CAA2C;gBAC3ClD,SAAS+C,MAAM,CAACI,IAAI,CAACC,KAAKC,SAAS,CAAC;oBAAEC,OAAO;gBAAO;YACtD,EAAE,OAAOC,OAAO;gBACd,6CAA6C;gBAC7CC;YACF;QACF,OAAO;YACLA;QACF;IACF,GAAG,MAAM,4BAA4B;;AACvC;AAEA,MAAMA,gBAAgB;IACpB,IAAIhB,mBAAmB;QACrBiB,cAAcjB;QACdA,oBAAoB;IACtB;AACF;AAEA,MAAMkB,2BAA2BC,IAAAA,kDAA2B;AAE5D,MAAMC,UAA4B;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAMC,iBAAiB,CAACC;IACtB,IAAIC;IAEJ,MAAMC,QAAQC,sBAAsB;QAClCF,UAAUrB,WAAW;YACnBoB;QACF;IACF;IAEA,OAAO;QACLI,qBAAqBF;QACrBvB,aAAasB;IACf;AACF;AAEA,IAAII,YAAY;AAEhB,MAAMC,mBAAmB,CAACC,UACxBA,QAAQhD,GAAG,CAAC,CAACiD;QACX,OAAQA,YAAYC,IAAI;YACtB,KAAK;YACL,KAAK;gBAAW;oBACd,OAAO;wBACL,GAAGD,WAAW;wBACdpD,MAAMoD,YAAYpD,IAAI,CAACG,GAAG,CAACmD;oBAC7B;gBACF;YACA,KAAK;gBAAmB;oBACtB,OAAOF;gBACT;YACA;gBAAS;oBACP,OAAO;gBACT;QACF;IACF;AAEF,8CAA8C;AAC9C,MAAM3B,qBAAqB;IACzB,IAAI,CAAC3C,SAAS+C,MAAM,IAAI/C,SAAS+C,MAAM,CAACC,UAAU,KAAKC,UAAUC,IAAI,EAAE;QACrE;IACF;IAEA,MAAMuB,OAAOnC,iBAAiBF,OAAO;IACrC,IAAIqC,KAAKC,MAAM,KAAK,GAAG;QACrB;IACF;IAEA,IAAI;QACF,MAAMC,UAAUvB,KAAKC,SAAS,CAAC;YAC7BC,OAAO;YACPmB,MAAMA;QACR;QAEAzE,SAAS+C,MAAM,CAACI,IAAI,CAACwB;IACvB,EAAE,OAAOpB,OAAO;QACdqB,QAAQrB,KAAK,CAACA;IAChB,SAAU;QACR,gEAAgE;QAChEjB,iBAAiBD,KAAK;IACxB;AACF;AAGO,MAAMrC,WAST;IACFqE,SAAS,EAAE;IACXQ,gBAAgB;IAChBC,aAAa;IACb/B,QAAQ;IACRgC,YAAYvD;IACZwD,QAAQ;IACRC,iBAAiB,CAACC;QAChBlF,SAASqE,OAAO,CAACnC,IAAI,CAACgD;QACtB,IAAIlF,SAAS6E,cAAc,EAAE;YAC3B;QACF;QACA,4EAA4E;QAC5E,MAAM9B,SAAS/C,SAAS+C,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX;QACF;QAEA,6BAA6B;QAC7B/C,SAAS6E,cAAc,GAAG;QAE1B,sDAAsD;QACtD7E,SAAS8E,WAAW,GAAGjB,eAAe;YACpC7D,SAAS6E,cAAc,GAAG;YAE1B,cAAc;YACd,IAAI;gBACF,MAAMF,UAAUvB,KAAKC,SAAS,CAAC;oBAC7BC,OAAO;oBACPe,SAASD,iBAAiBpE,SAASqE,OAAO;oBAC1CW,QAAQhF,SAASgF,MAAM;oBACvB,mGAAmG;oBACnGD,YAAY/E,SAAS+E,UAAU;gBACjC;gBAEAhC,OAAOI,IAAI,CAACwB;gBACZ3E,SAASqE,OAAO,GAAG,EAAE;gBACrBrE,SAAS+E,UAAU,GAAGvD;gBAEtB,6BAA6B;gBAC7BmB;YACF,EAAE,OAAM;YACN,0CAA0C;YAC1C,QAAQ,GACV;QACF;IACF;IACAwC,eAAe,CAACpC;QACd,4EAA4E;QAC5E,iCAAiC;QACjC,IAAI,CAACW,4BAA4B,CAAC0B,QAAQC,GAAG,CAACC,iBAAiB,EAAE;YAC/D;QACF;QACA,IAAIvC,OAAOC,UAAU,KAAKC,UAAUC,IAAI,EAAE;YACxC,YAAY;YACZ;QACF;QAEA,kEAAkE;QAClElD,SAAS8E,WAAW;QACpB9E,SAAS+C,MAAM,GAAGA;QAElB,uDAAuD;QACvDA,OAAOwC,gBAAgB,CAAC,SAAS;YAC/B3C;YACAY;QACF;QAEA,qCAAqC;QACrC,IAAIE,0BAA0B;YAC5B,IAAI;gBACF,MAAMiB,UAAUvB,KAAKC,SAAS,CAAC;oBAC7BC,OAAO;oBACPe,SAASD,iBAAiBpE,SAASqE,OAAO;oBAC1CW,QAAQhF,SAASgF,MAAM;oBACvBD,YAAY/E,SAAS+E,UAAU;gBACjC;gBAEAhC,OAAOI,IAAI,CAACwB;gBACZ3E,SAASqE,OAAO,GAAG,EAAE;gBACrBrE,SAAS+E,UAAU,GAAGvD;YACxB,EAAE,OAAM;YACN,qBAAqB,GACvB;QACF;QAEA,oDAAoD;QACpDmB;QAEA,2CAA2C;QAC3CE;IACF;AACF;AAEA,MAAM2B,mBAAmB,CACvBlD;IASA,IAAIA,IAAIiD,IAAI,KAAK,OAAO;QACtB,OAAOjD;IACT;IACA,OAAO;QACL,GAAGA,GAAG;QACNkE,MAAMC,IAAAA,8BAAY,EAACnE,IAAIkE,IAAI;IAC7B;AACF;AAEA,MAAME,iBAAiB,CAACnC;IACtB,MAAMoC,QAAQC,gBAAgBrC;IAC9B,OAAO;QACLgB,MAAM;QACNsB,QAAQtC,MAAMnC,OAAO,GAAG,GAAGmC,MAAMuC,IAAI,CAAC,EAAE,EAAEvC,MAAMnC,OAAO,EAAE,GAAG,GAAGmC,MAAMuC,IAAI,EAAE;QAC3EH;IACF;AACF;AAEA,MAAMI,iBAAiB,CAAC9E,OAAkBC;IACxC,iFAAiF;IACjFoB,iBAAiBtB,GAAG,CAACC,OAAOC;IAE5B,sCAAsC;IACtC,IAAI,CAACwC,0BAA0B;QAC7B;IACF;IAEA,0IAA0I;IAC1I,kCAAkC;IAClC,MAAMiC,QAAQC,gBAAgB,IAAII;IAClC,MAAMC,aAAaN,OAAOO,MAAM;IAChC,MAAMC,aAAaF,YAAYG,MAAM,GAAGvE,KAAK,MAAM,mCAAmC;;IACtF,MAAMqD,QAA+B;QACnCX,MAAM;QACN8B,oBAAoBF,cAAc;QAClCG,QAAQrF;QACRC,MAAMA,KAAKG,GAAG,CAAC,CAACC;YACd,IAAIA,eAAe0E,OAAO;gBACxB,OAAON,eAAepE;YACxB;YACA,OAAO;gBACLiD,MAAM;gBACNiB,MAAMe,IAAAA,0CAAwB,EAACjF;YACjC;QACF;IACF;IAEAtB,SAASiF,eAAe,CAACC;AAC3B;AAEO,MAAMrF,kBAAkB,CAACqB;IAC9B,iFAAiF;IACjFoB,iBAAiBtB,GAAG,CAAC,SAASE;IAC9B,sCAAsC;IACtC,IAAI,CAACwC,0BAA0B;QAC7B;IACF;IAEA,MAAM8C,eAAetF,KAAKuF,MAAM,CAAC,CAACnF,MAAQA,eAAe0E;IACzD,MAAMU,QAAQF,aAAaG,EAAE,CAAC;IAC9B,IAAID,OAAO;QACT,MAAME,SAASC,IAAAA,2BAAc,EAACH;QAC9B,IAAIE,QAAQ;YACV5G,SAAS+E,UAAU,GAAG6B;QACxB;IACF;IACA;;;;GAIC,GACD,MAAMjB,QAAQC,gBAAgB,IAAII;IAClC,MAAMC,aAAaN,OAAOO,MAAM;IAChC,MAAMC,aAAaF,YAAYG,MAAM,GAAGvE,KAAK;IAE7C,MAAMqD,QAAoC;QACxCX,MAAM;QACN+B,QAAQ;QACRQ,mBAAmBX,cAAc;QACjCjF,MAAMA,KAAKG,GAAG,CAAC,CAACC;YACd,IAAIA,eAAe0E,OAAO;gBACxB,OAAON,eAAepE;YACxB;YACA,OAAO;gBACLiD,MAAM;gBACNiB,MAAMe,IAAAA,0CAAwB,EAACjF;YACjC;QACF;IACF;IAEAtB,SAASiF,eAAe,CAACC;AAC3B;AAEA,MAAM6B,2BAA2B,CAC/BC,WACAC,cACAC;IAEA,MAAMhC,QAA6B;QACjCX,MAAM;QACNsB,QAAQ,CAAC,SAAS,EAAEmB,UAAU,EAAE,EAAEC,cAAc;QAChDtB,OAAOuB;QACPZ,QAAQ;IACV;IAEAtG,SAASiF,eAAe,CAACC;AAC3B;AAEA,MAAMU,kBAAkB,CAACrC;IACvB,IAAI4D,aAAa;IACjBC,IAAAA,uCAAwB,EAAC7D;IACzB4D,aAAaE,IAAAA,4BAAa,EAAC9D,UAAU;IACrC,MAAMoC,QAAQ,AAACpC,CAAAA,MAAMoC,KAAK,IAAI,EAAC,IAAKwB;IACpC,OAAOxB;AACT;AAEO,SAAS1F,sBAAsBqH,MAAe;IACnD,mCAAmC;IACnC,MAAMlG,UACJkG,kBAAkBtB,QACd,GAAGsB,OAAOxB,IAAI,CAAC,EAAE,EAAEwB,OAAOlG,OAAO,EAAE,GACnCgC,KAAKC,SAAS,CAACiE;IACrBhF,iBAAiBtB,GAAG,CAAC,SAAS;QAAC,CAAC,oBAAoB,EAAEI,SAAS;KAAC;IAEhE,sCAAsC;IACtC,IAAI,CAACsC,0BAA0B;QAC7B;IACF;IAEA,IAAI4D,kBAAkBtB,OAAO;QAC3BuB,mCAAmCD,QAAQ1B,gBAAgB0B;QAC3D;IACF;IACAE,sCAAsCF;AACxC;AAEA,MAAMC,qCAAqC,CACzChE,OACA2D;IAEA,MAAMN,SAASC,IAAAA,2BAAc,EAACtD;IAC9B,IAAIqD,QAAQ;QACV5G,SAAS+E,UAAU,GAAG6B;IACxB;IAEA,MAAM1B,QAAwB;QAC5BX,MAAM;QACNsB,QAAQ,CAAC,sBAAsB,EAAEtC,MAAMuC,IAAI,CAAC,EAAE,EAAEvC,MAAMnC,OAAO,EAAE;QAC/DuE,OAAOuB;QACPZ,QAAQ;IACV;IAEAtG,SAASiF,eAAe,CAACC;AAC3B;AAEA,MAAMsC,wCAAwC,CAACF;IAC7C,MAAMpC,QAAwB;QAC5BX,MAAM;QACN,kHAAkH;QAClHuC,mBAAmB;QACnBR,QAAQ;QACRpF,MAAM;YACJ;gBACEqD,MAAM;gBACNiB,MAAM,CAAC,qBAAqB,CAAC;gBAC7BiC,oBAAoB;YACtB;YACA;gBACElD,MAAM;gBACNiB,MAAMe,IAAAA,0CAAwB,EAACe;YACjC;SACD;IACH;IAEAtH,SAASiF,eAAe,CAACC;AAC3B;AAEA,MAAMwC,QAAQ,CAACxG;IACb,MAAMyG,WAAWzG,IAAI,CAAC,EAAE;IACxB,IAAI,OAAOyG,aAAa,UAAU;QAChC,OAAO;IACT;IACA,IAAIA,SAASC,UAAU,CAAC,mBAAmB;QACzC,OAAO;IACT;IAEA,IAAID,SAASC,UAAU,CAAC,UAAU;QAChC,OAAO;IACT;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,MAAMzG,2BAA2B,CAACD;IAChC,IAAIA,KAAKwD,MAAM,GAAG,GAAG;QACnB,OAAO;IACT;IAEA,MAAM,CAACmD,QAAQC,QAAQC,MAAM,GAAG7G;IAEhC,IACE,OAAO2G,WAAW,YAClB,OAAOC,WAAW,YAClB,OAAOC,UAAU,UACjB;QACA,OAAO;IACT;IAEA,OAAOF,OAAOD,UAAU,CAAC,aAAaE,OAAOE,QAAQ,CAAC;AACxD;AAEO,SAASlI,sBAAsByD,KAAY;IAChD,mCAAmC;IACnCjB,iBAAiBtB,GAAG,CAAC,SAAS;QAC5B,CAAC,eAAe,EAAEuC,MAAMuC,IAAI,CAAC,EAAE,EAAEvC,MAAMnC,OAAO,EAAE;KACjD;IAED,sCAAsC;IACtC,IAAI,CAACsC,0BAA0B;QAC7B;IACF;IAEAqD,yBAAyBxD,MAAMuC,IAAI,EAAEvC,MAAMnC,OAAO,EAAEwE,gBAAgBrC;AACtE;AAGO,MAAMxD,+BAA+B,CAACiF;IAC3C,2BAA2B;IAC3B,IAAIb,WAAW;QACb;IACF;IACA,+GAA+G;IAC/G,IAAI,OAAO8D,WAAW,aAAa;QACjC;IACF;IAEA,+BAA+B;IAC/B,IAAI;QACFrE,QAAQsE,OAAO,CAAC,CAAC5B,SACf6B,IAAAA,qCAAkB,EAAC7B,QAAQ,CAAC8B,GAAG,GAAGlH;gBAChC,IAAIwG,MAAMxG,OAAO;oBACf;gBACF;gBACA,IAAIC,yBAAyBD,OAAO;oBAClC;gBACF;gBACA6E,eAAeO,QAAQpF;YACzB;IAEJ,EAAE,OAAM,CAAC;IACTlB,SAASgF,MAAM,GAAGA;IAClBb,YAAY;IAEZ,yBAAyB;IACzB8D,OAAO1C,gBAAgB,CAAC,gBAAgB;QACtC3C;QACAY;QACA,8CAA8C;QAC9Cb;IACF;AACF", "ignoreList": [0]}