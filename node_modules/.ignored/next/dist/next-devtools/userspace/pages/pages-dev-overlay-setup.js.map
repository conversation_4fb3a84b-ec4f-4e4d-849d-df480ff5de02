{"version": 3, "sources": ["../../../../src/next-devtools/userspace/pages/pages-dev-overlay-setup.tsx"], "sourcesContent": ["import React from 'react'\nimport { renderPagesDevOverlay } from 'next/dist/compiled/next-devtools'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport {\n  attachHydrationErrorState,\n  storeHydrationErrorStateFromConsoleArgs,\n} from './hydration-error-state'\nimport { Router } from '../../../client/router'\nimport { getOwnerStack } from '../app/errors/stitched-error'\nimport { isRecoverableError } from '../../../client/react-client-callbacks/on-recoverable-error'\nimport { getSquashedHydrationErrorDetails } from './hydration-error-state'\nimport { PagesDevOverlayErrorBoundary } from './pages-dev-overlay-error-boundary'\nimport {\n  initializeDebugLogForwarding,\n  forwardUnhandledError,\n  logUnhandledRejection,\n  forwardErrorLog,\n} from '../app/forward-logs'\n\nconst usePagesDevOverlayBridge = () => {\n  React.useInsertionEffect(() => {\n    // NDT uses a different React instance so it's not technically a state update\n    // scheduled from useInsertionEffect.\n    renderPagesDevOverlay(\n      getOwnerStack,\n      getSquashedHydrationErrorDetails,\n      isRecoverableError\n    )\n  }, [])\n\n  React.useEffect(() => {\n    const { handleStaticIndicator } =\n      require('../../../client/dev/hot-reloader/pages/hot-reloader-pages') as typeof import('../../../client/dev/hot-reloader/pages/hot-reloader-pages')\n\n    Router.events.on('routeChangeComplete', handleStaticIndicator)\n\n    return function () {\n      Router.events.off('routeChangeComplete', handleStaticIndicator)\n    }\n  }, [])\n}\n\nexport type PagesDevOverlayBridgeType = typeof PagesDevOverlayBridge\n\ninterface PagesDevOverlayBridgeProps {\n  children?: React.ReactNode\n}\n\nexport function PagesDevOverlayBridge({\n  children,\n}: PagesDevOverlayBridgeProps) {\n  usePagesDevOverlayBridge()\n\n  return <PagesDevOverlayErrorBoundary>{children}</PagesDevOverlayErrorBoundary>\n}\n\nlet isRegistered = false\n\nfunction handleError(error: unknown) {\n  if (!error || !(error instanceof Error) || typeof error.stack !== 'string') {\n    // A non-error was thrown, we don't have anything to show. :-(\n    return\n  }\n\n  attachHydrationErrorState(error)\n\n  // Skip ModuleBuildError and ModuleNotFoundError, as it will be sent through onBuildError callback.\n  // This is to avoid same error as different type showing up on client to cause flashing.\n  if (\n    error.name !== 'ModuleBuildError' &&\n    error.name !== 'ModuleNotFoundError'\n  ) {\n    dispatcher.onUnhandledError(error)\n  }\n}\n\nlet origConsoleError = console.error\nfunction nextJsHandleConsoleError(...args: any[]) {\n  // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n  const maybeError = process.env.NODE_ENV !== 'production' ? args[1] : args[0]\n  storeHydrationErrorStateFromConsoleArgs(...args)\n  // TODO: Surfaces non-errors logged via `console.error`.\n  handleError(maybeError)\n  forwardErrorLog(args)\n  origConsoleError.apply(window.console, args)\n}\n\nfunction onUnhandledError(event: ErrorEvent) {\n  const error = event?.error\n  handleError(error)\n\n  if (error) {\n    forwardUnhandledError(error as Error)\n  }\n}\n\nfunction onUnhandledRejection(ev: PromiseRejectionEvent) {\n  const reason = ev?.reason\n  if (\n    !reason ||\n    !(reason instanceof Error) ||\n    typeof reason.stack !== 'string'\n  ) {\n    // A non-error was thrown, we don't have anything to show. :-(\n    return\n  }\n\n  dispatcher.onUnhandledRejection(reason)\n  logUnhandledRejection(reason)\n}\n\nexport function register() {\n  if (isRegistered) {\n    return\n  }\n  isRegistered = true\n\n  try {\n    Error.stackTraceLimit = 50\n  } catch {}\n\n  initializeDebugLogForwarding('pages')\n  window.addEventListener('error', onUnhandledError)\n  window.addEventListener('unhandledrejection', onUnhandledRejection)\n  window.console.error = nextJsHandleConsoleError\n}\n"], "names": ["PagesDevOverlayBridge", "register", "usePagesDevOverlayBridge", "React", "useInsertionEffect", "renderPagesDevOverlay", "getOwnerStack", "getSquashedHydrationErrorDetails", "isRecoverableError", "useEffect", "handleStaticIndicator", "require", "Router", "events", "on", "off", "children", "PagesDevOverlayErrorBoundary", "isRegistered", "handleError", "error", "Error", "stack", "attachHydrationErrorState", "name", "dispatcher", "onUnhandledError", "origConsoleError", "console", "nextJsHandleConsoleError", "args", "maybeError", "process", "env", "NODE_ENV", "storeHydrationErrorStateFromConsoleArgs", "forward<PERSON><PERSON><PERSON><PERSON><PERSON>", "apply", "window", "event", "forwardUnhandledError", "onUnhandledRejection", "ev", "reason", "logUnhandledRejection", "stackTraceLimit", "initializeDebugLogForwarding", "addEventListener"], "mappings": ";;;;;;;;;;;;;;;IAgDgBA,qBAAqB;eAArBA;;IA+DAC,QAAQ;eAARA;;;;;gEA/GE;8BACoB;qCAK/B;wBACgB;+BACO;oCACK;8CAEU;6BAMtC;AAEP,MAAMC,2BAA2B;IAC/BC,cAAK,CAACC,kBAAkB,CAAC;QACvB,6EAA6E;QAC7E,qCAAqC;QACrCC,IAAAA,mCAAqB,EACnBC,4BAAa,EACbC,qDAAgC,EAChCC,sCAAkB;IAEtB,GAAG,EAAE;IAELL,cAAK,CAACM,SAAS,CAAC;QACd,MAAM,EAAEC,qBAAqB,EAAE,GAC7BC,QAAQ;QAEVC,cAAM,CAACC,MAAM,CAACC,EAAE,CAAC,uBAAuBJ;QAExC,OAAO;YACLE,cAAM,CAACC,MAAM,CAACE,GAAG,CAAC,uBAAuBL;QAC3C;IACF,GAAG,EAAE;AACP;AAQO,SAASV,sBAAsB,EACpCgB,QAAQ,EACmB;IAC3Bd;IAEA,qBAAO,qBAACe,0DAA4B;kBAAED;;AACxC;AAEA,IAAIE,eAAe;AAEnB,SAASC,YAAYC,KAAc;IACjC,IAAI,CAACA,SAAS,CAAEA,CAAAA,iBAAiBC,KAAI,KAAM,OAAOD,MAAME,KAAK,KAAK,UAAU;QAC1E,8DAA8D;QAC9D;IACF;IAEAC,IAAAA,8CAAyB,EAACH;IAE1B,mGAAmG;IACnG,wFAAwF;IACxF,IACEA,MAAMI,IAAI,KAAK,sBACfJ,MAAMI,IAAI,KAAK,uBACf;QACAC,wBAAU,CAACC,gBAAgB,CAACN;IAC9B;AACF;AAEA,IAAIO,mBAAmBC,QAAQR,KAAK;AACpC,SAASS,yBAAyB,GAAGC,IAAW;IAC9C,iJAAiJ;IACjJ,MAAMC,aAAaC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAeJ,IAAI,CAAC,EAAE,GAAGA,IAAI,CAAC,EAAE;IAC5EK,IAAAA,4DAAuC,KAAIL;IAC3C,wDAAwD;IACxDX,YAAYY;IACZK,IAAAA,4BAAe,EAACN;IAChBH,iBAAiBU,KAAK,CAACC,OAAOV,OAAO,EAAEE;AACzC;AAEA,SAASJ,iBAAiBa,KAAiB;IACzC,MAAMnB,QAAQmB,OAAOnB;IACrBD,YAAYC;IAEZ,IAAIA,OAAO;QACToB,IAAAA,kCAAqB,EAACpB;IACxB;AACF;AAEA,SAASqB,qBAAqBC,EAAyB;IACrD,MAAMC,SAASD,IAAIC;IACnB,IACE,CAACA,UACD,CAAEA,CAAAA,kBAAkBtB,KAAI,KACxB,OAAOsB,OAAOrB,KAAK,KAAK,UACxB;QACA,8DAA8D;QAC9D;IACF;IAEAG,wBAAU,CAACgB,oBAAoB,CAACE;IAChCC,IAAAA,kCAAqB,EAACD;AACxB;AAEO,SAAS1C;IACd,IAAIiB,cAAc;QAChB;IACF;IACAA,eAAe;IAEf,IAAI;QACFG,MAAMwB,eAAe,GAAG;IAC1B,EAAE,OAAM,CAAC;IAETC,IAAAA,yCAA4B,EAAC;IAC7BR,OAAOS,gBAAgB,CAAC,SAASrB;IACjCY,OAAOS,gBAAgB,CAAC,sBAAsBN;IAC9CH,OAAOV,OAAO,CAACR,KAAK,GAAGS;AACzB", "ignoreList": [0]}