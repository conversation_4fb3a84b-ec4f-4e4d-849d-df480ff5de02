{"version": 3, "sources": ["../../../src/next-devtools/server/restart-dev-server-middleware.ts"], "sourcesContent": ["import type { ServerResponse, IncomingMessage } from 'http'\nimport type { Telemetry } from '../../telemetry/storage'\nimport { RESTART_EXIT_CODE } from '../../server/lib/utils'\nimport { middlewareResponse } from './middleware-response'\nimport type { Project } from '../../build/swc/types'\nimport { invalidateFileSystemCache as invalidateWebpackFileSystemCache } from '../../build/webpack/cache-invalidation'\n\nconst EVENT_DEV_OVERLAY_RESTART_SERVER = 'DEV_OVERLAY_RESTART_SERVER'\n\ninterface RestartDevServerMiddlewareConfig {\n  telemetry: Telemetry\n  turbopackProject?: Project\n  webpackCacheDirectories?: Set<string>\n}\n\nexport function getRestartDevServerMiddleware({\n  telemetry,\n  turbopackProject,\n  webpackCacheDirectories,\n}: RestartDevServerMiddlewareConfig) {\n  /**\n   * Some random value between 1 and Number.MAX_SAFE_INTEGER (inclusive). The same value is returned\n   * on every call to `__nextjs_server_status` until the server is restarted.\n   *\n   * Can be used to determine if two server status responses are from the same process or a\n   * different (restarted) process.\n   */\n  const executionId: number =\n    Math.floor(Math.random() * Number.MAX_SAFE_INTEGER) + 1\n\n  async function handleRestartRequest(\n    req: IncomingMessage,\n    res: ServerResponse,\n    searchParams: URLSearchParams\n  ) {\n    if (req.method !== 'POST') {\n      return middlewareResponse.methodNotAllowed(res)\n    }\n\n    const shouldInvalidateFileSystemCache = searchParams.has(\n      'invalidateFileSystemCache'\n    )\n    if (shouldInvalidateFileSystemCache) {\n      if (webpackCacheDirectories != null) {\n        await Promise.all(\n          Array.from(webpackCacheDirectories).map(\n            invalidateWebpackFileSystemCache\n          )\n        )\n      }\n      if (turbopackProject != null) {\n        await turbopackProject.invalidateFileSystemCache()\n      }\n    }\n\n    telemetry.record({\n      eventName: EVENT_DEV_OVERLAY_RESTART_SERVER,\n      payload: { invalidateFileSystemCache: shouldInvalidateFileSystemCache },\n    })\n\n    // TODO: Use flushDetached\n    await telemetry.flush()\n\n    // do this async to try to give the response a chance to send\n    // it's not really important if it doesn't though\n    setTimeout(() => {\n      process.exit(RESTART_EXIT_CODE)\n    }, 0)\n\n    return middlewareResponse.noContent(res)\n  }\n\n  async function handleServerStatus(req: IncomingMessage, res: ServerResponse) {\n    if (req.method !== 'GET') {\n      return middlewareResponse.methodNotAllowed(res)\n    }\n\n    return middlewareResponse.json(res, {\n      executionId,\n    })\n  }\n\n  return async function (\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname, searchParams } = new URL(`http://n${req.url}`)\n\n    switch (pathname) {\n      case '/__nextjs_restart_dev':\n        return await handleRestartRequest(req, res, searchParams)\n      case '/__nextjs_server_status':\n        return await handleServerStatus(req, res)\n      default:\n        return next()\n    }\n  }\n}\n"], "names": ["getRestartDevServerMiddleware", "EVENT_DEV_OVERLAY_RESTART_SERVER", "telemetry", "turbopackProject", "webpackCacheDirectories", "executionId", "Math", "floor", "random", "Number", "MAX_SAFE_INTEGER", "handleRestartRequest", "req", "res", "searchParams", "method", "middlewareResponse", "methodNotAllowed", "shouldInvalidateFileSystemCache", "has", "Promise", "all", "Array", "from", "map", "invalidateWebpackFileSystemCache", "invalidateFileSystemCache", "record", "eventName", "payload", "flush", "setTimeout", "process", "exit", "RESTART_EXIT_CODE", "noContent", "handleServerStatus", "json", "next", "pathname", "URL", "url"], "mappings": ";;;;+<PERSON>eg<PERSON>;;;eAAAA;;;uBAbkB;oCACC;mCAE2C;AAE9E,MAAMC,mCAAmC;AAQlC,SAASD,8BAA8B,EAC5CE,SAAS,EACTC,gBAAgB,EAChBC,uBAAuB,EACU;IACjC;;;;;;GAMC,GACD,MAAMC,cACJC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAKC,OAAOC,gBAAgB,IAAI;IAExD,eAAeC,qBACbC,GAAoB,EACpBC,GAAmB,EACnBC,YAA6B;QAE7B,IAAIF,IAAIG,MAAM,KAAK,QAAQ;YACzB,OAAOC,sCAAkB,CAACC,gBAAgB,CAACJ;QAC7C;QAEA,MAAMK,kCAAkCJ,aAAaK,GAAG,CACtD;QAEF,IAAID,iCAAiC;YACnC,IAAId,2BAA2B,MAAM;gBACnC,MAAMgB,QAAQC,GAAG,CACfC,MAAMC,IAAI,CAACnB,yBAAyBoB,GAAG,CACrCC,4CAAgC;YAGtC;YACA,IAAItB,oBAAoB,MAAM;gBAC5B,MAAMA,iBAAiBuB,yBAAyB;YAClD;QACF;QAEAxB,UAAUyB,MAAM,CAAC;YACfC,WAAW3B;YACX4B,SAAS;gBAAEH,2BAA2BR;YAAgC;QACxE;QAEA,0BAA0B;QAC1B,MAAMhB,UAAU4B,KAAK;QAErB,6DAA6D;QAC7D,iDAAiD;QACjDC,WAAW;YACTC,QAAQC,IAAI,CAACC,wBAAiB;QAChC,GAAG;QAEH,OAAOlB,sCAAkB,CAACmB,SAAS,CAACtB;IACtC;IAEA,eAAeuB,mBAAmBxB,GAAoB,EAAEC,GAAmB;QACzE,IAAID,IAAIG,MAAM,KAAK,OAAO;YACxB,OAAOC,sCAAkB,CAACC,gBAAgB,CAACJ;QAC7C;QAEA,OAAOG,sCAAkB,CAACqB,IAAI,CAACxB,KAAK;YAClCR;QACF;IACF;IAEA,OAAO,eACLO,GAAoB,EACpBC,GAAmB,EACnByB,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAEzB,YAAY,EAAE,GAAG,IAAI0B,IAAI,CAAC,QAAQ,EAAE5B,IAAI6B,GAAG,EAAE;QAE/D,OAAQF;YACN,KAAK;gBACH,OAAO,MAAM5B,qBAAqBC,KAAKC,KAAKC;YAC9C,KAAK;gBACH,OAAO,MAAMsB,mBAAmBxB,KAAKC;YACvC;gBACE,OAAOyB;QACX;IACF;AACF", "ignoreList": [0]}