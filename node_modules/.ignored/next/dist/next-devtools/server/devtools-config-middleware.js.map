{"version": 3, "sources": ["../../../src/next-devtools/server/devtools-config-middleware.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type { DevToolsConfig } from '../dev-overlay/shared'\n\nimport { existsSync } from 'fs'\nimport { readFile, writeFile, mkdir } from 'fs/promises'\nimport { dirname, join } from 'path'\n\nimport { middlewareResponse } from './middleware-response'\nimport { devToolsConfigSchema } from '../shared/devtools-config-schema'\nimport { deepMerge } from '../shared/deepmerge'\n\nconst DEVTOOLS_CONFIG_FILENAME = 'next-devtools-config.json'\nconst DEVTOOLS_CONFIG_MIDDLEWARE_ENDPOINT = '/__nextjs_devtools_config'\n\nexport function devToolsConfigMiddleware({\n  distDir,\n  sendUpdateSignal,\n}: {\n  distDir: string\n  sendUpdateSignal: (data: DevToolsConfig) => void\n}) {\n  const configPath = join(distDir, 'cache', DEVTOOLS_CONFIG_FILENAME)\n\n  return async function devToolsConfigMiddlewareHandler(\n    req: IncomingMessage,\n    res: ServerResponse,\n    next: () => void\n  ): Promise<void> {\n    const { pathname } = new URL(`http://n${req.url}`)\n\n    if (pathname !== DEVTOOLS_CONFIG_MIDDLEWARE_ENDPOINT) {\n      return next()\n    }\n\n    if (req.method !== 'POST') {\n      return middlewareResponse.methodNotAllowed(res)\n    }\n\n    const currentConfig = await getDevToolsConfig(distDir)\n\n    const chunks: Buffer[] = []\n    for await (const chunk of req) {\n      chunks.push(Buffer.from(chunk))\n    }\n\n    let body = Buffer.concat(chunks).toString('utf8')\n    try {\n      body = JSON.parse(body)\n    } catch (error) {\n      console.error('[Next.js DevTools] Invalid config body passed:', error)\n      return middlewareResponse.badRequest(res)\n    }\n\n    const validation = devToolsConfigSchema.safeParse(body)\n    if (!validation.success) {\n      console.error(\n        '[Next.js DevTools] Invalid config passed:',\n        validation.error.message\n      )\n      return middlewareResponse.badRequest(res)\n    }\n\n    const newConfig = deepMerge(currentConfig, validation.data)\n    await writeFile(configPath, JSON.stringify(newConfig, null, 2))\n\n    sendUpdateSignal(newConfig)\n\n    return middlewareResponse.noContent(res)\n  }\n}\n\nexport async function getDevToolsConfig(\n  distDir: string\n): Promise<DevToolsConfig> {\n  const configPath = join(distDir, 'cache', DEVTOOLS_CONFIG_FILENAME)\n\n  if (!existsSync(configPath)) {\n    await mkdir(dirname(configPath), { recursive: true })\n    await writeFile(configPath, JSON.stringify({}))\n    return {}\n  }\n\n  return JSON.parse(await readFile(configPath, 'utf8'))\n}\n"], "names": ["devToolsConfigMiddleware", "getDevToolsConfig", "DEVTOOLS_CONFIG_FILENAME", "DEVTOOLS_CONFIG_MIDDLEWARE_ENDPOINT", "distDir", "sendUpdateSignal", "config<PERSON><PERSON>", "join", "devToolsConfigMiddlewareHandler", "req", "res", "next", "pathname", "URL", "url", "method", "middlewareResponse", "methodNotAllowed", "currentConfig", "chunks", "chunk", "push", "<PERSON><PERSON><PERSON>", "from", "body", "concat", "toString", "JSON", "parse", "error", "console", "badRequest", "validation", "devToolsConfigSchema", "safeParse", "success", "message", "newConfig", "deepMerge", "data", "writeFile", "stringify", "noContent", "existsSync", "mkdir", "dirname", "recursive", "readFile"], "mappings": ";;;;;;;;;;;;;;;IAcgBA,wBAAwB;eAAxBA;;IAyDMC,iBAAiB;eAAjBA;;;oBApEK;0BACgB;sBACb;oCAEK;sCACE;2BACX;AAE1B,MAAMC,2BAA2B;AACjC,MAAMC,sCAAsC;AAErC,SAASH,yBAAyB,EACvCI,OAAO,EACPC,gBAAgB,EAIjB;IACC,MAAMC,aAAaC,IAAAA,UAAI,EAACH,SAAS,SAASF;IAE1C,OAAO,eAAeM,gCACpBC,GAAoB,EACpBC,GAAmB,EACnBC,IAAgB;QAEhB,MAAM,EAAEC,QAAQ,EAAE,GAAG,IAAIC,IAAI,CAAC,QAAQ,EAAEJ,IAAIK,GAAG,EAAE;QAEjD,IAAIF,aAAaT,qCAAqC;YACpD,OAAOQ;QACT;QAEA,IAAIF,IAAIM,MAAM,KAAK,QAAQ;YACzB,OAAOC,sCAAkB,CAACC,gBAAgB,CAACP;QAC7C;QAEA,MAAMQ,gBAAgB,MAAMjB,kBAAkBG;QAE9C,MAAMe,SAAmB,EAAE;QAC3B,WAAW,MAAMC,SAASX,IAAK;YAC7BU,OAAOE,IAAI,CAACC,OAAOC,IAAI,CAACH;QAC1B;QAEA,IAAII,OAAOF,OAAOG,MAAM,CAACN,QAAQO,QAAQ,CAAC;QAC1C,IAAI;YACFF,OAAOG,KAAKC,KAAK,CAACJ;QACpB,EAAE,OAAOK,OAAO;YACdC,QAAQD,KAAK,CAAC,kDAAkDA;YAChE,OAAOb,sCAAkB,CAACe,UAAU,CAACrB;QACvC;QAEA,MAAMsB,aAAaC,0CAAoB,CAACC,SAAS,CAACV;QAClD,IAAI,CAACQ,WAAWG,OAAO,EAAE;YACvBL,QAAQD,KAAK,CACX,6CACAG,WAAWH,KAAK,CAACO,OAAO;YAE1B,OAAOpB,sCAAkB,CAACe,UAAU,CAACrB;QACvC;QAEA,MAAM2B,YAAYC,IAAAA,oBAAS,EAACpB,eAAec,WAAWO,IAAI;QAC1D,MAAMC,IAAAA,mBAAS,EAAClC,YAAYqB,KAAKc,SAAS,CAACJ,WAAW,MAAM;QAE5DhC,iBAAiBgC;QAEjB,OAAOrB,sCAAkB,CAAC0B,SAAS,CAAChC;IACtC;AACF;AAEO,eAAeT,kBACpBG,OAAe;IAEf,MAAME,aAAaC,IAAAA,UAAI,EAACH,SAAS,SAASF;IAE1C,IAAI,CAACyC,IAAAA,cAAU,EAACrC,aAAa;QAC3B,MAAMsC,IAAAA,eAAK,EAACC,IAAAA,aAAO,EAACvC,aAAa;YAAEwC,WAAW;QAAK;QACnD,MAAMN,IAAAA,mBAAS,EAAClC,YAAYqB,KAAKc,SAAS,CAAC,CAAC;QAC5C,OAAO,CAAC;IACV;IAEA,OAAOd,KAAKC,KAAK,CAAC,MAAMmB,IAAAA,kBAAQ,EAACzC,YAAY;AAC/C", "ignoreList": [0]}