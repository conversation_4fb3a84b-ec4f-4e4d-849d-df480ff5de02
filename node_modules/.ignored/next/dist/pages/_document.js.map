{"version": 3, "sources": ["../../src/pages/_document.tsx"], "sourcesContent": ["/// <reference types=\"webpack/module.d.ts\" />\n\nimport React, { type JSX } from 'react'\nimport { NEXT_BUILTIN_DOCUMENT } from '../shared/lib/constants'\nimport type {\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps,\n  DocumentType,\n  NEXT_DATA,\n} from '../shared/lib/utils'\nimport type { ScriptProps } from '../client/script'\nimport type { NextFontManifest } from '../build/webpack/plugins/next-font-manifest-plugin'\n\nimport { getPageFiles } from '../server/get-page-files'\nimport type { BuildManifest } from '../server/get-page-files'\nimport { htmlEscapeJsonString } from '../server/htmlescape'\nimport isError from '../lib/is-error'\n\nimport {\n  HtmlContext,\n  useHtmlContext,\n} from '../shared/lib/html-context.shared-runtime'\nimport type { HtmlProps } from '../shared/lib/html-context.shared-runtime'\nimport { encodeURIPath } from '../shared/lib/encode-uri-path'\nimport type { DeepReadonly } from '../shared/lib/deep-readonly'\nimport { getTracer } from '../server/lib/trace/tracer'\nimport { getTracedMetadata } from '../server/lib/trace/utils'\n\nexport type { DocumentContext, DocumentInitialProps, DocumentProps }\n\nexport type OriginProps = {\n  nonce?: string\n  crossOrigin?: 'anonymous' | 'use-credentials' | '' | undefined\n  children?: React.ReactNode\n}\n\ntype DocumentFiles = {\n  sharedFiles: readonly string[]\n  pageFiles: readonly string[]\n  allFiles: readonly string[]\n}\n\ntype HeadHTMLProps = React.DetailedHTMLProps<\n  React.HTMLAttributes<HTMLHeadElement>,\n  HTMLHeadElement\n>\n\ntype HeadProps = OriginProps & HeadHTMLProps\n\n/** Set of pages that have triggered a large data warning on production mode. */\nconst largePageDataWarnings = new Set<string>()\n\nfunction getDocumentFiles(\n  buildManifest: BuildManifest,\n  pathname: string\n): DocumentFiles {\n  const sharedFiles: readonly string[] = getPageFiles(buildManifest, '/_app')\n  const pageFiles: readonly string[] = getPageFiles(buildManifest, pathname)\n\n  return {\n    sharedFiles,\n    pageFiles,\n    allFiles: [...new Set([...sharedFiles, ...pageFiles])],\n  }\n}\n\nfunction getPolyfillScripts(context: HtmlProps, props: OriginProps) {\n  // polyfills.js has to be rendered as nomodule without async\n  // It also has to be the first script to load\n  const {\n    assetPrefix,\n    buildManifest,\n    assetQueryString,\n    disableOptimizedLoading,\n    crossOrigin,\n  } = context\n\n  return buildManifest.polyfillFiles\n    .filter(\n      (polyfill) => polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n    )\n    .map((polyfill) => (\n      <script\n        key={polyfill}\n        defer={!disableOptimizedLoading}\n        nonce={props.nonce}\n        crossOrigin={props.crossOrigin || crossOrigin}\n        noModule={true}\n        src={`${assetPrefix}/_next/${encodeURIPath(\n          polyfill\n        )}${assetQueryString}`}\n      />\n    ))\n}\n\nfunction hasComponentProps(child: any): child is React.ReactElement<any> {\n  return !!child && !!child.props\n}\n\nfunction getDynamicChunks(\n  context: HtmlProps,\n  props: OriginProps,\n  files: DocumentFiles\n) {\n  const {\n    dynamicImports,\n    assetPrefix,\n    isDevelopment,\n    assetQueryString,\n    disableOptimizedLoading,\n    crossOrigin,\n  } = context\n\n  return dynamicImports.map((file) => {\n    if (!file.endsWith('.js') || files.allFiles.includes(file)) return null\n\n    return (\n      <script\n        async={!isDevelopment && disableOptimizedLoading}\n        defer={!disableOptimizedLoading}\n        key={file}\n        src={`${assetPrefix}/_next/${encodeURIPath(file)}${assetQueryString}`}\n        nonce={props.nonce}\n        crossOrigin={props.crossOrigin || crossOrigin}\n      />\n    )\n  })\n}\n\nfunction getScripts(\n  context: HtmlProps,\n  props: OriginProps,\n  files: DocumentFiles\n) {\n  const {\n    assetPrefix,\n    buildManifest,\n    isDevelopment,\n    assetQueryString,\n    disableOptimizedLoading,\n    crossOrigin,\n  } = context\n\n  const normalScripts = files.allFiles.filter((file) => file.endsWith('.js'))\n  const lowPriorityScripts = buildManifest.lowPriorityFiles?.filter((file) =>\n    file.endsWith('.js')\n  )\n\n  return [...normalScripts, ...lowPriorityScripts].map((file) => {\n    return (\n      <script\n        key={file}\n        src={`${assetPrefix}/_next/${encodeURIPath(file)}${assetQueryString}`}\n        nonce={props.nonce}\n        async={!isDevelopment && disableOptimizedLoading}\n        defer={!disableOptimizedLoading}\n        crossOrigin={props.crossOrigin || crossOrigin}\n      />\n    )\n  })\n}\n\nfunction getPreNextWorkerScripts(context: HtmlProps, props: OriginProps) {\n  const { assetPrefix, scriptLoader, crossOrigin, nextScriptWorkers } = context\n\n  // disable `nextScriptWorkers` in edge runtime\n  if (!nextScriptWorkers || process.env.NEXT_RUNTIME === 'edge') return null\n\n  try {\n    // @ts-expect-error: Prevent webpack from processing this require\n    let { partytownSnippet } = __non_webpack_require__(\n      '@builder.io/partytown/integration'!\n    )\n\n    const children = Array.isArray(props.children)\n      ? props.children\n      : [props.children]\n\n    // Check to see if the user has defined their own Partytown configuration\n    const userDefinedConfig = children.find(\n      (child) =>\n        hasComponentProps(child) &&\n        child?.props?.dangerouslySetInnerHTML?.__html.length &&\n        'data-partytown-config' in child.props\n    )\n\n    return (\n      <>\n        {!userDefinedConfig && (\n          <script\n            data-partytown-config=\"\"\n            dangerouslySetInnerHTML={{\n              __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `,\n            }}\n          />\n        )}\n        <script\n          data-partytown=\"\"\n          dangerouslySetInnerHTML={{\n            __html: partytownSnippet(),\n          }}\n        />\n        {(scriptLoader.worker || []).map((file: ScriptProps, index: number) => {\n          const {\n            strategy,\n            src,\n            children: scriptChildren,\n            dangerouslySetInnerHTML,\n            ...scriptProps\n          } = file\n\n          let srcProps: {\n            src?: string\n            dangerouslySetInnerHTML?: ScriptProps['dangerouslySetInnerHTML']\n          } = {}\n\n          if (src) {\n            // Use external src if provided\n            srcProps.src = src\n          } else if (\n            dangerouslySetInnerHTML &&\n            dangerouslySetInnerHTML.__html\n          ) {\n            // Embed inline script if provided with dangerouslySetInnerHTML\n            srcProps.dangerouslySetInnerHTML = {\n              __html: dangerouslySetInnerHTML.__html,\n            }\n          } else if (scriptChildren) {\n            // Embed inline script if provided with children\n            srcProps.dangerouslySetInnerHTML = {\n              __html:\n                typeof scriptChildren === 'string'\n                  ? scriptChildren\n                  : Array.isArray(scriptChildren)\n                    ? scriptChildren.join('')\n                    : '',\n            }\n          } else {\n            throw new Error(\n              'Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script'\n            )\n          }\n\n          return (\n            <script\n              {...srcProps}\n              {...scriptProps}\n              type=\"text/partytown\"\n              key={src || index}\n              nonce={props.nonce}\n              data-nscript=\"worker\"\n              crossOrigin={props.crossOrigin || crossOrigin}\n            />\n          )\n        })}\n      </>\n    )\n  } catch (err) {\n    if (isError(err) && err.code !== 'MODULE_NOT_FOUND') {\n      console.warn(`Warning: ${err.message}`)\n    }\n    return null\n  }\n}\n\nfunction getPreNextScripts(context: HtmlProps, props: OriginProps) {\n  const { scriptLoader, disableOptimizedLoading, crossOrigin } = context\n\n  const webWorkerScripts = getPreNextWorkerScripts(context, props)\n\n  const beforeInteractiveScripts = (scriptLoader.beforeInteractive || [])\n    .filter((script) => script.src)\n    .map((file: ScriptProps, index: number) => {\n      const { strategy, ...scriptProps } = file\n      return (\n        <script\n          {...scriptProps}\n          key={scriptProps.src || index}\n          defer={scriptProps.defer ?? !disableOptimizedLoading}\n          nonce={scriptProps.nonce || props.nonce}\n          data-nscript=\"beforeInteractive\"\n          crossOrigin={props.crossOrigin || crossOrigin}\n        />\n      )\n    })\n\n  return (\n    <>\n      {webWorkerScripts}\n      {beforeInteractiveScripts}\n    </>\n  )\n}\n\nfunction getHeadHTMLProps(props: HeadProps) {\n  const { crossOrigin, nonce, ...restProps } = props\n\n  // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n  const headProps: HeadHTMLProps & {\n    [P in Exclude<keyof HeadProps, keyof HeadHTMLProps>]?: never\n  } = restProps\n\n  return headProps\n}\n\nfunction getNextFontLinkTags(\n  nextFontManifest: DeepReadonly<NextFontManifest> | undefined,\n  dangerousAsPath: string,\n  assetPrefix: string = ''\n) {\n  if (!nextFontManifest) {\n    return {\n      preconnect: null,\n      preload: null,\n    }\n  }\n\n  const appFontsEntry = nextFontManifest.pages['/_app']\n  const pageFontsEntry = nextFontManifest.pages[dangerousAsPath]\n\n  const preloadedFontFiles = Array.from(\n    new Set([...(appFontsEntry ?? []), ...(pageFontsEntry ?? [])])\n  )\n\n  // If no font files should preload but there's an entry for the path, add a preconnect tag.\n  const preconnectToSelf = !!(\n    preloadedFontFiles.length === 0 &&\n    (appFontsEntry || pageFontsEntry)\n  )\n\n  return {\n    preconnect: preconnectToSelf ? (\n      <link\n        data-next-font={\n          nextFontManifest.pagesUsingSizeAdjust ? 'size-adjust' : ''\n        }\n        rel=\"preconnect\"\n        href=\"/\"\n        crossOrigin=\"anonymous\"\n      />\n    ) : null,\n    preload: preloadedFontFiles\n      ? preloadedFontFiles.map((fontFile) => {\n          const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)![1]\n          return (\n            <link\n              key={fontFile}\n              rel=\"preload\"\n              href={`${assetPrefix}/_next/${encodeURIPath(fontFile)}`}\n              as=\"font\"\n              type={`font/${ext}`}\n              crossOrigin=\"anonymous\"\n              data-next-font={fontFile.includes('-s') ? 'size-adjust' : ''}\n            />\n          )\n        })\n      : null,\n  }\n}\n\n// Use `React.Component` to avoid errors from the RSC checks because\n// it can't be imported directly in Server Components:\n//\n//   import { Component } from 'react'\n//\n// More info: https://github.com/vercel/next.js/pull/40686\nexport class Head extends React.Component<HeadProps> {\n  static contextType = HtmlContext\n\n  context!: HtmlProps\n\n  getCssLinks(files: DocumentFiles): JSX.Element[] | null {\n    const {\n      assetPrefix,\n      assetQueryString,\n      dynamicImports,\n      dynamicCssManifest,\n      crossOrigin,\n      optimizeCss,\n    } = this.context\n    const cssFiles = files.allFiles.filter((f) => f.endsWith('.css'))\n    const sharedFiles: Set<string> = new Set(files.sharedFiles)\n\n    // Unmanaged files are CSS files that will be handled directly by the\n    // webpack runtime (`mini-css-extract-plugin`).\n    let unmanagedFiles: Set<string> = new Set([])\n    let localDynamicCssFiles = Array.from(\n      new Set(dynamicImports.filter((file) => file.endsWith('.css')))\n    )\n    if (localDynamicCssFiles.length) {\n      const existing = new Set(cssFiles)\n      localDynamicCssFiles = localDynamicCssFiles.filter(\n        (f) => !(existing.has(f) || sharedFiles.has(f))\n      )\n      unmanagedFiles = new Set(localDynamicCssFiles)\n      cssFiles.push(...localDynamicCssFiles)\n    }\n\n    let cssLinkElements: JSX.Element[] = []\n    cssFiles.forEach((file) => {\n      const isSharedFile = sharedFiles.has(file)\n      const isUnmanagedFile = unmanagedFiles.has(file)\n      const isFileInDynamicCssManifest = dynamicCssManifest.has(file)\n\n      if (!optimizeCss) {\n        cssLinkElements.push(\n          <link\n            key={`${file}-preload`}\n            nonce={this.props.nonce}\n            rel=\"preload\"\n            href={`${assetPrefix}/_next/${encodeURIPath(\n              file\n            )}${assetQueryString}`}\n            as=\"style\"\n            crossOrigin={this.props.crossOrigin || crossOrigin}\n          />\n        )\n      }\n\n      cssLinkElements.push(\n        <link\n          key={file}\n          nonce={this.props.nonce}\n          rel=\"stylesheet\"\n          href={`${assetPrefix}/_next/${encodeURIPath(\n            file\n          )}${assetQueryString}`}\n          crossOrigin={this.props.crossOrigin || crossOrigin}\n          data-n-g={isUnmanagedFile ? undefined : isSharedFile ? '' : undefined}\n          data-n-p={\n            isSharedFile || isUnmanagedFile || isFileInDynamicCssManifest\n              ? undefined\n              : ''\n          }\n        />\n      )\n    })\n\n    return cssLinkElements.length === 0 ? null : cssLinkElements\n  }\n\n  getPreloadDynamicChunks() {\n    const { dynamicImports, assetPrefix, assetQueryString, crossOrigin } =\n      this.context\n\n    return (\n      dynamicImports\n        .map((file) => {\n          if (!file.endsWith('.js')) {\n            return null\n          }\n\n          return (\n            <link\n              rel=\"preload\"\n              key={file}\n              href={`${assetPrefix}/_next/${encodeURIPath(\n                file\n              )}${assetQueryString}`}\n              as=\"script\"\n              nonce={this.props.nonce}\n              crossOrigin={this.props.crossOrigin || crossOrigin}\n            />\n          )\n        })\n        // Filter out nulled scripts\n        .filter(Boolean)\n    )\n  }\n\n  getPreloadMainLinks(files: DocumentFiles): JSX.Element[] | null {\n    const { assetPrefix, assetQueryString, scriptLoader, crossOrigin } =\n      this.context\n    const preloadFiles = files.allFiles.filter((file: string) => {\n      return file.endsWith('.js')\n    })\n\n    return [\n      ...(scriptLoader.beforeInteractive || []).map((file) => (\n        <link\n          key={file.src}\n          nonce={this.props.nonce}\n          rel=\"preload\"\n          href={file.src}\n          as=\"script\"\n          crossOrigin={this.props.crossOrigin || crossOrigin}\n        />\n      )),\n      ...preloadFiles.map((file: string) => (\n        <link\n          key={file}\n          nonce={this.props.nonce}\n          rel=\"preload\"\n          href={`${assetPrefix}/_next/${encodeURIPath(\n            file\n          )}${assetQueryString}`}\n          as=\"script\"\n          crossOrigin={this.props.crossOrigin || crossOrigin}\n        />\n      )),\n    ]\n  }\n\n  getBeforeInteractiveInlineScripts() {\n    const { scriptLoader } = this.context\n    const { nonce, crossOrigin } = this.props\n\n    return (scriptLoader.beforeInteractive || [])\n      .filter(\n        (script) =>\n          !script.src && (script.dangerouslySetInnerHTML || script.children)\n      )\n      .map((file: ScriptProps, index: number) => {\n        const {\n          strategy,\n          children,\n          dangerouslySetInnerHTML,\n          src,\n          ...scriptProps\n        } = file\n        let html: NonNullable<\n          ScriptProps['dangerouslySetInnerHTML']\n        >['__html'] = ''\n\n        if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n          html = dangerouslySetInnerHTML.__html\n        } else if (children) {\n          html =\n            typeof children === 'string'\n              ? children\n              : Array.isArray(children)\n                ? children.join('')\n                : ''\n        }\n\n        return (\n          <script\n            {...scriptProps}\n            dangerouslySetInnerHTML={{ __html: html }}\n            key={scriptProps.id || index}\n            nonce={nonce}\n            data-nscript=\"beforeInteractive\"\n            crossOrigin={\n              crossOrigin ||\n              (process.env.__NEXT_CROSS_ORIGIN as typeof crossOrigin)\n            }\n          />\n        )\n      })\n  }\n\n  getDynamicChunks(files: DocumentFiles) {\n    return getDynamicChunks(this.context, this.props, files)\n  }\n\n  getPreNextScripts() {\n    return getPreNextScripts(this.context, this.props)\n  }\n\n  getScripts(files: DocumentFiles) {\n    return getScripts(this.context, this.props, files)\n  }\n\n  getPolyfillScripts() {\n    return getPolyfillScripts(this.context, this.props)\n  }\n\n  render() {\n    const {\n      styles,\n      __NEXT_DATA__,\n      dangerousAsPath,\n      headTags,\n      unstable_runtimeJS,\n      unstable_JsPreload,\n      disableOptimizedLoading,\n      optimizeCss,\n      assetPrefix,\n      nextFontManifest,\n    } = this.context\n\n    const disableRuntimeJS = unstable_runtimeJS === false\n    const disableJsPreload =\n      unstable_JsPreload === false || !disableOptimizedLoading\n\n    this.context.docComponentsRendered.Head = true\n\n    let { head } = this.context\n    let cssPreloads: Array<JSX.Element> = []\n    let otherHeadElements: Array<JSX.Element> = []\n    if (head) {\n      head.forEach((child) => {\n        if (\n          child &&\n          child.type === 'link' &&\n          child.props['rel'] === 'preload' &&\n          child.props['as'] === 'style'\n        ) {\n          cssPreloads.push(child)\n        } else {\n          if (child) {\n            otherHeadElements.push(\n              React.cloneElement(child, { 'data-next-head': '' })\n            )\n          }\n        }\n      })\n      head = cssPreloads.concat(otherHeadElements)\n    }\n    let children: React.ReactNode[] = React.Children.toArray(\n      this.props.children\n    ).filter(Boolean)\n    // show a warning if Head contains <title> (only in development)\n    if (process.env.NODE_ENV !== 'production') {\n      children = React.Children.map(children, (child: any) => {\n        const isReactHelmet = child?.props?.['data-react-helmet']\n        if (!isReactHelmet) {\n          if (child?.type === 'title') {\n            console.warn(\n              \"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\"\n            )\n          } else if (\n            child?.type === 'meta' &&\n            child?.props?.name === 'viewport'\n          ) {\n            console.warn(\n              \"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\"\n            )\n          }\n        }\n        return child\n        // @types/react bug. Returned value from .map will not be `null` if you pass in `[null]`\n      })!\n      if (this.props.crossOrigin)\n        console.warn(\n          'Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated'\n        )\n    }\n\n    const files: DocumentFiles = getDocumentFiles(\n      this.context.buildManifest,\n      this.context.__NEXT_DATA__.page\n    )\n\n    const nextFontLinkTags = getNextFontLinkTags(\n      nextFontManifest,\n      dangerousAsPath,\n      assetPrefix\n    )\n\n    const tracingMetadata = getTracedMetadata(\n      getTracer().getTracePropagationData(),\n      this.context.experimentalClientTraceMetadata\n    )\n\n    const traceMetaTags = (tracingMetadata || []).map(\n      ({ key, value }, index) => (\n        <meta key={`next-trace-data-${index}`} name={key} content={value} />\n      )\n    )\n\n    return (\n      <head {...getHeadHTMLProps(this.props)}>\n        {this.context.isDevelopment && (\n          <>\n            <style\n              data-next-hide-fouc\n              dangerouslySetInnerHTML={{\n                __html: `body{display:none}`,\n              }}\n            />\n            <noscript data-next-hide-fouc>\n              <style\n                dangerouslySetInnerHTML={{\n                  __html: `body{display:block}`,\n                }}\n              />\n            </noscript>\n          </>\n        )}\n        {head}\n\n        {children}\n\n        {nextFontLinkTags.preconnect}\n        {nextFontLinkTags.preload}\n\n        {this.getBeforeInteractiveInlineScripts()}\n        {!optimizeCss && this.getCssLinks(files)}\n        {!optimizeCss && <noscript data-n-css={this.props.nonce ?? ''} />}\n\n        {!disableRuntimeJS &&\n          !disableJsPreload &&\n          this.getPreloadDynamicChunks()}\n        {!disableRuntimeJS &&\n          !disableJsPreload &&\n          this.getPreloadMainLinks(files)}\n\n        {!disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getPolyfillScripts()}\n\n        {!disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getPreNextScripts()}\n        {!disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getDynamicChunks(files)}\n        {!disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getScripts(files)}\n\n        {optimizeCss && this.getCssLinks(files)}\n        {optimizeCss && <noscript data-n-css={this.props.nonce ?? ''} />}\n        {this.context.isDevelopment && (\n          // this element is used to mount development styles so the\n          // ordering matches production\n          // (by default, style-loader injects at the bottom of <head />)\n          <noscript id=\"__next_css__DO_NOT_USE__\" />\n        )}\n        {traceMetaTags}\n        {styles || null}\n\n        {React.createElement(React.Fragment, {}, ...(headTags || []))}\n      </head>\n    )\n  }\n}\n\nfunction handleDocumentScriptLoaderItems(\n  scriptLoader: { beforeInteractive?: any[] },\n  __NEXT_DATA__: NEXT_DATA,\n  props: any\n): void {\n  if (!props.children) return\n\n  const scriptLoaderItems: ScriptProps[] = []\n\n  const children = Array.isArray(props.children)\n    ? props.children\n    : [props.children]\n\n  const headChildren = children.find(\n    (child: React.ReactElement) => child.type === Head\n  )?.props?.children\n  const bodyChildren = children.find(\n    (child: React.ReactElement) => child.type === 'body'\n  )?.props?.children\n\n  // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n  const combinedChildren = [\n    ...(Array.isArray(headChildren) ? headChildren : [headChildren]),\n    ...(Array.isArray(bodyChildren) ? bodyChildren : [bodyChildren]),\n  ]\n\n  React.Children.forEach(combinedChildren, (child: any) => {\n    if (!child) return\n\n    // When using the `next/script` component, register it in script loader.\n    if (child.type?.__nextScript) {\n      if (child.props.strategy === 'beforeInteractive') {\n        scriptLoader.beforeInteractive = (\n          scriptLoader.beforeInteractive || []\n        ).concat([\n          {\n            ...child.props,\n          },\n        ])\n        return\n      } else if (\n        ['lazyOnload', 'afterInteractive', 'worker'].includes(\n          child.props.strategy\n        )\n      ) {\n        scriptLoaderItems.push(child.props)\n        return\n      } else if (typeof child.props.strategy === 'undefined') {\n        scriptLoaderItems.push({ ...child.props, strategy: 'afterInteractive' })\n        return\n      }\n    }\n  })\n\n  __NEXT_DATA__.scriptLoader = scriptLoaderItems\n}\n\nexport class NextScript extends React.Component<OriginProps> {\n  static contextType = HtmlContext\n\n  context!: HtmlProps\n\n  getDynamicChunks(files: DocumentFiles) {\n    return getDynamicChunks(this.context, this.props, files)\n  }\n\n  getPreNextScripts() {\n    return getPreNextScripts(this.context, this.props)\n  }\n\n  getScripts(files: DocumentFiles) {\n    return getScripts(this.context, this.props, files)\n  }\n\n  getPolyfillScripts() {\n    return getPolyfillScripts(this.context, this.props)\n  }\n\n  static getInlineScriptSource(context: Readonly<HtmlProps>): string {\n    const { __NEXT_DATA__, largePageDataBytes } = context\n    try {\n      const data = JSON.stringify(__NEXT_DATA__)\n\n      if (largePageDataWarnings.has(__NEXT_DATA__.page)) {\n        return htmlEscapeJsonString(data)\n      }\n\n      const bytes =\n        process.env.NEXT_RUNTIME === 'edge'\n          ? new TextEncoder().encode(data).buffer.byteLength\n          : Buffer.from(data).byteLength\n      const prettyBytes = (\n        require('../lib/pretty-bytes') as typeof import('../lib/pretty-bytes')\n      ).default\n\n      if (largePageDataBytes && bytes > largePageDataBytes) {\n        if (process.env.NODE_ENV === 'production') {\n          largePageDataWarnings.add(__NEXT_DATA__.page)\n        }\n\n        console.warn(\n          `Warning: data for page \"${__NEXT_DATA__.page}\"${\n            __NEXT_DATA__.page === context.dangerousAsPath\n              ? ''\n              : ` (path \"${context.dangerousAsPath}\")`\n          } is ${prettyBytes(\n            bytes\n          )} which exceeds the threshold of ${prettyBytes(\n            largePageDataBytes\n          )}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`\n        )\n      }\n\n      return htmlEscapeJsonString(data)\n    } catch (err) {\n      if (isError(err) && err.message.indexOf('circular structure') !== -1) {\n        throw new Error(\n          `Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`\n        )\n      }\n      throw err\n    }\n  }\n\n  render() {\n    const {\n      assetPrefix,\n      buildManifest,\n      unstable_runtimeJS,\n      docComponentsRendered,\n      assetQueryString,\n      disableOptimizedLoading,\n      crossOrigin,\n    } = this.context\n    const disableRuntimeJS = unstable_runtimeJS === false\n\n    docComponentsRendered.NextScript = true\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (this.props.crossOrigin)\n        console.warn(\n          'Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated'\n        )\n    }\n\n    const files: DocumentFiles = getDocumentFiles(\n      this.context.buildManifest,\n      this.context.__NEXT_DATA__.page\n    )\n\n    return (\n      <>\n        {!disableRuntimeJS && buildManifest.devFiles\n          ? buildManifest.devFiles.map((file: string) => (\n              <script\n                key={file}\n                src={`${assetPrefix}/_next/${encodeURIPath(\n                  file\n                )}${assetQueryString}`}\n                nonce={this.props.nonce}\n                crossOrigin={this.props.crossOrigin || crossOrigin}\n              />\n            ))\n          : null}\n        {disableRuntimeJS ? null : (\n          <script\n            id=\"__NEXT_DATA__\"\n            type=\"application/json\"\n            nonce={this.props.nonce}\n            crossOrigin={this.props.crossOrigin || crossOrigin}\n            dangerouslySetInnerHTML={{\n              __html: NextScript.getInlineScriptSource(this.context),\n            }}\n          />\n        )}\n        {disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getPolyfillScripts()}\n        {disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getPreNextScripts()}\n        {disableOptimizedLoading &&\n          !disableRuntimeJS &&\n          this.getDynamicChunks(files)}\n        {disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files)}\n      </>\n    )\n  }\n}\n\nexport function Html(\n  props: React.DetailedHTMLProps<\n    React.HtmlHTMLAttributes<HTMLHtmlElement>,\n    HTMLHtmlElement\n  >\n) {\n  const { docComponentsRendered, locale, scriptLoader, __NEXT_DATA__ } =\n    useHtmlContext()\n\n  docComponentsRendered.Html = true\n  handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props)\n\n  return <html {...props} lang={props.lang || locale || undefined} />\n}\n\nexport function Main() {\n  const { docComponentsRendered } = useHtmlContext()\n  docComponentsRendered.Main = true\n  // @ts-ignore\n  return <next-js-internal-body-render-target />\n}\n\n/**\n * `Document` component handles the initial `document` markup and renders only on the server side.\n * Commonly used for implementing server side rendering for `css-in-js` libraries.\n */\nexport default class Document<P = {}> extends React.Component<\n  DocumentProps & P\n> {\n  /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */\n  static getInitialProps(ctx: DocumentContext): Promise<DocumentInitialProps> {\n    return ctx.defaultGetInitialProps(ctx)\n  }\n\n  render() {\n    return (\n      <Html>\n        <Head nonce={this.props.nonce} />\n        <body>\n          <Main />\n          <NextScript nonce={this.props.nonce} />\n        </body>\n      </Html>\n    )\n  }\n}\n\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument: DocumentType =\n  function InternalFunctionDocument() {\n    return (\n      <Html>\n        <Head />\n        <body>\n          <Main />\n          <NextScript />\n        </body>\n      </Html>\n    )\n  }\n;(Document as any)[NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument\n"], "names": ["Head", "Html", "Main", "NextScript", "Document", "largePageDataWarnings", "Set", "getDocumentFiles", "buildManifest", "pathname", "sharedFiles", "getPageFiles", "pageFiles", "allFiles", "getPolyfillScripts", "context", "props", "assetPrefix", "assetQueryString", "disableOptimizedLoading", "crossOrigin", "polyfillFiles", "filter", "polyfill", "endsWith", "map", "script", "defer", "nonce", "noModule", "src", "encodeURIPath", "hasComponentProps", "child", "getDynamicChunks", "files", "dynamicImports", "isDevelopment", "file", "includes", "async", "getScripts", "normalScripts", "lowPriorityScripts", "lowPriorityFiles", "getPreNextWorkerScripts", "<PERSON><PERSON><PERSON><PERSON>", "nextScriptWorkers", "process", "env", "NEXT_RUNTIME", "partytownSnippet", "__non_webpack_require__", "children", "Array", "isArray", "userDefinedConfig", "find", "dangerouslySetInnerHTML", "__html", "length", "data-partytown-config", "data-partytown", "worker", "index", "strategy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scriptProps", "srcProps", "join", "Error", "type", "key", "data-nscript", "err", "isError", "code", "console", "warn", "message", "getPreNextScripts", "webWorkerScripts", "beforeInteractiveScripts", "beforeInteractive", "getHeadHTMLProps", "restProps", "headProps", "getNextFontLinkTags", "nextFontManifest", "dangerousAsPath", "preconnect", "preload", "appFontsEntry", "pages", "pageFontsEntry", "preloadedFontFiles", "from", "preconnectToSelf", "link", "data-next-font", "pagesUsingSizeAdjust", "rel", "href", "fontFile", "ext", "exec", "as", "React", "Component", "contextType", "HtmlContext", "getCssLinks", "dynamicCssManifest", "optimizeCss", "cssFiles", "f", "unmanagedFiles", "localDynamicCssFiles", "existing", "has", "push", "cssLinkElements", "for<PERSON>ach", "isSharedFile", "isUnmanagedFile", "isFileInDynamicCssManifest", "data-n-g", "undefined", "data-n-p", "getPreloadDynamicChunks", "Boolean", "getPreloadMainLinks", "preloadFiles", "getBeforeInteractiveInlineScripts", "html", "id", "__NEXT_CROSS_ORIGIN", "render", "styles", "__NEXT_DATA__", "headTags", "unstable_runtimeJS", "unstable_JsPreload", "disableRuntimeJS", "disableJsPreload", "doc<PERSON><PERSON><PERSON><PERSON><PERSON>ed", "head", "cssPreloads", "otherHeadElements", "cloneElement", "concat", "Children", "toArray", "NODE_ENV", "isReactHelmet", "name", "page", "nextFontLinkTags", "tracingMetadata", "getTracedMetadata", "getTracer", "getTracePropagationData", "experimentalClientTraceMetadata", "traceMetaTags", "value", "meta", "content", "style", "data-next-hide-fouc", "noscript", "data-n-css", "createElement", "Fragment", "handleDocumentScriptLoaderItems", "scriptLoaderItems", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "combinedChildren", "__nextScript", "getInlineScriptSource", "largePageDataBytes", "data", "JSON", "stringify", "htmlEscapeJsonString", "bytes", "TextEncoder", "encode", "buffer", "byteLength", "<PERSON><PERSON><PERSON>", "prettyBytes", "require", "default", "add", "indexOf", "devFiles", "locale", "useHtmlContext", "lang", "next-js-internal-body-render-target", "getInitialProps", "ctx", "defaultGetInitialProps", "body", "InternalFunctionDocument", "NEXT_BUILTIN_DOCUMENT"], "mappings": "AAAA,6CAA6C;;;;;;;;;;;;;;;;;;;IAmXhCA,IAAI;eAAJA;;IAyiBGC,IAAI;eAAJA;;IAeAC,IAAI;eAAJA;;IApJHC,UAAU;eAAVA;;IA2Jb;;;CAGC,GACD,OAsBC;eAtBoBC;;;;+DAp7BW;2BACM;8BAWT;4BAEQ;gEACjB;0CAKb;+BAEuB;wBAEJ;uBACQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBlC,8EAA8E,GAC9E,MAAMC,wBAAwB,IAAIC;AAElC,SAASC,iBACPC,aAA4B,EAC5BC,QAAgB;IAEhB,MAAMC,cAAiCC,IAAAA,0BAAY,EAACH,eAAe;IACnE,MAAMI,YAA+BD,IAAAA,0BAAY,EAACH,eAAeC;IAEjE,OAAO;QACLC;QACAE;QACAC,UAAU;eAAI,IAAIP,IAAI;mBAAII;mBAAgBE;aAAU;SAAE;IACxD;AACF;AAEA,SAASE,mBAAmBC,OAAkB,EAAEC,KAAkB;IAChE,4DAA4D;IAC5D,6CAA6C;IAC7C,MAAM,EACJC,WAAW,EACXT,aAAa,EACbU,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACZ,GAAGL;IAEJ,OAAOP,cAAca,aAAa,CAC/BC,MAAM,CACL,CAACC,WAAaA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAE9DC,GAAG,CAAC,CAACF,yBACJ,qBAACG;YAECC,OAAO,CAACR;YACRS,OAAOZ,MAAMY,KAAK;YAClBR,aAAaJ,MAAMI,WAAW,IAAIA;YAClCS,UAAU;YACVC,KAAK,GAAGb,YAAY,OAAO,EAAEc,IAAAA,4BAAa,EACxCR,YACEL,kBAAkB;WAPjBK;AAUb;AAEA,SAASS,kBAAkBC,KAAU;IACnC,OAAO,CAAC,CAACA,SAAS,CAAC,CAACA,MAAMjB,KAAK;AACjC;AAEA,SAASkB,iBACPnB,OAAkB,EAClBC,KAAkB,EAClBmB,KAAoB;IAEpB,MAAM,EACJC,cAAc,EACdnB,WAAW,EACXoB,aAAa,EACbnB,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACZ,GAAGL;IAEJ,OAAOqB,eAAeX,GAAG,CAAC,CAACa;QACzB,IAAI,CAACA,KAAKd,QAAQ,CAAC,UAAUW,MAAMtB,QAAQ,CAAC0B,QAAQ,CAACD,OAAO,OAAO;QAEnE,qBACE,qBAACZ;YACCc,OAAO,CAACH,iBAAiBlB;YACzBQ,OAAO,CAACR;YAERW,KAAK,GAAGb,YAAY,OAAO,EAAEc,IAAAA,4BAAa,EAACO,QAAQpB,kBAAkB;YACrEU,OAAOZ,MAAMY,KAAK;YAClBR,aAAaJ,MAAMI,WAAW,IAAIA;WAH7BkB;IAMX;AACF;AAEA,SAASG,WACP1B,OAAkB,EAClBC,KAAkB,EAClBmB,KAAoB;QAYO3B;IAV3B,MAAM,EACJS,WAAW,EACXT,aAAa,EACb6B,aAAa,EACbnB,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACZ,GAAGL;IAEJ,MAAM2B,gBAAgBP,MAAMtB,QAAQ,CAACS,MAAM,CAAC,CAACgB,OAASA,KAAKd,QAAQ,CAAC;IACpE,MAAMmB,sBAAqBnC,kCAAAA,cAAcoC,gBAAgB,qBAA9BpC,gCAAgCc,MAAM,CAAC,CAACgB,OACjEA,KAAKd,QAAQ,CAAC;IAGhB,OAAO;WAAIkB;WAAkBC;KAAmB,CAAClB,GAAG,CAAC,CAACa;QACpD,qBACE,qBAACZ;YAECI,KAAK,GAAGb,YAAY,OAAO,EAAEc,IAAAA,4BAAa,EAACO,QAAQpB,kBAAkB;YACrEU,OAAOZ,MAAMY,KAAK;YAClBY,OAAO,CAACH,iBAAiBlB;YACzBQ,OAAO,CAACR;YACRC,aAAaJ,MAAMI,WAAW,IAAIA;WAL7BkB;IAQX;AACF;AAEA,SAASO,wBAAwB9B,OAAkB,EAAEC,KAAkB;IACrE,MAAM,EAAEC,WAAW,EAAE6B,YAAY,EAAE1B,WAAW,EAAE2B,iBAAiB,EAAE,GAAGhC;IAEtE,8CAA8C;IAC9C,IAAI,CAACgC,qBAAqBC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ,OAAO;IAEtE,IAAI;QACF,iEAAiE;QACjE,IAAI,EAAEC,gBAAgB,EAAE,GAAGC,wBACzB;QAGF,MAAMC,WAAWC,MAAMC,OAAO,CAACvC,MAAMqC,QAAQ,IACzCrC,MAAMqC,QAAQ,GACd;YAACrC,MAAMqC,QAAQ;SAAC;QAEpB,yEAAyE;QACzE,MAAMG,oBAAoBH,SAASI,IAAI,CACrC,CAACxB;gBAECA,sCAAAA;mBADAD,kBAAkBC,WAClBA,0BAAAA,eAAAA,MAAOjB,KAAK,sBAAZiB,uCAAAA,aAAcyB,uBAAuB,qBAArCzB,qCAAuC0B,MAAM,CAACC,MAAM,KACpD,2BAA2B3B,MAAMjB,KAAK;;QAG1C,qBACE;;gBACG,CAACwC,mCACA,qBAAC9B;oBACCmC,yBAAsB;oBACtBH,yBAAyB;wBACvBC,QAAQ,CAAC;;oBAEH,EAAE1C,YAAY;;UAExB,CAAC;oBACC;;8BAGJ,qBAACS;oBACCoC,kBAAe;oBACfJ,yBAAyB;wBACvBC,QAAQR;oBACV;;gBAEAL,CAAAA,aAAaiB,MAAM,IAAI,EAAE,AAAD,EAAGtC,GAAG,CAAC,CAACa,MAAmB0B;oBACnD,MAAM,EACJC,QAAQ,EACRnC,GAAG,EACHuB,UAAUa,cAAc,EACxBR,uBAAuB,EACvB,GAAGS,aACJ,GAAG7B;oBAEJ,IAAI8B,WAGA,CAAC;oBAEL,IAAItC,KAAK;wBACP,+BAA+B;wBAC/BsC,SAAStC,GAAG,GAAGA;oBACjB,OAAO,IACL4B,2BACAA,wBAAwBC,MAAM,EAC9B;wBACA,+DAA+D;wBAC/DS,SAASV,uBAAuB,GAAG;4BACjCC,QAAQD,wBAAwBC,MAAM;wBACxC;oBACF,OAAO,IAAIO,gBAAgB;wBACzB,gDAAgD;wBAChDE,SAASV,uBAAuB,GAAG;4BACjCC,QACE,OAAOO,mBAAmB,WACtBA,iBACAZ,MAAMC,OAAO,CAACW,kBACZA,eAAeG,IAAI,CAAC,MACpB;wBACV;oBACF,OAAO;wBACL,MAAM,qBAEL,CAFK,IAAIC,MACR,iJADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,qBACE,0BAAC5C;wBACE,GAAG0C,QAAQ;wBACX,GAAGD,WAAW;wBACfI,MAAK;wBACLC,KAAK1C,OAAOkC;wBACZpC,OAAOZ,MAAMY,KAAK;wBAClB6C,gBAAa;wBACbrD,aAAaJ,MAAMI,WAAW,IAAIA;;gBAGxC;;;IAGN,EAAE,OAAOsD,KAAK;QACZ,IAAIC,IAAAA,gBAAO,EAACD,QAAQA,IAAIE,IAAI,KAAK,oBAAoB;YACnDC,QAAQC,IAAI,CAAC,CAAC,SAAS,EAAEJ,IAAIK,OAAO,EAAE;QACxC;QACA,OAAO;IACT;AACF;AAEA,SAASC,kBAAkBjE,OAAkB,EAAEC,KAAkB;IAC/D,MAAM,EAAE8B,YAAY,EAAE3B,uBAAuB,EAAEC,WAAW,EAAE,GAAGL;IAE/D,MAAMkE,mBAAmBpC,wBAAwB9B,SAASC;IAE1D,MAAMkE,2BAA2B,AAACpC,CAAAA,aAAaqC,iBAAiB,IAAI,EAAE,AAAD,EAClE7D,MAAM,CAAC,CAACI,SAAWA,OAAOI,GAAG,EAC7BL,GAAG,CAAC,CAACa,MAAmB0B;QACvB,MAAM,EAAEC,QAAQ,EAAE,GAAGE,aAAa,GAAG7B;QACrC,qBACE,0BAACZ;YACE,GAAGyC,WAAW;YACfK,KAAKL,YAAYrC,GAAG,IAAIkC;YACxBrC,OAAOwC,YAAYxC,KAAK,IAAI,CAACR;YAC7BS,OAAOuC,YAAYvC,KAAK,IAAIZ,MAAMY,KAAK;YACvC6C,gBAAa;YACbrD,aAAaJ,MAAMI,WAAW,IAAIA;;IAGxC;IAEF,qBACE;;YACG6D;YACAC;;;AAGP;AAEA,SAASE,iBAAiBpE,KAAgB;IACxC,MAAM,EAAEI,WAAW,EAAEQ,KAAK,EAAE,GAAGyD,WAAW,GAAGrE;IAE7C,sGAAsG;IACtG,MAAMsE,YAEFD;IAEJ,OAAOC;AACT;AAEA,SAASC,oBACPC,gBAA4D,EAC5DC,eAAuB,EACvBxE,cAAsB,EAAE;IAExB,IAAI,CAACuE,kBAAkB;QACrB,OAAO;YACLE,YAAY;YACZC,SAAS;QACX;IACF;IAEA,MAAMC,gBAAgBJ,iBAAiBK,KAAK,CAAC,QAAQ;IACrD,MAAMC,iBAAiBN,iBAAiBK,KAAK,CAACJ,gBAAgB;IAE9D,MAAMM,qBAAqBzC,MAAM0C,IAAI,CACnC,IAAI1F,IAAI;WAAKsF,iBAAiB,EAAE;WAAOE,kBAAkB,EAAE;KAAE;IAG/D,2FAA2F;IAC3F,MAAMG,mBAAmB,CAAC,CACxBF,CAAAA,mBAAmBnC,MAAM,KAAK,KAC7BgC,CAAAA,iBAAiBE,cAAa,CAAC;IAGlC,OAAO;QACLJ,YAAYO,iCACV,qBAACC;YACCC,kBACEX,iBAAiBY,oBAAoB,GAAG,gBAAgB;YAE1DC,KAAI;YACJC,MAAK;YACLlF,aAAY;aAEZ;QACJuE,SAASI,qBACLA,mBAAmBtE,GAAG,CAAC,CAAC8E;YACtB,MAAMC,MAAM,8BAA8BC,IAAI,CAACF,SAAU,CAAC,EAAE;YAC5D,qBACE,qBAACL;gBAECG,KAAI;gBACJC,MAAM,GAAGrF,YAAY,OAAO,EAAEc,IAAAA,4BAAa,EAACwE,WAAW;gBACvDG,IAAG;gBACHnC,MAAM,CAAC,KAAK,EAAEiC,KAAK;gBACnBpF,aAAY;gBACZ+E,kBAAgBI,SAAShE,QAAQ,CAAC,QAAQ,gBAAgB;eANrDgE;QASX,KACA;IACN;AACF;AAQO,MAAMvG,aAAa2G,cAAK,CAACC,SAAS;qBAChCC,cAAcC,qCAAW;IAIhCC,YAAY5E,KAAoB,EAAwB;QACtD,MAAM,EACJlB,WAAW,EACXC,gBAAgB,EAChBkB,cAAc,EACd4E,kBAAkB,EAClB5F,WAAW,EACX6F,WAAW,EACZ,GAAG,IAAI,CAAClG,OAAO;QAChB,MAAMmG,WAAW/E,MAAMtB,QAAQ,CAACS,MAAM,CAAC,CAAC6F,IAAMA,EAAE3F,QAAQ,CAAC;QACzD,MAAMd,cAA2B,IAAIJ,IAAI6B,MAAMzB,WAAW;QAE1D,qEAAqE;QACrE,+CAA+C;QAC/C,IAAI0G,iBAA8B,IAAI9G,IAAI,EAAE;QAC5C,IAAI+G,uBAAuB/D,MAAM0C,IAAI,CACnC,IAAI1F,IAAI8B,eAAed,MAAM,CAAC,CAACgB,OAASA,KAAKd,QAAQ,CAAC;QAExD,IAAI6F,qBAAqBzD,MAAM,EAAE;YAC/B,MAAM0D,WAAW,IAAIhH,IAAI4G;YACzBG,uBAAuBA,qBAAqB/F,MAAM,CAChD,CAAC6F,IAAM,CAAEG,CAAAA,SAASC,GAAG,CAACJ,MAAMzG,YAAY6G,GAAG,CAACJ,EAAC;YAE/CC,iBAAiB,IAAI9G,IAAI+G;YACzBH,SAASM,IAAI,IAAIH;QACnB;QAEA,IAAII,kBAAiC,EAAE;QACvCP,SAASQ,OAAO,CAAC,CAACpF;YAChB,MAAMqF,eAAejH,YAAY6G,GAAG,CAACjF;YACrC,MAAMsF,kBAAkBR,eAAeG,GAAG,CAACjF;YAC3C,MAAMuF,6BAA6Bb,mBAAmBO,GAAG,CAACjF;YAE1D,IAAI,CAAC2E,aAAa;gBAChBQ,gBAAgBD,IAAI,eAClB,qBAACtB;oBAECtE,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;oBACvByE,KAAI;oBACJC,MAAM,GAAGrF,YAAY,OAAO,EAAEc,IAAAA,4BAAa,EACzCO,QACEpB,kBAAkB;oBACtBwF,IAAG;oBACHtF,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;mBAPlC,GAAGkB,KAAK,QAAQ,CAAC;YAU5B;YAEAmF,gBAAgBD,IAAI,eAClB,qBAACtB;gBAECtE,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;gBACvByE,KAAI;gBACJC,MAAM,GAAGrF,YAAY,OAAO,EAAEc,IAAAA,4BAAa,EACzCO,QACEpB,kBAAkB;gBACtBE,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;gBACvC0G,YAAUF,kBAAkBG,YAAYJ,eAAe,KAAKI;gBAC5DC,YACEL,gBAAgBC,mBAAmBC,6BAC/BE,YACA;eAXDzF;QAeX;QAEA,OAAOmF,gBAAgB7D,MAAM,KAAK,IAAI,OAAO6D;IAC/C;IAEAQ,0BAA0B;QACxB,MAAM,EAAE7F,cAAc,EAAEnB,WAAW,EAAEC,gBAAgB,EAAEE,WAAW,EAAE,GAClE,IAAI,CAACL,OAAO;QAEd,OACEqB,eACGX,GAAG,CAAC,CAACa;YACJ,IAAI,CAACA,KAAKd,QAAQ,CAAC,QAAQ;gBACzB,OAAO;YACT;YAEA,qBACE,qBAAC0E;gBACCG,KAAI;gBAEJC,MAAM,GAAGrF,YAAY,OAAO,EAAEc,IAAAA,4BAAa,EACzCO,QACEpB,kBAAkB;gBACtBwF,IAAG;gBACH9E,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;gBACvBR,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;eANlCkB;QASX,EACA,4BAA4B;SAC3BhB,MAAM,CAAC4G;IAEd;IAEAC,oBAAoBhG,KAAoB,EAAwB;QAC9D,MAAM,EAAElB,WAAW,EAAEC,gBAAgB,EAAE4B,YAAY,EAAE1B,WAAW,EAAE,GAChE,IAAI,CAACL,OAAO;QACd,MAAMqH,eAAejG,MAAMtB,QAAQ,CAACS,MAAM,CAAC,CAACgB;YAC1C,OAAOA,KAAKd,QAAQ,CAAC;QACvB;QAEA,OAAO;eACF,AAACsB,CAAAA,aAAaqC,iBAAiB,IAAI,EAAE,AAAD,EAAG1D,GAAG,CAAC,CAACa,qBAC7C,qBAAC4D;oBAECtE,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;oBACvByE,KAAI;oBACJC,MAAMhE,KAAKR,GAAG;oBACd4E,IAAG;oBACHtF,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;mBALlCkB,KAAKR,GAAG;eAQdsG,aAAa3G,GAAG,CAAC,CAACa,qBACnB,qBAAC4D;oBAECtE,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;oBACvByE,KAAI;oBACJC,MAAM,GAAGrF,YAAY,OAAO,EAAEc,IAAAA,4BAAa,EACzCO,QACEpB,kBAAkB;oBACtBwF,IAAG;oBACHtF,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;mBAPlCkB;SAUV;IACH;IAEA+F,oCAAoC;QAClC,MAAM,EAAEvF,YAAY,EAAE,GAAG,IAAI,CAAC/B,OAAO;QACrC,MAAM,EAAEa,KAAK,EAAER,WAAW,EAAE,GAAG,IAAI,CAACJ,KAAK;QAEzC,OAAO,AAAC8B,CAAAA,aAAaqC,iBAAiB,IAAI,EAAE,AAAD,EACxC7D,MAAM,CACL,CAACI,SACC,CAACA,OAAOI,GAAG,IAAKJ,CAAAA,OAAOgC,uBAAuB,IAAIhC,OAAO2B,QAAQ,AAAD,GAEnE5B,GAAG,CAAC,CAACa,MAAmB0B;YACvB,MAAM,EACJC,QAAQ,EACRZ,QAAQ,EACRK,uBAAuB,EACvB5B,GAAG,EACH,GAAGqC,aACJ,GAAG7B;YACJ,IAAIgG,OAEU;YAEd,IAAI5E,2BAA2BA,wBAAwBC,MAAM,EAAE;gBAC7D2E,OAAO5E,wBAAwBC,MAAM;YACvC,OAAO,IAAIN,UAAU;gBACnBiF,OACE,OAAOjF,aAAa,WAChBA,WACAC,MAAMC,OAAO,CAACF,YACZA,SAASgB,IAAI,CAAC,MACd;YACV;YAEA,qBACE,0BAAC3C;gBACE,GAAGyC,WAAW;gBACfT,yBAAyB;oBAAEC,QAAQ2E;gBAAK;gBACxC9D,KAAKL,YAAYoE,EAAE,IAAIvE;gBACvBpC,OAAOA;gBACP6C,gBAAa;gBACbrD,aACEA,eACC4B,QAAQC,GAAG,CAACuF,mBAAmB;;QAIxC;IACJ;IAEAtG,iBAAiBC,KAAoB,EAAE;QACrC,OAAOD,iBAAiB,IAAI,CAACnB,OAAO,EAAE,IAAI,CAACC,KAAK,EAAEmB;IACpD;IAEA6C,oBAAoB;QAClB,OAAOA,kBAAkB,IAAI,CAACjE,OAAO,EAAE,IAAI,CAACC,KAAK;IACnD;IAEAyB,WAAWN,KAAoB,EAAE;QAC/B,OAAOM,WAAW,IAAI,CAAC1B,OAAO,EAAE,IAAI,CAACC,KAAK,EAAEmB;IAC9C;IAEArB,qBAAqB;QACnB,OAAOA,mBAAmB,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,KAAK;IACpD;IAEAyH,SAAS;QACP,MAAM,EACJC,MAAM,EACNC,aAAa,EACblD,eAAe,EACfmD,QAAQ,EACRC,kBAAkB,EAClBC,kBAAkB,EAClB3H,uBAAuB,EACvB8F,WAAW,EACXhG,WAAW,EACXuE,gBAAgB,EACjB,GAAG,IAAI,CAACzE,OAAO;QAEhB,MAAMgI,mBAAmBF,uBAAuB;QAChD,MAAMG,mBACJF,uBAAuB,SAAS,CAAC3H;QAEnC,IAAI,CAACJ,OAAO,CAACkI,qBAAqB,CAACjJ,IAAI,GAAG;QAE1C,IAAI,EAAEkJ,IAAI,EAAE,GAAG,IAAI,CAACnI,OAAO;QAC3B,IAAIoI,cAAkC,EAAE;QACxC,IAAIC,oBAAwC,EAAE;QAC9C,IAAIF,MAAM;YACRA,KAAKxB,OAAO,CAAC,CAACzF;gBACZ,IACEA,SACAA,MAAMsC,IAAI,KAAK,UACftC,MAAMjB,KAAK,CAAC,MAAM,KAAK,aACvBiB,MAAMjB,KAAK,CAAC,KAAK,KAAK,SACtB;oBACAmI,YAAY3B,IAAI,CAACvF;gBACnB,OAAO;oBACL,IAAIA,OAAO;wBACTmH,kBAAkB5B,IAAI,eACpBb,cAAK,CAAC0C,YAAY,CAACpH,OAAO;4BAAE,kBAAkB;wBAAG;oBAErD;gBACF;YACF;YACAiH,OAAOC,YAAYG,MAAM,CAACF;QAC5B;QACA,IAAI/F,WAA8BsD,cAAK,CAAC4C,QAAQ,CAACC,OAAO,CACtD,IAAI,CAACxI,KAAK,CAACqC,QAAQ,EACnB/B,MAAM,CAAC4G;QACT,gEAAgE;QAChE,IAAIlF,QAAQC,GAAG,CAACwG,QAAQ,KAAK,cAAc;YACzCpG,WAAWsD,cAAK,CAAC4C,QAAQ,CAAC9H,GAAG,CAAC4B,UAAU,CAACpB;oBACjBA;gBAAtB,MAAMyH,gBAAgBzH,0BAAAA,eAAAA,MAAOjB,KAAK,qBAAZiB,YAAc,CAAC,oBAAoB;gBACzD,IAAI,CAACyH,eAAe;wBAOhBzH;oBANF,IAAIA,CAAAA,yBAAAA,MAAOsC,IAAI,MAAK,SAAS;wBAC3BM,QAAQC,IAAI,CACV;oBAEJ,OAAO,IACL7C,CAAAA,yBAAAA,MAAOsC,IAAI,MAAK,UAChBtC,CAAAA,0BAAAA,gBAAAA,MAAOjB,KAAK,qBAAZiB,cAAc0H,IAAI,MAAK,YACvB;wBACA9E,QAAQC,IAAI,CACV;oBAEJ;gBACF;gBACA,OAAO7C;YACP,wFAAwF;YAC1F;YACA,IAAI,IAAI,CAACjB,KAAK,CAACI,WAAW,EACxByD,QAAQC,IAAI,CACV;QAEN;QAEA,MAAM3C,QAAuB5B,iBAC3B,IAAI,CAACQ,OAAO,CAACP,aAAa,EAC1B,IAAI,CAACO,OAAO,CAAC4H,aAAa,CAACiB,IAAI;QAGjC,MAAMC,mBAAmBtE,oBACvBC,kBACAC,iBACAxE;QAGF,MAAM6I,kBAAkBC,IAAAA,wBAAiB,EACvCC,IAAAA,iBAAS,IAAGC,uBAAuB,IACnC,IAAI,CAAClJ,OAAO,CAACmJ,+BAA+B;QAG9C,MAAMC,gBAAgB,AAACL,CAAAA,mBAAmB,EAAE,AAAD,EAAGrI,GAAG,CAC/C,CAAC,EAAE+C,GAAG,EAAE4F,KAAK,EAAE,EAAEpG,sBACf,qBAACqG;gBAAsCV,MAAMnF;gBAAK8F,SAASF;eAAhD,CAAC,gBAAgB,EAAEpG,OAAO;QAIzC,qBACE,sBAACkF;YAAM,GAAG9D,iBAAiB,IAAI,CAACpE,KAAK,CAAC;;gBACnC,IAAI,CAACD,OAAO,CAACsB,aAAa,kBACzB;;sCACE,qBAACkI;4BACCC,qBAAmB;4BACnB9G,yBAAyB;gCACvBC,QAAQ,CAAC,kBAAkB,CAAC;4BAC9B;;sCAEF,qBAAC8G;4BAASD,qBAAmB;sCAC3B,cAAA,qBAACD;gCACC7G,yBAAyB;oCACvBC,QAAQ,CAAC,mBAAmB,CAAC;gCAC/B;;;;;gBAKPuF;gBAEA7F;gBAEAwG,iBAAiBnE,UAAU;gBAC3BmE,iBAAiBlE,OAAO;gBAExB,IAAI,CAAC0C,iCAAiC;gBACtC,CAACpB,eAAe,IAAI,CAACF,WAAW,CAAC5E;gBACjC,CAAC8E,6BAAe,qBAACwD;oBAASC,cAAY,IAAI,CAAC1J,KAAK,CAACY,KAAK,IAAI;;gBAE1D,CAACmH,oBACA,CAACC,oBACD,IAAI,CAACf,uBAAuB;gBAC7B,CAACc,oBACA,CAACC,oBACD,IAAI,CAACb,mBAAmB,CAAChG;gBAE1B,CAAChB,2BACA,CAAC4H,oBACD,IAAI,CAACjI,kBAAkB;gBAExB,CAACK,2BACA,CAAC4H,oBACD,IAAI,CAAC/D,iBAAiB;gBACvB,CAAC7D,2BACA,CAAC4H,oBACD,IAAI,CAAC7G,gBAAgB,CAACC;gBACvB,CAAChB,2BACA,CAAC4H,oBACD,IAAI,CAACtG,UAAU,CAACN;gBAEjB8E,eAAe,IAAI,CAACF,WAAW,CAAC5E;gBAChC8E,6BAAe,qBAACwD;oBAASC,cAAY,IAAI,CAAC1J,KAAK,CAACY,KAAK,IAAI;;gBACzD,IAAI,CAACb,OAAO,CAACsB,aAAa,IACzB,0DAA0D;gBAC1D,8BAA8B;gBAC9B,+DAA+D;8BAC/D,qBAACoI;oBAASlC,IAAG;;gBAEd4B;gBACAzB,UAAU;8BAEV/B,cAAK,CAACgE,aAAa,CAAChE,cAAK,CAACiE,QAAQ,EAAE,CAAC,MAAOhC,YAAY,EAAE;;;IAGjE;AACF;AAEA,SAASiC,gCACP/H,YAA2C,EAC3C6F,aAAwB,EACxB3H,KAAU;QAUWqC,sBAAAA,gBAGAA,uBAAAA;IAXrB,IAAI,CAACrC,MAAMqC,QAAQ,EAAE;IAErB,MAAMyH,oBAAmC,EAAE;IAE3C,MAAMzH,WAAWC,MAAMC,OAAO,CAACvC,MAAMqC,QAAQ,IACzCrC,MAAMqC,QAAQ,GACd;QAACrC,MAAMqC,QAAQ;KAAC;IAEpB,MAAM0H,gBAAe1H,iBAAAA,SAASI,IAAI,CAChC,CAACxB,QAA8BA,MAAMsC,IAAI,KAAKvE,2BAD3BqD,uBAAAA,eAElBrC,KAAK,qBAFaqC,qBAEXA,QAAQ;IAClB,MAAM2H,gBAAe3H,kBAAAA,SAASI,IAAI,CAChC,CAACxB,QAA8BA,MAAMsC,IAAI,KAAK,6BAD3BlB,wBAAAA,gBAElBrC,KAAK,qBAFaqC,sBAEXA,QAAQ;IAElB,+GAA+G;IAC/G,MAAM4H,mBAAmB;WACnB3H,MAAMC,OAAO,CAACwH,gBAAgBA,eAAe;YAACA;SAAa;WAC3DzH,MAAMC,OAAO,CAACyH,gBAAgBA,eAAe;YAACA;SAAa;KAChE;IAEDrE,cAAK,CAAC4C,QAAQ,CAAC7B,OAAO,CAACuD,kBAAkB,CAAChJ;YAIpCA;QAHJ,IAAI,CAACA,OAAO;QAEZ,wEAAwE;QACxE,KAAIA,cAAAA,MAAMsC,IAAI,qBAAVtC,YAAYiJ,YAAY,EAAE;YAC5B,IAAIjJ,MAAMjB,KAAK,CAACiD,QAAQ,KAAK,qBAAqB;gBAChDnB,aAAaqC,iBAAiB,GAAG,AAC/BrC,CAAAA,aAAaqC,iBAAiB,IAAI,EAAE,AAAD,EACnCmE,MAAM,CAAC;oBACP;wBACE,GAAGrH,MAAMjB,KAAK;oBAChB;iBACD;gBACD;YACF,OAAO,IACL;gBAAC;gBAAc;gBAAoB;aAAS,CAACuB,QAAQ,CACnDN,MAAMjB,KAAK,CAACiD,QAAQ,GAEtB;gBACA6G,kBAAkBtD,IAAI,CAACvF,MAAMjB,KAAK;gBAClC;YACF,OAAO,IAAI,OAAOiB,MAAMjB,KAAK,CAACiD,QAAQ,KAAK,aAAa;gBACtD6G,kBAAkBtD,IAAI,CAAC;oBAAE,GAAGvF,MAAMjB,KAAK;oBAAEiD,UAAU;gBAAmB;gBACtE;YACF;QACF;IACF;IAEA0E,cAAc7F,YAAY,GAAGgI;AAC/B;AAEO,MAAM3K,mBAAmBwG,cAAK,CAACC,SAAS;qBACtCC,cAAcC,qCAAW;IAIhC5E,iBAAiBC,KAAoB,EAAE;QACrC,OAAOD,iBAAiB,IAAI,CAACnB,OAAO,EAAE,IAAI,CAACC,KAAK,EAAEmB;IACpD;IAEA6C,oBAAoB;QAClB,OAAOA,kBAAkB,IAAI,CAACjE,OAAO,EAAE,IAAI,CAACC,KAAK;IACnD;IAEAyB,WAAWN,KAAoB,EAAE;QAC/B,OAAOM,WAAW,IAAI,CAAC1B,OAAO,EAAE,IAAI,CAACC,KAAK,EAAEmB;IAC9C;IAEArB,qBAAqB;QACnB,OAAOA,mBAAmB,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,KAAK;IACpD;IAEA,OAAOmK,sBAAsBpK,OAA4B,EAAU;QACjE,MAAM,EAAE4H,aAAa,EAAEyC,kBAAkB,EAAE,GAAGrK;QAC9C,IAAI;YACF,MAAMsK,OAAOC,KAAKC,SAAS,CAAC5C;YAE5B,IAAItI,sBAAsBkH,GAAG,CAACoB,cAAciB,IAAI,GAAG;gBACjD,OAAO4B,IAAAA,gCAAoB,EAACH;YAC9B;YAEA,MAAMI,QACJzI,QAAQC,GAAG,CAACC,YAAY,KAAK,SACzB,IAAIwI,cAAcC,MAAM,CAACN,MAAMO,MAAM,CAACC,UAAU,GAChDC,OAAO9F,IAAI,CAACqF,MAAMQ,UAAU;YAClC,MAAME,cAAc,AAClBC,QAAQ,uBACRC,OAAO;YAET,IAAIb,sBAAsBK,QAAQL,oBAAoB;gBACpD,IAAIpI,QAAQC,GAAG,CAACwG,QAAQ,KAAK,cAAc;oBACzCpJ,sBAAsB6L,GAAG,CAACvD,cAAciB,IAAI;gBAC9C;gBAEA/E,QAAQC,IAAI,CACV,CAAC,wBAAwB,EAAE6D,cAAciB,IAAI,CAAC,CAAC,EAC7CjB,cAAciB,IAAI,KAAK7I,QAAQ0E,eAAe,GAC1C,KACA,CAAC,QAAQ,EAAE1E,QAAQ0E,eAAe,CAAC,EAAE,CAAC,CAC3C,IAAI,EAAEsG,YACLN,OACA,gCAAgC,EAAEM,YAClCX,oBACA,mHAAmH,CAAC;YAE1H;YAEA,OAAOI,IAAAA,gCAAoB,EAACH;QAC9B,EAAE,OAAO3G,KAAK;YACZ,IAAIC,IAAAA,gBAAO,EAACD,QAAQA,IAAIK,OAAO,CAACoH,OAAO,CAAC,0BAA0B,CAAC,GAAG;gBACpE,MAAM,qBAEL,CAFK,IAAI7H,MACR,CAAC,wDAAwD,EAAEqE,cAAciB,IAAI,CAAC,sDAAsD,CAAC,GADjI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAMlF;QACR;IACF;IAEA+D,SAAS;QACP,MAAM,EACJxH,WAAW,EACXT,aAAa,EACbqI,kBAAkB,EAClBI,qBAAqB,EACrB/H,gBAAgB,EAChBC,uBAAuB,EACvBC,WAAW,EACZ,GAAG,IAAI,CAACL,OAAO;QAChB,MAAMgI,mBAAmBF,uBAAuB;QAEhDI,sBAAsB9I,UAAU,GAAG;QAEnC,IAAI6C,QAAQC,GAAG,CAACwG,QAAQ,KAAK,cAAc;YACzC,IAAI,IAAI,CAACzI,KAAK,CAACI,WAAW,EACxByD,QAAQC,IAAI,CACV;QAEN;QAEA,MAAM3C,QAAuB5B,iBAC3B,IAAI,CAACQ,OAAO,CAACP,aAAa,EAC1B,IAAI,CAACO,OAAO,CAAC4H,aAAa,CAACiB,IAAI;QAGjC,qBACE;;gBACG,CAACb,oBAAoBvI,cAAc4L,QAAQ,GACxC5L,cAAc4L,QAAQ,CAAC3K,GAAG,CAAC,CAACa,qBAC1B,qBAACZ;wBAECI,KAAK,GAAGb,YAAY,OAAO,EAAEc,IAAAA,4BAAa,EACxCO,QACEpB,kBAAkB;wBACtBU,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;wBACvBR,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;uBALlCkB,SAQT;gBACHyG,mBAAmB,qBAClB,qBAACrH;oBACC6G,IAAG;oBACHhE,MAAK;oBACL3C,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;oBACvBR,aAAa,IAAI,CAACJ,KAAK,CAACI,WAAW,IAAIA;oBACvCsC,yBAAyB;wBACvBC,QAAQxD,WAAWgL,qBAAqB,CAAC,IAAI,CAACpK,OAAO;oBACvD;;gBAGHI,2BACC,CAAC4H,oBACD,IAAI,CAACjI,kBAAkB;gBACxBK,2BACC,CAAC4H,oBACD,IAAI,CAAC/D,iBAAiB;gBACvB7D,2BACC,CAAC4H,oBACD,IAAI,CAAC7G,gBAAgB,CAACC;gBACvBhB,2BAA2B,CAAC4H,oBAAoB,IAAI,CAACtG,UAAU,CAACN;;;IAGvE;AACF;AAEO,SAASlC,KACde,KAGC;IAED,MAAM,EAAEiI,qBAAqB,EAAEoD,MAAM,EAAEvJ,YAAY,EAAE6F,aAAa,EAAE,GAClE2D,IAAAA,wCAAc;IAEhBrD,sBAAsBhJ,IAAI,GAAG;IAC7B4K,gCAAgC/H,cAAc6F,eAAe3H;IAE7D,qBAAO,qBAACsH;QAAM,GAAGtH,KAAK;QAAEuL,MAAMvL,MAAMuL,IAAI,IAAIF,UAAUtE;;AACxD;AAEO,SAAS7H;IACd,MAAM,EAAE+I,qBAAqB,EAAE,GAAGqD,IAAAA,wCAAc;IAChDrD,sBAAsB/I,IAAI,GAAG;IAC7B,aAAa;IACb,qBAAO,qBAACsM;AACV;AAMe,MAAMpM,iBAAyBuG,cAAK,CAACC,SAAS;IAG3D;;;GAGC,GACD,OAAO6F,gBAAgBC,GAAoB,EAAiC;QAC1E,OAAOA,IAAIC,sBAAsB,CAACD;IACpC;IAEAjE,SAAS;QACP,qBACE,sBAACxI;;8BACC,qBAACD;oBAAK4B,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;;8BAC7B,sBAACgL;;sCACC,qBAAC1M;sCACD,qBAACC;4BAAWyB,OAAO,IAAI,CAACZ,KAAK,CAACY,KAAK;;;;;;IAI3C;AACF;AAEA,8EAA8E;AAC9E,2DAA2D;AAC3D,MAAMiL,2BACJ,SAASA;IACP,qBACE,sBAAC5M;;0BACC,qBAACD;0BACD,sBAAC4M;;kCACC,qBAAC1M;kCACD,qBAACC;;;;;AAIT;AACAC,QAAgB,CAAC0M,gCAAqB,CAAC,GAAGD", "ignoreList": [0]}