"use strict";var Pt=Object.create;var ie=Object.defineProperty;var _t=Object.getOwnPropertyDescriptor;var It=Object.getOwnPropertyNames;var Dt=Object.getPrototypeOf,Ut=Object.prototype.hasOwnProperty;var De=(e,r)=>{for(var t in r)ie(e,t,{get:r[t],enumerable:!0})},Ue=(e,r,t,i)=>{if(r&&typeof r=="object"||typeof r=="function")for(let o of It(r))!Ut.call(e,o)&&o!==t&&ie(e,o,{get:()=>r[o],enumerable:!(i=_t(r,o))||i.enumerable});return e};var T=(e,r,t)=>(t=e!=null?Pt(Dt(e)):{},Ue(r||!e||!e.__esModule?ie(t,"default",{value:e,enumerable:!0}):t,e)),Lt=e=>Ue(ie({},"__esModule",{value:!0}),e);var si={};De(si,{Features:()=>R.Features,Instrumentation:()=>Ie,Polyfills:()=>R.Polyfills,__unstable__loadDesignSystem:()=>Jr,compile:()=>Qr,compileAst:()=>Zr,env:()=>ne,loadModule:()=>Pe,normalizePath:()=>de,optimize:()=>ni,toSourceMap:()=>li});module.exports=Lt(si);var Rt=T(require("module")),Ot=require("url");var ne={};De(ne,{DEBUG:()=>he});var he=Kt(process.env.DEBUG);function Kt(e){if(typeof e=="boolean")return e;if(e===void 0)return!1;if(e==="true"||e==="1")return!0;if(e==="false"||e==="0")return!1;if(e==="*")return!0;let r=e.split(",").map(t=>t.split(":")[0]);return r.includes("-tailwindcss")?!1:!!r.includes("tailwindcss")}var F=T(require("enhanced-resolve")),At=require("jiti"),me=T(require("fs")),Oe=T(require("fs/promises")),Y=T(require("path")),Ve=require("url"),R=require("tailwindcss");var oe=T(require("fs/promises")),W=T(require("path")),zt=[/import[\s\S]*?['"](.{3,}?)['"]/gi,/import[\s\S]*from[\s\S]*?['"](.{3,}?)['"]/gi,/export[\s\S]*from[\s\S]*?['"](.{3,}?)['"]/gi,/require\(['"`](.+)['"`]\)/gi],Mt=[".js",".cjs",".mjs"],Ft=["",".js",".cjs",".mjs",".ts",".cts",".mts",".jsx",".tsx"],jt=["",".ts",".cts",".mts",".tsx",".js",".cjs",".mjs",".jsx"];async function Wt(e,r){for(let t of r){let i=`${e}${t}`;if((await oe.default.stat(i).catch(()=>null))?.isFile())return i}for(let t of r){let i=`${e}/index${t}`;if(await oe.default.access(i).then(()=>!0,()=>!1))return i}return null}async function Le(e,r,t,i){let o=Mt.includes(i)?Ft:jt,a=await Wt(W.default.resolve(t,r),o);if(a===null||e.has(a))return;e.add(a),t=W.default.dirname(a),i=W.default.extname(a);let n=await oe.default.readFile(a,"utf-8"),s=[];for(let l of zt)for(let u of n.matchAll(l))u[1].startsWith(".")&&s.push(Le(e,u[1],t,i));await Promise.all(s)}async function Ke(e){let r=new Set;return await Le(r,e,W.default.dirname(e),W.default.extname(e)),Array.from(r)}var Ee=T(require("path"));function B(e){return{kind:"word",value:e}}function Bt(e,r){return{kind:"function",value:e,nodes:r}}function Yt(e){return{kind:"separator",value:e}}function S(e){let r="";for(let t of e)switch(t.kind){case"word":case"separator":{r+=t.value;break}case"function":r+=t.value+"("+S(t.nodes)+")"}return r}var ze=92,Gt=41,Me=58,Fe=44,qt=34,je=61,We=62,Be=60,Ye=10,Ht=40,Zt=39,Qt=47,Ge=32,qe=9;function A(e){e=e.replaceAll(`\r
`,`
`);let r=[],t=[],i=null,o="",a;for(let n=0;n<e.length;n++){let s=e.charCodeAt(n);switch(s){case ze:{o+=e[n]+e[n+1],n++;break}case Qt:{if(o.length>0){let u=B(o);i?i.nodes.push(u):r.push(u),o=""}let l=B(e[n]);i?i.nodes.push(l):r.push(l);break}case Me:case Fe:case je:case We:case Be:case Ye:case Ge:case qe:{if(o.length>0){let c=B(o);i?i.nodes.push(c):r.push(c),o=""}let l=n,u=n+1;for(;u<e.length&&(a=e.charCodeAt(u),!(a!==Me&&a!==Fe&&a!==je&&a!==We&&a!==Be&&a!==Ye&&a!==Ge&&a!==qe));u++);n=u-1;let p=Yt(e.slice(l,u));i?i.nodes.push(p):r.push(p);break}case Zt:case qt:{let l=n;for(let u=n+1;u<e.length;u++)if(a=e.charCodeAt(u),a===ze)u+=1;else if(a===s){n=u;break}o+=e.slice(l,n+1);break}case Ht:{let l=Bt(o,[]);o="",i?i.nodes.push(l):r.push(l),t.push(l),i=l;break}case Gt:{let l=t.pop();if(o.length>0){let u=B(o);l?.nodes.push(u),o=""}t.length>0?i=t[t.length-1]:i=null;break}default:o+=String.fromCharCode(s)}}return o.length>0&&r.push(B(o)),r}var g=class extends Map{constructor(t){super();this.factory=t}get(t){let i=super.get(t);return i===void 0&&(i=this.factory(t,this),this.set(t,i)),i}};var hi=new Uint8Array(256);var ae=new Uint8Array(256);function y(e,r){let t=0,i=[],o=0,a=e.length,n=r.charCodeAt(0);for(let s=0;s<a;s++){let l=e.charCodeAt(s);if(t===0&&l===n){i.push(e.slice(o,s)),o=s+1;continue}switch(l){case 92:s+=1;break;case 39:case 34:for(;++s<a;){let u=e.charCodeAt(s);if(u===92){s+=1;continue}if(u===l)break}break;case 40:ae[t]=41,t++;break;case 91:ae[t]=93,t++;break;case 123:ae[t]=125,t++;break;case 93:case 125:case 41:t>0&&l===ae[t-1]&&t--;break}}return i.push(e.slice(o)),i}var ve=(n=>(n[n.Continue=0]="Continue",n[n.Skip=1]="Skip",n[n.Stop=2]="Stop",n[n.Replace=3]="Replace",n[n.ReplaceSkip=4]="ReplaceSkip",n[n.ReplaceStop=5]="ReplaceStop",n))(ve||{}),w={Continue:{kind:0},Skip:{kind:1},Stop:{kind:2},Replace:e=>({kind:3,nodes:Array.isArray(e)?e:[e]}),ReplaceSkip:e=>({kind:4,nodes:Array.isArray(e)?e:[e]}),ReplaceStop:e=>({kind:5,nodes:Array.isArray(e)?e:[e]})};function v(e,r){typeof r=="function"?He(e,r):He(e,r.enter,r.exit)}function He(e,r=()=>w.Continue,t=()=>w.Continue){let i=[[e,0,null]],o={parent:null,depth:0,path(){let a=[];for(let n=1;n<i.length;n++){let s=i[n][2];s&&a.push(s)}return a}};for(;i.length>0;){let a=i.length-1,n=i[a],s=n[0],l=n[1],u=n[2];if(l>=s.length){i.pop();continue}if(o.parent=u,o.depth=a,l>=0){let m=s[l],d=r(m,o)??w.Continue;switch(d.kind){case 0:{m.nodes&&m.nodes.length>0&&i.push([m.nodes,0,m]),n[1]=~l;continue}case 2:return;case 1:{n[1]=~l;continue}case 3:{s.splice(l,1,...d.nodes);continue}case 5:{s.splice(l,1,...d.nodes);return}case 4:{s.splice(l,1,...d.nodes),n[1]+=d.nodes.length;continue}default:throw new Error(`Invalid \`WalkAction.${ve[d.kind]??`Unknown(${d.kind})`}\` in enter.`)}}let p=~l,c=s[p],f=t(c,o)??w.Continue;switch(f.kind){case 0:n[1]=p+1;continue;case 2:return;case 3:{s.splice(p,1,...f.nodes),n[1]=p+f.nodes.length;continue}case 5:{s.splice(p,1,...f.nodes);return}case 4:{s.splice(p,1,...f.nodes),n[1]=p+f.nodes.length;continue}default:throw new Error(`Invalid \`WalkAction.${ve[f.kind]??`Unknown(${f.kind})`}\` in exit.`)}}}var Ti=new g(e=>{let r=A(e),t=new Set;return v(r,(i,o)=>{let a=o.parent===null?r:o.parent.nodes??[];if(i.kind==="word"&&(i.value==="+"||i.value==="-"||i.value==="*"||i.value==="/")){let n=a.indexOf(i)??-1;if(n===-1)return;let s=a[n-1];if(s?.kind!=="separator"||s.value!==" ")return;let l=a[n+1];if(l?.kind!=="separator"||l.value!==" ")return;t.add(s),t.add(l)}else i.kind==="separator"&&i.value.length>0&&i.value.trim()===""?(a[0]===i||a[a.length-1]===i)&&t.add(i):i.kind==="separator"&&i.value.trim()===","&&(i.value=",")}),t.size>0&&v(r,i=>{if(t.has(i))return t.delete(i),w.ReplaceSkip([])}),we(r),S(r)});var Ei=new g(e=>{let r=A(e);return r.length===3&&r[0].kind==="word"&&r[0].value==="&"&&r[1].kind==="separator"&&r[1].value===":"&&r[2].kind==="function"&&r[2].value==="is"?S(r[2].nodes):e});function we(e){for(let r of e)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=G(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=G(r.value);for(let t=0;t<r.nodes.length;t++)we([r.nodes[t]]);break}r.value=G(r.value),we(r.nodes);break}case"separator":r.value=G(r.value);break;case"word":{(r.value[0]!=="-"||r.value[1]!=="-")&&(r.value=G(r.value));break}default:Jt(r)}}var Ni=new g(e=>{let r=A(e);return r.length===1&&r[0].kind==="function"&&r[0].value==="var"});function Jt(e){throw new Error(`Unexpected value: ${e}`)}function G(e){return e.replaceAll("_",String.raw`\_`).replaceAll(" ","_")}var Xt=process.env.FEATURES_ENV!=="stable";var P=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,Ki=new RegExp(`^${P.source}$`);var zi=new RegExp(`^${P.source}%$`);var Mi=new RegExp(`^${P.source}s*/s*${P.source}$`);var er=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],Fi=new RegExp(`^${P.source}(${er.join("|")})$`);var tr=["deg","rad","grad","turn"],ji=new RegExp(`^${P.source}(${tr.join("|")})$`);var Wi=new RegExp(`^${P.source} +${P.source} +${P.source}$`);function C(e){let r=Number(e);return Number.isInteger(r)&&r>=0&&String(r)===String(e)}function q(e,r){if(r===null)return e;let t=Number(r);return Number.isNaN(t)||(r=`${t*100}%`),r==="100%"?e:`color-mix(in oklab, ${e} ${r}, transparent)`}var nr={"--alpha":or,"--spacing":ar,"--theme":lr,theme:sr};function or(e,r,t,...i){let[o,a]=y(t,"/").map(n=>n.trim());if(!o||!a)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${o||"var(--my-color)"} / ${a||"50%"})\``);if(i.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${o||"var(--my-color)"} / ${a||"50%"})\``);return q(o,a)}function ar(e,r,t,...i){if(!t)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(i.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${i.length+1}.`);let o=e.theme.resolve(null,["--spacing"]);if(!o)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${o} * ${t})`}function lr(e,r,t,...i){if(!t.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let o=!1;t.endsWith(" inline")&&(o=!0,t=t.slice(0,-7)),r.kind==="at-rule"&&(o=!0);let a=e.resolveThemeValue(t,o);if(!a){if(i.length>0)return i.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(i.length===0)return a;let n=i.join(", ");if(n==="initial")return a;if(a==="initial")return n;if(a.startsWith("var(")||a.startsWith("theme(")||a.startsWith("--theme(")){let s=A(a);return fr(s,n),S(s)}return a}function sr(e,r,t,...i){t=ur(t);let o=e.resolveThemeValue(t);if(!o&&i.length>0)return i.join(", ");if(!o)throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return o}var pn=new RegExp(Object.keys(nr).map(e=>`${e}\\(`).join("|"));function ur(e){if(e[0]!=="'"&&e[0]!=='"')return e;let r="",t=e[0];for(let i=1;i<e.length-1;i++){let o=e[i],a=e[i+1];o==="\\"&&(a===t||a==="\\")?(r+=a,i++):r+=o}return r}function fr(e,r){v(e,t=>{if(t.kind==="function"&&!(t.value!=="var"&&t.value!=="theme"&&t.value!=="--theme"))if(t.nodes.length===1)t.nodes.push({kind:"word",value:`, ${r}`});else{let i=t.nodes[t.nodes.length-1];i.kind==="word"&&i.value==="initial"&&(i.value=r)}})}var pr=/^(?<value>[-+]?(?:\d*\.)?\d+)(?<unit>[a-z]+|%)?$/i,Xe=new g(e=>{let r=pr.exec(e);if(!r)return null;let t=r.groups?.value;if(t===void 0)return null;let i=Number(t);if(Number.isNaN(i))return null;let o=r.groups?.unit;return o===void 0?[i,null]:[i,o]});function et(e,r="top",t="right",i="bottom",o="left"){return tt(`${e}-${r}`,`${e}-${t}`,`${e}-${i}`,`${e}-${o}`)}function tt(e="top",r="right",t="bottom",i="left"){return{1:[[e,0],[r,0],[t,0],[i,0]],2:[[e,0],[r,1],[t,0],[i,1]],3:[[e,0],[r,1],[t,2],[i,1]],4:[[e,0],[r,1],[t,2],[i,3]]}}function K(e,r){return{1:[[e,0],[r,0]],2:[[e,0],[r,1]]}}var Rn={inset:tt(),margin:et("margin"),padding:et("padding"),gap:K("row-gap","column-gap")},On={"inset-block":K("top","bottom"),"inset-inline":K("left","right"),"margin-block":K("margin-top","margin-bottom"),"margin-inline":K("margin-left","margin-right"),"padding-block":K("padding-top","padding-bottom"),"padding-inline":K("padding-left","padding-right")};var lo=Symbol();var so=Symbol();var uo=Symbol();var fo=Symbol();var co=Symbol();var po=Symbol();var mo=Symbol();var go=Symbol();var ho=Symbol();var vo=Symbol();var wo=Symbol();var yo=Symbol();var ko=Symbol();var Q=92,se=47,ue=42,at=34,lt=39,Ar=58,fe=59,E=10,ce=13,J=32,X=9,st=123,be=125,Ce=40,ut=41,Cr=91,Sr=93,ft=45,xe=64,$r=33;function te(e,r){let t=r?.from?{file:r.from,code:e}:null;e[0]==="\uFEFF"&&(e=" "+e.slice(1));let i=[],o=[],a=[],n=null,s=null,l="",u="",p=0,c;for(let f=0;f<e.length;f++){let m=e.charCodeAt(f);if(!(m===ce&&(c=e.charCodeAt(f+1),c===E)))if(m===Q)l===""&&(p=f),l+=e.slice(f,f+2),f+=1;else if(m===se&&e.charCodeAt(f+1)===ue){let d=f;for(let h=f+2;h<e.length;h++)if(c=e.charCodeAt(h),c===Q)h+=1;else if(c===ue&&e.charCodeAt(h+1)===se){f=h+1;break}let x=e.slice(d,f+1);if(x.charCodeAt(2)===$r){let h=$e(x.slice(2,-2));o.push(h),t&&(h.src=[t,d,f+1],h.dst=[t,d,f+1])}}else if(m===lt||m===at){let d=ct(e,f,m);l+=e.slice(f,d+1),f=d}else{if((m===J||m===E||m===X)&&(c=e.charCodeAt(f+1))&&(c===J||c===E||c===X||c===ce&&(c=e.charCodeAt(f+2))&&c==E))continue;if(m===E){if(l.length===0)continue;c=l.charCodeAt(l.length-1),c!==J&&c!==E&&c!==X&&(l+=" ")}else if(m===ft&&e.charCodeAt(f+1)===ft&&l.length===0){let d="",x=f,h=-1;for(let k=f+2;k<e.length;k++)if(c=e.charCodeAt(k),c===Q)k+=1;else if(c===lt||c===at)k=ct(e,k,c);else if(c===se&&e.charCodeAt(k+1)===ue){for(let j=k+2;j<e.length;j++)if(c=e.charCodeAt(j),c===Q)j+=1;else if(c===ue&&e.charCodeAt(j+1)===se){k=j+1;break}}else if(h===-1&&c===Ar)h=l.length+k-x;else if(c===fe&&d.length===0){l+=e.slice(x,k),f=k;break}else if(c===Ce)d+=")";else if(c===Cr)d+="]";else if(c===st)d+="}";else if((c===be||e.length-1===k)&&d.length===0){f=k-1,l+=e.slice(x,k);break}else(c===ut||c===Sr||c===be)&&d.length>0&&e[k]===d[d.length-1]&&(d=d.slice(0,-1));let U=Ae(l,h);if(!U)throw new Error("Invalid custom property, expected a value");t&&(U.src=[t,x,f],U.dst=[t,x,f]),n?n.nodes.push(U):i.push(U),l=""}else if(m===fe&&l.charCodeAt(0)===xe)s=ee(l),t&&(s.src=[t,p,f],s.dst=[t,p,f]),n?n.nodes.push(s):i.push(s),l="",s=null;else if(m===fe&&u[u.length-1]!==")"){let d=Ae(l);if(!d){if(l.length===0)continue;throw new Error(`Invalid declaration: \`${l.trim()}\``)}t&&(d.src=[t,p,f],d.dst=[t,p,f]),n?n.nodes.push(d):i.push(d),l=""}else if(m===st&&u[u.length-1]!==")")u+="}",s=_(l.trim()),t&&(s.src=[t,p,f],s.dst=[t,p,f]),n&&n.nodes.push(s),a.push(n),n=s,l="",s=null;else if(m===be&&u[u.length-1]!==")"){if(u==="")throw new Error("Missing opening {");if(u=u.slice(0,-1),l.length>0)if(l.charCodeAt(0)===xe)s=ee(l),t&&(s.src=[t,p,f],s.dst=[t,p,f]),n?n.nodes.push(s):i.push(s),l="",s=null;else{let x=l.indexOf(":");if(n){let h=Ae(l,x);if(!h)throw new Error(`Invalid declaration: \`${l.trim()}\``);t&&(h.src=[t,p,f],h.dst=[t,p,f]),n.nodes.push(h)}}let d=a.pop()??null;d===null&&n&&i.push(n),n=d,l="",s=null}else if(m===Ce)u+=")",l+="(";else if(m===ut){if(u[u.length-1]!==")")throw new Error("Missing opening (");u=u.slice(0,-1),l+=")"}else{if(l.length===0&&(m===J||m===E||m===X))continue;l===""&&(p=f),l+=String.fromCharCode(m)}}}if(l.charCodeAt(0)===xe){let f=ee(l);t&&(f.src=[t,p,e.length],f.dst=[t,p,e.length]),i.push(f)}if(u.length>0&&n){if(n.kind==="rule")throw new Error(`Missing closing } at ${n.selector}`);if(n.kind==="at-rule")throw new Error(`Missing closing } at ${n.name} ${n.params}`)}return o.length>0?o.concat(i):i}function ee(e,r=[]){let t=e,i="";for(let o=5;o<e.length;o++){let a=e.charCodeAt(o);if(a===J||a===X||a===Ce){t=e.slice(0,o),i=e.slice(o);break}}return $(t.trim(),i.trim(),r)}function Ae(e,r=e.indexOf(":")){if(r===-1)return null;let t=e.indexOf("!important",r+1);return V(e.slice(0,r).trim(),e.slice(r+1,t===-1?e.length:t).trim(),t!==-1)}function ct(e,r,t){let i;for(let o=r+1;o<e.length;o++)if(i=e.charCodeAt(o),i===Q)o+=1;else{if(i===t)return o;if(i===fe&&(e.charCodeAt(o+1)===E||e.charCodeAt(o+1)===ce&&e.charCodeAt(o+2)===E))throw new Error(`Unterminated string: ${e.slice(r,o+1)+String.fromCharCode(t)}`);if(i===E||i===ce&&e.charCodeAt(o+1)===E)throw new Error(`Unterminated string: ${e.slice(r,o)+String.fromCharCode(t)}`)}return r}var Te={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}};function M(e){return{__BARE_VALUE__:e}}var N=M(e=>{if(C(e.value))return e.value}),b=M(e=>{if(C(e.value))return`${e.value}%`}),I=M(e=>{if(C(e.value))return`${e.value}px`}),dt=M(e=>{if(C(e.value))return`${e.value}ms`}),pe=M(e=>{if(C(e.value))return`${e.value}deg`}),Rr=M(e=>{if(e.fraction===null)return;let[r,t]=y(e.fraction,"/");if(!(!C(r)||!C(t)))return e.fraction}),mt=M(e=>{if(C(Number(e.value)))return`repeat(${e.value}, minmax(0, 1fr))`}),Or={accentColor:({theme:e})=>e("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...Rr},backdropBlur:({theme:e})=>e("blur"),backdropBrightness:({theme:e})=>({...e("brightness"),...b}),backdropContrast:({theme:e})=>({...e("contrast"),...b}),backdropGrayscale:({theme:e})=>({...e("grayscale"),...b}),backdropHueRotate:({theme:e})=>({...e("hueRotate"),...pe}),backdropInvert:({theme:e})=>({...e("invert"),...b}),backdropOpacity:({theme:e})=>({...e("opacity"),...b}),backdropSaturate:({theme:e})=>({...e("saturate"),...b}),backdropSepia:({theme:e})=>({...e("sepia"),...b}),backgroundColor:({theme:e})=>e("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:e})=>e("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),borderOpacity:({theme:e})=>e("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:e})=>e("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...I},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:e})=>e("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...b},caretColor:({theme:e})=>e("colors"),colors:()=>({...Te}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...N},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...b},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:e})=>e("borderColor"),divideOpacity:({theme:e})=>e("borderOpacity"),divideWidth:({theme:e})=>({...e("borderWidth"),...I}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:e})=>e("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...e("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...N},flexShrink:{0:"0",DEFAULT:"1",...N},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:e})=>e("spacing"),gradientColorStops:({theme:e})=>e("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...b},grayscale:{0:"0",DEFAULT:"100%",...b},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...N},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...N},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...N},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...N},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...mt},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...mt},height:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...pe},inset:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),invert:{0:"0",DEFAULT:"100%",...b},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:e})=>({auto:"auto",...e("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...N},maxHeight:({theme:e})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),maxWidth:({theme:e})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...e("spacing")}),minHeight:({theme:e})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),minWidth:({theme:e})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...b},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...N},outlineColor:({theme:e})=>e("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...I},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...I},padding:({theme:e})=>e("spacing"),placeholderColor:({theme:e})=>e("colors"),placeholderOpacity:({theme:e})=>e("opacity"),ringColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),ringOffsetColor:({theme:e})=>e("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...I},ringOpacity:({theme:e})=>({DEFAULT:"0.5",...e("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...I},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...pe},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...b},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...b},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:e})=>e("spacing"),scrollPadding:({theme:e})=>e("spacing"),sepia:{0:"0",DEFAULT:"100%",...b},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...pe},space:({theme:e})=>e("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:e})=>({none:"none",...e("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...N},supports:{},data:{},textColor:({theme:e})=>e("colors"),textDecorationColor:({theme:e})=>e("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...I},textIndent:({theme:e})=>e("spacing"),textOpacity:({theme:e})=>e("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...I},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...dt},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...dt},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:e})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),size:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),width:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...N}};var _r=64;function L(e,r=[]){return{kind:"rule",selector:e,nodes:r}}function $(e,r="",t=[]){return{kind:"at-rule",name:e,params:r,nodes:t}}function _(e,r=[]){return e.charCodeAt(0)===_r?ee(e,r):L(e,r)}function V(e,r,t=!1){return{kind:"declaration",property:e,value:r,important:t}}function $e(e){return{kind:"comment",value:e}}function z(e,r){let t=0,i={file:null,code:""};function o(n,s=0){let l="",u="  ".repeat(s);if(n.kind==="declaration"){if(l+=`${u}${n.property}: ${n.value}${n.important?" !important":""};
`,r){t+=u.length;let p=t;t+=n.property.length,t+=2,t+=n.value?.length??0,n.important&&(t+=11);let c=t;t+=2,n.dst=[i,p,c]}}else if(n.kind==="rule"){if(l+=`${u}${n.selector} {
`,r){t+=u.length;let p=t;t+=n.selector.length,t+=1;let c=t;n.dst=[i,p,c],t+=2}for(let p of n.nodes)l+=o(p,s+1);l+=`${u}}
`,r&&(t+=u.length,t+=2)}else if(n.kind==="at-rule"){if(n.nodes.length===0){let p=`${u}${n.name} ${n.params};
`;if(r){t+=u.length;let c=t;t+=n.name.length,t+=1,t+=n.params.length;let f=t;t+=2,n.dst=[i,c,f]}return p}if(l+=`${u}${n.name}${n.params?` ${n.params} `:" "}{
`,r){t+=u.length;let p=t;t+=n.name.length,n.params&&(t+=1,t+=n.params.length),t+=1;let c=t;n.dst=[i,p,c],t+=2}for(let p of n.nodes)l+=o(p,s+1);l+=`${u}}
`,r&&(t+=u.length,t+=2)}else if(n.kind==="comment"){if(l+=`${u}/*${n.value}*/
`,r){t+=u.length;let p=t;t+=2+n.value.length+2;let c=t;n.dst=[i,p,c],t+=1}}else if(n.kind==="context"||n.kind==="at-root")return"";return l}let a="";for(let n of e)a+=o(n,0);return i.code=a,a}function Ir(e,r){if(typeof e!="string")throw new TypeError("expected path to be a string");if(e==="\\"||e==="/")return"/";var t=e.length;if(t<=1)return e;var i="";if(t>4&&e[3]==="\\"){var o=e[2];(o==="?"||o===".")&&e.slice(0,2)==="\\\\"&&(e=e.slice(2),i="//")}var a=e.split(/[/\\]+/);return r!==!1&&a[a.length-1]===""&&a.pop(),i+a.join("/")}function de(e){let r=Ir(e);return e.startsWith("\\\\")&&r.startsWith("/")&&!r.startsWith("//")?`/${r}`:r}var Ne=/(?<!@import\s+)(?<=^|[^\w\-\u0080-\uffff])url\((\s*('[^']+'|"[^"]+")\s*|[^'")]+)\)/,gt=/(?<=image-set\()((?:[\w-]{1,256}\([^)]*\)|[^)])*)(?=\))/,Dr=/(?:gradient|element|cross-fade|image)\(/,Ur=/^\s*data:/i,Lr=/^([a-z]+:)?\/\//,Kr=/^[A-Z_][.\w-]*\(/i,zr=/(?:^|\s)(?<url>[\w-]+\([^)]*\)|"[^"]*"|'[^']*'|[^,]\S*[^,])\s*(?:\s(?<descriptor>\w[^,]+))?(?:,|$)/g,Mr=/(?<!\\)"/g,Fr=/(?: |\\t|\\n|\\f|\\r)+/g,jr=e=>Ur.test(e),Wr=e=>Lr.test(e);async function ht({css:e,base:r,root:t}){if(!e.includes("url(")&&!e.includes("image-set("))return e;let i=te(e),o=[];function a(n){if(n[0]==="/")return n;let s=Ee.posix.join(de(r),n),l=Ee.posix.relative(de(t),s);return l.startsWith(".")||(l="./"+l),l}return v(i,n=>{if(n.kind!=="declaration"||!n.value)return;let s=Ne.test(n.value),l=gt.test(n.value);if(s||l){let u=l?Br:vt;o.push(u(n.value,a).then(p=>{n.value=p}))}}),o.length&&await Promise.all(o),z(i)}function vt(e,r){return yt(e,Ne,async t=>{let[i,o]=t;return await wt(o.trim(),i,r)})}async function Br(e,r){return await yt(e,gt,async t=>{let[,i]=t;return await Gr(i,async({url:a})=>Ne.test(a)?await vt(a,r):Dr.test(a)?a:await wt(a,a,r))})}async function wt(e,r,t,i="url"){let o="",a=e[0];if((a==='"'||a==="'")&&(o=a,e=e.slice(1,-1)),Yr(e))return r;let n=await t(e);return o===""&&n!==encodeURI(n)&&(o='"'),o==="'"&&n.includes("'")&&(o='"'),o==='"'&&n.includes('"')&&(n=n.replace(Mr,'\\"')),`${i}(${o}${n}${o})`}function Yr(e,r){return Wr(e)||jr(e)||!e[0].match(/[\.a-zA-Z0-9_]/)||Kr.test(e)}function Gr(e,r){return Promise.all(qr(e).map(async({url:t,descriptor:i})=>({url:await r({url:t,descriptor:i}),descriptor:i}))).then(Hr)}function qr(e){let r=e.trim().replace(Fr," ").replace(/\r?\n/,"").replace(/,\s+/,", ").replaceAll(/\s+/g," ").matchAll(zr);return Array.from(r,({groups:t})=>({url:t?.url?.trim()??"",descriptor:t?.descriptor?.trim()??""})).filter(({url:t})=>!!t)}function Hr(e){return e.map(({url:r,descriptor:t})=>r+(t?` ${t}`:"")).join(", ")}async function yt(e,r,t){let i,o=e,a="";for(;i=r.exec(o);)a+=o.slice(0,i.index),a+=await t(i),o=o.slice(i.index+i[0].length);return a+=o,a}var ii={};function Ct({base:e,from:r,polyfills:t,onDependency:i,shouldRewriteUrls:o,customCssResolver:a,customJsResolver:n}){return{base:e,polyfills:t,from:r,async loadModule(s,l){return Pe(s,l,i,n)},async loadStylesheet(s,l){let u=await $t(s,l,i,a);return o&&(u.content=await ht({css:u.content,root:e,base:u.base})),u}}}async function St(e,r){if(e.root&&e.root!=="none"){let t=/[*{]/,i=[];for(let a of e.root.pattern.split("/")){if(t.test(a))break;i.push(a)}if(!await Oe.default.stat(Y.default.resolve(r,i.join("/"))).then(a=>a.isDirectory()).catch(()=>!1))throw new Error(`The \`source(${e.root.pattern})\` does not exist`)}}async function Zr(e,r){let t=await(0,R.compileAst)(e,Ct(r));return await St(t,r.base),t}async function Qr(e,r){let t=await(0,R.compile)(e,Ct(r));return await St(t,r.base),t}async function Jr(e,{base:r}){return(0,R.__unstable__loadDesignSystem)(e,{base:r,async loadModule(t,i){return Pe(t,i,()=>{})},async loadStylesheet(t,i){return $t(t,i,()=>{})}})}async function Pe(e,r,t,i){if(e[0]!=="."){let s=await xt(e,r,i);if(!s)throw new Error(`Could not resolve '${e}' from '${r}'`);let l=await bt((0,Ve.pathToFileURL)(s).href);return{path:s,base:Y.default.dirname(s),module:l.default??l}}let o=await xt(e,r,i);if(!o)throw new Error(`Could not resolve '${e}' from '${r}'`);let[a,n]=await Promise.all([bt((0,Ve.pathToFileURL)(o).href+"?id="+Date.now()),Ke(o)]);for(let s of n)t(s);return{path:o,base:Y.default.dirname(o),module:a.default??a}}async function $t(e,r,t,i){let o=await ei(e,r,i);if(!o)throw new Error(`Could not resolve '${e}' from '${r}'`);if(t(o),typeof globalThis.__tw_readFile=="function"){let n=await globalThis.__tw_readFile(o,"utf-8");if(n)return{path:o,base:Y.default.dirname(o),content:n}}let a=await Oe.default.readFile(o,"utf-8");return{path:o,base:Y.default.dirname(o),content:a}}var kt=null;async function bt(e){if(typeof globalThis.__tw_load=="function"){let r=await globalThis.__tw_load(e);if(r)return r}try{return await import(e)}catch{return kt??=(0,At.createJiti)(ii.url,{moduleCache:!1,fsCache:!1}),await kt.import(e)}}var _e=["node_modules",...process.env.NODE_PATH?[process.env.NODE_PATH]:[]],Xr=F.default.ResolverFactory.createResolver({fileSystem:new F.default.CachedInputFileSystem(me.default,4e3),useSyncFileSystemCalls:!0,extensions:[".css"],mainFields:["style"],conditionNames:["style"],modules:_e});async function ei(e,r,t){if(typeof globalThis.__tw_resolve=="function"){let i=globalThis.__tw_resolve(e,r);if(i)return Promise.resolve(i)}if(t){let i=await t(e,r);if(i)return i}return Re(Xr,e,r)}var ti=F.default.ResolverFactory.createResolver({fileSystem:new F.default.CachedInputFileSystem(me.default,4e3),useSyncFileSystemCalls:!0,extensions:[".js",".json",".node",".ts"],conditionNames:["node","import"],modules:_e}),ri=F.default.ResolverFactory.createResolver({fileSystem:new F.default.CachedInputFileSystem(me.default,4e3),useSyncFileSystemCalls:!0,extensions:[".js",".json",".node",".ts"],conditionNames:["node","require"],modules:_e});async function xt(e,r,t){if(typeof globalThis.__tw_resolve=="function"){let i=globalThis.__tw_resolve(e,r);if(i)return Promise.resolve(i)}if(t){let i=await t(e,r);if(i)return i}return Re(ti,e,r).catch(()=>Re(ri,e,r))}function Re(e,r,t){return new Promise((i,o)=>e.resolve({},t,r,{},(a,n)=>{if(a)return o(a);i(n)}))}Symbol.dispose??=Symbol("Symbol.dispose");Symbol.asyncDispose??=Symbol("Symbol.asyncDispose");var Ie=class{constructor(r=t=>void process.stderr.write(`${t}
`)){this.defaultFlush=r}#r=new g(()=>({value:0}));#t=new g(()=>({value:0n}));#e=[];hit(r){this.#r.get(r).value++}start(r){let t=this.#e.map(o=>o.label).join("//"),i=`${t}${t.length===0?"":"//"}${r}`;this.#r.get(i).value++,this.#t.get(i),this.#e.push({id:i,label:r,namespace:t,value:process.hrtime.bigint()})}end(r){let t=process.hrtime.bigint();if(this.#e[this.#e.length-1].label!==r)throw new Error(`Mismatched timer label: \`${r}\`, expected \`${this.#e[this.#e.length-1].label}\``);let i=this.#e.pop(),o=t-i.value;this.#t.get(i.id).value+=o}reset(){this.#r.clear(),this.#t.clear(),this.#e.splice(0)}report(r=this.defaultFlush){let t=[],i=!1;for(let n=this.#e.length-1;n>=0;n--)this.end(this.#e[n].label);for(let[n,{value:s}]of this.#r.entries()){if(this.#t.has(n))continue;t.length===0&&(i=!0,t.push("Hits:"));let l=n.split("//").length;t.push(`${"  ".repeat(l)}${n} ${ge(Tt(`\xD7 ${s}`))}`)}this.#t.size>0&&i&&t.push(`
Timers:`);let o=-1/0,a=new Map;for(let[n,{value:s}]of this.#t){let l=`${(Number(s)/1e6).toFixed(2)}ms`;a.set(n,l),o=Math.max(o,l.length)}for(let n of this.#t.keys()){let s=n.split("//").length;t.push(`${ge(`[${a.get(n).padStart(o," ")}]`)}${"  ".repeat(s-1)}${s===1?" ":ge(" \u21B3 ")}${n.split("//").pop()} ${this.#r.get(n).value===1?"":ge(Tt(`\xD7 ${this.#r.get(n).value}`))}`.trimEnd())}r(`
${t.join(`
`)}
`),this.reset()}[Symbol.dispose](){he&&this.report()}};function ge(e){return`\x1B[2m${e}\x1B[22m`}function Tt(e){return`\x1B[34m${e}\x1B[39m`}var Et=T(require("@jridgewell/remapping")),D=require("lightningcss"),Nt=T(require("magic-string"));function ni(e,{file:r="input.css",minify:t=!1,map:i}={}){function o(l,u){return(0,D.transform)({filename:r,code:l,minify:t,sourceMap:typeof u<"u",inputSourceMap:u,drafts:{customMedia:!0},nonStandard:{deepSelectorCombinator:!0},include:D.Features.Nesting|D.Features.MediaQueries,exclude:D.Features.LogicalProperties|D.Features.DirSelector|D.Features.LightDark,targets:{safari:16<<16|1024,ios_saf:16<<16|1024,firefox:8388608,chrome:7274496},errorRecovery:!0})}let a=o(Buffer.from(e),i);if(i=a.map?.toString(),a.warnings=a.warnings.filter(l=>!/'(deep|slotted|global)' is not recognized as a valid pseudo-/.test(l.message)),a.warnings.length>0){let l=e.split(`
`),u=[`Found ${a.warnings.length} ${a.warnings.length===1?"warning":"warnings"} while optimizing generated CSS:`];for(let[p,c]of a.warnings.entries()){u.push(""),a.warnings.length>1&&u.push(`Issue #${p+1}:`);let f=2,m=Math.max(0,c.loc.line-f-1),d=Math.min(l.length,c.loc.line+f),x=l.slice(m,d).map((h,U)=>m+U+1===c.loc.line?`${re("\u2502")} ${h}`:re(`\u2502 ${h}`));x.splice(c.loc.line-m,0,`${re("\u2506")}${" ".repeat(c.loc.column-1)} ${oi(`${re("^--")} ${c.message}`)}`,`${re("\u2506")}`),u.push(...x)}u.push(""),console.warn(u.join(`
`))}a=o(a.code,i),i=a.map?.toString();let n=a.code.toString(),s=new Nt.default(n);if(s.replaceAll("@media not (","@media not all and ("),i!==void 0&&s.hasChanged()){let l=s.generateMap({source:"original",hires:"boundary"}).toString();i=(0,Et.default)([l,i],()=>null).toString()}return n=s.toString(),{code:n,map:i}}function re(e){return`\x1B[2m${e}\x1B[22m`}function oi(e){return`\x1B[33m${e}\x1B[39m`}var Vt=require("source-map-js");function ai(e){let r=new Vt.SourceMapGenerator,t=1,i=new g(o=>({url:o?.url??`<unknown ${t++}>`,content:o?.content??"<none>"}));for(let o of e.mappings){let a=i.get(o.originalPosition?.source??null);r.addMapping({generated:o.generatedPosition,original:o.originalPosition,source:a.url,name:o.name}),r.setSourceContent(a.url,a.content)}return r.toString()}function li(e){let r=typeof e=="string"?e:ai(e);return{raw:r,get inline(){let t="";return t+="/*# sourceMappingURL=data:application/json;base64,",t+=Buffer.from(r,"utf-8").toString("base64"),t+=` */
`,t}}}process.versions.bun||Rt.register?.((0,Ot.pathToFileURL)(require.resolve("@tailwindcss/node/esm-cache-loader")));0&&(module.exports={Features,Instrumentation,Polyfills,__unstable__loadDesignSystem,compile,compileAst,env,loadModule,normalizePath,optimize,toSourceMap});
