var je=Object.defineProperty;var o=(e,t)=>je(e,"name",{value:t,configurable:!0});import m from"node:path";import ie from"node:fs";import Fe from"node:module";import{resolveExports as De}from"resolve-pkg-maps";import Ee from"fs";import he from"os";import Ie from"path";function h(e){return e.startsWith("\\\\?\\")?e:e.replace(/\\/g,"/")}o(h,"slash");const G=o(e=>{const t=ie[e];return(s,...n)=>{const l=`${e}:${n.join(":")}`;let i=s==null?void 0:s.get(l);return i===void 0&&(i=Reflect.apply(t,ie,n),s==null||s.set(l,i)),i}},"cacheFs"),I=G("existsSync"),Be=G("readFileSync"),R=G("statSync"),oe=o((e,t,s)=>{for(;;){const n=m.posix.join(e,t);if(I(s,n))return n;const l=m.dirname(e);if(l===e)return;e=l}},"findUp"),Q=/^\.{1,2}(\/.*)?$/,H=o(e=>{const t=h(e);return Q.test(t)?t:`./${t}`},"normalizeRelativePath");function Le(e,t=!1){const s=e.length;let n=0,l="",i=0,r=16,f=0,u=0,g=0,w=0,b=0;function y(c,_){let D=0,T=0;for(;D<c;){let p=e.charCodeAt(n);if(p>=48&&p<=57)T=T*16+p-48;else if(p>=65&&p<=70)T=T*16+p-65+10;else if(p>=97&&p<=102)T=T*16+p-97+10;else break;n++,D++}return D<c&&(T=-1),T}o(y,"scanHexDigits");function v(c){n=c,l="",i=0,r=16,b=0}o(v,"setPosition");function j(){let c=n;if(e.charCodeAt(n)===48)n++;else for(n++;n<e.length&&$(e.charCodeAt(n));)n++;if(n<e.length&&e.charCodeAt(n)===46)if(n++,n<e.length&&$(e.charCodeAt(n)))for(n++;n<e.length&&$(e.charCodeAt(n));)n++;else return b=3,e.substring(c,n);let _=n;if(n<e.length&&(e.charCodeAt(n)===69||e.charCodeAt(n)===101))if(n++,(n<e.length&&e.charCodeAt(n)===43||e.charCodeAt(n)===45)&&n++,n<e.length&&$(e.charCodeAt(n))){for(n++;n<e.length&&$(e.charCodeAt(n));)n++;_=n}else b=3;return e.substring(c,_)}o(j,"scanNumber");function d(){let c="",_=n;for(;;){if(n>=s){c+=e.substring(_,n),b=2;break}const D=e.charCodeAt(n);if(D===34){c+=e.substring(_,n),n++;break}if(D===92){if(c+=e.substring(_,n),n++,n>=s){b=2;break}switch(e.charCodeAt(n++)){case 34:c+='"';break;case 92:c+="\\";break;case 47:c+="/";break;case 98:c+="\b";break;case 102:c+="\f";break;case 110:c+=`
`;break;case 114:c+="\r";break;case 116:c+="	";break;case 117:const p=y(4);p>=0?c+=String.fromCharCode(p):b=4;break;default:b=5}_=n;continue}if(D>=0&&D<=31)if(S(D)){c+=e.substring(_,n),b=2;break}else b=6;n++}return c}o(d,"scanString");function A(){if(l="",b=0,i=n,u=f,w=g,n>=s)return i=s,r=17;let c=e.charCodeAt(n);if(X(c)){do n++,l+=String.fromCharCode(c),c=e.charCodeAt(n);while(X(c));return r=15}if(S(c))return n++,l+=String.fromCharCode(c),c===13&&e.charCodeAt(n)===10&&(n++,l+=`
`),f++,g=n,r=14;switch(c){case 123:return n++,r=1;case 125:return n++,r=2;case 91:return n++,r=3;case 93:return n++,r=4;case 58:return n++,r=6;case 44:return n++,r=5;case 34:return n++,l=d(),r=10;case 47:const _=n-1;if(e.charCodeAt(n+1)===47){for(n+=2;n<s&&!S(e.charCodeAt(n));)n++;return l=e.substring(_,n),r=12}if(e.charCodeAt(n+1)===42){n+=2;const D=s-1;let T=!1;for(;n<D;){const p=e.charCodeAt(n);if(p===42&&e.charCodeAt(n+1)===47){n+=2,T=!0;break}n++,S(p)&&(p===13&&e.charCodeAt(n)===10&&n++,f++,g=n)}return T||(n++,b=1),l=e.substring(_,n),r=13}return l+=String.fromCharCode(c),n++,r=16;case 45:if(l+=String.fromCharCode(c),n++,n===s||!$(e.charCodeAt(n)))return r=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return l+=j(),r=11;default:for(;n<s&&E(c);)n++,c=e.charCodeAt(n);if(i!==n){switch(l=e.substring(i,n),l){case"true":return r=8;case"false":return r=9;case"null":return r=7}return r=16}return l+=String.fromCharCode(c),n++,r=16}}o(A,"scanNext");function E(c){if(X(c)||S(c))return!1;switch(c){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}o(E,"isUnknownContentCharacter");function B(){let c;do c=A();while(c>=12&&c<=15);return c}return o(B,"scanNextNonTrivia"),{setPosition:v,getPosition:o(()=>n,"getPosition"),scan:t?B:A,getToken:o(()=>r,"getToken"),getTokenValue:o(()=>l,"getTokenValue"),getTokenOffset:o(()=>i,"getTokenOffset"),getTokenLength:o(()=>n-i,"getTokenLength"),getTokenStartLine:o(()=>u,"getTokenStartLine"),getTokenStartCharacter:o(()=>i-w,"getTokenStartCharacter"),getTokenError:o(()=>b,"getTokenError")}}o(Le,"createScanner");function X(e){return e===32||e===9}o(X,"isWhiteSpace");function S(e){return e===10||e===13}o(S,"isLineBreak");function $(e){return e>=48&&e<=57}o($,"isDigit");var re;(function(e){e[e.lineFeed=10]="lineFeed",e[e.carriageReturn=13]="carriageReturn",e[e.space=32]="space",e[e._0=48]="_0",e[e._1=49]="_1",e[e._2=50]="_2",e[e._3=51]="_3",e[e._4=52]="_4",e[e._5=53]="_5",e[e._6=54]="_6",e[e._7=55]="_7",e[e._8=56]="_8",e[e._9=57]="_9",e[e.a=97]="a",e[e.b=98]="b",e[e.c=99]="c",e[e.d=100]="d",e[e.e=101]="e",e[e.f=102]="f",e[e.g=103]="g",e[e.h=104]="h",e[e.i=105]="i",e[e.j=106]="j",e[e.k=107]="k",e[e.l=108]="l",e[e.m=109]="m",e[e.n=110]="n",e[e.o=111]="o",e[e.p=112]="p",e[e.q=113]="q",e[e.r=114]="r",e[e.s=115]="s",e[e.t=116]="t",e[e.u=117]="u",e[e.v=118]="v",e[e.w=119]="w",e[e.x=120]="x",e[e.y=121]="y",e[e.z=122]="z",e[e.A=65]="A",e[e.B=66]="B",e[e.C=67]="C",e[e.D=68]="D",e[e.E=69]="E",e[e.F=70]="F",e[e.G=71]="G",e[e.H=72]="H",e[e.I=73]="I",e[e.J=74]="J",e[e.K=75]="K",e[e.L=76]="L",e[e.M=77]="M",e[e.N=78]="N",e[e.O=79]="O",e[e.P=80]="P",e[e.Q=81]="Q",e[e.R=82]="R",e[e.S=83]="S",e[e.T=84]="T",e[e.U=85]="U",e[e.V=86]="V",e[e.W=87]="W",e[e.X=88]="X",e[e.Y=89]="Y",e[e.Z=90]="Z",e[e.asterisk=42]="asterisk",e[e.backslash=92]="backslash",e[e.closeBrace=125]="closeBrace",e[e.closeBracket=93]="closeBracket",e[e.colon=58]="colon",e[e.comma=44]="comma",e[e.dot=46]="dot",e[e.doubleQuote=34]="doubleQuote",e[e.minus=45]="minus",e[e.openBrace=123]="openBrace",e[e.openBracket=91]="openBracket",e[e.plus=43]="plus",e[e.slash=47]="slash",e[e.formFeed=12]="formFeed",e[e.tab=9]="tab"})(re||(re={})),new Array(20).fill(0).map((e,t)=>" ".repeat(t));const x=200;new Array(x).fill(0).map((e,t)=>`
`+" ".repeat(t)),new Array(x).fill(0).map((e,t)=>"\r"+" ".repeat(t)),new Array(x).fill(0).map((e,t)=>`\r
`+" ".repeat(t)),new Array(x).fill(0).map((e,t)=>`
`+"	".repeat(t)),new Array(x).fill(0).map((e,t)=>"\r"+"	".repeat(t)),new Array(x).fill(0).map((e,t)=>`\r
`+"	".repeat(t));var W;(function(e){e.DEFAULT={allowTrailingComma:!1}})(W||(W={}));function $e(e,t=[],s=W.DEFAULT){let n=null,l=[];const i=[];function r(u){Array.isArray(l)?l.push(u):n!==null&&(l[n]=u)}return o(r,"onValue"),xe(e,{onObjectBegin:o(()=>{const u={};r(u),i.push(l),l=u,n=null},"onObjectBegin"),onObjectProperty:o(u=>{n=u},"onObjectProperty"),onObjectEnd:o(()=>{l=i.pop()},"onObjectEnd"),onArrayBegin:o(()=>{const u=[];r(u),i.push(l),l=u,n=null},"onArrayBegin"),onArrayEnd:o(()=>{l=i.pop()},"onArrayEnd"),onLiteralValue:r,onError:o((u,g,w)=>{t.push({error:u,offset:g,length:w})},"onError")},s),l[0]}o($e,"parse$1");function xe(e,t,s=W.DEFAULT){const n=Le(e,!1),l=[];let i=0;function r(k){return k?()=>i===0&&k(n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter()):()=>!0}o(r,"toNoArgVisit");function f(k){return k?F=>i===0&&k(F,n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter()):()=>!0}o(f,"toOneArgVisit");function u(k){return k?F=>i===0&&k(F,n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter(),()=>l.slice()):()=>!0}o(u,"toOneArgVisitWithPath");function g(k){return k?()=>{i>0?i++:k(n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter(),()=>l.slice())===!1&&(i=1)}:()=>!0}o(g,"toBeginVisit");function w(k){return k?()=>{i>0&&i--,i===0&&k(n.getTokenOffset(),n.getTokenLength(),n.getTokenStartLine(),n.getTokenStartCharacter())}:()=>!0}o(w,"toEndVisit");const b=g(t.onObjectBegin),y=u(t.onObjectProperty),v=w(t.onObjectEnd),j=g(t.onArrayBegin),d=w(t.onArrayEnd),A=u(t.onLiteralValue),E=f(t.onSeparator),B=r(t.onComment),c=f(t.onError),_=s&&s.disallowComments,D=s&&s.allowTrailingComma;function T(){for(;;){const k=n.scan();switch(n.getTokenError()){case 4:p(14);break;case 5:p(15);break;case 3:p(13);break;case 1:_||p(11);break;case 2:p(12);break;case 6:p(16);break}switch(k){case 12:case 13:_?p(10):B();break;case 16:p(1);break;case 15:case 14:break;default:return k}}}o(T,"scanNext");function p(k,F=[],le=[]){if(c(k),F.length+le.length>0){let P=n.getToken();for(;P!==17;){if(F.indexOf(P)!==-1){T();break}else if(le.indexOf(P)!==-1)break;P=T()}}}o(p,"handleError");function U(k){const F=n.getTokenValue();return k?A(F):(y(F),l.push(F)),T(),!0}o(U,"parseString");function a(){switch(n.getToken()){case 11:const k=n.getTokenValue();let F=Number(k);isNaN(F)&&(p(2),F=0),A(F);break;case 7:A(null);break;case 8:A(!0);break;case 9:A(!1);break;default:return!1}return T(),!0}o(a,"parseLiteral");function N(){return n.getToken()!==10?(p(3,[],[2,5]),!1):(U(!1),n.getToken()===6?(E(":"),T(),z()||p(4,[],[2,5])):p(5,[],[2,5]),l.pop(),!0)}o(N,"parseProperty");function _e(){b(),T();let k=!1;for(;n.getToken()!==2&&n.getToken()!==17;){if(n.getToken()===5){if(k||p(4,[],[]),E(","),T(),n.getToken()===2&&D)break}else k&&p(6,[],[]);N()||p(4,[],[2,5]),k=!0}return v(),n.getToken()!==2?p(7,[2],[]):T(),!0}o(_e,"parseObject");function ye(){j(),T();let k=!0,F=!1;for(;n.getToken()!==4&&n.getToken()!==17;){if(n.getToken()===5){if(F||p(4,[],[]),E(","),T(),n.getToken()===4&&D)break}else F&&p(6,[],[]);k?(l.push(0),k=!1):l[l.length-1]++,z()||p(4,[],[4,5]),F=!0}return d(),k||l.pop(),n.getToken()!==4?p(8,[4],[]):T(),!0}o(ye,"parseArray");function z(){switch(n.getToken()){case 3:return ye();case 1:return _e();case 10:return U(!0);default:return a()}}return o(z,"parseValue"),T(),n.getToken()===17?s.allowEmptyContent?!0:(p(4,[],[]),!1):z()?(n.getToken()!==17&&p(9,[],[]),!0):(p(4,[],[]),!1)}o(xe,"visit");var ue;(function(e){e[e.None=0]="None",e[e.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=2]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",e[e.InvalidUnicode=4]="InvalidUnicode",e[e.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",e[e.InvalidCharacter=6]="InvalidCharacter"})(ue||(ue={}));var fe;(function(e){e[e.OpenBraceToken=1]="OpenBraceToken",e[e.CloseBraceToken=2]="CloseBraceToken",e[e.OpenBracketToken=3]="OpenBracketToken",e[e.CloseBracketToken=4]="CloseBracketToken",e[e.CommaToken=5]="CommaToken",e[e.ColonToken=6]="ColonToken",e[e.NullKeyword=7]="NullKeyword",e[e.TrueKeyword=8]="TrueKeyword",e[e.FalseKeyword=9]="FalseKeyword",e[e.StringLiteral=10]="StringLiteral",e[e.NumericLiteral=11]="NumericLiteral",e[e.LineCommentTrivia=12]="LineCommentTrivia",e[e.BlockCommentTrivia=13]="BlockCommentTrivia",e[e.LineBreakTrivia=14]="LineBreakTrivia",e[e.Trivia=15]="Trivia",e[e.Unknown=16]="Unknown",e[e.EOF=17]="EOF"})(fe||(fe={}));const Ue=$e;var ce;(function(e){e[e.InvalidSymbol=1]="InvalidSymbol",e[e.InvalidNumberFormat=2]="InvalidNumberFormat",e[e.PropertyNameExpected=3]="PropertyNameExpected",e[e.ValueExpected=4]="ValueExpected",e[e.ColonExpected=5]="ColonExpected",e[e.CommaExpected=6]="CommaExpected",e[e.CloseBraceExpected=7]="CloseBraceExpected",e[e.CloseBracketExpected=8]="CloseBracketExpected",e[e.EndOfFileExpected=9]="EndOfFileExpected",e[e.InvalidCommentToken=10]="InvalidCommentToken",e[e.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",e[e.UnexpectedEndOfString=12]="UnexpectedEndOfString",e[e.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",e[e.InvalidUnicode=14]="InvalidUnicode",e[e.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",e[e.InvalidCharacter=16]="InvalidCharacter"})(ce||(ce={}));const ae=o((e,t)=>Ue(Be(t,e,"utf8")),"readJsonc"),Y=Symbol("implicitBaseUrl"),L="${configDir}",Se=o(()=>{const{findPnpApi:e}=Fe;return e&&e(process.cwd())},"getPnpApi"),Z=o((e,t,s,n)=>{const l=`resolveFromPackageJsonPath:${e}:${t}:${s}`;if(n!=null&&n.has(l))return n.get(l);const i=ae(e,n);if(!i)return;let r=t||"tsconfig.json";if(!s&&i.exports)try{const[f]=De(i.exports,t,["require","types"]);r=f}catch{return!1}else!t&&i.tsconfig&&(r=i.tsconfig);return r=m.join(e,"..",r),n==null||n.set(l,r),r},"resolveFromPackageJsonPath"),q="package.json",K="tsconfig.json",Ne=o((e,t,s)=>{let n=e;if(e===".."&&(n=m.join(n,K)),e[0]==="."&&(n=m.resolve(t,n)),m.isAbsolute(n)){if(I(s,n)){if(R(s,n).isFile())return n}else if(!n.endsWith(".json")){const v=`${n}.json`;if(I(s,v))return v}return}const[l,...i]=e.split("/"),r=l[0]==="@"?`${l}/${i.shift()}`:l,f=i.join("/"),u=Se();if(u){const{resolveRequest:v}=u;try{if(r===e){const j=v(m.join(r,q),t);if(j){const d=Z(j,f,!1,s);if(d&&I(s,d))return d}}else{let j;try{j=v(e,t,{extensions:[".json"]})}catch{j=v(m.join(e,K),t)}if(j)return j}}catch{}}const g=oe(m.resolve(t),m.join("node_modules",r),s);if(!g||!R(s,g).isDirectory())return;const w=m.join(g,q);if(I(s,w)){const v=Z(w,f,!1,s);if(v===!1)return;if(v&&I(s,v)&&R(s,v).isFile())return v}const b=m.join(g,f),y=b.endsWith(".json");if(!y){const v=`${b}.json`;if(I(s,v))return v}if(I(s,b)){if(R(s,b).isDirectory()){const v=m.join(b,q);if(I(s,v)){const d=Z(v,"",!0,s);if(d&&I(s,d))return d}const j=m.join(b,K);if(I(s,j))return j}else if(y)return b}},"resolveExtendsPath"),C=o((e,t)=>H(m.relative(e,t)),"pathRelative"),ge=["files","include","exclude"],pe=o((e,t,s)=>{const n=m.join(t,s),l=m.relative(e,n);return h(l)||"./"},"resolveAndRelativize"),Pe=o((e,t,s)=>{const n=m.relative(e,t);if(!n)return s;const l=s.startsWith("./")?s.slice(2):s;return h(`${n}/${l}`)},"prefixPattern"),Re=o((e,t,s,n)=>{const l=Ne(e,t,n);if(!l)throw new Error(`File '${e}' not found.`);if(s.has(l))throw new Error(`Circularity detected while resolving configuration: ${l}`);s.add(l);const i=m.dirname(l),r=me(l,n,s);delete r.references;const{compilerOptions:f}=r;if(f){const{baseUrl:u}=f;u&&!u.startsWith(L)&&(f.baseUrl=pe(t,i,u));const{outDir:g}=f;g&&!g.startsWith(L)&&(f.outDir=pe(t,i,g))}for(const u of ge){const g=r[u];g&&(r[u]=g.map(w=>w.startsWith(L)?w:Pe(t,i,w)))}return r},"resolveExtends"),We=["outDir","declarationDir"],me=o((e,t,s=new Set)=>{let n;try{n=ae(e,t)||{}}catch{throw new Error(`Cannot resolve tsconfig at path: ${e}`)}if(typeof n!="object")throw new SyntaxError(`Failed to parse tsconfig at: ${e}`);const l=m.dirname(e);if(n.compilerOptions){const{compilerOptions:i}=n;i.paths&&!i.baseUrl&&(i[Y]=l)}if(n.extends){const i=Array.isArray(n.extends)?n.extends:[n.extends];delete n.extends;for(const r of i.reverse()){const f=Re(r,l,new Set(s),t),u={...f,...n,compilerOptions:{...f.compilerOptions,...n.compilerOptions}};f.watchOptions&&(u.watchOptions={...f.watchOptions,...n.watchOptions}),n=u}}if(n.compilerOptions){const{compilerOptions:i}=n,r=["baseUrl","rootDir"];for(const f of r){const u=i[f];if(u&&!u.startsWith(L)){const g=m.resolve(l,u),w=C(l,g);i[f]=w}}for(const f of We){let u=i[f];u&&(Array.isArray(n.exclude)||(n.exclude=[]),n.exclude.includes(u)||n.exclude.push(u),u.startsWith(L)||(u=H(u)),i[f]=u)}}else n.compilerOptions={};if(n.include?(n.include=n.include.map(h),n.files&&delete n.files):n.files&&(n.files=n.files.map(i=>i.startsWith(L)?i:H(i))),n.watchOptions){const{watchOptions:i}=n;i.excludeDirectories&&(i.excludeDirectories=i.excludeDirectories.map(r=>h(m.resolve(l,r))))}return n},"_parseTsconfig"),V=o((e,t)=>{if(e.startsWith(L))return h(m.join(t,e.slice(L.length)))},"interpolateConfigDir"),Ve=["outDir","declarationDir","outFile","rootDir","baseUrl","tsBuildInfoFile"],Me=o(e=>{var t,s,n,l,i,r,f,u,g,w,b,y,v,j,d,A,E,B,c,_,D,T,p,U;if(e.strict){const a=["noImplicitAny","noImplicitThis","strictNullChecks","strictFunctionTypes","strictBindCallApply","strictPropertyInitialization","strictBuiltinIteratorReturn","alwaysStrict","useUnknownInCatchVariables"];for(const N of a)e[N]===void 0&&(e[N]=!0)}if(e.target){let a=e.target.toLowerCase();a==="es2015"&&(a="es6"),e.target=a,a==="esnext"&&((t=e.module)!=null||(e.module="es6"),(s=e.useDefineForClassFields)!=null||(e.useDefineForClassFields=!0)),(a==="es6"||a==="es2016"||a==="es2017"||a==="es2018"||a==="es2019"||a==="es2020"||a==="es2021"||a==="es2022"||a==="es2023"||a==="es2024")&&((n=e.module)!=null||(e.module="es6")),(a==="es2022"||a==="es2023"||a==="es2024")&&((l=e.useDefineForClassFields)!=null||(e.useDefineForClassFields=!0))}if(e.module){let a=e.module.toLowerCase();a==="es2015"&&(a="es6"),e.module=a,(a==="es6"||a==="es2020"||a==="es2022"||a==="esnext"||a==="none"||a==="system"||a==="umd"||a==="amd")&&((i=e.moduleResolution)!=null||(e.moduleResolution="classic")),a==="system"&&((r=e.allowSyntheticDefaultImports)!=null||(e.allowSyntheticDefaultImports=!0)),(a==="node16"||a==="nodenext"||a==="preserve")&&((f=e.esModuleInterop)!=null||(e.esModuleInterop=!0),(u=e.allowSyntheticDefaultImports)!=null||(e.allowSyntheticDefaultImports=!0)),(a==="node16"||a==="nodenext")&&((g=e.moduleDetection)!=null||(e.moduleDetection="force"),(w=e.useDefineForClassFields)!=null||(e.useDefineForClassFields=!0)),a==="node16"&&((b=e.target)!=null||(e.target="es2022"),(y=e.moduleResolution)!=null||(e.moduleResolution="node16")),a==="nodenext"&&((v=e.target)!=null||(e.target="esnext"),(j=e.moduleResolution)!=null||(e.moduleResolution="nodenext")),a==="preserve"&&((d=e.moduleResolution)!=null||(e.moduleResolution="bundler"))}if(e.moduleResolution){let a=e.moduleResolution.toLowerCase();a==="node"&&(a="node10"),e.moduleResolution=a,(a==="node16"||a==="nodenext"||a==="bundler")&&((A=e.resolvePackageJsonExports)!=null||(e.resolvePackageJsonExports=!0),(E=e.resolvePackageJsonImports)!=null||(e.resolvePackageJsonImports=!0)),a==="bundler"&&((B=e.allowSyntheticDefaultImports)!=null||(e.allowSyntheticDefaultImports=!0),(c=e.resolveJsonModule)!=null||(e.resolveJsonModule=!0))}e.esModuleInterop&&((_=e.allowSyntheticDefaultImports)!=null||(e.allowSyntheticDefaultImports=!0)),e.verbatimModuleSyntax&&((D=e.isolatedModules)!=null||(e.isolatedModules=!0),(T=e.preserveConstEnums)!=null||(e.preserveConstEnums=!0)),e.isolatedModules&&((p=e.preserveConstEnums)!=null||(e.preserveConstEnums=!0)),e.rewriteRelativeImportExtensions&&((U=e.allowImportingTsExtensions)!=null||(e.allowImportingTsExtensions=!0))},"normalizeCompilerOptions"),ke=o((e,t=new Map)=>{const s=m.resolve(e),n=me(s,t),l=m.dirname(s),{compilerOptions:i}=n;if(i){for(const f of Ve){const u=i[f];if(u){const g=V(u,l);i[f]=g?C(l,g):u}}for(const f of["rootDirs","typeRoots"]){const u=i[f];u&&(i[f]=u.map(g=>{const w=V(g,l);return w?C(l,w):g}))}const{paths:r}=i;if(r)for(const f of Object.keys(r))r[f]=r[f].map(u=>{var g;return(g=V(u,l))!=null?g:u});Me(i)}for(const r of ge){const f=n[r];f&&(n[r]=f.map(u=>{var g;return(g=V(u,l))!=null?g:u}))}return n},"parseTsconfig"),Je=o((e=process.cwd(),t="tsconfig.json",s=new Map)=>{const n=oe(h(e),t,s);if(!n)return null;const l=ke(n,s);return{path:n,config:l}},"getTsconfig"),Oe=/\*/g,we=o((e,t)=>{const s=e.match(Oe);if(s&&s.length>1)throw new Error(t)},"assertStarCount"),ze=o(e=>{if(e.includes("*")){const[t,s]=e.split("*");return{prefix:t,suffix:s}}return e},"parsePattern"),Ge=o(({prefix:e,suffix:t},s)=>s.startsWith(e)&&s.endsWith(t),"isPatternMatch"),Qe=o((e,t,s)=>Object.entries(e).map(([n,l])=>(we(n,`Pattern '${n}' can have at most one '*' character.`),{pattern:ze(n),substitutions:l.map(i=>{if(we(i,`Substitution '${i}' in pattern '${n}' can have at most one '*' character.`),!t&&!Q.test(i))throw new Error("Non-relative paths are not allowed when 'baseUrl' is not set. Did you forget a leading './'?");return m.resolve(s,i)})})),"parsePaths"),He=o(e=>{const{compilerOptions:t}=e.config;if(!t)return null;const{baseUrl:s,paths:n}=t;if(!s&&!n)return null;const l=Y in t&&t[Y],i=m.resolve(m.dirname(e.path),s||l||"."),r=n?Qe(n,s,i):[];return f=>{if(Q.test(f))return[];const u=[];for(const y of r){if(y.pattern===f)return y.substitutions.map(h);typeof y.pattern!="string"&&u.push(y)}let g,w=-1;for(const y of u)Ge(y.pattern,f)&&y.pattern.prefix.length>w&&(w=y.pattern.prefix.length,g=y);if(!g)return s?[h(m.join(i,f))]:[];const b=f.slice(g.pattern.prefix.length,f.length-g.pattern.suffix.length);return g.substitutions.map(y=>h(y.replace("*",b)))}},"createPathsMatcher");var Xe=Object.defineProperty,M=o((e,t)=>Xe(e,"name",{value:t,configurable:!0}),"s");const be=M(e=>{let t="";for(let s=0;s<e.length;s+=1){const n=e[s],l=n.toUpperCase();t+=n===l?n.toLowerCase():l}return t},"invertCase"),ee=new Map,ve=M((e,t)=>{const s=Ie.join(e,`.is-fs-case-sensitive-test-${process.pid}`);try{return t.writeFileSync(s,""),!t.existsSync(be(s))}finally{try{t.unlinkSync(s)}catch{}}},"checkDirectoryCaseWithWrite"),Ye=M((e,t,s)=>{try{return ve(e,s)}catch(n){if(t===void 0)return ve(he.tmpdir(),s);throw n}},"checkDirectoryCaseWithFallback"),Ze=M((e,t=Ee,s=!0)=>{const n=e!=null?e:process.cwd();if(s&&ee.has(n))return ee.get(n);let l;const i=be(n);return i!==n&&t.existsSync(n)?l=!t.existsSync(i):l=Ye(n,e,t),s&&ee.set(n,l),l},"isFsCaseSensitive"),{join:J}=m.posix,ne={ts:[".ts",".tsx",".d.ts"],cts:[".cts",".d.cts"],mts:[".mts",".d.mts"]},qe=o(e=>{const t=[...ne.ts],s=[...ne.cts],n=[...ne.mts];return e!=null&&e.allowJs&&(t.push(".js",".jsx"),s.push(".cjs"),n.push(".mjs")),[...t,...s,...n]},"getSupportedExtensions"),Ke=o(e=>{const t=[];if(!e)return t;const{outDir:s,declarationDir:n}=e;return s&&t.push(s),n&&t.push(n),t},"getDefaultExcludeSpec"),de=o(e=>e.replaceAll(/[.*+?^${}()|[\]\\]/g,String.raw`\$&`),"escapeForRegexp"),Ce=["node_modules","bower_components","jspm_packages"],te=`(?!(${Ce.join("|")})(/|$))`,en=/(?:^|\/)[^.*?]+$/,Te="**/*",O="[^/]",se="[^./]",Ae=process.platform==="win32",nn=o(({config:e,path:t},s=Ze())=>{if("extends"in e)throw new Error("tsconfig#extends must be resolved. Use getTsconfig or parseTsconfig to resolve it.");if(!m.isAbsolute(t))throw new Error("The tsconfig path must be absolute");Ae&&(t=h(t));const n=m.dirname(t),{files:l,include:i,exclude:r,compilerOptions:f}=e,u=l==null?void 0:l.map(d=>J(n,d)),g=qe(f),w=s?"":"i",y=(r||Ke(f)).map(d=>{const A=J(n,d),E=de(A).replaceAll(String.raw`\*\*/`,"(.+/)?").replaceAll(String.raw`\*`,`${O}*`).replaceAll(String.raw`\?`,O);return new RegExp(`^${E}($|/)`,w)}),v=l||i?i:[Te],j=v?v.map(d=>{let A=J(n,d);en.test(A)&&(A=J(A,Te));const E=de(A).replaceAll(String.raw`/\*\*`,`(/${te}${se}${O}*)*?`).replaceAll(/(\/)?\\\*/g,(B,c)=>{const _=`(${se}|(\\.(?!min\\.js$))?)*`;return c?`/${te}${se}${_}`:_}).replaceAll(/(\/)?\\\?/g,(B,c)=>{const _=O;return c?`/${te}${_}`:_});return new RegExp(`^${E}$`,w)}):void 0;return d=>{if(!m.isAbsolute(d))throw new Error("filePath must be absolute");if(Ae&&(d=h(d)),u!=null&&u.includes(d))return e;if(!(!g.some(A=>d.endsWith(A))||y.some(A=>A.test(d)))&&j&&j.some(A=>A.test(d)))return e}},"createFilesMatcher");export{nn as createFilesMatcher,He as createPathsMatcher,Je as getTsconfig,ke as parseTsconfig};
