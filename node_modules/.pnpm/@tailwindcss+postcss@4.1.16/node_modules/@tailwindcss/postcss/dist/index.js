"use strict";var Xe=Object.create;var ge=Object.defineProperty;var et=Object.getOwnPropertyDescriptor;var tt=Object.getOwnPropertyNames;var rt=Object.getPrototypeOf,it=Object.prototype.hasOwnProperty;var de=(e,r)=>(r=Symbol[e])?r:Symbol.for("Symbol."+e),me=e=>{throw TypeError(e)};var nt=(e,r,t,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let a of tt(r))!it.call(e,a)&&a!==t&&ge(e,a,{get:()=>r[a],enumerable:!(n=et(r,a))||n.enumerable});return e};var H=(e,r,t)=>(t=e!=null?Xe(rt(e)):{},nt(r||!e||!e.__esModule?ge(t,"default",{value:e,enumerable:!0}):t,e));var he=(e,r,t)=>{if(r!=null){typeof r!="object"&&typeof r!="function"&&me("Object expected");var n,a;t&&(n=r[de("asyncDispose")]),n===void 0&&(n=r[de("dispose")],t&&(a=n)),typeof n!="function"&&me("Object not disposable"),a&&(n=function(){try{a.call(this)}catch(o){return Promise.reject(o)}}),e.push([t,n,r])}else t&&e.push([t]);return r},ve=(e,r,t)=>{var n=typeof SuppressedError=="function"?SuppressedError:function(i,l,u,s){return s=Error(u),s.name="SuppressedError",s.error=i,s.suppressed=l,s},a=i=>r=t?new n(i,r,"An error was suppressed during disposal"):(t=!0,i),o=i=>{for(;i=e.pop();)try{var l=i[1]&&i[1].call(i[2]);if(i[0])return Promise.resolve(l).then(o,u=>(a(u),o()))}catch(u){a(u)}if(t)throw r};return o()};var Be=H(require("@alloc/quick-lru")),x=require("@tailwindcss/node"),Ye=require("@tailwindcss/node/require-cache"),Ge=require("@tailwindcss/oxide"),qe=H(require("fs")),$=H(require("path"));function F(e){return{kind:"word",value:e}}function ot(e,r){return{kind:"function",value:e,nodes:r}}function at(e){return{kind:"separator",value:e}}function T(e){let r="";for(let t of e)switch(t.kind){case"word":case"separator":{r+=t.value;break}case"function":r+=t.value+"("+T(t.nodes)+")"}return r}var ke=92,lt=41,we=58,ye=44,st=34,be=61,xe=62,Ae=60,Ce=10,ut=40,ft=39,ct=47,Se=32,Te=9;function C(e){e=e.replaceAll(`\r
`,`
`);let r=[],t=[],n=null,a="",o;for(let i=0;i<e.length;i++){let l=e.charCodeAt(i);switch(l){case ke:{a+=e[i]+e[i+1],i++;break}case ct:{if(a.length>0){let s=F(a);n?n.nodes.push(s):r.push(s),a=""}let u=F(e[i]);n?n.nodes.push(u):r.push(u);break}case we:case ye:case be:case xe:case Ae:case Ce:case Se:case Te:{if(a.length>0){let c=F(a);n?n.nodes.push(c):r.push(c),a=""}let u=i,s=i+1;for(;s<e.length&&(o=e.charCodeAt(s),!(o!==we&&o!==ye&&o!==be&&o!==xe&&o!==Ae&&o!==Ce&&o!==Se&&o!==Te));s++);i=s-1;let f=at(e.slice(u,s));n?n.nodes.push(f):r.push(f);break}case ft:case st:{let u=i;for(let s=i+1;s<e.length;s++)if(o=e.charCodeAt(s),o===ke)s+=1;else if(o===l){i=s;break}a+=e.slice(u,i+1);break}case ut:{let u=ot(a,[]);a="",n?n.nodes.push(u):r.push(u),t.push(u),n=u;break}case lt:{let u=t.pop();if(a.length>0){let s=F(a);u?.nodes.push(s),a=""}t.length>0?n=t[t.length-1]:n=null;break}default:a+=String.fromCharCode(l)}}return a.length>0&&r.push(F(a)),r}var d=class extends Map{constructor(t){super();this.factory=t}get(t){let n=super.get(t);return n===void 0&&(n=this.factory(t,this),this.set(t,n)),n}};var nr=new Uint8Array(256);var Z=new Uint8Array(256);function w(e,r){let t=0,n=[],a=0,o=e.length,i=r.charCodeAt(0);for(let l=0;l<o;l++){let u=e.charCodeAt(l);if(t===0&&u===i){n.push(e.slice(a,l)),a=l+1;continue}switch(u){case 92:l+=1;break;case 39:case 34:for(;++l<o;){let s=e.charCodeAt(l);if(s===92){l+=1;continue}if(s===u)break}break;case 40:Z[t]=41,t++;break;case 91:Z[t]=93,t++;break;case 123:Z[t]=125,t++;break;case 93:case 125:case 41:t>0&&u===Z[t-1]&&t--;break}}return n.push(e.slice(a)),n}var te=(i=>(i[i.Continue=0]="Continue",i[i.Skip=1]="Skip",i[i.Stop=2]="Stop",i[i.Replace=3]="Replace",i[i.ReplaceSkip=4]="ReplaceSkip",i[i.ReplaceStop=5]="ReplaceStop",i))(te||{}),h={Continue:{kind:0},Skip:{kind:1},Stop:{kind:2},Replace:e=>({kind:3,nodes:Array.isArray(e)?e:[e]}),ReplaceSkip:e=>({kind:4,nodes:Array.isArray(e)?e:[e]}),ReplaceStop:e=>({kind:5,nodes:Array.isArray(e)?e:[e]})};function v(e,r){typeof r=="function"?$e(e,r):$e(e,r.enter,r.exit)}function $e(e,r=()=>h.Continue,t=()=>h.Continue){let n=[[e,0,null]],a={parent:null,depth:0,path(){let o=[];for(let i=1;i<n.length;i++){let l=n[i][2];l&&o.push(l)}return o}};for(;n.length>0;){let o=n.length-1,i=n[o],l=i[0],u=i[1],s=i[2];if(u>=l.length){n.pop();continue}if(a.parent=s,a.depth=o,u>=0){let I=l[u],N=r(I,a)??h.Continue;switch(N.kind){case 0:{I.nodes&&I.nodes.length>0&&n.push([I.nodes,0,I]),i[1]=~u;continue}case 2:return;case 1:{i[1]=~u;continue}case 3:{l.splice(u,1,...N.nodes);continue}case 5:{l.splice(u,1,...N.nodes);return}case 4:{l.splice(u,1,...N.nodes),i[1]+=N.nodes.length;continue}default:throw new Error(`Invalid \`WalkAction.${te[N.kind]??`Unknown(${N.kind})`}\` in enter.`)}}let f=~u,c=l[f],m=t(c,a)??h.Continue;switch(m.kind){case 0:i[1]=f+1;continue;case 2:return;case 3:{l.splice(f,1,...m.nodes),i[1]=f+m.nodes.length;continue}case 5:{l.splice(f,1,...m.nodes);return}case 4:{l.splice(f,1,...m.nodes),i[1]=f+m.nodes.length;continue}default:throw new Error(`Invalid \`WalkAction.${te[m.kind]??`Unknown(${m.kind})`}\` in exit.`)}}}var gr=new d(e=>{let r=C(e),t=new Set;return v(r,(n,a)=>{let o=a.parent===null?r:a.parent.nodes??[];if(n.kind==="word"&&(n.value==="+"||n.value==="-"||n.value==="*"||n.value==="/")){let i=o.indexOf(n)??-1;if(i===-1)return;let l=o[i-1];if(l?.kind!=="separator"||l.value!==" ")return;let u=o[i+1];if(u?.kind!=="separator"||u.value!==" ")return;t.add(l),t.add(u)}else n.kind==="separator"&&n.value.length>0&&n.value.trim()===""?(o[0]===n||o[o.length-1]===n)&&t.add(n):n.kind==="separator"&&n.value.trim()===","&&(n.value=",")}),t.size>0&&v(r,n=>{if(t.has(n))return t.delete(n),h.ReplaceSkip([])}),re(r),T(r)});var hr=new d(e=>{let r=C(e);return r.length===3&&r[0].kind==="word"&&r[0].value==="&"&&r[1].kind==="separator"&&r[1].value===":"&&r[2].kind==="function"&&r[2].value==="is"?T(r[2].nodes):e});function re(e){for(let r of e)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=j(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=j(r.value);for(let t=0;t<r.nodes.length;t++)re([r.nodes[t]]);break}r.value=j(r.value),re(r.nodes);break}case"separator":r.value=j(r.value);break;case"word":{(r.value[0]!=="-"||r.value[1]!=="-")&&(r.value=j(r.value));break}default:pt(r)}}var vr=new d(e=>{let r=C(e);return r.length===1&&r[0].kind==="function"&&r[0].value==="var"});function pt(e){throw new Error(`Unexpected value: ${e}`)}function j(e){return e.replaceAll("_",String.raw`\_`).replaceAll(" ","_")}var dt=process.env.FEATURES_ENV!=="stable";var O=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,$r=new RegExp(`^${O.source}$`);var Nr=new RegExp(`^${O.source}%$`);var Er=new RegExp(`^${O.source}s*/s*${O.source}$`);var mt=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],Vr=new RegExp(`^${O.source}(${mt.join("|")})$`);var gt=["deg","rad","grad","turn"],Rr=new RegExp(`^${O.source}(${gt.join("|")})$`);var Pr=new RegExp(`^${O.source} +${O.source} +${O.source}$`);function S(e){let r=Number(e);return Number.isInteger(r)&&r>=0&&String(r)===String(e)}function W(e,r){if(r===null)return e;let t=Number(r);return Number.isNaN(t)||(r=`${t*100}%`),r==="100%"?e:`color-mix(in oklab, ${e} ${r}, transparent)`}var kt={"--alpha":wt,"--spacing":yt,"--theme":bt,theme:xt};function wt(e,r,t,...n){let[a,o]=w(t,"/").map(i=>i.trim());if(!a||!o)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${a||"var(--my-color)"} / ${o||"50%"})\``);if(n.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${a||"var(--my-color)"} / ${o||"50%"})\``);return W(a,o)}function yt(e,r,t,...n){if(!t)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(n.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${n.length+1}.`);let a=e.theme.resolve(null,["--spacing"]);if(!a)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${a} * ${t})`}function bt(e,r,t,...n){if(!t.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let a=!1;t.endsWith(" inline")&&(a=!0,t=t.slice(0,-7)),r.kind==="at-rule"&&(a=!0);let o=e.resolveThemeValue(t,a);if(!o){if(n.length>0)return n.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(n.length===0)return o;let i=n.join(", ");if(i==="initial")return o;if(o==="initial")return i;if(o.startsWith("var(")||o.startsWith("theme(")||o.startsWith("--theme(")){let l=C(o);return Ct(l,i),T(l)}return o}function xt(e,r,t,...n){t=At(t);let a=e.resolveThemeValue(t);if(!a&&n.length>0)return n.join(", ");if(!a)throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return a}var Xr=new RegExp(Object.keys(kt).map(e=>`${e}\\(`).join("|"));function At(e){if(e[0]!=="'"&&e[0]!=='"')return e;let r="",t=e[0];for(let n=1;n<e.length-1;n++){let a=e[n],o=e[n+1];a==="\\"&&(o===t||o==="\\")?(r+=o,n++):r+=a}return r}function Ct(e,r){v(e,t=>{if(t.kind==="function"&&!(t.value!=="var"&&t.value!=="theme"&&t.value!=="--theme"))if(t.nodes.length===1)t.nodes.push({kind:"word",value:`, ${r}`});else{let n=t.nodes[t.nodes.length-1];n.kind==="word"&&n.value==="initial"&&(n.value=r)}})}var Tt=/^(?<value>[-+]?(?:\d*\.)?\d+)(?<unit>[a-z]+|%)?$/i,Re=new d(e=>{let r=Tt.exec(e);if(!r)return null;let t=r.groups?.value;if(t===void 0)return null;let n=Number(t);if(Number.isNaN(n))return null;let a=r.groups?.unit;return a===void 0?[n,null]:[n,a]});function Pe(e,r="top",t="right",n="bottom",a="left"){return Oe(`${e}-${r}`,`${e}-${t}`,`${e}-${n}`,`${e}-${a}`)}function Oe(e="top",r="right",t="bottom",n="left"){return{1:[[e,0],[r,0],[t,0],[n,0]],2:[[e,0],[r,1],[t,0],[n,1]],3:[[e,0],[r,1],[t,2],[n,1]],4:[[e,0],[r,1],[t,2],[n,3]]}}function K(e,r){return{1:[[e,0],[r,0]],2:[[e,0],[r,1]]}}var ki={inset:Oe(),margin:Pe("margin"),padding:Pe("padding"),gap:K("row-gap","column-gap")},wi={"inset-block":K("top","bottom"),"inset-inline":K("left","right"),"margin-block":K("margin-top","margin-bottom"),"margin-inline":K("margin-left","margin-right"),"padding-block":K("padding-top","padding-bottom"),"padding-inline":K("padding-left","padding-right")};var qi=Symbol();var Hi=Symbol();var Zi=Symbol();var Qi=Symbol();var Ji=Symbol();var Xi=Symbol();var en=Symbol();var tn=Symbol();var rn=Symbol();var nn=Symbol();var on=Symbol();var an=Symbol();var ln=Symbol();var Ut=32,Lt=9;var Kt=40;function Le(e,r=[]){let t=e,n="";for(let a=5;a<e.length;a++){let o=e.charCodeAt(a);if(o===Ut||o===Lt||o===Kt){t=e.slice(0,a),n=e.slice(a);break}}return A(t.trim(),n.trim(),r)}var ae={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}};function M(e){return{__BARE_VALUE__:e}}var V=M(e=>{if(S(e.value))return e.value}),b=M(e=>{if(S(e.value))return`${e.value}%`}),U=M(e=>{if(S(e.value))return`${e.value}px`}),Me=M(e=>{if(S(e.value))return`${e.value}ms`}),X=M(e=>{if(S(e.value))return`${e.value}deg`}),Wt=M(e=>{if(e.fraction===null)return;let[r,t]=w(e.fraction,"/");if(!(!S(r)||!S(t)))return e.fraction}),Fe=M(e=>{if(S(Number(e.value)))return`repeat(${e.value}, minmax(0, 1fr))`}),Bt={accentColor:({theme:e})=>e("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...Wt},backdropBlur:({theme:e})=>e("blur"),backdropBrightness:({theme:e})=>({...e("brightness"),...b}),backdropContrast:({theme:e})=>({...e("contrast"),...b}),backdropGrayscale:({theme:e})=>({...e("grayscale"),...b}),backdropHueRotate:({theme:e})=>({...e("hueRotate"),...X}),backdropInvert:({theme:e})=>({...e("invert"),...b}),backdropOpacity:({theme:e})=>({...e("opacity"),...b}),backdropSaturate:({theme:e})=>({...e("saturate"),...b}),backdropSepia:({theme:e})=>({...e("sepia"),...b}),backgroundColor:({theme:e})=>e("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:e})=>e("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),borderOpacity:({theme:e})=>e("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:e})=>e("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...U},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:e})=>e("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...b},caretColor:({theme:e})=>e("colors"),colors:()=>({...ae}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...V},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...b},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:e})=>e("borderColor"),divideOpacity:({theme:e})=>e("borderOpacity"),divideWidth:({theme:e})=>({...e("borderWidth"),...U}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:e})=>e("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...e("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...V},flexShrink:{0:"0",DEFAULT:"1",...V},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:e})=>e("spacing"),gradientColorStops:({theme:e})=>e("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...b},grayscale:{0:"0",DEFAULT:"100%",...b},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...V},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...V},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...V},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...V},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Fe},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...Fe},height:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...X},inset:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),invert:{0:"0",DEFAULT:"100%",...b},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:e})=>({auto:"auto",...e("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...V},maxHeight:({theme:e})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),maxWidth:({theme:e})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...e("spacing")}),minHeight:({theme:e})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),minWidth:({theme:e})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...b},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...V},outlineColor:({theme:e})=>e("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...U},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...U},padding:({theme:e})=>e("spacing"),placeholderColor:({theme:e})=>e("colors"),placeholderOpacity:({theme:e})=>e("opacity"),ringColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),ringOffsetColor:({theme:e})=>e("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...U},ringOpacity:({theme:e})=>({DEFAULT:"0.5",...e("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...U},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...X},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...b},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...b},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:e})=>e("spacing"),scrollPadding:({theme:e})=>e("spacing"),sepia:{0:"0",DEFAULT:"100%",...b},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...X},space:({theme:e})=>e("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:e})=>({none:"none",...e("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...V},supports:{},data:{},textColor:({theme:e})=>e("colors"),textDecorationColor:({theme:e})=>e("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...U},textIndent:({theme:e})=>e("spacing"),textOpacity:({theme:e})=>e("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...U},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Me},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Me},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:e})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),size:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),width:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...V}};function le(e){let r=[0];for(let a=0;a<e.length;a++)e.charCodeAt(a)===10&&r.push(a+1);function t(a){let o=0,i=r.length;for(;i>0;){let u=(i|0)>>1,s=o+u;r[s]<=a?(o=s+1,i=i-u-1):i=u}o-=1;let l=a-r[o];return{line:o+1,column:l}}function n({line:a,column:o}){a-=1,a=Math.min(Math.max(a,0),r.length-1);let i=r[a],l=r[a+1]??i;return Math.min(Math.max(i+o,0),l)}return{find:t,findOffset:n}}var Gt=64;function L(e,r=[]){return{kind:"rule",selector:e,nodes:r}}function A(e,r="",t=[]){return{kind:"at-rule",name:e,params:r,nodes:t}}function _(e,r=[]){return e.charCodeAt(0)===Gt?Le(e,r):L(e,r)}function E(e,r,t=!1){return{kind:"declaration",property:e,value:r,important:t}}function J(e){return{kind:"comment",value:e}}function z(e,r){let t=0,n={file:null,code:""};function a(i,l=0){let u="",s="  ".repeat(l);if(i.kind==="declaration"){if(u+=`${s}${i.property}: ${i.value}${i.important?" !important":""};
`,r){t+=s.length;let f=t;t+=i.property.length,t+=2,t+=i.value?.length??0,i.important&&(t+=11);let c=t;t+=2,i.dst=[n,f,c]}}else if(i.kind==="rule"){if(u+=`${s}${i.selector} {
`,r){t+=s.length;let f=t;t+=i.selector.length,t+=1;let c=t;i.dst=[n,f,c],t+=2}for(let f of i.nodes)u+=a(f,l+1);u+=`${s}}
`,r&&(t+=s.length,t+=2)}else if(i.kind==="at-rule"){if(i.nodes.length===0){let f=`${s}${i.name} ${i.params};
`;if(r){t+=s.length;let c=t;t+=i.name.length,t+=1,t+=i.params.length;let m=t;t+=2,i.dst=[n,c,m]}return f}if(u+=`${s}${i.name}${i.params?` ${i.params} `:" "}{
`,r){t+=s.length;let f=t;t+=i.name.length,i.params&&(t+=1,t+=i.params.length),t+=1;let c=t;i.dst=[n,f,c],t+=2}for(let f of i.nodes)u+=a(f,l+1);u+=`${s}}
`,r&&(t+=s.length,t+=2)}else if(i.kind==="comment"){if(u+=`${s}/*${i.value}*/
`,r){t+=s.length;let f=t;t+=2+i.value.length+2;let c=t;i.dst=[n,f,c],t+=1}}else if(i.kind==="context"||i.kind==="at-root")return"";return u}let o="";for(let i of e)o+=a(i,0);return n.code=o,o}var qt=33;function je(e,r,t){let n=new d(s=>new e.Input(s.code,{map:t?.input.map,from:s.file??void 0})),a=new d(s=>le(s.code)),o=e.root();o.source=t;function i(s){if(!s||!s[0])return;let f=a.get(s[0]),c=f.find(s[1]),m=f.find(s[2]);return{input:n.get(s[0]),start:{line:c.line,column:c.column+1,offset:s[1]},end:{line:m.line,column:m.column+1,offset:s[2]}}}function l(s,f){let c=i(f);c?s.source=c:delete s.source}function u(s,f){if(s.kind==="declaration"){let c=e.decl({prop:s.property,value:s.value??"",important:s.important});l(c,s.src),f.append(c)}else if(s.kind==="rule"){let c=e.rule({selector:s.selector});l(c,s.src),c.raws.semicolon=!0,f.append(c);for(let m of s.nodes)u(m,c)}else if(s.kind==="at-rule"){let c=e.atRule({name:s.name.slice(1),params:s.params});l(c,s.src),c.raws.semicolon=!0,f.append(c);for(let m of s.nodes)u(m,c)}else if(s.kind==="comment"){let c=e.comment({text:s.value});c.raws.left="",c.raws.right="",l(c,s.src),f.append(c)}else s.kind==="at-root"||s.kind}for(let s of r)u(s,o);return o}function We(e){let r=new d(o=>({file:o.file??o.id??null,code:o.css}));function t(o){let i=o.source;if(!i)return;let l=i.input;if(l&&i.start!==void 0&&i.end!==void 0)return[r.get(l),i.start.offset,i.end.offset]}function n(o,i){if(o.type==="decl"){let l=E(o.prop,o.value,o.important);l.src=t(o),i.push(l)}else if(o.type==="rule"){let l=_(o.selector);l.src=t(o),o.each(u=>n(u,l.nodes)),i.push(l)}else if(o.type==="atrule"){let l=A(`@${o.name}`,o.params);l.src=t(o),o.each(u=>n(u,l.nodes)),i.push(l)}else if(o.type==="comment"){if(o.text.charCodeAt(0)!==qt)return;let l=J(o.text);l.src=t(o),i.push(l)}}let a=[];return e.each(o=>n(o,a)),a}var fe=require("@tailwindcss/node"),G=H(require("path")),se="'",ue='"';function ce(){let e=new WeakSet;function r(t){let n=t.root().source?.input.file;if(!n)return;let a=t.source?.input.file;if(!a||e.has(t))return;let o=t.params[0],i=o[0]===ue&&o[o.length-1]===ue?ue:o[0]===se&&o[o.length-1]===se?se:null;if(!i)return;let l=t.params.slice(1,-1),u="";if(l.startsWith("!")&&(l=l.slice(1),u="!"),!l.startsWith("./")&&!l.startsWith("../"))return;let s=G.default.posix.join((0,fe.normalizePath)(G.default.dirname(a)),l),f=G.default.posix.dirname((0,fe.normalizePath)(n)),c=G.default.posix.relative(f,s);c.startsWith(".")||(c="./"+c),t.params=i+u+c+i,e.add(t)}return{postcssPlugin:"tailwindcss-postcss-fix-relative-paths",Once(t){t.walkAtRules(/source|plugin|config/,r)}}}var p=x.env.DEBUG,pe=new Be.default({maxSize:50});function Ht(e,r,t){let n=`${r}:${t.base??""}:${JSON.stringify(t.optimize)}`;if(pe.has(n))return pe.get(n);let a={mtimes:new Map,compiler:null,scanner:null,tailwindCssAst:[],cachedPostCssAst:e.root(),optimizedPostCssAst:e.root(),fullRebuildPaths:[]};return pe.set(n,a),a}function Zt(e={}){let r=e.base??process.cwd(),t=e.optimize??process.env.NODE_ENV==="production",n=e.transformAssetUrls??!0;return{postcssPlugin:"@tailwindcss/postcss",plugins:[ce(),{postcssPlugin:"tailwindcss",async Once(a,{result:o,postcss:i}){var N=[];try{let l=he(N,new x.Instrumentation);let u=o.opts.from??"";let s=u.endsWith(".module.css");p&&l.start(`[@tailwindcss/postcss] ${(0,$.relative)(r,u)}`);{p&&l.start("Quick bail check");let y=!0;if(a.walkAtRules(g=>{if(g.name==="import"||g.name==="reference"||g.name==="theme"||g.name==="variant"||g.name==="config"||g.name==="plugin"||g.name==="apply"||g.name==="tailwind")return y=!1,!1}),y)return;p&&l.end("Quick bail check")}let f=Ht(i,u,e);let c=$.default.dirname($.default.resolve(u));let m=f.compiler===null;async function I(){p&&l.start("Setup compiler"),f.fullRebuildPaths.length>0&&!m&&(0,Ye.clearRequireCache)(f.fullRebuildPaths),f.fullRebuildPaths=[],p&&l.start("PostCSS AST -> Tailwind CSS AST");let y=We(a);p&&l.end("PostCSS AST -> Tailwind CSS AST"),p&&l.start("Create compiler");let g=await(0,x.compileAst)(y,{from:o.opts.from,base:c,shouldRewriteUrls:n,onDependency:ee=>f.fullRebuildPaths.push(ee),polyfills:s?x.Polyfills.All^x.Polyfills.AtProperty:x.Polyfills.All});return p&&l.end("Create compiler"),p&&l.end("Setup compiler"),g}try{if(f.compiler??=I(),(await f.compiler).features===x.Features.None)return;let y="incremental";p&&l.start("Register full rebuild paths");{for(let k of f.fullRebuildPaths)o.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:$.default.resolve(k),parent:o.opts.from});let D=o.messages.flatMap(k=>k.type!=="dependency"?[]:k.file);D.push(u);for(let k of D){let R=qe.default.statSync(k,{throwIfNoEntry:!1})?.mtimeMs??null;if(R===null){k===u&&(y="full");continue}f.mtimes.get(k)!==R&&(y="full",f.mtimes.set(k,R))}}p&&l.end("Register full rebuild paths"),y==="full"&&!m&&(f.compiler=I());let g=await f.compiler;if(f.scanner===null||y==="full"){p&&l.start("Setup scanner");let D=(g.root==="none"?[]:g.root===null?[{base:r,pattern:"**/*",negated:!1}]:[{...g.root,negated:!1}]).concat(g.sources);f.scanner=new Ge.Scanner({sources:D}),p&&l.end("Setup scanner")}p&&l.start("Scan for candidates");let ee=g.features&x.Features.Utilities?f.scanner.scan():[];if(p&&l.end("Scan for candidates"),g.features&x.Features.Utilities){p&&l.start("Register dependency messages");let D=$.default.resolve(r,u);for(let k of f.scanner.files){let R=$.default.resolve(k);R!==D&&o.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:R,parent:o.opts.from})}for(let{base:k,pattern:R}of f.scanner.globs)R==="*"&&r===k||(R===""?o.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:$.default.resolve(k),parent:o.opts.from}):o.messages.push({type:"dir-dependency",plugin:"@tailwindcss/postcss",dir:$.default.resolve(k),glob:R,parent:o.opts.from}));p&&l.end("Register dependency messages")}p&&l.start("Build utilities");let q=g.build(ee);if(p&&l.end("Build utilities"),f.tailwindCssAst!==q)if(t){p&&l.start("Optimization"),p&&l.start("AST -> CSS");let D=z(q);p&&l.end("AST -> CSS"),p&&l.start("Lightning CSS");let k=(0,x.optimize)(D,{minify:typeof t=="object"?t.minify:!0});p&&l.end("Lightning CSS"),p&&l.start("CSS -> PostCSS AST"),f.optimizedPostCssAst=i.parse(k.code,o.opts),p&&l.end("CSS -> PostCSS AST"),p&&l.end("Optimization")}else p&&l.start("Transform Tailwind CSS AST into PostCSS AST"),f.cachedPostCssAst=je(i,q,a.source),p&&l.end("Transform Tailwind CSS AST into PostCSS AST");f.tailwindCssAst=q,p&&l.start("Update PostCSS AST"),a.removeAll(),a.append(t?f.optimizedPostCssAst.clone().nodes:f.cachedPostCssAst.clone().nodes),a.raws.indent="  ",p&&l.end("Update PostCSS AST"),p&&l.end(`[@tailwindcss/postcss] ${(0,$.relative)(r,u)}`)}catch(y){f.compiler=null;for(let g of f.fullRebuildPaths)o.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:$.default.resolve(g),parent:o.opts.from});throw console.error(y),y&&typeof y=="object"&&"message"in y?a.error(`${y.message}`):a.error(`${y}`)}}catch(Ze){var Qe=Ze,Je=!0}finally{ve(N,Qe,Je)}}}]}}var He=Object.assign(Zt,{postcss:!0});module.exports=He;
