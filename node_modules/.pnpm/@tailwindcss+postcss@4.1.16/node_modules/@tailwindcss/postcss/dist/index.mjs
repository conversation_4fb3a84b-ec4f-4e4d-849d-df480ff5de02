var pe=(e,r)=>(r=Symbol[e])?r:Symbol.for("Symbol."+e),de=e=>{throw TypeError(e)};var me=(e,r,t)=>{if(r!=null){typeof r!="object"&&typeof r!="function"&&de("Object expected");var o,l;t&&(o=r[pe("asyncDispose")]),o===void 0&&(o=r[pe("dispose")],t&&(l=o)),typeof o!="function"&&de("Object not disposable"),l&&(o=function(){try{l.call(this)}catch(n){return Promise.reject(n)}}),e.push([t,o,r])}else t&&e.push([t]);return r},ge=(e,r,t)=>{var o=typeof SuppressedError=="function"?SuppressedError:function(i,a,u,s){return s=Error(u),s.name="SuppressedError",s.error=i,s.suppressed=a,s},l=i=>r=t?new o(i,r,"An error was suppressed during disposal"):(t=!0,i),n=i=>{for(;i=e.pop();)try{var a=i[1]&&i[1].call(i[2]);if(i[0])return Promise.resolve(a).then(n,u=>(l(u),n()))}catch(u){l(u)}if(t)throw r};return n()};import Lt from"@alloc/quick-lru";import{compileAst as Kt,env as zt,Features as ue,Instrumentation as Mt,optimize as Ft,Polyfills as fe}from"@tailwindcss/node";import{clearRequireCache as jt}from"@tailwindcss/node/require-cache";import{Scanner as Wt}from"@tailwindcss/oxide";import Bt from"fs";import D,{relative as We}from"path";function M(e){return{kind:"word",value:e}}function qe(e,r){return{kind:"function",value:e,nodes:r}}function He(e){return{kind:"separator",value:e}}function S(e){let r="";for(let t of e)switch(t.kind){case"word":case"separator":{r+=t.value;break}case"function":r+=t.value+"("+S(t.nodes)+")"}return r}var he=92,Ze=41,ve=58,ke=44,Qe=34,we=61,ye=62,be=60,xe=10,Je=40,Xe=39,et=47,Ae=32,Ce=9;function A(e){e=e.replaceAll(`\r
`,`
`);let r=[],t=[],o=null,l="",n;for(let i=0;i<e.length;i++){let a=e.charCodeAt(i);switch(a){case he:{l+=e[i]+e[i+1],i++;break}case et:{if(l.length>0){let s=M(l);o?o.nodes.push(s):r.push(s),l=""}let u=M(e[i]);o?o.nodes.push(u):r.push(u);break}case ve:case ke:case we:case ye:case be:case xe:case Ae:case Ce:{if(l.length>0){let c=M(l);o?o.nodes.push(c):r.push(c),l=""}let u=i,s=i+1;for(;s<e.length&&(n=e.charCodeAt(s),!(n!==ve&&n!==ke&&n!==we&&n!==ye&&n!==be&&n!==xe&&n!==Ae&&n!==Ce));s++);i=s-1;let f=He(e.slice(u,s));o?o.nodes.push(f):r.push(f);break}case Xe:case Qe:{let u=i;for(let s=i+1;s<e.length;s++)if(n=e.charCodeAt(s),n===he)s+=1;else if(n===a){i=s;break}l+=e.slice(u,i+1);break}case Je:{let u=qe(l,[]);l="",o?o.nodes.push(u):r.push(u),t.push(u),o=u;break}case Ze:{let u=t.pop();if(l.length>0){let s=M(l);u?.nodes.push(s),l=""}t.length>0?o=t[t.length-1]:o=null;break}default:l+=String.fromCharCode(a)}}return l.length>0&&r.push(M(l)),r}var d=class extends Map{constructor(t){super();this.factory=t}get(t){let o=super.get(t);return o===void 0&&(o=this.factory(t,this),this.set(t,o)),o}};var tr=new Uint8Array(256);var G=new Uint8Array(256);function w(e,r){let t=0,o=[],l=0,n=e.length,i=r.charCodeAt(0);for(let a=0;a<n;a++){let u=e.charCodeAt(a);if(t===0&&u===i){o.push(e.slice(l,a)),l=a+1;continue}switch(u){case 92:a+=1;break;case 39:case 34:for(;++a<n;){let s=e.charCodeAt(a);if(s===92){a+=1;continue}if(s===u)break}break;case 40:G[t]=41,t++;break;case 91:G[t]=93,t++;break;case 123:G[t]=125,t++;break;case 93:case 125:case 41:t>0&&u===G[t-1]&&t--;break}}return o.push(e.slice(l)),o}var X=(i=>(i[i.Continue=0]="Continue",i[i.Skip=1]="Skip",i[i.Stop=2]="Stop",i[i.Replace=3]="Replace",i[i.ReplaceSkip=4]="ReplaceSkip",i[i.ReplaceStop=5]="ReplaceStop",i))(X||{}),h={Continue:{kind:0},Skip:{kind:1},Stop:{kind:2},Replace:e=>({kind:3,nodes:Array.isArray(e)?e:[e]}),ReplaceSkip:e=>({kind:4,nodes:Array.isArray(e)?e:[e]}),ReplaceStop:e=>({kind:5,nodes:Array.isArray(e)?e:[e]})};function v(e,r){typeof r=="function"?Se(e,r):Se(e,r.enter,r.exit)}function Se(e,r=()=>h.Continue,t=()=>h.Continue){let o=[[e,0,null]],l={parent:null,depth:0,path(){let n=[];for(let i=1;i<o.length;i++){let a=o[i][2];a&&n.push(a)}return n}};for(;o.length>0;){let n=o.length-1,i=o[n],a=i[0],u=i[1],s=i[2];if(u>=a.length){o.pop();continue}if(l.parent=s,l.depth=n,u>=0){let O=a[u],T=r(O,l)??h.Continue;switch(T.kind){case 0:{O.nodes&&O.nodes.length>0&&o.push([O.nodes,0,O]),i[1]=~u;continue}case 2:return;case 1:{i[1]=~u;continue}case 3:{a.splice(u,1,...T.nodes);continue}case 5:{a.splice(u,1,...T.nodes);return}case 4:{a.splice(u,1,...T.nodes),i[1]+=T.nodes.length;continue}default:throw new Error(`Invalid \`WalkAction.${X[T.kind]??`Unknown(${T.kind})`}\` in enter.`)}}let f=~u,c=a[f],m=t(c,l)??h.Continue;switch(m.kind){case 0:i[1]=f+1;continue;case 2:return;case 3:{a.splice(f,1,...m.nodes),i[1]=f+m.nodes.length;continue}case 5:{a.splice(f,1,...m.nodes);return}case 4:{a.splice(f,1,...m.nodes),i[1]=f+m.nodes.length;continue}default:throw new Error(`Invalid \`WalkAction.${X[m.kind]??`Unknown(${m.kind})`}\` in exit.`)}}}var pr=new d(e=>{let r=A(e),t=new Set;return v(r,(o,l)=>{let n=l.parent===null?r:l.parent.nodes??[];if(o.kind==="word"&&(o.value==="+"||o.value==="-"||o.value==="*"||o.value==="/")){let i=n.indexOf(o)??-1;if(i===-1)return;let a=n[i-1];if(a?.kind!=="separator"||a.value!==" ")return;let u=n[i+1];if(u?.kind!=="separator"||u.value!==" ")return;t.add(a),t.add(u)}else o.kind==="separator"&&o.value.length>0&&o.value.trim()===""?(n[0]===o||n[n.length-1]===o)&&t.add(o):o.kind==="separator"&&o.value.trim()===","&&(o.value=",")}),t.size>0&&v(r,o=>{if(t.has(o))return t.delete(o),h.ReplaceSkip([])}),ee(r),S(r)});var dr=new d(e=>{let r=A(e);return r.length===3&&r[0].kind==="word"&&r[0].value==="&"&&r[1].kind==="separator"&&r[1].value===":"&&r[2].kind==="function"&&r[2].value==="is"?S(r[2].nodes):e});function ee(e){for(let r of e)switch(r.kind){case"function":{if(r.value==="url"||r.value.endsWith("_url")){r.value=F(r.value);break}if(r.value==="var"||r.value.endsWith("_var")||r.value==="theme"||r.value.endsWith("_theme")){r.value=F(r.value);for(let t=0;t<r.nodes.length;t++)ee([r.nodes[t]]);break}r.value=F(r.value),ee(r.nodes);break}case"separator":r.value=F(r.value);break;case"word":{(r.value[0]!=="-"||r.value[1]!=="-")&&(r.value=F(r.value));break}default:tt(r)}}var mr=new d(e=>{let r=A(e);return r.length===1&&r[0].kind==="function"&&r[0].value==="var"});function tt(e){throw new Error(`Unexpected value: ${e}`)}function F(e){return e.replaceAll("_",String.raw`\_`).replaceAll(" ","_")}var rt=process.env.FEATURES_ENV!=="stable";var R=/[+-]?\d*\.?\d+(?:[eE][+-]?\d+)?/,Cr=new RegExp(`^${R.source}$`);var Sr=new RegExp(`^${R.source}%$`);var Tr=new RegExp(`^${R.source}s*/s*${R.source}$`);var it=["cm","mm","Q","in","pc","pt","px","em","ex","ch","rem","lh","rlh","vw","vh","vmin","vmax","vb","vi","svw","svh","lvw","lvh","dvw","dvh","cqw","cqh","cqi","cqb","cqmin","cqmax"],$r=new RegExp(`^${R.source}(${it.join("|")})$`);var nt=["deg","rad","grad","turn"],Nr=new RegExp(`^${R.source}(${nt.join("|")})$`);var Er=new RegExp(`^${R.source} +${R.source} +${R.source}$`);function C(e){let r=Number(e);return Number.isInteger(r)&&r>=0&&String(r)===String(e)}function j(e,r){if(r===null)return e;let t=Number(r);return Number.isNaN(t)||(r=`${t*100}%`),r==="100%"?e:`color-mix(in oklab, ${e} ${r}, transparent)`}var lt={"--alpha":st,"--spacing":ut,"--theme":ft,theme:ct};function st(e,r,t,...o){let[l,n]=w(t,"/").map(i=>i.trim());if(!l||!n)throw new Error(`The --alpha(\u2026) function requires a color and an alpha value, e.g.: \`--alpha(${l||"var(--my-color)"} / ${n||"50%"})\``);if(o.length>0)throw new Error(`The --alpha(\u2026) function only accepts one argument, e.g.: \`--alpha(${l||"var(--my-color)"} / ${n||"50%"})\``);return j(l,n)}function ut(e,r,t,...o){if(!t)throw new Error("The --spacing(\u2026) function requires an argument, but received none.");if(o.length>0)throw new Error(`The --spacing(\u2026) function only accepts a single argument, but received ${o.length+1}.`);let l=e.theme.resolve(null,["--spacing"]);if(!l)throw new Error("The --spacing(\u2026) function requires that the `--spacing` theme variable exists, but it was not found.");return`calc(${l} * ${t})`}function ft(e,r,t,...o){if(!t.startsWith("--"))throw new Error("The --theme(\u2026) function can only be used with CSS variables from your theme.");let l=!1;t.endsWith(" inline")&&(l=!0,t=t.slice(0,-7)),r.kind==="at-rule"&&(l=!0);let n=e.resolveThemeValue(t,l);if(!n){if(o.length>0)return o.join(", ");throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the variable name is correct or provide a fallback value to silence this error.`)}if(o.length===0)return n;let i=o.join(", ");if(i==="initial")return n;if(n==="initial")return i;if(n.startsWith("var(")||n.startsWith("theme(")||n.startsWith("--theme(")){let a=A(n);return dt(a,i),S(a)}return n}function ct(e,r,t,...o){t=pt(t);let l=e.resolveThemeValue(t);if(!l&&o.length>0)return o.join(", ");if(!l)throw new Error(`Could not resolve value for theme function: \`theme(${t})\`. Consider checking if the path is correct or provide a fallback value to silence this error.`);return l}var Zr=new RegExp(Object.keys(lt).map(e=>`${e}\\(`).join("|"));function pt(e){if(e[0]!=="'"&&e[0]!=='"')return e;let r="",t=e[0];for(let o=1;o<e.length-1;o++){let l=e[o],n=e[o+1];l==="\\"&&(n===t||n==="\\")?(r+=n,o++):r+=l}return r}function dt(e,r){v(e,t=>{if(t.kind==="function"&&!(t.value!=="var"&&t.value!=="theme"&&t.value!=="--theme"))if(t.nodes.length===1)t.nodes.push({kind:"word",value:`, ${r}`});else{let o=t.nodes[t.nodes.length-1];o.kind==="word"&&o.value==="initial"&&(o.value=r)}})}var gt=/^(?<value>[-+]?(?:\d*\.)?\d+)(?<unit>[a-z]+|%)?$/i,Ee=new d(e=>{let r=gt.exec(e);if(!r)return null;let t=r.groups?.value;if(t===void 0)return null;let o=Number(t);if(Number.isNaN(o))return null;let l=r.groups?.unit;return l===void 0?[o,null]:[o,l]});function Ve(e,r="top",t="right",o="bottom",l="left"){return Re(`${e}-${r}`,`${e}-${t}`,`${e}-${o}`,`${e}-${l}`)}function Re(e="top",r="right",t="bottom",o="left"){return{1:[[e,0],[r,0],[t,0],[o,0]],2:[[e,0],[r,1],[t,0],[o,1]],3:[[e,0],[r,1],[t,2],[o,1]],4:[[e,0],[r,1],[t,2],[o,3]]}}function L(e,r){return{1:[[e,0],[r,0]],2:[[e,0],[r,1]]}}var gi={inset:Re(),margin:Ve("margin"),padding:Ve("padding"),gap:L("row-gap","column-gap")},hi={"inset-block":L("top","bottom"),"inset-inline":L("left","right"),"margin-block":L("margin-top","margin-bottom"),"margin-inline":L("margin-left","margin-right"),"padding-block":L("padding-top","padding-bottom"),"padding-inline":L("padding-left","padding-right")};var Bi=Symbol();var Yi=Symbol();var Gi=Symbol();var qi=Symbol();var Hi=Symbol();var Zi=Symbol();var Qi=Symbol();var Ji=Symbol();var Xi=Symbol();var en=Symbol();var tn=Symbol();var rn=Symbol();var nn=Symbol();var Tt=32,$t=9;var Nt=40;function De(e,r=[]){let t=e,o="";for(let l=5;l<e.length;l++){let n=e.charCodeAt(l);if(n===Tt||n===$t||n===Nt){t=e.slice(0,l),o=e.slice(l);break}}return x(t.trim(),o.trim(),r)}var ne={inherit:"inherit",current:"currentcolor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"oklch(98.4% 0.003 247.858)",100:"oklch(96.8% 0.007 247.896)",200:"oklch(92.9% 0.013 255.508)",300:"oklch(86.9% 0.022 252.894)",400:"oklch(70.4% 0.04 256.788)",500:"oklch(55.4% 0.046 257.417)",600:"oklch(44.6% 0.043 257.281)",700:"oklch(37.2% 0.044 257.287)",800:"oklch(27.9% 0.041 260.031)",900:"oklch(20.8% 0.042 265.755)",950:"oklch(12.9% 0.042 264.695)"},gray:{50:"oklch(98.5% 0.002 247.839)",100:"oklch(96.7% 0.003 264.542)",200:"oklch(92.8% 0.006 264.531)",300:"oklch(87.2% 0.01 258.338)",400:"oklch(70.7% 0.022 261.325)",500:"oklch(55.1% 0.027 264.364)",600:"oklch(44.6% 0.03 256.802)",700:"oklch(37.3% 0.034 259.733)",800:"oklch(27.8% 0.033 256.848)",900:"oklch(21% 0.034 264.665)",950:"oklch(13% 0.028 261.692)"},zinc:{50:"oklch(98.5% 0 0)",100:"oklch(96.7% 0.001 286.375)",200:"oklch(92% 0.004 286.32)",300:"oklch(87.1% 0.006 286.286)",400:"oklch(70.5% 0.015 286.067)",500:"oklch(55.2% 0.016 285.938)",600:"oklch(44.2% 0.017 285.786)",700:"oklch(37% 0.013 285.805)",800:"oklch(27.4% 0.006 286.033)",900:"oklch(21% 0.006 285.885)",950:"oklch(14.1% 0.005 285.823)"},neutral:{50:"oklch(98.5% 0 0)",100:"oklch(97% 0 0)",200:"oklch(92.2% 0 0)",300:"oklch(87% 0 0)",400:"oklch(70.8% 0 0)",500:"oklch(55.6% 0 0)",600:"oklch(43.9% 0 0)",700:"oklch(37.1% 0 0)",800:"oklch(26.9% 0 0)",900:"oklch(20.5% 0 0)",950:"oklch(14.5% 0 0)"},stone:{50:"oklch(98.5% 0.001 106.423)",100:"oklch(97% 0.001 106.424)",200:"oklch(92.3% 0.003 48.717)",300:"oklch(86.9% 0.005 56.366)",400:"oklch(70.9% 0.01 56.259)",500:"oklch(55.3% 0.013 58.071)",600:"oklch(44.4% 0.011 73.639)",700:"oklch(37.4% 0.01 67.558)",800:"oklch(26.8% 0.007 34.298)",900:"oklch(21.6% 0.006 56.043)",950:"oklch(14.7% 0.004 49.25)"},red:{50:"oklch(97.1% 0.013 17.38)",100:"oklch(93.6% 0.032 17.717)",200:"oklch(88.5% 0.062 18.334)",300:"oklch(80.8% 0.114 19.571)",400:"oklch(70.4% 0.191 22.216)",500:"oklch(63.7% 0.237 25.331)",600:"oklch(57.7% 0.245 27.325)",700:"oklch(50.5% 0.213 27.518)",800:"oklch(44.4% 0.177 26.899)",900:"oklch(39.6% 0.141 25.723)",950:"oklch(25.8% 0.092 26.042)"},orange:{50:"oklch(98% 0.016 73.684)",100:"oklch(95.4% 0.038 75.164)",200:"oklch(90.1% 0.076 70.697)",300:"oklch(83.7% 0.128 66.29)",400:"oklch(75% 0.183 55.934)",500:"oklch(70.5% 0.213 47.604)",600:"oklch(64.6% 0.222 41.116)",700:"oklch(55.3% 0.195 38.402)",800:"oklch(47% 0.157 37.304)",900:"oklch(40.8% 0.123 38.172)",950:"oklch(26.6% 0.079 36.259)"},amber:{50:"oklch(98.7% 0.022 95.277)",100:"oklch(96.2% 0.059 95.617)",200:"oklch(92.4% 0.12 95.746)",300:"oklch(87.9% 0.169 91.605)",400:"oklch(82.8% 0.189 84.429)",500:"oklch(76.9% 0.188 70.08)",600:"oklch(66.6% 0.179 58.318)",700:"oklch(55.5% 0.163 48.998)",800:"oklch(47.3% 0.137 46.201)",900:"oklch(41.4% 0.112 45.904)",950:"oklch(27.9% 0.077 45.635)"},yellow:{50:"oklch(98.7% 0.026 102.212)",100:"oklch(97.3% 0.071 103.193)",200:"oklch(94.5% 0.129 101.54)",300:"oklch(90.5% 0.182 98.111)",400:"oklch(85.2% 0.199 91.936)",500:"oklch(79.5% 0.184 86.047)",600:"oklch(68.1% 0.162 75.834)",700:"oklch(55.4% 0.135 66.442)",800:"oklch(47.6% 0.114 61.907)",900:"oklch(42.1% 0.095 57.708)",950:"oklch(28.6% 0.066 53.813)"},lime:{50:"oklch(98.6% 0.031 120.757)",100:"oklch(96.7% 0.067 122.328)",200:"oklch(93.8% 0.127 124.321)",300:"oklch(89.7% 0.196 126.665)",400:"oklch(84.1% 0.238 128.85)",500:"oklch(76.8% 0.233 130.85)",600:"oklch(64.8% 0.2 131.684)",700:"oklch(53.2% 0.157 131.589)",800:"oklch(45.3% 0.124 130.933)",900:"oklch(40.5% 0.101 131.063)",950:"oklch(27.4% 0.072 132.109)"},green:{50:"oklch(98.2% 0.018 155.826)",100:"oklch(96.2% 0.044 156.743)",200:"oklch(92.5% 0.084 155.995)",300:"oklch(87.1% 0.15 154.449)",400:"oklch(79.2% 0.209 151.711)",500:"oklch(72.3% 0.219 149.579)",600:"oklch(62.7% 0.194 149.214)",700:"oklch(52.7% 0.154 150.069)",800:"oklch(44.8% 0.119 151.328)",900:"oklch(39.3% 0.095 152.535)",950:"oklch(26.6% 0.065 152.934)"},emerald:{50:"oklch(97.9% 0.021 166.113)",100:"oklch(95% 0.052 163.051)",200:"oklch(90.5% 0.093 164.15)",300:"oklch(84.5% 0.143 164.978)",400:"oklch(76.5% 0.177 163.223)",500:"oklch(69.6% 0.17 162.48)",600:"oklch(59.6% 0.145 163.225)",700:"oklch(50.8% 0.118 165.612)",800:"oklch(43.2% 0.095 166.913)",900:"oklch(37.8% 0.077 168.94)",950:"oklch(26.2% 0.051 172.552)"},teal:{50:"oklch(98.4% 0.014 180.72)",100:"oklch(95.3% 0.051 180.801)",200:"oklch(91% 0.096 180.426)",300:"oklch(85.5% 0.138 181.071)",400:"oklch(77.7% 0.152 181.912)",500:"oklch(70.4% 0.14 182.503)",600:"oklch(60% 0.118 184.704)",700:"oklch(51.1% 0.096 186.391)",800:"oklch(43.7% 0.078 188.216)",900:"oklch(38.6% 0.063 188.416)",950:"oklch(27.7% 0.046 192.524)"},cyan:{50:"oklch(98.4% 0.019 200.873)",100:"oklch(95.6% 0.045 203.388)",200:"oklch(91.7% 0.08 205.041)",300:"oklch(86.5% 0.127 207.078)",400:"oklch(78.9% 0.154 211.53)",500:"oklch(71.5% 0.143 215.221)",600:"oklch(60.9% 0.126 221.723)",700:"oklch(52% 0.105 223.128)",800:"oklch(45% 0.085 224.283)",900:"oklch(39.8% 0.07 227.392)",950:"oklch(30.2% 0.056 229.695)"},sky:{50:"oklch(97.7% 0.013 236.62)",100:"oklch(95.1% 0.026 236.824)",200:"oklch(90.1% 0.058 230.902)",300:"oklch(82.8% 0.111 230.318)",400:"oklch(74.6% 0.16 232.661)",500:"oklch(68.5% 0.169 237.323)",600:"oklch(58.8% 0.158 241.966)",700:"oklch(50% 0.134 242.749)",800:"oklch(44.3% 0.11 240.79)",900:"oklch(39.1% 0.09 240.876)",950:"oklch(29.3% 0.066 243.157)"},blue:{50:"oklch(97% 0.014 254.604)",100:"oklch(93.2% 0.032 255.585)",200:"oklch(88.2% 0.059 254.128)",300:"oklch(80.9% 0.105 251.813)",400:"oklch(70.7% 0.165 254.624)",500:"oklch(62.3% 0.214 259.815)",600:"oklch(54.6% 0.245 262.881)",700:"oklch(48.8% 0.243 264.376)",800:"oklch(42.4% 0.199 265.638)",900:"oklch(37.9% 0.146 265.522)",950:"oklch(28.2% 0.091 267.935)"},indigo:{50:"oklch(96.2% 0.018 272.314)",100:"oklch(93% 0.034 272.788)",200:"oklch(87% 0.065 274.039)",300:"oklch(78.5% 0.115 274.713)",400:"oklch(67.3% 0.182 276.935)",500:"oklch(58.5% 0.233 277.117)",600:"oklch(51.1% 0.262 276.966)",700:"oklch(45.7% 0.24 277.023)",800:"oklch(39.8% 0.195 277.366)",900:"oklch(35.9% 0.144 278.697)",950:"oklch(25.7% 0.09 281.288)"},violet:{50:"oklch(96.9% 0.016 293.756)",100:"oklch(94.3% 0.029 294.588)",200:"oklch(89.4% 0.057 293.283)",300:"oklch(81.1% 0.111 293.571)",400:"oklch(70.2% 0.183 293.541)",500:"oklch(60.6% 0.25 292.717)",600:"oklch(54.1% 0.281 293.009)",700:"oklch(49.1% 0.27 292.581)",800:"oklch(43.2% 0.232 292.759)",900:"oklch(38% 0.189 293.745)",950:"oklch(28.3% 0.141 291.089)"},purple:{50:"oklch(97.7% 0.014 308.299)",100:"oklch(94.6% 0.033 307.174)",200:"oklch(90.2% 0.063 306.703)",300:"oklch(82.7% 0.119 306.383)",400:"oklch(71.4% 0.203 305.504)",500:"oklch(62.7% 0.265 303.9)",600:"oklch(55.8% 0.288 302.321)",700:"oklch(49.6% 0.265 301.924)",800:"oklch(43.8% 0.218 303.724)",900:"oklch(38.1% 0.176 304.987)",950:"oklch(29.1% 0.149 302.717)"},fuchsia:{50:"oklch(97.7% 0.017 320.058)",100:"oklch(95.2% 0.037 318.852)",200:"oklch(90.3% 0.076 319.62)",300:"oklch(83.3% 0.145 321.434)",400:"oklch(74% 0.238 322.16)",500:"oklch(66.7% 0.295 322.15)",600:"oklch(59.1% 0.293 322.896)",700:"oklch(51.8% 0.253 323.949)",800:"oklch(45.2% 0.211 324.591)",900:"oklch(40.1% 0.17 325.612)",950:"oklch(29.3% 0.136 325.661)"},pink:{50:"oklch(97.1% 0.014 343.198)",100:"oklch(94.8% 0.028 342.258)",200:"oklch(89.9% 0.061 343.231)",300:"oklch(82.3% 0.12 346.018)",400:"oklch(71.8% 0.202 349.761)",500:"oklch(65.6% 0.241 354.308)",600:"oklch(59.2% 0.249 0.584)",700:"oklch(52.5% 0.223 3.958)",800:"oklch(45.9% 0.187 3.815)",900:"oklch(40.8% 0.153 2.432)",950:"oklch(28.4% 0.109 3.907)"},rose:{50:"oklch(96.9% 0.015 12.422)",100:"oklch(94.1% 0.03 12.58)",200:"oklch(89.2% 0.058 10.001)",300:"oklch(81% 0.117 11.638)",400:"oklch(71.2% 0.194 13.428)",500:"oklch(64.5% 0.246 16.439)",600:"oklch(58.6% 0.253 17.585)",700:"oklch(51.4% 0.222 16.935)",800:"oklch(45.5% 0.188 13.697)",900:"oklch(41% 0.159 10.272)",950:"oklch(27.1% 0.105 12.094)"}};function z(e){return{__BARE_VALUE__:e}}var N=z(e=>{if(C(e.value))return e.value}),b=z(e=>{if(C(e.value))return`${e.value}%`}),I=z(e=>{if(C(e.value))return`${e.value}px`}),Ke=z(e=>{if(C(e.value))return`${e.value}ms`}),Z=z(e=>{if(C(e.value))return`${e.value}deg`}),Ot=z(e=>{if(e.fraction===null)return;let[r,t]=w(e.fraction,"/");if(!(!C(r)||!C(t)))return e.fraction}),ze=z(e=>{if(C(Number(e.value)))return`repeat(${e.value}, minmax(0, 1fr))`}),_t={accentColor:({theme:e})=>e("colors"),animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aria:{busy:'busy="true"',checked:'checked="true"',disabled:'disabled="true"',expanded:'expanded="true"',hidden:'hidden="true"',pressed:'pressed="true"',readonly:'readonly="true"',required:'required="true"',selected:'selected="true"'},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9",...Ot},backdropBlur:({theme:e})=>e("blur"),backdropBrightness:({theme:e})=>({...e("brightness"),...b}),backdropContrast:({theme:e})=>({...e("contrast"),...b}),backdropGrayscale:({theme:e})=>({...e("grayscale"),...b}),backdropHueRotate:({theme:e})=>({...e("hueRotate"),...Z}),backdropInvert:({theme:e})=>({...e("invert"),...b}),backdropOpacity:({theme:e})=>({...e("opacity"),...b}),backdropSaturate:({theme:e})=>({...e("saturate"),...b}),backdropSepia:({theme:e})=>({...e("sepia"),...b}),backgroundColor:({theme:e})=>e("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:e})=>e("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},borderColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),borderOpacity:({theme:e})=>e("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:e})=>e("spacing"),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px",...I},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:e})=>e("colors"),brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2",...b},caretColor:({theme:e})=>e("colors"),colors:()=>({...ne}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",...N},container:{},content:{none:"none"},contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2",...b},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:e})=>e("borderColor"),divideOpacity:({theme:e})=>e("borderOpacity"),divideWidth:({theme:e})=>({...e("borderWidth"),...I}),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:e})=>e("colors"),flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",...e("spacing")}),flexGrow:{0:"0",DEFAULT:"1",...N},flexShrink:{0:"0",DEFAULT:"1",...N},fontFamily:{sans:["ui-sans-serif","system-ui","sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:e})=>e("spacing"),gradientColorStops:({theme:e})=>e("colors"),gradientColorStopPositions:{"0%":"0%","5%":"5%","10%":"10%","15%":"15%","20%":"20%","25%":"25%","30%":"30%","35%":"35%","40%":"40%","45%":"45%","50%":"50%","55%":"55%","60%":"60%","65%":"65%","70%":"70%","75%":"75%","80%":"80%","85%":"85%","90%":"90%","95%":"95%","100%":"100%",...b},grayscale:{0:"0",DEFAULT:"100%",...b},gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...N},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...N},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...N},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13",...N},gridTemplateColumns:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...ze},gridTemplateRows:{none:"none",subgrid:"subgrid",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))",...ze},height:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg",...Z},inset:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),invert:{0:"0",DEFAULT:"100%",...b},keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},listStyleImage:{none:"none"},margin:({theme:e})=>({auto:"auto",...e("spacing")}),lineClamp:{1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",...N},maxHeight:({theme:e})=>({none:"none",full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),maxWidth:({theme:e})=>({none:"none",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...e("spacing")}),minHeight:({theme:e})=>({full:"100%",screen:"100vh",svh:"100svh",lvh:"100lvh",dvh:"100dvh",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),minWidth:({theme:e})=>({full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",15:"0.15",20:"0.2",25:"0.25",30:"0.3",35:"0.35",40:"0.4",45:"0.45",50:"0.5",55:"0.55",60:"0.6",65:"0.65",70:"0.7",75:"0.75",80:"0.8",85:"0.85",90:"0.9",95:"0.95",100:"1",...b},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",...N},outlineColor:({theme:e})=>e("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...I},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...I},padding:({theme:e})=>e("spacing"),placeholderColor:({theme:e})=>e("colors"),placeholderOpacity:({theme:e})=>e("opacity"),ringColor:({theme:e})=>({DEFAULT:"currentcolor",...e("colors")}),ringOffsetColor:({theme:e})=>e("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...I},ringOpacity:({theme:e})=>({DEFAULT:"0.5",...e("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...I},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg",...Z},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2",...b},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",...b},screens:{sm:"40rem",md:"48rem",lg:"64rem",xl:"80rem","2xl":"96rem"},scrollMargin:({theme:e})=>e("spacing"),scrollPadding:({theme:e})=>e("spacing"),sepia:{0:"0",DEFAULT:"100%",...b},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",...Z},space:({theme:e})=>e("spacing"),spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},stroke:({theme:e})=>({none:"none",...e("colors")}),strokeWidth:{0:"0",1:"1",2:"2",...N},supports:{},data:{},textColor:({theme:e})=>e("colors"),textDecorationColor:({theme:e})=>e("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...I},textIndent:({theme:e})=>e("spacing"),textOpacity:({theme:e})=>e("opacity"),textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px",...I},transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Ke},transitionDuration:{DEFAULT:"150ms",0:"0s",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms",...Ke},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, outline-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:e})=>({"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%",...e("spacing")}),size:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),width:({theme:e})=>({auto:"auto","1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",svw:"100svw",lvw:"100lvw",dvw:"100dvw",min:"min-content",max:"max-content",fit:"fit-content",...e("spacing")}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50",...N}};function oe(e){let r=[0];for(let l=0;l<e.length;l++)e.charCodeAt(l)===10&&r.push(l+1);function t(l){let n=0,i=r.length;for(;i>0;){let u=(i|0)>>1,s=n+u;r[s]<=l?(n=s+1,i=i-u-1):i=u}n-=1;let a=l-r[n];return{line:n+1,column:a}}function o({line:l,column:n}){l-=1,l=Math.min(Math.max(l,0),r.length-1);let i=r[l],a=r[l+1]??i;return Math.min(Math.max(i+n,0),a)}return{find:t,findOffset:o}}var Dt=64;function U(e,r=[]){return{kind:"rule",selector:e,nodes:r}}function x(e,r="",t=[]){return{kind:"at-rule",name:e,params:r,nodes:t}}function P(e,r=[]){return e.charCodeAt(0)===Dt?De(e,r):U(e,r)}function $(e,r,t=!1){return{kind:"declaration",property:e,value:r,important:t}}function H(e){return{kind:"comment",value:e}}function K(e,r){let t=0,o={file:null,code:""};function l(i,a=0){let u="",s="  ".repeat(a);if(i.kind==="declaration"){if(u+=`${s}${i.property}: ${i.value}${i.important?" !important":""};
`,r){t+=s.length;let f=t;t+=i.property.length,t+=2,t+=i.value?.length??0,i.important&&(t+=11);let c=t;t+=2,i.dst=[o,f,c]}}else if(i.kind==="rule"){if(u+=`${s}${i.selector} {
`,r){t+=s.length;let f=t;t+=i.selector.length,t+=1;let c=t;i.dst=[o,f,c],t+=2}for(let f of i.nodes)u+=l(f,a+1);u+=`${s}}
`,r&&(t+=s.length,t+=2)}else if(i.kind==="at-rule"){if(i.nodes.length===0){let f=`${s}${i.name} ${i.params};
`;if(r){t+=s.length;let c=t;t+=i.name.length,t+=1,t+=i.params.length;let m=t;t+=2,i.dst=[o,c,m]}return f}if(u+=`${s}${i.name}${i.params?` ${i.params} `:" "}{
`,r){t+=s.length;let f=t;t+=i.name.length,i.params&&(t+=1,t+=i.params.length),t+=1;let c=t;i.dst=[o,f,c],t+=2}for(let f of i.nodes)u+=l(f,a+1);u+=`${s}}
`,r&&(t+=s.length,t+=2)}else if(i.kind==="comment"){if(u+=`${s}/*${i.value}*/
`,r){t+=s.length;let f=t;t+=2+i.value.length+2;let c=t;i.dst=[o,f,c],t+=1}}else if(i.kind==="context"||i.kind==="at-root")return"";return u}let n="";for(let i of e)n+=l(i,0);return o.code=n,n}var Ut=33;function Me(e,r,t){let o=new d(s=>new e.Input(s.code,{map:t?.input.map,from:s.file??void 0})),l=new d(s=>oe(s.code)),n=e.root();n.source=t;function i(s){if(!s||!s[0])return;let f=l.get(s[0]),c=f.find(s[1]),m=f.find(s[2]);return{input:o.get(s[0]),start:{line:c.line,column:c.column+1,offset:s[1]},end:{line:m.line,column:m.column+1,offset:s[2]}}}function a(s,f){let c=i(f);c?s.source=c:delete s.source}function u(s,f){if(s.kind==="declaration"){let c=e.decl({prop:s.property,value:s.value??"",important:s.important});a(c,s.src),f.append(c)}else if(s.kind==="rule"){let c=e.rule({selector:s.selector});a(c,s.src),c.raws.semicolon=!0,f.append(c);for(let m of s.nodes)u(m,c)}else if(s.kind==="at-rule"){let c=e.atRule({name:s.name.slice(1),params:s.params});a(c,s.src),c.raws.semicolon=!0,f.append(c);for(let m of s.nodes)u(m,c)}else if(s.kind==="comment"){let c=e.comment({text:s.value});c.raws.left="",c.raws.right="",a(c,s.src),f.append(c)}else s.kind==="at-root"||s.kind}for(let s of r)u(s,n);return n}function Fe(e){let r=new d(n=>({file:n.file??n.id??null,code:n.css}));function t(n){let i=n.source;if(!i)return;let a=i.input;if(a&&i.start!==void 0&&i.end!==void 0)return[r.get(a),i.start.offset,i.end.offset]}function o(n,i){if(n.type==="decl"){let a=$(n.prop,n.value,n.important);a.src=t(n),i.push(a)}else if(n.type==="rule"){let a=P(n.selector);a.src=t(n),n.each(u=>o(u,a.nodes)),i.push(a)}else if(n.type==="atrule"){let a=x(`@${n.name}`,n.params);a.src=t(n),n.each(u=>o(u,a.nodes)),i.push(a)}else if(n.type==="comment"){if(n.text.charCodeAt(0)!==Ut)return;let a=H(n.text);a.src=t(n),i.push(a)}}let l=[];return e.each(n=>o(n,l)),l}import{normalizePath as je}from"@tailwindcss/node";import Q from"path";var ae="'",le='"';function se(){let e=new WeakSet;function r(t){let o=t.root().source?.input.file;if(!o)return;let l=t.source?.input.file;if(!l||e.has(t))return;let n=t.params[0],i=n[0]===le&&n[n.length-1]===le?le:n[0]===ae&&n[n.length-1]===ae?ae:null;if(!i)return;let a=t.params.slice(1,-1),u="";if(a.startsWith("!")&&(a=a.slice(1),u="!"),!a.startsWith("./")&&!a.startsWith("../"))return;let s=Q.posix.join(je(Q.dirname(l)),a),f=Q.posix.dirname(je(o)),c=Q.posix.relative(f,s);c.startsWith(".")||(c="./"+c),t.params=i+u+c+i,e.add(t)}return{postcssPlugin:"tailwindcss-postcss-fix-relative-paths",Once(t){t.walkAtRules(/source|plugin|config/,r)}}}var p=zt.DEBUG,ce=new Lt({maxSize:50});function Yt(e,r,t){let o=`${r}:${t.base??""}:${JSON.stringify(t.optimize)}`;if(ce.has(o))return ce.get(o);let l={mtimes:new Map,compiler:null,scanner:null,tailwindCssAst:[],cachedPostCssAst:e.root(),optimizedPostCssAst:e.root(),fullRebuildPaths:[]};return ce.set(o,l),l}function Gt(e={}){let r=e.base??process.cwd(),t=e.optimize??process.env.NODE_ENV==="production",o=e.transformAssetUrls??!0;return{postcssPlugin:"@tailwindcss/postcss",plugins:[se(),{postcssPlugin:"tailwindcss",async Once(l,{result:n,postcss:i}){var T=[];try{let a=me(T,new Mt);let u=n.opts.from??"";let s=u.endsWith(".module.css");p&&a.start(`[@tailwindcss/postcss] ${We(r,u)}`);{p&&a.start("Quick bail check");let y=!0;if(l.walkAtRules(g=>{if(g.name==="import"||g.name==="reference"||g.name==="theme"||g.name==="variant"||g.name==="config"||g.name==="plugin"||g.name==="apply"||g.name==="tailwind")return y=!1,!1}),y)return;p&&a.end("Quick bail check")}let f=Yt(i,u,e);let c=D.dirname(D.resolve(u));let m=f.compiler===null;async function O(){p&&a.start("Setup compiler"),f.fullRebuildPaths.length>0&&!m&&jt(f.fullRebuildPaths),f.fullRebuildPaths=[],p&&a.start("PostCSS AST -> Tailwind CSS AST");let y=Fe(l);p&&a.end("PostCSS AST -> Tailwind CSS AST"),p&&a.start("Create compiler");let g=await Kt(y,{from:n.opts.from,base:c,shouldRewriteUrls:o,onDependency:J=>f.fullRebuildPaths.push(J),polyfills:s?fe.All^fe.AtProperty:fe.All});return p&&a.end("Create compiler"),p&&a.end("Setup compiler"),g}try{if(f.compiler??=O(),(await f.compiler).features===ue.None)return;let y="incremental";p&&a.start("Register full rebuild paths");{for(let k of f.fullRebuildPaths)n.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:D.resolve(k),parent:n.opts.from});let _=n.messages.flatMap(k=>k.type!=="dependency"?[]:k.file);_.push(u);for(let k of _){let E=Bt.statSync(k,{throwIfNoEntry:!1})?.mtimeMs??null;if(E===null){k===u&&(y="full");continue}f.mtimes.get(k)!==E&&(y="full",f.mtimes.set(k,E))}}p&&a.end("Register full rebuild paths"),y==="full"&&!m&&(f.compiler=O());let g=await f.compiler;if(f.scanner===null||y==="full"){p&&a.start("Setup scanner");let _=(g.root==="none"?[]:g.root===null?[{base:r,pattern:"**/*",negated:!1}]:[{...g.root,negated:!1}]).concat(g.sources);f.scanner=new Wt({sources:_}),p&&a.end("Setup scanner")}p&&a.start("Scan for candidates");let J=g.features&ue.Utilities?f.scanner.scan():[];if(p&&a.end("Scan for candidates"),g.features&ue.Utilities){p&&a.start("Register dependency messages");let _=D.resolve(r,u);for(let k of f.scanner.files){let E=D.resolve(k);E!==_&&n.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:E,parent:n.opts.from})}for(let{base:k,pattern:E}of f.scanner.globs)E==="*"&&r===k||(E===""?n.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:D.resolve(k),parent:n.opts.from}):n.messages.push({type:"dir-dependency",plugin:"@tailwindcss/postcss",dir:D.resolve(k),glob:E,parent:n.opts.from}));p&&a.end("Register dependency messages")}p&&a.start("Build utilities");let Y=g.build(J);if(p&&a.end("Build utilities"),f.tailwindCssAst!==Y)if(t){p&&a.start("Optimization"),p&&a.start("AST -> CSS");let _=K(Y);p&&a.end("AST -> CSS"),p&&a.start("Lightning CSS");let k=Ft(_,{minify:typeof t=="object"?t.minify:!0});p&&a.end("Lightning CSS"),p&&a.start("CSS -> PostCSS AST"),f.optimizedPostCssAst=i.parse(k.code,n.opts),p&&a.end("CSS -> PostCSS AST"),p&&a.end("Optimization")}else p&&a.start("Transform Tailwind CSS AST into PostCSS AST"),f.cachedPostCssAst=Me(i,Y,l.source),p&&a.end("Transform Tailwind CSS AST into PostCSS AST");f.tailwindCssAst=Y,p&&a.start("Update PostCSS AST"),l.removeAll(),l.append(t?f.optimizedPostCssAst.clone().nodes:f.cachedPostCssAst.clone().nodes),l.raws.indent="  ",p&&a.end("Update PostCSS AST"),p&&a.end(`[@tailwindcss/postcss] ${We(r,u)}`)}catch(y){f.compiler=null;for(let g of f.fullRebuildPaths)n.messages.push({type:"dependency",plugin:"@tailwindcss/postcss",file:D.resolve(g),parent:n.opts.from});throw console.error(y),y&&typeof y=="object"&&"message"in y?l.error(`${y.message}`):l.error(`${y}`)}}catch(Be){var Ye=Be,Ge=!0}finally{ge(T,Ye,Ge)}}}]}}var Zl=Object.assign(Gt,{postcss:!0});export{Zl as default};
