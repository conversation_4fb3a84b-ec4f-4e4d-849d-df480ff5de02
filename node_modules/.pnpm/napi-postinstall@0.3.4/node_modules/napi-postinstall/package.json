{"name": "napi-postinstall", "version": "0.3.4", "type": "commonjs", "description": "The `postinstall` script helper for handling native bindings in legacy `npm` versions", "repository": "git+https://github.com/un-ts/napi-postinstall.git", "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://www.1stG.me)", "funding": "https://opencollective.com/napi-postinstall", "license": "MIT", "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "bin": "./lib/cli.js", "main": "./lib/index.js", "types": "./lib/index.d.ts", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./fallback": {"types": "./lib/fallback.d.ts", "default": "./lib/fallback.js"}, "./package.json": "./package.json"}, "files": ["lib", "!**/*.tsbuildinfo"]}