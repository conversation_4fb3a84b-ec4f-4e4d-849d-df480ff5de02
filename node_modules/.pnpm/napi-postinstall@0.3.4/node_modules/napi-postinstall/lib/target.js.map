{"version": 3, "file": "target.js", "sourceRoot": "", "sources": ["../src/target.ts"], "names": [], "mappings": ";;AAmCA,kCAgDC;AAjFD,iDAAgE;AAGhE,MAAM,aAAa,GAA+B;IAChD,MAAM,EAAE,KAAK;IACb,OAAO,EAAE,OAAO;IAChB,IAAI,EAAE,MAAM;IACZ,KAAK,EAAE,KAAK;IACZ,WAAW,EAAE,SAAS;IACtB,SAAS,EAAE,SAAS;IACpB,WAAW,EAAE,OAAO;CACrB,CAAA;AAED,MAAM,iBAAiB,GAA6B;IAClD,KAAK,EAAE,OAAO;IACd,OAAO,EAAE,SAAS;IAClB,MAAM,EAAE,QAAQ;IAChB,OAAO,EAAE,OAAO;IAChB,IAAI,EAAE,aAAa;CACpB,CAAA;AAED,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,CAAA;AAYhD,SAAgB,WAAW,CAAC,SAAiB;IAC3C,IACE,SAAS,KAAK,0BAAW;QACzB,SAAS,KAAK,GAAG,0BAAW,mBAAmB;QAC/C,SAAS,CAAC,UAAU,CAAC,GAAG,qBAAM,IAAI,mBAAI,GAAG,CAAC,EAC1C,CAAC;QACD,OAAO;YACL,MAAM,EAAE,SAAS;YACjB,eAAe,EAAE,0BAAW;YAC5B,QAAQ,EAAE,mBAAI;YACd,IAAI,EAAE,qBAAM;YACZ,GAAG,EAAE,mBAAI;SACV,CAAA;IACH,CAAC;IACD,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,mBAAI,CAAC;QACrC,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,mBAAI,EAAE;QACrC,CAAC,CAAC,SAAS,CAAA;IACb,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IACjC,IAAI,GAAW,CAAA;IACf,IAAI,GAAW,CAAA;IACf,IAAI,GAAG,GAAkB,IAAI,CAAA;IAC7B,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAGzB,CAAC;QAAA,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,OAAO,CAAA;IACvB,CAAC;SAAM,CAAC;QAKN,CAAC;QAAA,CAAC,GAAG,EAAE,AAAD,EAAG,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,OAAO,CAAA;IACrC,CAAC;IAED,IAAI,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;QAChC,GAAG,GAAG,GAAG,CAAA;QAET,GAAG,GAAG,IAAI,CAAA;IACZ,CAAC;IACD,MAAM,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,IAAK,GAAgB,CAAA;IAC5D,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,IAAK,GAAkB,CAAA;IAEtD,OAAO;QACL,MAAM,EAAE,SAAS;QACjB,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,QAAQ,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,QAAQ,IAAI,IAAI,EAAE;QAC3E,QAAQ;QACR,IAAI;QACJ,GAAG;KACJ,CAAA;AACH,CAAC"}