module.exports = [
"[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/Documents/augment-projects/flywheel-media/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "chunks/e6fca_b6af4813._.js",
  "chunks/[root-of-the-server]__b2fd1ed3._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[turbopack-node]/transforms/postcss.ts { CONFIG => \"[project]/Documents/augment-projects/flywheel-media/postcss.config.mjs [postcss] (ecmascript)\" } [postcss] (ecmascript)");
    });
});
}),
];