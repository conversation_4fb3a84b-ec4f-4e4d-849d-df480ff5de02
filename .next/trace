[{"name": "generate-buildid", "duration": 144, "timestamp": 980017583783, "id": 4, "parentId": 1, "tags": {}, "startTime": 1762275121993, "traceId": "86235a6c4a318487"}, {"name": "load-custom-routes", "duration": 213, "timestamp": 980017583984, "id": 5, "parentId": 1, "tags": {}, "startTime": 1762275121994, "traceId": "86235a6c4a318487"}, {"name": "create-dist-dir", "duration": 169, "timestamp": 980017584285, "id": 6, "parentId": 1, "tags": {}, "startTime": 1762275121994, "traceId": "86235a6c4a318487"}, {"name": "clean", "duration": 19863, "timestamp": 980017585137, "id": 7, "parentId": 1, "tags": {}, "startTime": 1762275121995, "traceId": "86235a6c4a318487"}, {"name": "create-pages-mapping", "duration": 137, "timestamp": 980017612174, "id": 8, "parentId": 1, "tags": {}, "startTime": 1762275122022, "traceId": "86235a6c4a318487"}, {"name": "collect-app-files", "duration": 1195, "timestamp": 980017612333, "id": 9, "parentId": 1, "tags": {}, "startTime": 1762275122022, "traceId": "86235a6c4a318487"}, {"name": "create-app-mapping", "duration": 1422, "timestamp": 980017613545, "id": 10, "parentId": 1, "tags": {}, "startTime": 1762275122023, "traceId": "86235a6c4a318487"}, {"name": "create-app-layouts", "duration": 100, "timestamp": 980017614990, "id": 11, "parentId": 1, "tags": {}, "startTime": 1762275122025, "traceId": "86235a6c4a318487"}, {"name": "collect-default-files", "duration": 318, "timestamp": 980017615823, "id": 13, "parentId": 1, "tags": {}, "startTime": 1762275122026, "traceId": "86235a6c4a318487"}, {"name": "generate-route-types", "duration": 48387, "timestamp": 980017615387, "id": 12, "parentId": 1, "tags": {}, "startTime": 1762275122025, "traceId": "86235a6c4a318487"}, {"name": "public-dir-conflict-check", "duration": 50, "timestamp": 980017663907, "id": 14, "parentId": 1, "tags": {}, "startTime": 1762275122074, "traceId": "86235a6c4a318487"}, {"name": "generate-routes-manifest", "duration": 1584, "timestamp": 980017663997, "id": 15, "parentId": 1, "tags": {}, "startTime": 1762275122074, "traceId": "86235a6c4a318487"}, {"name": "run-turbopack", "duration": 7850870, "timestamp": 980017670116, "id": 17, "parentId": 1, "tags": {}, "startTime": 1762275122080, "traceId": "86235a6c4a318487"}, {"name": "run-typescript", "duration": 1665497, "timestamp": 980025547711, "id": 19, "parentId": 1, "tags": {}, "startTime": 1762275129958, "traceId": "86235a6c4a318487"}, {"name": "check-static-error-page", "duration": 7947, "timestamp": 980027226503, "id": 22, "parentId": 21, "tags": {}, "startTime": 1762275131636, "traceId": "86235a6c4a318487"}, {"name": "is-page-static", "duration": 241641, "timestamp": 980027238022, "id": 30, "parentId": 24, "tags": {}, "startTime": 1762275131648, "traceId": "86235a6c4a318487"}, {"name": "check-page", "duration": 250793, "timestamp": 980027228926, "id": 24, "parentId": 21, "tags": {"page": "/_global-error"}, "startTime": 1762275131639, "traceId": "86235a6c4a318487"}, {"name": "is-page-static", "duration": 277181, "timestamp": 980027238455, "id": 32, "parentId": 29, "tags": {}, "startTime": 1762275131648, "traceId": "86235a6c4a318487"}, {"name": "check-page", "duration": 281756, "timestamp": 980027233921, "id": 29, "parentId": 21, "tags": {"page": "/favicon.ico"}, "startTime": 1762275131644, "traceId": "86235a6c4a318487"}, {"name": "is-page-static", "duration": 281855, "timestamp": 980027238335, "id": 31, "parentId": 23, "tags": {}, "startTime": 1762275131648, "traceId": "86235a6c4a318487"}, {"name": "check-page", "duration": 292651, "timestamp": 980027227567, "id": 23, "parentId": 21, "tags": {"page": "/_not-found"}, "startTime": 1762275131637, "traceId": "86235a6c4a318487"}, {"name": "is-page-static", "duration": 278123, "timestamp": 980027243747, "id": 34, "parentId": 26, "tags": {}, "startTime": 1762275131654, "traceId": "86235a6c4a318487"}, {"name": "check-page", "duration": 292562, "timestamp": 980027229340, "id": 26, "parentId": 21, "tags": {"page": "/contact"}, "startTime": 1762275131639, "traceId": "86235a6c4a318487"}, {"name": "is-page-static", "duration": 279561, "timestamp": 980027243648, "id": 33, "parentId": 25, "tags": {}, "startTime": 1762275131654, "traceId": "86235a6c4a318487"}, {"name": "check-page", "duration": 294012, "timestamp": 980027229272, "id": 25, "parentId": 21, "tags": {"page": "/about"}, "startTime": 1762275131639, "traceId": "86235a6c4a318487"}, {"name": "is-page-static", "duration": 291967, "timestamp": 980027243782, "id": 35, "parentId": 28, "tags": {}, "startTime": 1762275131654, "traceId": "86235a6c4a318487"}, {"name": "check-page", "duration": 304776, "timestamp": 980027231020, "id": 28, "parentId": 21, "tags": {"page": "/services"}, "startTime": 1762275131641, "traceId": "86235a6c4a318487"}, {"name": "is-page-static", "duration": 298276, "timestamp": 980027243891, "id": 36, "parentId": 27, "tags": {}, "startTime": 1762275131654, "traceId": "86235a6c4a318487"}, {"name": "check-page", "duration": 313025, "timestamp": 980027229506, "id": 27, "parentId": 21, "tags": {"page": "/"}, "startTime": 1762275131639, "traceId": "86235a6c4a318487"}, {"name": "static-check", "duration": 316853, "timestamp": 980027225697, "id": 21, "parentId": 1, "tags": {}, "startTime": 1762275131636, "traceId": "86235a6c4a318487"}, {"name": "generate-required-server-files", "duration": 295, "timestamp": 980027542904, "id": 38, "parentId": 1, "tags": {}, "startTime": 1762275131953, "traceId": "86235a6c4a318487"}, {"name": "write-routes-manifest", "duration": 403, "timestamp": 980027544104, "id": 39, "parentId": 1, "tags": {}, "startTime": 1762275131954, "traceId": "86235a6c4a318487"}, {"name": "load-dotenv", "duration": 21, "timestamp": 980027551960, "id": 42, "parentId": 41, "tags": {}, "startTime": 1762275131962, "traceId": "86235a6c4a318487"}, {"name": "run-export-path-map", "duration": 208, "timestamp": 980027553743, "id": 43, "parentId": 41, "tags": {}, "startTime": 1762275131964, "traceId": "86235a6c4a318487"}, {"name": "next-export", "duration": 380930, "timestamp": 980027551205, "id": 41, "parentId": 1, "tags": {}, "startTime": 1762275131961, "traceId": "86235a6c4a318487"}, {"name": "move-exported-app-not-found-", "duration": 588, "timestamp": 980027933626, "id": 44, "parentId": 40, "tags": {}, "startTime": 1762275132343, "traceId": "86235a6c4a318487"}, {"name": "move-exported-app-global-error-", "duration": 352, "timestamp": 980027934278, "id": 45, "parentId": 40, "tags": {}, "startTime": 1762275132344, "traceId": "86235a6c4a318487"}, {"name": "static-generation", "duration": 388571, "timestamp": 980027547139, "id": 40, "parentId": 1, "tags": {}, "startTime": 1762275131957, "traceId": "86235a6c4a318487"}, {"name": "write-routes-manifest", "duration": 153, "timestamp": 980027935772, "id": 46, "parentId": 1, "tags": {}, "startTime": 1762275132346, "traceId": "86235a6c4a318487"}, {"name": "load-dotenv", "duration": 6, "timestamp": 980027945027, "id": 49, "parentId": 48, "tags": {}, "startTime": 1762275132355, "traceId": "86235a6c4a318487"}, {"name": "copy-next-static-directory", "duration": 3693, "timestamp": 980027947309, "id": 50, "parentId": 48, "tags": {}, "startTime": 1762275132357, "traceId": "86235a6c4a318487"}, {"name": "is-next-image-imported", "duration": 175, "timestamp": 980027951022, "id": 51, "parentId": 48, "tags": {}, "startTime": 1762275132361, "traceId": "86235a6c4a318487"}, {"name": "run-export-path-map", "duration": 20, "timestamp": 980027951350, "id": 52, "parentId": 48, "tags": {}, "startTime": 1762275132361, "traceId": "86235a6c4a318487"}, {"name": "copy-public-directory", "duration": 699, "timestamp": 980027951549, "id": 53, "parentId": 48, "tags": {}, "startTime": 1762275132361, "traceId": "86235a6c4a318487"}, {"name": "next-export", "duration": 2442451, "timestamp": 980027945022, "id": 48, "parentId": 1, "tags": {}, "startTime": 1762275132355, "traceId": "86235a6c4a318487"}, {"name": "output-export-full-static-export", "duration": 2442630, "timestamp": 980027944920, "id": 47, "parentId": 1, "tags": {}, "startTime": 1762275132355, "traceId": "86235a6c4a318487"}, {"name": "print-tree-view", "duration": 4812, "timestamp": 980030388907, "id": 54, "parentId": 1, "tags": {}, "startTime": 1762275134799, "traceId": "86235a6c4a318487"}, {"name": "telemetry-flush", "duration": 32750, "timestamp": 980030393741, "id": 55, "parentId": 1, "tags": {}, "startTime": 1762275134804, "traceId": "86235a6c4a318487"}, {"name": "next-build", "duration": 13002546, "timestamp": 980017423981, "id": 1, "tags": {"buildMode": "default", "version": "16.0.1", "bundler": "turbopack", "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1762275121834, "traceId": "86235a6c4a318487"}]