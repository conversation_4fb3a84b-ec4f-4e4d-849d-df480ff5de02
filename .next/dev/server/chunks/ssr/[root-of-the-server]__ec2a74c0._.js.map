{"version": 3, "sources": [], "sections": [{"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,6RAAO,EAAC,IAAA,uPAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?:\n    | 'default'\n    | 'destructive'\n    | 'outline'\n    | 'secondary'\n    | 'ghost'\n    | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    const baseClasses =\n      'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50';\n\n    const variants = {\n      default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n      destructive:\n        'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n      outline:\n        'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n      ghost: 'hover:bg-accent hover:text-accent-foreground',\n      link: 'text-primary underline-offset-4 hover:underline',\n    };\n\n    const sizes = {\n      default: 'h-10 px-4 py-2',\n      sm: 'h-9 rounded-md px-3',\n      lg: 'h-11 rounded-md px-8',\n      icon: 'h-10 w-10',\n    };\n\n    return (\n      <button\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAeA,MAAM,uBAAS,6aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,MAAM,cACJ;IAEF,MAAM,WAAW;QACf,SAAS;QACT,aACE;QACF,SACE;QACF,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,wcAAC;QACC,WAAW,IAAA,kLAAE,EAAC,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/app/services/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Button } from '@/components/ui/button';\n\nconst services = [\n  {\n    icon: '🎯',\n    title: 'Strategic Affiliate Partnerships',\n    description:\n      'Unlock growth with tailored affiliate partnerships that enhance visibility and drive customer engagement.',\n    features: [\n      'Partner Network Development',\n      'Performance Tracking',\n      'Revenue Optimization',\n      'Strategic Planning',\n    ],\n  },\n  {\n    icon: '📊',\n    title: 'Affiliate Marketing',\n    description:\n      'At flywheel-media, we empower brands through strategic affiliate marketing, connecting them with ideal customers for meaningful growth.',\n    features: [\n      'Campaign Management',\n      'Audience Targeting',\n      'Conversion Optimization',\n      'ROI Analysis',\n    ],\n  },\n  {\n    icon: '🌐',\n    title: 'Global Network Access',\n    description:\n      'Leverage our extensive global network to reach audiences beyond borders with partners spanning multiple industries.',\n    features: [\n      'International Reach',\n      'Multi-Industry Partners',\n      'Cross-Border Solutions',\n      'Market Expansion',\n    ],\n  },\n  {\n    icon: '🚀',\n    title: 'Technology Solutions',\n    description:\n      'Stay ahead with our cutting-edge adtech solutions designed for competitive digital landscapes.',\n    features: [\n      'Advanced Analytics',\n      'Real-time Tracking',\n      'Automated Optimization',\n      'Custom Integrations',\n    ],\n  },\n  {\n    icon: '🔍',\n    title: 'Transparency & Reporting',\n    description:\n      'Comprehensive reports and full transparency so you can see exactly how your campaigns perform.',\n    features: [\n      'Detailed Analytics',\n      'Performance Metrics',\n      'Custom Reports',\n      'Real-time Data',\n    ],\n  },\n  {\n    icon: '👥',\n    title: 'Dedicated Support',\n    description:\n      'Our experienced account managers work closely with you to ensure optimal campaign performance.',\n    features: [\n      'Personal Account Manager',\n      '24/7 Support',\n      'Strategic Consultation',\n      'Ongoing Optimization',\n    ],\n  },\n];\n\nexport default function ServicesPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Our Services\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Comprehensive affiliate marketing solutions designed to drive\n              growth and maximize your business potential\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Services Grid */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {services.map((service, index) => (\n              <div\n                key={index}\n                className=\"bg-white rounded-lg border border-gray-200 p-8 hover:shadow-lg transition-shadow duration-300\"\n              >\n                <div className=\"text-4xl mb-4\">{service.icon}</div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  {service.title}\n                </h3>\n                <p className=\"text-gray-600 mb-6\">{service.description}</p>\n\n                <ul className=\"space-y-2 mb-6\">\n                  {service.features.map((feature, featureIndex) => (\n                    <li\n                      key={featureIndex}\n                      className=\"text-sm text-gray-500 flex items-start\"\n                    >\n                      <span className=\"text-blue-500 mr-2\">✓</span>\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n\n                <Link href=\"/contact\" className=\"w-full\">\n                  <Button variant=\"outline\" className=\"w-full\">\n                    Get Started\n                  </Button>\n                </Link>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-blue-600 py-20\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-6\">\n            Ready to Get Started?\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8\">\n            Let's discuss how our services can help accelerate your business\n            growth through strategic affiliate marketing.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/contact\">\n              <Button variant=\"secondary\" size=\"lg\">\n                Contact Us Today\n              </Button>\n            </Link>\n            <Link href=\"/about\">\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"border-white text-white hover:bg-white hover:text-blue-600\"\n              >\n                Learn More About Us\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEA,MAAM,WAAW;IACf;QACE,MAAM;QACN,OAAO;QACP,aACE;QACF,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aACE;QACF,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aACE;QACF,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aACE;QACF,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aACE;QACF,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aACE;QACF,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;CACD;AAEc,SAAS;IACtB,qBACE,wcAAC;QAAI,WAAU;;0BAEb,wcAAC;gBAAQ,WAAU;0BACjB,cAAA,wcAAC;oBAAI,WAAU;8BACb,cAAA,wcAAC;wBAAI,WAAU;;0CACb,wcAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,wcAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;0BAS7D,wcAAC;gBAAQ,WAAU;0BACjB,cAAA,wcAAC;oBAAI,WAAU;8BACb,cAAA,wcAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,wcAAC;gCAEC,WAAU;;kDAEV,wcAAC;wCAAI,WAAU;kDAAiB,QAAQ,IAAI;;;;;;kDAC5C,wcAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK;;;;;;kDAEhB,wcAAC;wCAAE,WAAU;kDAAsB,QAAQ,WAAW;;;;;;kDAEtD,wcAAC;wCAAG,WAAU;kDACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,wcAAC;gDAEC,WAAU;;kEAEV,wcAAC;wDAAK,WAAU;kEAAqB;;;;;;oDACpC;;+CAJI;;;;;;;;;;kDASX,wcAAC,oZAAI;wCAAC,MAAK;wCAAW,WAAU;kDAC9B,cAAA,wcAAC,qMAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAS;;;;;;;;;;;;+BAtB1C;;;;;;;;;;;;;;;;;;;;0BAiCf,wcAAC;gBAAQ,WAAU;0BACjB,cAAA,wcAAC;oBAAI,WAAU;;sCACb,wcAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,wcAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,wcAAC;4BAAI,WAAU;;8CACb,wcAAC,oZAAI;oCAAC,MAAK;8CACT,cAAA,wcAAC,qMAAM;wCAAC,SAAQ;wCAAY,MAAK;kDAAK;;;;;;;;;;;8CAIxC,wcAAC,oZAAI;oCAAC,MAAK;8CACT,cAAA,wcAAC,qMAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}