module.exports = [
"[project]/Documents/augment-projects/flywheel-media/src/app/favicon.ico (static in ecmascript, tag client)", ((__turbopack_context__) => {

__turbopack_context__.v("/_next/static/media/favicon.0b3bf435.ico");}),
"[project]/Documents/augment-projects/flywheel-media/src/app/favicon.ico.mjs { IMAGE => \"[project]/Documents/augment-projects/flywheel-media/src/app/favicon.ico (static in ecmascript, tag client)\" } [app-rsc] (structured image object, ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$augment$2d$projects$2f$flywheel$2d$media$2f$src$2f$app$2f$favicon$2e$ico__$28$static__in__ecmascript$2c$__tag__client$29$__ = __turbopack_context__.i("[project]/Documents/augment-projects/flywheel-media/src/app/favicon.ico (static in ecmascript, tag client)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$Documents$2f$augment$2d$projects$2f$flywheel$2d$media$2f$src$2f$app$2f$favicon$2e$ico__$28$static__in__ecmascript$2c$__tag__client$29$__["default"],
    width: 256,
    height: 256
};
}),
];

//# sourceMappingURL=Documents_augment-projects_flywheel-media_src_app_6768793f._.js.map