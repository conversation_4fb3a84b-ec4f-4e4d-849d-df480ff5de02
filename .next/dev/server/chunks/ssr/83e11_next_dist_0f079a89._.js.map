{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,oQACRC,QAAQ,CAAC,YAAY,CAAEC,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/router-reducer/router-reducer-types.ts"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-types'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../../shared/lib/app-router-types'\nimport type { FetchServerResponseResult } from './fetch-server-response'\n\nexport const ACTION_REFRESH = 'refresh'\nexport const ACTION_NAVIGATE = 'navigate'\nexport const ACTION_RESTORE = 'restore'\nexport const ACTION_SERVER_PATCH = 'server-patch'\nexport const ACTION_HMR_REFRESH = 'hmr-refresh'\nexport const ACTION_SERVER_ACTION = 'server-action'\n\nexport type RouterChangeByServerResponse = ({\n  navigatedAt,\n  previousTree,\n  serverResponse,\n}: {\n  navigatedAt: number\n  previousTree: FlightRouterState\n  serverResponse: FetchServerResponseResult\n}) => void\n\nexport interface Mutable {\n  mpaNavigation?: boolean\n  patchedTree?: FlightRouterState\n  renderedSearch?: string\n  canonicalUrl?: string\n  scrollableSegments?: FlightSegmentPath[]\n  pendingPush?: boolean\n  cache?: CacheNode\n  hashFragment?: string\n  shouldScroll?: boolean\n  preserveCustomHistoryState?: boolean\n  onlyHashChange?: boolean\n  collectedDebugInfo?: Array<unknown>\n}\n\nexport interface ServerActionMutable extends Mutable {\n  inFlightServerAction?: Promise<any> | null\n}\n\n/**\n * Refresh triggers a refresh of the full page data.\n * - fetches the Flight data and fills rsc at the root of the cache.\n * - The router state is updated at the root.\n */\nexport interface RefreshAction {\n  type: typeof ACTION_REFRESH\n  origin: Location['origin']\n}\n\nexport interface HmrRefreshAction {\n  type: typeof ACTION_HMR_REFRESH\n  origin: Location['origin']\n}\n\nexport type ServerActionDispatcher = (\n  args: Omit<\n    ServerActionAction,\n    'type' | 'mutable' | 'navigate' | 'changeByServerResponse' | 'cache'\n  >\n) => void\n\nexport interface ServerActionAction {\n  type: typeof ACTION_SERVER_ACTION\n  actionId: string\n  actionArgs: any[]\n  resolve: (value: any) => void\n  reject: (reason?: any) => void\n  didRevalidate?: boolean\n}\n\n/**\n * Navigate triggers a navigation to the provided url. It supports two types: `push` and `replace`.\n *\n * `navigateType`:\n * - `push` - pushes a new history entry in the browser history\n * - `replace` - replaces the current history entry in the browser history\n *\n * Navigate has multiple cache heuristics:\n * - page was prefetched\n *  - Apply router state tree from prefetch\n *  - Apply Flight data from prefetch to the cache\n *  - If Flight data is a string, it's a redirect and the state is updated to trigger a redirect\n *  - Check if hard navigation is needed\n *    - Hard navigation happens when a dynamic parameter below the common layout changed\n *    - When hard navigation is needed the cache is invalidated below the flightSegmentPath\n *    - The missing cache nodes of the page will be fetched in layout-router and trigger the SERVER_PATCH action\n *  - If hard navigation is not needed\n *    - The cache is reused\n *    - If any cache nodes are missing they'll be fetched in layout-router and trigger the SERVER_PATCH action\n * - page was not prefetched\n *  - The navigate was called from `next/router` (`router.push()` / `router.replace()`) / `next/link` without prefetched data available (e.g. the prefetch didn't come back from the server before clicking the link)\n *    - Flight data is fetched in the reducer (suspends the reducer)\n *    - Router state tree is created based on Flight data\n *    - Cache is filled based on the Flight data\n *\n * Above steps explain 3 cases:\n * - `soft` - Reuses the existing cache and fetches missing nodes in layout-router.\n * - `hard` - Creates a new cache where cache nodes are removed below the common layout and fetches missing nodes in layout-router.\n * - `optimistic` (explicit no prefetch) - Creates a new cache and kicks off the data fetch in the reducer. The data fetch is awaited in the layout-router.\n */\nexport interface NavigateAction {\n  type: typeof ACTION_NAVIGATE\n  url: URL\n  isExternalUrl: boolean\n  locationSearch: Location['search']\n  navigateType: 'push' | 'replace'\n  shouldScroll: boolean\n}\n\n/**\n * Restore applies the provided router state.\n * - Used for `popstate` (back/forward navigation) where a known router state has to be applied.\n * - Also used when syncing the router state with `pushState`/`replaceState` calls.\n * - Router state is applied as-is from the history state, if available.\n * - If the history state does not contain the router state, the existing router state is used.\n * - If any cache node is missing it will be fetched in layout-router during rendering and the server-patch case.\n * - If existing cache nodes match these are used.\n */\nexport interface RestoreAction {\n  type: typeof ACTION_RESTORE\n  url: URL\n  historyState: AppHistoryState | undefined\n}\n\nexport type AppHistoryState = {\n  tree: FlightRouterState\n  renderedSearch: string\n}\n\n/**\n * Server-patch applies the provided Flight data to the cache and router tree.\n * - Only triggered in layout-router.\n * - Creates a new cache and router state with the Flight data applied.\n */\nexport interface ServerPatchAction {\n  type: typeof ACTION_SERVER_PATCH\n  navigatedAt: number\n  serverResponse: FetchServerResponseResult\n  previousTree: FlightRouterState\n}\n\n/**\n * PrefetchKind defines the type of prefetching that should be done.\n * - `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully.\n * - `full` - prefetch the page data fully.\n * - `temporary` - a temporary prefetch entry is added to the cache, this is used when prefetch={false} is used in next/link or when you push a route programmatically.\n */\n\nexport enum PrefetchKind {\n  AUTO = 'auto',\n  FULL = 'full',\n  TEMPORARY = 'temporary',\n}\n\n/**\n * Prefetch adds the provided FlightData to the prefetch cache\n * - Creates the router state tree based on the patch in FlightData\n * - Adds the FlightData to the prefetch cache\n * - In ACTION_NAVIGATE the prefetch cache is checked and the router state tree and FlightData are applied.\n */\n\nexport interface PushRef {\n  /**\n   * If the app-router should push a new history entry in app-router's useEffect()\n   */\n  pendingPush: boolean\n  /**\n   * Multi-page navigation through location.href.\n   */\n  mpaNavigation: boolean\n  /**\n   * Skip applying the router state to the browser history state.\n   */\n  preserveCustomHistoryState: boolean\n}\n\nexport type FocusAndScrollRef = {\n  /**\n   * If focus and scroll should be set in the layout-router's useEffect()\n   */\n  apply: boolean\n  /**\n   * The hash fragment that should be scrolled to.\n   */\n  hashFragment: string | null\n  /**\n   * The paths of the segments that should be focused.\n   */\n  segmentPaths: FlightSegmentPath[]\n  /**\n   * If only the URLs hash fragment changed\n   */\n  onlyHashChange: boolean\n}\n\n/**\n * Handles keeping the state of app-router.\n */\nexport type AppRouterState = {\n  /**\n   * The router state, this is written into the history state in app-router using replaceState/pushState.\n   * - Has to be serializable as it is written into the history state.\n   * - Holds which segments and parallel routes are shown on the screen.\n   */\n  tree: FlightRouterState\n  /**\n   * The cache holds React nodes for every segment that is shown on screen as well as previously shown segments.\n   * It also holds in-progress data requests.\n   */\n  cache: CacheNode\n  /**\n   * Decides if the update should create a new history entry and if the navigation has to trigger a browser navigation.\n   */\n  pushRef: PushRef\n  /**\n   * Decides if the update should apply scroll and focus management.\n   */\n  focusAndScrollRef: FocusAndScrollRef\n  /**\n   * The canonical url that is pushed/replaced.\n   * - This is the url you see in the browser.\n   */\n  canonicalUrl: string\n  renderedSearch: string\n  /**\n   * The underlying \"url\" representing the UI state, which is used for intercepting routes.\n   */\n  nextUrl: string | null\n\n  /**\n   * The previous next-url that was used previous to a dynamic navigation.\n   */\n  previousNextUrl: string | null\n\n  debugInfo: Array<unknown> | null\n}\n\nexport type ReadonlyReducerState = Readonly<AppRouterState>\nexport type ReducerState =\n  | (Promise<AppRouterState> & { _debugInfo?: Array<unknown> })\n  | AppRouterState\nexport type ReducerActions = Readonly<\n  | RefreshAction\n  | NavigateAction\n  | RestoreAction\n  | ServerPatchAction\n  | HmrRefreshAction\n  | ServerActionAction\n>\n"], "names": ["ACTION_REFRESH", "ACTION_NAVIGATE", "ACTION_RESTORE", "ACTION_SERVER_PATCH", "ACTION_HMR_REFRESH", "ACTION_SERVER_ACTION", "PrefetchKind"], "mappings": ";;;;;;;;;;;;;;;;AAOO,MAAMA,iBAAiB,UAAS;AAChC,MAAMC,kBAAkB,WAAU;AAClC,MAAMC,iBAAiB,UAAS;AAChC,MAAMC,sBAAsB,eAAc;AAC1C,MAAMC,qBAAqB,cAAa;AACxC,MAAMC,uBAAuB,gBAAe;AA4I5C,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;;WAAAA;MAIX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,oQACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactDOM\n"], "names": ["module", "exports", "require", "vendored", "ReactDOM"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,oQACRC,QAAQ,CAAC,YAAY,CAAEC,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n"], "names": ["module", "exports", "require", "vendored", "AppRouterContext"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,oQACRC,QAAQ,CAAC,WAAW,CAACC,gBAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactServerDOMTurbopackClient\n"], "names": ["module", "exports", "require", "vendored", "ReactServerDOMTurbopackClient"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,oQACRC,QAAQ,CAAC,YAAY,CAAEC,6BAA6B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/app-router-headers.ts"], "sourcesContent": ["export const RSC_HEADER = 'rsc' as const\nexport const ACTION_HEADER = 'next-action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'next-router-state-tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'next-router-prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change next-router-state-tree to be a segment path, we can use\n// that instead. Then next-router-prefetch and next-router-segment-prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'next-router-segment-prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'next-hmr-refresh' as const\nexport const NEXT_HMR_REFRESH_HASH_COOKIE = '__next_hmr_refresh_hash__' as const\nexport const NEXT_URL = 'next-url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\nexport const NEXT_ACTION_NOT_FOUND_HEADER = 'x-nextjs-action-not-found' as const\nexport const NEXT_REQUEST_ID_HEADER = 'x-nextjs-request-id' as const\nexport const NEXT_HTML_REQUEST_ID_HEADER = 'x-nextjs-html-request-id' as const\n"], "names": ["RSC_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_HMR_REFRESH_HASH_COOKIE", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "FLIGHT_HEADERS", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_ACTION_NOT_FOUND_HEADER", "NEXT_REQUEST_ID_HEADER", "NEXT_HTML_REQUEST_ID_HEADER"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAMA,aAAa,MAAc;AACjC,MAAMC,gBAAgB,cAAsB;AAI5C,MAAMC,gCAAgC,yBAAiC;AACvE,MAAMC,8BAA8B,uBAA+B;AAKnE,MAAMC,sCACX,+BAAuC;AAClC,MAAMC,0BAA0B,mBAA2B;AAC3D,MAAMC,+BAA+B,4BAAoC;AACzE,MAAMC,WAAW,WAAmB;AACpC,MAAMC,0BAA0B,mBAA2B;AAE3D,MAAMC,iBAAiB;IAC5BT;IACAE;IACAC;IACAE;IACAD;CACD,CAAS;AAEH,MAAMM,uBAAuB,OAAe;AAE5C,MAAMC,gCAAgC,sBAA8B;AACpE,MAAMC,2BAA2B,qBAA6B;AAC9D,MAAMC,6BAA6B,0BAAkC;AACrE,MAAMC,8BAA8B,2BAAmC;AACvE,MAAMC,2BAA2B,qBAA6B;AAC9D,MAAMC,+BAA+B,4BAAoC;AACzE,MAAMC,yBAAyB,sBAA8B;AAC7D,MAAMC,8BAA8B,2BAAmC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/is-thenable.ts"], "sourcesContent": ["/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n"], "names": ["isThenable", "promise", "then"], "mappings": "AAAA;;;;;CAKC,GACD;;;;AAAO,SAASA,WACdC,OAAuB;IAEvB,OACEA,YAAY,QACZ,OAAOA,YAAY,YACnB,UAAUA,WACV,OAAOA,QAAQC,IAAI,KAAK;AAE5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/next-devtools/dev-overlay.shim.ts"], "sourcesContent": ["export function renderAppDevOverlay() {\n  throw new Error(\n    \"Next DevTools: Can't render in this environment. This is a bug in Next.js\"\n  )\n}\n\nexport function renderPagesDevOverlay() {\n  throw new Error(\n    \"Next DevTools: Can't render in this environment. This is a bug in Next.js\"\n  )\n}\n\n// TODO: Extract into separate functions that are imported\nexport const dispatcher = new Proxy(\n  {},\n  {\n    get: (_, prop) => {\n      return () => {\n        throw new Error(\n          `Next DevTools: Can't dispatch ${String(prop)} in this environment. This is a bug in Next.js`\n        )\n      }\n    },\n  }\n)\n"], "names": ["dispatcher", "renderAppDevOverlay", "renderPagesDevOverlay", "Error", "Proxy", "get", "_", "prop", "String"], "mappings": ";;;;;;;;;;;;;;;IAaaA,UAAU,EAAA;eAAVA;;IAbGC,mBAAmB,EAAA;eAAnBA;;IAMAC,qBAAqB,EAAA;eAArBA;;;AANT,SAASD;IACd,MAAM,OAAA,cAEL,CAFK,IAAIE,MACR,8EADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASD;IACd,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,8EADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAGO,MAAMH,aAAa,IAAII,MAC5B,CAAC,GACD;IACEC,KAAK,CAACC,GAAGC;QACP,OAAO;YACL,MAAM,OAAA,cAEL,CAFK,IAAIJ,MACR,CAAC,8BAA8B,EAAEK,OAAOD,MAAM,8CAA8C,CAAC,GADzF,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/next-devtools/userspace/use-app-dev-rendering-indicator.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useTransition } from 'react'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\n\nexport const useAppDevRenderingIndicator = () => {\n  const [isPending, startTransition] = useTransition()\n\n  useEffect(() => {\n    if (isPending) {\n      dispatcher.renderingIndicatorShow()\n    } else {\n      dispatcher.renderingIndicatorHide()\n    }\n  }, [isPending])\n\n  return startTransition\n}\n"], "names": ["useEffect", "useTransition", "dispatcher", "useAppDevRenderingIndicator", "isPending", "startTransition", "renderingIndicatorShow", "renderingIndicatorHide"], "mappings": ";;;;AAEA,SAASA,SAAS,EAAEC,aAAa,QAAQ,QAAO;AAChD,SAASC,UAAU,QAAQ,mCAAkC;AAH7D;;;AAKO,MAAMC,8BAA8B;IACzC,MAAM,CAACC,WAAWC,gBAAgB,OAAGJ,gbAAAA;QAErCD,4aAAAA,EAAU;QACR,IAAII,WAAW;YACbF,kZAAAA,CAAWI,sBAAsB;QACnC,OAAO;YACLJ,kZAAAA,CAAWK,sBAAsB;QACnC;IACF,GAAG;QAACH;KAAU;IAEd,OAAOC;AACT,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/use-action-queue.ts"], "sourcesContent": ["import type { Dispatch } from 'react'\nimport React, { use, useMemo } from 'react'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport type { AppRouterActionQueue } from './app-router-instance'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  ReducerState,\n} from './router-reducer/router-reducer-types'\n\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch: Dispatch<ReducerActions> | null = null\n\nexport function dispatchAppRouterAction(action: ReducerActions) {\n  if (dispatch === null) {\n    throw new Error(\n      'Internal Next.js error: Router action dispatched before initialization.'\n    )\n  }\n  dispatch(action)\n}\n\nconst __DEV__ = process.env.NODE_ENV !== 'production'\nconst promisesWithDebugInfo: WeakMap<\n  Promise<AppRouterState>,\n  Promise<AppRouterState> & { _debugInfo?: Array<unknown> }\n> = __DEV__ ? new WeakMap() : (null as any)\n\nexport function useActionQueue(\n  actionQueue: AppRouterActionQueue\n): AppRouterState {\n  const [state, setState] = React.useState<ReducerState>(actionQueue.state)\n\n  // Because of a known issue that requires to decode Flight streams inside the\n  // render phase, we have to be a bit clever and assign the dispatch method to\n  // a module-level variable upon initialization. The useState hook in this\n  // module only exists to synchronize state that lives outside of React.\n  // Ideally, what we'd do instead is pass the state as a prop to root.render;\n  // this is conceptually how we're modeling the app router state, despite the\n  // weird implementation details.\n  if (process.env.NODE_ENV !== 'production') {\n    const { useAppDevRenderingIndicator } =\n      require('../../next-devtools/userspace/use-app-dev-rendering-indicator') as typeof import('../../next-devtools/userspace/use-app-dev-rendering-indicator')\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const appDevRenderingIndicator = useAppDevRenderingIndicator()\n\n    dispatch = (action: ReducerActions) => {\n      appDevRenderingIndicator(() => {\n        actionQueue.dispatch(action, setState)\n      })\n    }\n  } else {\n    dispatch = (action: ReducerActions) =>\n      actionQueue.dispatch(action, setState)\n  }\n\n  // When navigating to a non-prefetched route, then App Router state will be\n  // blocked until the server responds. We need to transfer the `_debugInfo`\n  // from the underlying Flight response onto the top-level promise that is\n  // passed to React (via `use`) so that the latency is accurately represented\n  // in the React DevTools.\n  const stateWithDebugInfo = useMemo(() => {\n    if (!__DEV__) {\n      return state\n    }\n\n    if (isThenable(state)) {\n      // useMemo can't be used to cache a Promise since the memoized value is thrown\n      // away when we suspend. So we use a WeakMap to cache the Promise with debug info.\n      let promiseWithDebugInfo = promisesWithDebugInfo.get(state)\n      if (promiseWithDebugInfo === undefined) {\n        const debugInfo: Array<unknown> = []\n        promiseWithDebugInfo = Promise.resolve(state).then((asyncState) => {\n          if (asyncState.debugInfo !== null) {\n            debugInfo.push(...asyncState.debugInfo)\n          }\n          return asyncState\n        }) as Promise<AppRouterState> & { _debugInfo?: Array<unknown> }\n        promiseWithDebugInfo._debugInfo = debugInfo\n\n        promisesWithDebugInfo.set(state, promiseWithDebugInfo)\n      }\n\n      return promiseWithDebugInfo\n    }\n    return state\n  }, [state])\n\n  return isThenable(stateWithDebugInfo)\n    ? use(stateWithDebugInfo)\n    : stateWithDebugInfo\n}\n"], "names": ["React", "use", "useMemo", "isThenable", "dispatch", "dispatchAppRouterAction", "action", "Error", "__DEV__", "process", "env", "NODE_ENV", "promisesWithDebugInfo", "WeakMap", "useActionQueue", "actionQueue", "state", "setState", "useState", "useAppDevRenderingIndicator", "require", "appDevRenderingIndicator", "stateWithDebugInfo", "promiseWithDebugInfo", "get", "undefined", "debugInfo", "Promise", "resolve", "then", "asyncState", "push", "_debugInfo", "set"], "mappings": ";;;;;;AACA,OAAOA,SAASC,GAAG,EAAEC,OAAO,QAAQ,QAAO;AAC3C,SAASC,UAAU,QAAQ,+BAA8B;;;AAQzD,6EAA6E;AAC7E,+EAA+E;AAC/E,cAAc;AACd,IAAIC,WAA4C;AAEzC,SAASC,wBAAwBC,MAAsB;IAC5D,IAAIF,aAAa,MAAM;QACrB,MAAM,OAAA,cAEL,CAFK,IAAIG,MACR,4EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACAH,SAASE;AACX;AAEA,MAAME,UAAUC,QAAQC,GAAG,CAACC,QAAQ,gCAAK;AACzC,MAAMC,wBAGFJ,uCAAU,IAAIK,YAAa;AAExB,SAASC,eACdC,WAAiC;IAEjC,MAAM,CAACC,OAAOC,SAAS,GAAGjB,0aAAAA,CAAMkB,QAAQ,CAAeH,YAAYC,KAAK;IAExE,6EAA6E;IAC7E,6EAA6E;IAC7E,yEAAyE;IACzE,uEAAuE;IACvE,4EAA4E;IAC5E,4EAA4E;IAC5E,gCAAgC;IAChC,IAAIP,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,MAAM,EAAEQ,2BAA2B,EAAE,GACnCC,QAAQ;QACV,sDAAsD;QACtD,MAAMC,2BAA2BF;QAEjCf,WAAW,CAACE;YACVe,yBAAyB;gBACvBN,YAAYX,QAAQ,CAACE,QAAQW;YAC/B;QACF;IACF,OAAO;;IAKP,2EAA2E;IAC3E,0EAA0E;IAC1E,yEAAyE;IACzE,4EAA4E;IAC5E,yBAAyB;IACzB,MAAMK,yBAAqBpB,0aAAAA,EAAQ;QACjC,IAAI,CAACM,SAAS;;QAId,QAAIL,8YAAAA,EAAWa,QAAQ;YACrB,8EAA8E;YAC9E,kFAAkF;YAClF,IAAIO,uBAAuBX,sBAAsBY,GAAG,CAACR;YACrD,IAAIO,yBAAyBE,WAAW;gBACtC,MAAMC,YAA4B,EAAE;gBACpCH,uBAAuBI,QAAQC,OAAO,CAACZ,OAAOa,IAAI,CAAC,CAACC;oBAClD,IAAIA,WAAWJ,SAAS,KAAK,MAAM;wBACjCA,UAAUK,IAAI,IAAID,WAAWJ,SAAS;oBACxC;oBACA,OAAOI;gBACT;gBACAP,qBAAqBS,UAAU,GAAGN;gBAElCd,sBAAsBqB,GAAG,CAACjB,OAAOO;YACnC;YAEA,OAAOA;QACT;QACA,OAAOP;IACT,GAAG;QAACA;KAAM;IAEV,WAAOb,8YAAAA,EAAWmB,0BACdrB,saAAAA,EAAIqB,sBACJA;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/app-call-server.ts"], "sourcesContent": ["import { startTransition } from 'react'\nimport { ACTION_SERVER_ACTION } from './components/router-reducer/router-reducer-types'\nimport { dispatchAppRouterAction } from './components/use-action-queue'\n\nexport async function callServer(actionId: string, actionArgs: any[]) {\n  return new Promise((resolve, reject) => {\n    startTransition(() => {\n      dispatchAppRouterAction({\n        type: ACTION_SERVER_ACTION,\n        actionId,\n        actionArgs,\n        resolve,\n        reject,\n      })\n    })\n  })\n}\n"], "names": ["startTransition", "ACTION_SERVER_ACTION", "dispatchAppRouterAction", "callServer", "actionId", "actionArgs", "Promise", "resolve", "reject", "type"], "mappings": ";;;;AAAA,SAASA,eAAe,QAAQ,QAAO;AACvC,SAASC,oBAAoB,QAAQ,mDAAkD;AACvF,SAASC,uBAAuB,QAAQ,gCAA+B;;;;AAEhE,eAAeC,WAAWC,QAAgB,EAAEC,UAAiB;IAClE,OAAO,IAAIC,QAAQ,CAACC,SAASC;YAC3BR,kbAAAA,EAAgB;gBACdE,0aAAAA,EAAwB;gBACtBO,MAAMR,gcAAAA;gBACNG;gBACAC;gBACAE;gBACAC;YACF;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/app-find-source-map-url.ts"], "sourcesContent": ["const basePath = process.env.__NEXT_ROUTER_BASEPATH || ''\nconst pathname = `${basePath}/__nextjs_source-map`\n\nexport const findSourceMapURL =\n  process.env.NODE_ENV === 'development'\n    ? function findSourceMapURL(filename: string): string | null {\n        if (filename === '') {\n          return null\n        }\n\n        if (\n          filename.startsWith(document.location.origin) &&\n          filename.includes('/_next/static')\n        ) {\n          // This is a request for a client chunk. This can only happen when\n          // using Turbopack. In this case, since we control how those source\n          // maps are generated, we can safely assume that the sourceMappingURL\n          // is relative to the filename, with an added `.map` extension. The\n          // browser can just request this file, and it gets served through the\n          // normal dev server, without the need to route this through\n          // the `/__nextjs_source-map` dev middleware.\n          return `${filename}.map`\n        }\n\n        const url = new URL(pathname, document.location.origin)\n        url.searchParams.set('filename', filename)\n\n        return url.href\n      }\n    : undefined\n"], "names": ["basePath", "process", "env", "__NEXT_ROUTER_BASEPATH", "pathname", "findSourceMapURL", "NODE_ENV", "filename", "startsWith", "document", "location", "origin", "includes", "url", "URL", "searchParams", "set", "href", "undefined"], "mappings": ";;;;AAAA,MAAMA,WAAWC,QAAQC,GAAG,CAACC,sBAAsB,MAAI;AACvD,MAAMC,WAAW,GAAGJ,SAAS,oBAAoB,CAAC;AAE3C,MAAMK,mBACXJ,QAAQC,GAAG,CAACI,QAAQ,KAAK,cACrB,SAASD,iBAAiBE,QAAgB;IACxC,IAAIA,aAAa,IAAI;QACnB,OAAO;IACT;IAEA,IACEA,SAASC,UAAU,CAACC,SAASC,QAAQ,CAACC,MAAM,KAC5CJ,SAASK,QAAQ,CAAC,kBAClB;QACA,kEAAkE;QAClE,mEAAmE;QACnE,qEAAqE;QACrE,mEAAmE;QACnE,qEAAqE;QACrE,4DAA4D;QAC5D,6CAA6C;QAC7C,OAAO,GAAGL,SAAS,IAAI,CAAC;IAC1B;IAEA,MAAMM,MAAM,IAAIC,IAAIV,UAAUK,SAASC,QAAQ,CAACC,MAAM;IACtDE,IAAIE,YAAY,CAACC,GAAG,CAAC,YAAYT;IAEjC,OAAOM,IAAII,IAAI;AACjB,IACAC,UAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/segment.ts"], "sourcesContent": ["import type { FlightRouterState, Segment } from './app-router-types'\n\nexport function getSegmentValue(segment: Segment) {\n  return Array.isArray(segment) ? segment[1] : segment\n}\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport function computeSelectedLayoutSegment(\n  segments: string[] | null,\n  parallelRouteKey: string\n): string | null {\n  if (!segments || segments.length === 0) {\n    return null\n  }\n\n  // For 'children', use first segment; for other parallel routes, use last segment\n  const rawSegment =\n    parallelRouteKey === 'children'\n      ? segments[0]\n      : segments[segments.length - 1]\n\n  // If the default slot is showing, return null since it's not technically \"selected\" (it's a fallback)\n  // Returning an internal value like `__DEFAULT__` would be confusing\n  return rawSegment === DEFAULT_SEGMENT_KEY ? null : rawSegment\n}\n\n/** Get the canonical parameters from the current level to the leaf node. */\nexport function getSelectedLayoutSegmentPath(\n  tree: FlightRouterState,\n  parallelRouteKey: string,\n  first = true,\n  segmentPath: string[] = []\n): string[] {\n  let node: FlightRouterState\n  if (first) {\n    // Use the provided parallel route key on the first parallel route\n    node = tree[1][parallelRouteKey]\n  } else {\n    // After first parallel route prefer children, if there's no children pick the first parallel route.\n    const parallelRoutes = tree[1]\n    node = parallelRoutes.children ?? Object.values(parallelRoutes)[0]\n  }\n\n  if (!node) return segmentPath\n  const segment = node[0]\n\n  let segmentValue = getSegmentValue(segment)\n\n  if (!segmentValue || segmentValue.startsWith(PAGE_SEGMENT_KEY)) {\n    return segmentPath\n  }\n\n  segmentPath.push(segmentValue)\n\n  return getSelectedLayoutSegmentPath(\n    node,\n    parallelRouteKey,\n    false,\n    segmentPath\n  )\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n"], "names": ["getSegmentValue", "segment", "Array", "isArray", "isGroupSegment", "endsWith", "isParallelRouteSegment", "startsWith", "addSearchParamsIfPageSegment", "searchParams", "isPageSegment", "includes", "PAGE_SEGMENT_KEY", "stringified<PERSON><PERSON>y", "JSON", "stringify", "computeSelectedLayoutSegment", "segments", "parallelRouteKey", "length", "rawSegment", "DEFAULT_SEGMENT_KEY", "getSelectedLayoutSegmentPath", "tree", "first", "segmentPath", "node", "parallelRoutes", "children", "Object", "values", "segmentValue", "push"], "mappings": ";;;;;;;;;;;;;;;;;;AAEO,SAASA,gBAAgBC,OAAgB;IAC9C,OAAOC,MAAMC,OAAO,CAACF,WAAWA,OAAO,CAAC,EAAE,GAAGA;AAC/C;AAEO,SAASG,eAAeH,OAAe;IAC5C,sCAAsC;IACtC,OAAOA,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQI,QAAQ,CAAC;AAChD;AAEO,SAASC,uBAAuBL,OAAe;IACpD,OAAOA,QAAQM,UAAU,CAAC,QAAQN,YAAY;AAChD;AAEO,SAASO,6BACdP,OAAgB,EAChBQ,YAA2D;IAE3D,MAAMC,gBAAgBT,QAAQU,QAAQ,CAACC;IAEvC,IAAIF,eAAe;QACjB,MAAMG,mBAAmBC,KAAKC,SAAS,CAACN;QACxC,OAAOI,qBAAqB,OACxBD,mBAAmB,MAAMC,mBACzBD;IACN;IAEA,OAAOX;AACT;AAEO,SAASe,6BACdC,QAAyB,EACzBC,gBAAwB;IAExB,IAAI,CAACD,YAAYA,SAASE,MAAM,KAAK,GAAG;QACtC,OAAO;IACT;IAEA,iFAAiF;IACjF,MAAMC,aACJF,qBAAqB,aACjBD,QAAQ,CAAC,EAAE,GACXA,QAAQ,CAACA,SAASE,MAAM,GAAG,EAAE;IAEnC,sGAAsG;IACtG,oEAAoE;IACpE,OAAOC,eAAeC,sBAAsB,OAAOD;AACrD;AAGO,SAASE,6BACdC,IAAuB,EACvBL,gBAAwB,EACxBM,QAAQ,IAAI,EACZC,cAAwB,EAAE;IAE1B,IAAIC;IACJ,IAAIF,OAAO;QACT,kEAAkE;QAClEE,OAAOH,IAAI,CAAC,EAAE,CAACL,iBAAiB;IAClC,OAAO;QACL,oGAAoG;QACpG,MAAMS,iBAAiBJ,IAAI,CAAC,EAAE;QAC9BG,OAAOC,eAAeC,QAAQ,IAAIC,OAAOC,MAAM,CAACH,eAAe,CAAC,EAAE;IACpE;IAEA,IAAI,CAACD,MAAM,OAAOD;IAClB,MAAMxB,UAAUyB,IAAI,CAAC,EAAE;IAEvB,IAAIK,eAAe/B,gBAAgBC;IAEnC,IAAI,CAAC8B,gBAAgBA,aAAaxB,UAAU,CAACK,mBAAmB;QAC9D,OAAOa;IACT;IAEAA,YAAYO,IAAI,CAACD;IAEjB,OAAOT,6BACLI,MACAR,kBACA,OACAO;AAEJ;AAEO,MAAMb,mBAAmB,WAAU;AACnC,MAAMS,sBAAsB,cAAa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 459, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/segment-cache/segment-value-encoding.ts"], "sourcesContent": ["import { PAGE_SEGMENT_KEY } from '../segment'\nimport type { Segment as FlightRouterStateSegment } from '../app-router-types'\n\n// TypeScript trick to simulate opaque types, like in Flow.\ntype Opaque<K, T> = T & { __brand: K }\n\nexport type SegmentRequestKeyPart = Opaque<'SegmentRequestKeyPart', string>\nexport type SegmentRequestKey = Opaque<'SegmentRequestKey', string>\nexport type SegmentCacheKeyPart = Opaque<'SegmentCacheKeyPart', string>\nexport type SegmentCacheKey = Opaque<'SegmentCacheKey', string>\n\nexport const ROOT_SEGMENT_REQUEST_KEY = '' as SegmentRequestKey\nexport const ROOT_SEGMENT_CACHE_KEY = '' as SegmentCacheKey\n\nexport function createSegmentRequestKeyPart(\n  segment: FlightRouterStateSegment\n): SegmentRequestKeyPart {\n  if (typeof segment === 'string') {\n    if (segment.startsWith(PAGE_SEGMENT_KEY)) {\n      // The Flight Router State type sometimes includes the search params in\n      // the page segment. However, the Segment Cache tracks this as a separate\n      // key. So, we strip the search params here, and then add them back when\n      // the cache entry is turned back into a FlightRouterState. This is an\n      // unfortunate consequence of the FlightRouteState being used both as a\n      // transport type and as a cache key; we'll address this once more of the\n      // Segment Cache implementation has settled.\n      // TODO: We should hoist the search params out of the FlightRouterState\n      // type entirely, This is our plan for dynamic route params, too.\n      return PAGE_SEGMENT_KEY as SegmentRequestKeyPart\n    }\n    const safeName =\n      // TODO: FlightRouterState encodes Not Found routes as \"/_not-found\".\n      // But params typically don't include the leading slash. We should use\n      // a different encoding to avoid this special case.\n      segment === '/_not-found'\n        ? '_not-found'\n        : encodeToFilesystemAndURLSafeString(segment)\n    // Since this is not a dynamic segment, it's fully encoded. It does not\n    // need to be \"hydrated\" with a param value.\n    return safeName as SegmentRequestKeyPart\n  }\n\n  const name = segment[0]\n  const paramType = segment[2]\n  const safeName = encodeToFilesystemAndURLSafeString(name)\n\n  const encodedName = '$' + paramType + '$' + safeName\n  return encodedName as SegmentRequestKeyPart\n}\n\nexport function appendSegmentRequestKeyPart(\n  parentRequestKey: SegmentRequestKey,\n  parallelRouteKey: string,\n  childRequestKeyPart: SegmentRequestKeyPart\n): SegmentRequestKey {\n  // Aside from being filesystem safe, segment keys are also designed so that\n  // each segment and parallel route creates its own subdirectory. Roughly in\n  // the same shape as the source app directory. This is mostly just for easier\n  // debugging (you can open up the build folder and navigate the output); if\n  // we wanted to do we could just use a flat structure.\n\n  // Omit the parallel route key for children, since this is the most\n  // common case. Saves some bytes (and it's what the app directory does).\n  const slotKey =\n    parallelRouteKey === 'children'\n      ? childRequestKeyPart\n      : `@${encodeToFilesystemAndURLSafeString(parallelRouteKey)}/${childRequestKeyPart}`\n  return (parentRequestKey + '/' + slotKey) as SegmentRequestKey\n}\n\nexport function createSegmentCacheKeyPart(\n  requestKeyPart: SegmentRequestKeyPart,\n  segment: FlightRouterStateSegment\n): SegmentCacheKeyPart {\n  if (typeof segment === 'string') {\n    return requestKeyPart as any as SegmentCacheKeyPart\n  }\n  const paramValue = segment[1]\n  const safeValue = encodeToFilesystemAndURLSafeString(paramValue)\n  return (requestKeyPart + '$' + safeValue) as SegmentCacheKeyPart\n}\n\nexport function appendSegmentCacheKeyPart(\n  parentSegmentKey: SegmentCacheKey,\n  parallelRouteKey: string,\n  childCacheKeyPart: SegmentCacheKeyPart\n): SegmentCacheKey {\n  const slotKey =\n    parallelRouteKey === 'children'\n      ? childCacheKeyPart\n      : `@${encodeToFilesystemAndURLSafeString(parallelRouteKey)}/${childCacheKeyPart}`\n  return (parentSegmentKey + '/' + slotKey) as SegmentCacheKey\n}\n\n// Define a regex pattern to match the most common characters found in a route\n// param. It excludes anything that might not be cross-platform filesystem\n// compatible, like |. It does not need to be precise because the fallback is to\n// just base64url-encode the whole parameter, which is fine; we just don't do it\n// by default for compactness, and for easier debugging.\nconst simpleParamValueRegex = /^[a-zA-Z0-9\\-_@]+$/\n\nfunction encodeToFilesystemAndURLSafeString(value: string) {\n  if (simpleParamValueRegex.test(value)) {\n    return value\n  }\n  // If there are any unsafe characters, base64url-encode the entire value.\n  // We also add a ! prefix so it doesn't collide with the simple case.\n  const base64url = btoa(value)\n    .replace(/\\+/g, '-') // Replace '+' with '-'\n    .replace(/\\//g, '_') // Replace '/' with '_'\n    .replace(/=+$/, '') // Remove trailing '='\n  return '!' + base64url\n}\n\nexport function convertSegmentPathToStaticExportFilename(\n  segmentPath: string\n): string {\n  return `__next${segmentPath.replace(/\\//g, '.')}.txt`\n}\n"], "names": ["PAGE_SEGMENT_KEY", "ROOT_SEGMENT_REQUEST_KEY", "ROOT_SEGMENT_CACHE_KEY", "createSegmentRequestKeyPart", "segment", "startsWith", "safeName", "encodeToFilesystemAndURLSafeString", "name", "paramType", "encodedName", "appendSegmentRequestKeyPart", "parentRequestKey", "parallelRouteKey", "childRequestKeyPart", "<PERSON><PERSON><PERSON>", "createSegmentCacheKeyPart", "requestKeyPart", "paramValue", "safeValue", "appendSegmentCacheKeyPart", "parentSegmentKey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "simpleParamValueRegex", "value", "test", "base64url", "btoa", "replace", "convertSegmentPathToStaticExportFilename", "segmentPath"], "mappings": ";;;;;;;;;;;;;;;;AAAA,SAASA,gBAAgB,QAAQ,aAAY;;AAWtC,MAAMC,2BAA2B,GAAuB;AACxD,MAAMC,yBAAyB,GAAqB;AAEpD,SAASC,4BACdC,OAAiC;IAEjC,IAAI,OAAOA,YAAY,UAAU;QAC/B,IAAIA,QAAQC,UAAU,CAACL,6YAAAA,GAAmB;YACxC,uEAAuE;YACvE,yEAAyE;YACzE,wEAAwE;YACxE,sEAAsE;YACtE,uEAAuE;YACvE,yEAAyE;YACzE,4CAA4C;YAC5C,uEAAuE;YACvE,iEAAiE;YACjE,OAAOA,6YAAAA;QACT;QACA,MAAMM,WACJ,AACA,qEADqE,CACC;QACtE,mDAAmD;QACnDF,YAAY,gBACR,eACAG,mCAAmCH;QACzC,uEAAuE;QACvE,4CAA4C;QAC5C,OAAOE;IACT;IAEA,MAAME,OAAOJ,OAAO,CAAC,EAAE;IACvB,MAAMK,YAAYL,OAAO,CAAC,EAAE;IAC5B,MAAME,WAAWC,mCAAmCC;IAEpD,MAAME,cAAc,MAAMD,YAAY,MAAMH;IAC5C,OAAOI;AACT;AAEO,SAASC,4BACdC,gBAAmC,EACnCC,gBAAwB,EACxBC,mBAA0C;IAE1C,2EAA2E;IAC3E,2EAA2E;IAC3E,6EAA6E;IAC7E,2EAA2E;IAC3E,sDAAsD;IAEtD,mEAAmE;IACnE,wEAAwE;IACxE,MAAMC,UACJF,qBAAqB,aACjBC,sBACA,CAAC,CAAC,EAAEP,mCAAmCM,kBAAkB,CAAC,EAAEC,qBAAqB;IACvF,OAAQF,mBAAmB,MAAMG;AACnC;AAEO,SAASC,0BACdC,cAAqC,EACrCb,OAAiC;IAEjC,IAAI,OAAOA,YAAY,UAAU;QAC/B,OAAOa;IACT;IACA,MAAMC,aAAad,OAAO,CAAC,EAAE;IAC7B,MAAMe,YAAYZ,mCAAmCW;IACrD,OAAQD,iBAAiB,MAAME;AACjC;AAEO,SAASC,0BACdC,gBAAiC,EACjCR,gBAAwB,EACxBS,iBAAsC;IAEtC,MAAMP,UACJF,qBAAqB,aACjBS,oBACA,CAAC,CAAC,EAAEf,mCAAmCM,kBAAkB,CAAC,EAAES,mBAAmB;IACrF,OAAQD,mBAAmB,MAAMN;AACnC;AAEA,8EAA8E;AAC9E,0EAA0E;AAC1E,gFAAgF;AAChF,gFAAgF;AAChF,wDAAwD;AACxD,MAAMQ,wBAAwB;AAE9B,SAAShB,mCAAmCiB,KAAa;IACvD,IAAID,sBAAsBE,IAAI,CAACD,QAAQ;QACrC,OAAOA;IACT;IACA,yEAAyE;IACzE,qEAAqE;IACrE,MAAME,YAAYC,KAAKH,OACpBI,OAAO,CAAC,OAAO,KAAK,uBAAuB;KAC3CA,OAAO,CAAC,OAAO,KAAK,uBAAuB;KAC3CA,OAAO,CAAC,OAAO,IAAI,sBAAsB;;IAC5C,OAAO,MAAMF;AACf;AAEO,SAASG,yCACdC,WAAmB;IAEnB,OAAO,CAAC,MAAM,EAAEA,YAAYF,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 554, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/route-params.ts"], "sourcesContent": ["import type { DynamicParamTypesShort } from '../shared/lib/app-router-types'\nimport {\n  addSearchParamsIfPageSegment,\n  DEFAULT_SEGMENT_KEY,\n  PAGE_SEGMENT_KEY,\n} from '../shared/lib/segment'\nimport { ROOT_SEGMENT_REQUEST_KEY } from '../shared/lib/segment-cache/segment-value-encoding'\nimport {\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n  NEXT_RSC_UNION_QUERY,\n} from './components/app-router-headers'\nimport type { NormalizedSearch } from './components/segment-cache'\nimport type { RSCResponse } from './components/router-reducer/fetch-server-response'\nimport type { ParsedUrlQuery } from 'querystring'\n\nexport type RouteParamValue = string | Array<string> | null\n\nexport type RouteParam = {\n  name: string\n  value: RouteParamValue\n  type: DynamicParamTypesShort\n}\n\nexport function getRenderedSearch(\n  response: RSCResponse<unknown> | Response\n): NormalizedSearch {\n  // If the server performed a rewrite, the search params used to render the\n  // page will be different from the params in the request URL. In this case,\n  // the response will include a header that gives the rewritten search query.\n  const rewrittenQuery = response.headers.get(NEXT_REWRITTEN_QUERY_HEADER)\n  if (rewrittenQuery !== null) {\n    return (\n      rewrittenQuery === '' ? '' : '?' + rewrittenQuery\n    ) as NormalizedSearch\n  }\n  // If the header is not present, there was no rewrite, so we use the search\n  // query of the response URL.\n  return urlToUrlWithoutFlightMarker(new URL(response.url))\n    .search as NormalizedSearch\n}\n\nexport function getRenderedPathname(\n  response: RSCResponse<unknown> | Response\n): string {\n  // If the server performed a rewrite, the pathname used to render the\n  // page will be different from the pathname in the request URL. In this case,\n  // the response will include a header that gives the rewritten pathname.\n  const rewrittenPath = response.headers.get(NEXT_REWRITTEN_PATH_HEADER)\n  return (\n    rewrittenPath ?? urlToUrlWithoutFlightMarker(new URL(response.url)).pathname\n  )\n}\n\nexport function parseDynamicParamFromURLPart(\n  paramType: DynamicParamTypesShort,\n  pathnameParts: Array<string>,\n  partIndex: number\n): RouteParamValue {\n  // This needs to match the behavior in get-dynamic-param.ts.\n  switch (paramType) {\n    // Catchalls\n    case 'c':\n    case 'ci': {\n      // Catchalls receive all the remaining URL parts. If there are no\n      // remaining pathname parts, return an empty array.\n      return partIndex < pathnameParts.length\n        ? pathnameParts.slice(partIndex).map((s) => encodeURIComponent(s))\n        : []\n    }\n    // Optional catchalls\n    case 'oc': {\n      // Optional catchalls receive all the remaining URL parts, unless this is\n      // the end of the pathname, in which case they return null.\n      return partIndex < pathnameParts.length\n        ? pathnameParts.slice(partIndex).map((s) => encodeURIComponent(s))\n        : null\n    }\n    // Dynamic\n    case 'd':\n    case 'di': {\n      if (partIndex >= pathnameParts.length) {\n        // The route tree expected there to be more parts in the URL than there\n        // actually are. This could happen if the x-nextjs-rewritten-path header\n        // is incorrectly set, or potentially due to bug in Next.js. TODO:\n        // Should this be a hard error? During a prefetch, we can just abort.\n        // During a client navigation, we could trigger a hard refresh. But if\n        // it happens during initial render, we don't really have any\n        // recovery options.\n        return ''\n      }\n      return encodeURIComponent(pathnameParts[partIndex])\n    }\n    default:\n      paramType satisfies never\n      return ''\n  }\n}\n\nexport function doesStaticSegmentAppearInURL(segment: string): boolean {\n  // This is not a parameterized segment; however, we need to determine\n  // whether or not this segment appears in the URL. For example, this route\n  // groups do not appear in the URL, so they should be skipped. Any other\n  // special cases must be handled here.\n  // TODO: Consider encoding this directly into the router tree instead of\n  // inferring it on the client based on the segment type. Something like\n  // a `doesAppearInURL` flag in FlightRouterState.\n  if (\n    segment === ROOT_SEGMENT_REQUEST_KEY ||\n    // For some reason, the loader tree sometimes includes extra __PAGE__\n    // \"layouts\" when part of a parallel route. But it's not a leaf node.\n    // Otherwise, we wouldn't need this special case because pages are\n    // always leaf nodes.\n    // TODO: Investigate why the loader produces these fake page segments.\n    segment.startsWith(PAGE_SEGMENT_KEY) ||\n    // Route groups.\n    (segment[0] === '(' && segment.endsWith(')')) ||\n    segment === DEFAULT_SEGMENT_KEY ||\n    segment === '/_not-found'\n  ) {\n    return false\n  } else {\n    // All other segment types appear in the URL\n    return true\n  }\n}\n\nexport function getCacheKeyForDynamicParam(\n  paramValue: RouteParamValue,\n  renderedSearch: NormalizedSearch\n): string {\n  // This needs to match the logic in get-dynamic-param.ts, until we're able to\n  // unify the various implementations so that these are always computed on\n  // the client.\n  if (typeof paramValue === 'string') {\n    // TODO: Refactor or remove this helper function to accept a string rather\n    // than the whole segment type. Also we can probably just append the\n    // search string instead of turning it into JSON.\n    const pageSegmentWithSearchParams = addSearchParamsIfPageSegment(\n      paramValue,\n      Object.fromEntries(new URLSearchParams(renderedSearch))\n    ) as string\n    return pageSegmentWithSearchParams\n  } else if (paramValue === null) {\n    return ''\n  } else {\n    return paramValue.join('/')\n  }\n}\n\nexport function urlToUrlWithoutFlightMarker(url: URL): URL {\n  const urlWithoutFlightParameters = new URL(url)\n  urlWithoutFlightParameters.searchParams.delete(NEXT_RSC_UNION_QUERY)\n  if (process.env.NODE_ENV === 'production') {\n    if (\n      process.env.__NEXT_CONFIG_OUTPUT === 'export' &&\n      urlWithoutFlightParameters.pathname.endsWith('.txt')\n    ) {\n      const { pathname } = urlWithoutFlightParameters\n      const length = pathname.endsWith('/index.txt') ? 10 : 4\n      // Slice off `/index.txt` or `.txt` from the end of the pathname\n      urlWithoutFlightParameters.pathname = pathname.slice(0, -length)\n    }\n  }\n  return urlWithoutFlightParameters\n}\n\nexport function getParamValueFromCacheKey(\n  paramCacheKey: string,\n  paramType: DynamicParamTypesShort\n) {\n  // Turn the cache key string sent by the server (as part of FlightRouterState)\n  // into a value that can be passed to `useParams` and client components.\n  const isCatchAll = paramType === 'c' || paramType === 'oc'\n  if (isCatchAll) {\n    // Catch-all param keys are a concatenation of the path segments.\n    // See equivalent logic in `getSelectedParams`.\n    // TODO: We should just pass the array directly, rather than concatenate\n    // it to a string and then split it back to an array. It needs to be an\n    // array in some places, like when passing a key React, but we can convert\n    // it at runtime in those places.\n    return paramCacheKey.split('/')\n  }\n  return paramCacheKey\n}\n\nexport function urlSearchParamsToParsedUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  // Converts a URLSearchParams object to the same type used by the server when\n  // creating search params props, i.e. the type returned by Node's\n  // \"querystring\" module.\n  const result: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    if (result[key] === undefined) {\n      result[key] = value\n    } else if (Array.isArray(result[key])) {\n      result[key].push(value)\n    } else {\n      result[key] = [result[key], value]\n    }\n  }\n  return result\n}\n"], "names": ["addSearchParamsIfPageSegment", "DEFAULT_SEGMENT_KEY", "PAGE_SEGMENT_KEY", "ROOT_SEGMENT_REQUEST_KEY", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_RSC_UNION_QUERY", "getRenderedSearch", "response", "rewritten<PERSON><PERSON><PERSON>", "headers", "get", "urlToUrlWithoutFlightMarker", "URL", "url", "search", "getRenderedPathname", "rewrittenPath", "pathname", "parseDynamicParamFromURLPart", "paramType", "pathnameParts", "partIndex", "length", "slice", "map", "s", "encodeURIComponent", "doesStaticSegmentAppearInURL", "segment", "startsWith", "endsWith", "getCacheKeyForDynamicParam", "paramValue", "renderedSearch", "pageSegmentWithSearchParams", "Object", "fromEntries", "URLSearchParams", "join", "urlWithoutFlightParameters", "searchParams", "delete", "process", "env", "NODE_ENV", "__NEXT_CONFIG_OUTPUT", "getParamValueFromCacheKey", "param<PERSON><PERSON><PERSON><PERSON>", "isCatchAll", "split", "urlSearchParamsToParsedUrlQuery", "result", "key", "value", "entries", "undefined", "Array", "isArray", "push"], "mappings": ";;;;;;;;;;;;;;;;;;AACA,SACEA,4BAA4B,EAC5BC,mBAAmB,EACnBC,gBAAgB,QACX,wBAAuB;AAC9B,SAASC,wBAAwB,QAAQ,qDAAoD;AAC7F,SACEC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,oBAAoB,QACf,kCAAiC;;;;AAajC,SAASC,kBACdC,QAAyC;IAEzC,0EAA0E;IAC1E,2EAA2E;IAC3E,4EAA4E;IAC5E,MAAMC,iBAAiBD,SAASE,OAAO,CAACC,GAAG,CAACN,gbAAAA;IAC5C,IAAII,mBAAmB,MAAM;QAC3B,OACEA,mBAAmB,KAAK,KAAK,MAAMA;IAEvC;IACA,2EAA2E;IAC3E,6BAA6B;IAC7B,OAAOG,4BAA4B,IAAIC,IAAIL,SAASM,GAAG,GACpDC,MAAM;AACX;AAEO,SAASC,oBACdR,QAAyC;IAEzC,qEAAqE;IACrE,6EAA6E;IAC7E,wEAAwE;IACxE,MAAMS,gBAAgBT,SAASE,OAAO,CAACC,GAAG,CAACP,+aAAAA;IAC3C,OACEa,iBAAiBL,4BAA4B,IAAIC,IAAIL,SAASM,GAAG,GAAGI,QAAQ;AAEhF;AAEO,SAASC,6BACdC,SAAiC,EACjCC,aAA4B,EAC5BC,SAAiB;IAEjB,4DAA4D;IAC5D,OAAQF;QACN,YAAY;QACZ,KAAK;QACL,KAAK;YAAM;gBACT,iEAAiE;gBACjE,mDAAmD;gBACnD,OAAOE,YAAYD,cAAcE,MAAM,GACnCF,cAAcG,KAAK,CAACF,WAAWG,GAAG,CAAC,CAACC,IAAMC,mBAAmBD,MAC7D,EAAE;YACR;QACA,qBAAqB;QACrB,KAAK;YAAM;gBACT,yEAAyE;gBACzE,2DAA2D;gBAC3D,OAAOJ,YAAYD,cAAcE,MAAM,GACnCF,cAAcG,KAAK,CAACF,WAAWG,GAAG,CAAC,CAACC,IAAMC,mBAAmBD,MAC7D;YACN;QACA,UAAU;QACV,KAAK;QACL,KAAK;YAAM;gBACT,IAAIJ,aAAaD,cAAcE,MAAM,EAAE;oBACrC,uEAAuE;oBACvE,wEAAwE;oBACxE,kEAAkE;oBAClE,qEAAqE;oBACrE,sEAAsE;oBACtE,6DAA6D;oBAC7D,oBAAoB;oBACpB,OAAO;gBACT;gBACA,OAAOI,mBAAmBN,aAAa,CAACC,UAAU;YACpD;QACA;YACEF;YACA,OAAO;IACX;AACF;AAEO,SAASQ,6BAA6BC,OAAe;IAC1D,qEAAqE;IACrE,0EAA0E;IAC1E,wEAAwE;IACxE,sCAAsC;IACtC,wEAAwE;IACxE,uEAAuE;IACvE,iDAAiD;IACjD,IACEA,YAAY1B,8bAAAA,IACZ,qEAAqE;IACrE,qEAAqE;IACrE,kEAAkE;IAClE,qBAAqB;IACrB,sEAAsE;IACtE0B,QAAQC,UAAU,CAAC5B,6YAAAA,KACnB,gBAAgB;IACf2B,OAAO,CAAC,EAAE,KAAK,OAAOA,QAAQE,QAAQ,CAAC,QACxCF,YAAY5B,gZAAAA,IACZ4B,YAAY,eACZ;QACA,OAAO;IACT,OAAO;QACL,4CAA4C;QAC5C,OAAO;IACT;AACF;AAEO,SAASG,2BACdC,UAA2B,EAC3BC,cAAgC;IAEhC,6EAA6E;IAC7E,yEAAyE;IACzE,cAAc;IACd,IAAI,OAAOD,eAAe,UAAU;QAClC,0EAA0E;QAC1E,oEAAoE;QACpE,iDAAiD;QACjD,MAAME,kCAA8BnC,yZAAAA,EAClCiC,YACAG,OAAOC,WAAW,CAAC,IAAIC,gBAAgBJ;QAEzC,OAAOC;IACT,OAAO,IAAIF,eAAe,MAAM;QAC9B,OAAO;IACT,OAAO;QACL,OAAOA,WAAWM,IAAI,CAAC;IACzB;AACF;AAEO,SAAS3B,4BAA4BE,GAAQ;IAClD,MAAM0B,6BAA6B,IAAI3B,IAAIC;IAC3C0B,2BAA2BC,YAAY,CAACC,MAAM,CAACpC,yaAAAA;IAC/C,IAAIqC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;;IAW3C,OAAOL;AACT;AAEO,SAASO,0BACdC,aAAqB,EACrB5B,SAAiC;IAEjC,8EAA8E;IAC9E,wEAAwE;IACxE,MAAM6B,aAAa7B,cAAc,OAAOA,cAAc;IACtD,IAAI6B,YAAY;QACd,iEAAiE;QACjE,+CAA+C;QAC/C,wEAAwE;QACxE,uEAAuE;QACvE,0EAA0E;QAC1E,iCAAiC;QACjC,OAAOD,cAAcE,KAAK,CAAC;IAC7B;IACA,OAAOF;AACT;AAEO,SAASG,gCACdV,YAA6B;IAE7B,6EAA6E;IAC7E,iEAAiE;IACjE,wBAAwB;IACxB,MAAMW,SAAyB,CAAC;IAChC,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIb,aAAac,OAAO,GAAI;QACjD,IAAIH,MAAM,CAACC,IAAI,KAAKG,WAAW;YAC7BJ,MAAM,CAACC,IAAI,GAAGC;QAChB,OAAO,IAAIG,MAAMC,OAAO,CAACN,MAAM,CAACC,IAAI,GAAG;YACrCD,MAAM,CAACC,IAAI,CAACM,IAAI,CAACL;QACnB,OAAO;YACLF,MAAM,CAACC,IAAI,GAAG;gBAACD,MAAM,CAACC,IAAI;gBAAEC;aAAM;QACpC;IACF;IACA,OAAOF;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 718, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/router-reducer/create-href-from-url.ts"], "sourcesContent": ["export function createHrefFromUrl(\n  url: Pick<URL, 'pathname' | 'search' | 'hash'>,\n  includeHash: boolean = true\n): string {\n  return url.pathname + url.search + (includeHash ? url.hash : '')\n}\n"], "names": ["createHrefFromUrl", "url", "includeHash", "pathname", "search", "hash"], "mappings": ";;;;AAAO,SAASA,kBACdC,GAA8C,EAC9CC,cAAuB,IAAI;IAE3B,OAAOD,IAAIE,QAAQ,GAAGF,IAAIG,MAAM,GAAIF,CAAAA,cAAcD,IAAII,IAAI,GAAG,EAAC;AAChE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/flight-data-helpers.ts"], "sourcesContent": ["import type {\n  CacheNodeSeedData,\n  FlightData,\n  FlightDataPath,\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n  HeadData,\n  InitialRSCPayload,\n} from '../shared/lib/app-router-types'\nimport { PAGE_SEGMENT_KEY } from '../shared/lib/segment'\nimport type { NormalizedSearch } from './components/segment-cache'\nimport {\n  getCacheKeyForDynamicParam,\n  parseDynamicParamFromURLPart,\n  doesStaticSegmentAppearInURL,\n  getRenderedPathname,\n  getRenderedSearch,\n} from './route-params'\nimport { createHrefFromUrl } from './components/router-reducer/create-href-from-url'\n\nexport type NormalizedFlightData = {\n  /**\n   * The full `FlightSegmentPath` inclusive of the final `Segment`\n   */\n  segmentPath: FlightSegmentPath\n  /**\n   * The `FlightSegmentPath` exclusive of the final `Segment`\n   */\n  pathToSegment: FlightSegmentPath\n  segment: Segment\n  tree: FlightRouterState\n  seedData: CacheNodeSeedData | null\n  head: HeadData\n  isHeadPartial: boolean\n  isRootRender: boolean\n}\n\n// TODO: We should only have to export `normalizeFlightData`, however because the initial flight data\n// that gets passed to `createInitialRouterState` doesn't conform to the `FlightDataPath` type (it's missing the root segment)\n// we're currently exporting it so we can use it directly. This should be fixed as part of the unification of\n// the different ways we express `FlightSegmentPath`.\nexport function getFlightDataPartsFromPath(\n  flightDataPath: FlightDataPath\n): NormalizedFlightData {\n  // Pick the last 4 items from the `FlightDataPath` to get the [tree, seedData, viewport, isHeadPartial].\n  const flightDataPathLength = 4\n  // tree, seedData, and head are *always* the last three items in the `FlightDataPath`.\n  const [tree, seedData, head, isHeadPartial] =\n    flightDataPath.slice(-flightDataPathLength)\n  // The `FlightSegmentPath` is everything except the last three items. For a root render, it won't be present.\n  const segmentPath = flightDataPath.slice(0, -flightDataPathLength)\n\n  return {\n    // TODO: Unify these two segment path helpers. We are inconsistently pushing an empty segment (\"\")\n    // to the start of the segment path in some places which makes it hard to use solely the segment path.\n    // Look for \"// TODO-APP: remove ''\" in the codebase.\n    pathToSegment: segmentPath.slice(0, -1),\n    segmentPath,\n    // if the `FlightDataPath` corresponds with the root, there'll be no segment path,\n    // in which case we default to ''.\n    segment: segmentPath[segmentPath.length - 1] ?? '',\n    tree,\n    seedData,\n    head,\n    isHeadPartial,\n    isRootRender: flightDataPath.length === flightDataPathLength,\n  }\n}\n\nexport function createInitialRSCPayloadFromFallbackPrerender(\n  response: Response,\n  fallbackInitialRSCPayload: InitialRSCPayload\n): InitialRSCPayload {\n  // This is a static fallback page. In order to hydrate the page, we need to\n  // parse the client params from the URL, but to account for the possibility\n  // that the page was rewritten, we need to check the response headers\n  // for x-nextjs-rewritten-path or x-nextjs-rewritten-query headers. Since\n  // we can't access the headers of the initial document response, the client\n  // performs a fetch request to the current location. Since it's possible that\n  // the fetch request will be dynamically rewritten to a different path than\n  // the initial document, this fetch request delivers _all_ the hydration data\n  // for the page; it was not inlined into the document, like it normally\n  // would be.\n  //\n  // TODO: Consider treating the case where fetch is rewritten to a different\n  // path from the document as a special deopt case. We should optimistically\n  // assume this won't happen, inline the data into the document, and perform\n  // a minimal request (like a HEAD or range request) to verify that the\n  // response matches. Tricky to get right because we need to account for\n  // all the different deployment environments we support, like output:\n  // \"export\" mode, where we currently don't assume that custom response\n  // headers are present.\n\n  // Patch the Flight data sent by the server with the correct params parsed\n  // from the URL + response object.\n  const renderedPathname = getRenderedPathname(response)\n  const renderedSearch = getRenderedSearch(response)\n  const canonicalUrl = createHrefFromUrl(new URL(location.href))\n  const originalFlightDataPath = fallbackInitialRSCPayload.f[0]\n  const originalFlightRouterState = originalFlightDataPath[0]\n  return {\n    b: fallbackInitialRSCPayload.b,\n    c: canonicalUrl.split('/'),\n    q: renderedSearch,\n    i: fallbackInitialRSCPayload.i,\n    f: [\n      [\n        fillInFallbackFlightRouterState(\n          originalFlightRouterState,\n          renderedPathname,\n          renderedSearch as NormalizedSearch\n        ),\n        originalFlightDataPath[1],\n        originalFlightDataPath[2],\n        originalFlightDataPath[2],\n      ],\n    ],\n    m: fallbackInitialRSCPayload.m,\n    G: fallbackInitialRSCPayload.G,\n    s: fallbackInitialRSCPayload.s,\n    S: fallbackInitialRSCPayload.S,\n  }\n}\n\nfunction fillInFallbackFlightRouterState(\n  flightRouterState: FlightRouterState,\n  renderedPathname: string,\n  renderedSearch: NormalizedSearch\n): FlightRouterState {\n  const pathnameParts = renderedPathname.split('/').filter((p) => p !== '')\n  const index = 0\n  return fillInFallbackFlightRouterStateImpl(\n    flightRouterState,\n    renderedSearch,\n    pathnameParts,\n    index\n  )\n}\n\nfunction fillInFallbackFlightRouterStateImpl(\n  flightRouterState: FlightRouterState,\n  renderedSearch: NormalizedSearch,\n  pathnameParts: Array<string>,\n  pathnamePartsIndex: number\n): FlightRouterState {\n  const originalSegment = flightRouterState[0]\n  let newSegment: Segment\n  let doesAppearInURL: boolean\n  if (typeof originalSegment === 'string') {\n    newSegment = originalSegment\n    doesAppearInURL = doesStaticSegmentAppearInURL(originalSegment)\n  } else {\n    const paramName = originalSegment[0]\n    const paramType = originalSegment[2]\n    const paramValue = parseDynamicParamFromURLPart(\n      paramType,\n      pathnameParts,\n      pathnamePartsIndex\n    )\n    const cacheKey = getCacheKeyForDynamicParam(paramValue, renderedSearch)\n    newSegment = [paramName, cacheKey, paramType]\n    doesAppearInURL = true\n  }\n\n  // Only increment the index if the segment appears in the URL. If it's a\n  // \"virtual\" segment, like a route group, it remains the same.\n  const childPathnamePartsIndex = doesAppearInURL\n    ? pathnamePartsIndex + 1\n    : pathnamePartsIndex\n\n  const children = flightRouterState[1]\n  const newChildren: { [key: string]: FlightRouterState } = {}\n  for (let key in children) {\n    const childFlightRouterState = children[key]\n    newChildren[key] = fillInFallbackFlightRouterStateImpl(\n      childFlightRouterState,\n      renderedSearch,\n      pathnameParts,\n      childPathnamePartsIndex\n    )\n  }\n\n  const newState: FlightRouterState = [\n    newSegment,\n    newChildren,\n    null,\n    flightRouterState[3],\n    flightRouterState[4],\n  ]\n  return newState\n}\n\nexport function getNextFlightSegmentPath(\n  flightSegmentPath: FlightSegmentPath\n): FlightSegmentPath {\n  // Since `FlightSegmentPath` is a repeated tuple of `Segment` and `ParallelRouteKey`, we slice off two items\n  // to get the next segment path.\n  return flightSegmentPath.slice(2)\n}\n\nexport function normalizeFlightData(\n  flightData: FlightData\n): NormalizedFlightData[] | string {\n  // FlightData can be a string when the server didn't respond with a proper flight response,\n  // or when a redirect happens, to signal to the client that it needs to perform an MPA navigation.\n  if (typeof flightData === 'string') {\n    return flightData\n  }\n\n  return flightData.map((flightDataPath) =>\n    getFlightDataPartsFromPath(flightDataPath)\n  )\n}\n\n/**\n * This function is used to prepare the flight router state for the request.\n * It removes markers that are not needed by the server, and are purely used\n * for stashing state on the client.\n * @param flightRouterState - The flight router state to prepare.\n * @param isHmrRefresh - Whether this is an HMR refresh request.\n * @returns The prepared flight router state.\n */\nexport function prepareFlightRouterStateForRequest(\n  flightRouterState: FlightRouterState,\n  isHmrRefresh?: boolean\n): string {\n  // HMR requests need the complete, unmodified state for proper functionality\n  if (isHmrRefresh) {\n    return encodeURIComponent(JSON.stringify(flightRouterState))\n  }\n\n  return encodeURIComponent(\n    JSON.stringify(stripClientOnlyDataFromFlightRouterState(flightRouterState))\n  )\n}\n\n/**\n * Recursively strips client-only data from FlightRouterState while preserving\n * server-needed information for proper rendering decisions.\n */\nfunction stripClientOnlyDataFromFlightRouterState(\n  flightRouterState: FlightRouterState\n): FlightRouterState {\n  const [\n    segment,\n    parallelRoutes,\n    _url, // Intentionally unused - URLs are client-only\n    refreshMarker,\n    isRootLayout,\n    hasLoadingBoundary,\n  ] = flightRouterState\n\n  // __PAGE__ segments are always fetched from the server, so there's\n  // no need to send them up\n  const cleanedSegment = stripSearchParamsFromPageSegment(segment)\n\n  // Recursively process parallel routes\n  const cleanedParallelRoutes: { [key: string]: FlightRouterState } = {}\n  for (const [key, childState] of Object.entries(parallelRoutes)) {\n    cleanedParallelRoutes[key] =\n      stripClientOnlyDataFromFlightRouterState(childState)\n  }\n\n  const result: FlightRouterState = [\n    cleanedSegment,\n    cleanedParallelRoutes,\n    null, // URLs omitted - server reconstructs paths from segments\n    shouldPreserveRefreshMarker(refreshMarker) ? refreshMarker : null,\n  ]\n\n  // Append optional fields if present\n  if (isRootLayout !== undefined) {\n    result[4] = isRootLayout\n  }\n  if (hasLoadingBoundary !== undefined) {\n    result[5] = hasLoadingBoundary\n  }\n\n  return result\n}\n\n/**\n * Strips search parameters from __PAGE__ segments to prevent sensitive\n * client-side data from being sent to the server.\n */\nfunction stripSearchParamsFromPageSegment(segment: Segment): Segment {\n  if (\n    typeof segment === 'string' &&\n    segment.startsWith(PAGE_SEGMENT_KEY + '?')\n  ) {\n    return PAGE_SEGMENT_KEY\n  }\n  return segment\n}\n\n/**\n * Determines whether the refresh marker should be sent to the server\n * Client-only markers like 'refresh' are stripped, while server-needed markers\n * like 'refetch' and 'inside-shared-layout' are preserved.\n */\nfunction shouldPreserveRefreshMarker(\n  refreshMarker: FlightRouterState[3]\n): boolean {\n  return Boolean(refreshMarker && refreshMarker !== 'refresh')\n}\n"], "names": ["PAGE_SEGMENT_KEY", "getCacheKeyForDynamicParam", "parseDynamicParamFromURLPart", "doesStaticSegmentAppearInURL", "getRenderedPathname", "getRenderedSearch", "createHrefFromUrl", "getFlightDataPartsFromPath", "flightDataPath", "flightDataPathLength", "tree", "seedData", "head", "isHeadPartial", "slice", "segmentPath", "pathToSegment", "segment", "length", "isRootRender", "createInitialRSCPayloadFromFallbackPrerender", "response", "fallbackInitialRSCPayload", "renderedPathname", "renderedSearch", "canonicalUrl", "URL", "location", "href", "originalFlightDataPath", "f", "originalFlightRouterState", "b", "c", "split", "q", "i", "fillInFallbackFlightRouterState", "m", "G", "s", "S", "flightRouterState", "pathnameParts", "filter", "p", "index", "fillInFallbackFlightRouterStateImpl", "pathnamePartsIndex", "originalSegment", "newSegment", "doesAppearInURL", "paramName", "paramType", "paramValue", "cache<PERSON>ey", "childPathnamePartsIndex", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "childFlightRouterState", "newState", "getNextFlightSegmentPath", "flightSegmentPath", "normalizeFlightData", "flightData", "map", "prepareFlightRouterStateForRequest", "isHmrRefresh", "encodeURIComponent", "JSON", "stringify", "stripClientOnlyDataFromFlightRouterState", "parallelRoutes", "_url", "refresh<PERSON><PERSON><PERSON>", "isRootLayout", "hasLoadingBoundary", "cleanedSegment", "stripSearchParamsFromPageSegment", "cleanedParallelRoutes", "childState", "Object", "entries", "result", "shouldPreserveRefreshMarker", "undefined", "startsWith", "Boolean"], "mappings": ";;;;;;;;;;;;AAUA,SAASA,gBAAgB,QAAQ,wBAAuB;AAExD,SACEC,0BAA0B,EAC1BC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,mBAAmB,EACnBC,iBAAiB,QACZ,iBAAgB;AACvB,SAASC,iBAAiB,QAAQ,mDAAkD;;;;AAuB7E,SAASC,2BACdC,cAA8B;IAE9B,wGAAwG;IACxG,MAAMC,uBAAuB;IAC7B,sFAAsF;IACtF,MAAM,CAACC,MAAMC,UAAUC,MAAMC,cAAc,GACzCL,eAAeM,KAAK,CAAC,CAACL;IACxB,6GAA6G;IAC7G,MAAMM,cAAcP,eAAeM,KAAK,CAAC,GAAG,CAACL;IAE7C,OAAO;QACL,kGAAkG;QAClG,sGAAsG;QACtG,qDAAqD;QACrDO,eAAeD,YAAYD,KAAK,CAAC,GAAG,CAAC;QACrCC;QACA,kFAAkF;QAClF,kCAAkC;QAClCE,SAASF,WAAW,CAACA,YAAYG,MAAM,GAAG,EAAE,IAAI;QAChDR;QACAC;QACAC;QACAC;QACAM,cAAcX,eAAeU,MAAM,KAAKT;IAC1C;AACF;AAEO,SAASW,6CACdC,QAAkB,EAClBC,yBAA4C;IAE5C,2EAA2E;IAC3E,2EAA2E;IAC3E,qEAAqE;IACrE,yEAAyE;IACzE,2EAA2E;IAC3E,6EAA6E;IAC7E,2EAA2E;IAC3E,6EAA6E;IAC7E,uEAAuE;IACvE,YAAY;IACZ,EAAE;IACF,2EAA2E;IAC3E,2EAA2E;IAC3E,2EAA2E;IAC3E,sEAAsE;IACtE,uEAAuE;IACvE,qEAAqE;IACrE,sEAAsE;IACtE,uBAAuB;IAEvB,0EAA0E;IAC1E,kCAAkC;IAClC,MAAMC,uBAAmBnB,iZAAAA,EAAoBiB;IAC7C,MAAMG,qBAAiBnB,+YAAAA,EAAkBgB;IACzC,MAAMI,mBAAenB,gcAAAA,EAAkB,IAAIoB,IAAIC,SAASC,IAAI;IAC5D,MAAMC,yBAAyBP,0BAA0BQ,CAAC,CAAC,EAAE;IAC7D,MAAMC,4BAA4BF,sBAAsB,CAAC,EAAE;IAC3D,OAAO;QACLG,GAAGV,0BAA0BU,CAAC;QAC9BC,GAAGR,aAAaS,KAAK,CAAC;QACtBC,GAAGX;QACHY,GAAGd,0BAA0Bc,CAAC;QAC9BN,GAAG;YACD;gBACEO,gCACEN,2BACAR,kBACAC;gBAEFK,sBAAsB,CAAC,EAAE;gBACzBA,sBAAsB,CAAC,EAAE;gBACzBA,sBAAsB,CAAC,EAAE;aAC1B;SACF;QACDS,GAAGhB,0BAA0BgB,CAAC;QAC9BC,GAAGjB,0BAA0BiB,CAAC;QAC9BC,GAAGlB,0BAA0BkB,CAAC;QAC9BC,GAAGnB,0BAA0BmB,CAAC;IAChC;AACF;AAEA,SAASJ,gCACPK,iBAAoC,EACpCnB,gBAAwB,EACxBC,cAAgC;IAEhC,MAAMmB,gBAAgBpB,iBAAiBW,KAAK,CAAC,KAAKU,MAAM,CAAC,CAACC,IAAMA,MAAM;IACtE,MAAMC,QAAQ;IACd,OAAOC,oCACLL,mBACAlB,gBACAmB,eACAG;AAEJ;AAEA,SAASC,oCACPL,iBAAoC,EACpClB,cAAgC,EAChCmB,aAA4B,EAC5BK,kBAA0B;IAE1B,MAAMC,kBAAkBP,iBAAiB,CAAC,EAAE;IAC5C,IAAIQ;IACJ,IAAIC;IACJ,IAAI,OAAOF,oBAAoB,UAAU;QACvCC,aAAaD;QACbE,sBAAkBhD,0ZAAAA,EAA6B8C;IACjD,OAAO;QACL,MAAMG,YAAYH,eAAe,CAAC,EAAE;QACpC,MAAMI,YAAYJ,eAAe,CAAC,EAAE;QACpC,MAAMK,iBAAapD,0ZAAAA,EACjBmD,WACAV,eACAK;QAEF,MAAMO,eAAWtD,wZAAAA,EAA2BqD,YAAY9B;QACxD0B,aAAa;YAACE;YAAWG;YAAUF;SAAU;QAC7CF,kBAAkB;IACpB;IAEA,wEAAwE;IACxE,8DAA8D;IAC9D,MAAMK,0BAA0BL,kBAC5BH,qBAAqB,IACrBA;IAEJ,MAAMS,WAAWf,iBAAiB,CAAC,EAAE;IACrC,MAAMgB,cAAoD,CAAC;IAC3D,IAAK,IAAIC,OAAOF,SAAU;QACxB,MAAMG,yBAAyBH,QAAQ,CAACE,IAAI;QAC5CD,WAAW,CAACC,IAAI,GAAGZ,oCACjBa,wBACApC,gBACAmB,eACAa;IAEJ;IAEA,MAAMK,WAA8B;QAClCX;QACAQ;QACA;QACAhB,iBAAiB,CAAC,EAAE;QACpBA,iBAAiB,CAAC,EAAE;KACrB;IACD,OAAOmB;AACT;AAEO,SAASC,yBACdC,iBAAoC;IAEpC,4GAA4G;IAC5G,gCAAgC;IAChC,OAAOA,kBAAkBjD,KAAK,CAAC;AACjC;AAEO,SAASkD,oBACdC,UAAsB;IAEtB,2FAA2F;IAC3F,kGAAkG;IAClG,IAAI,OAAOA,eAAe,UAAU;QAClC,OAAOA;IACT;IAEA,OAAOA,WAAWC,GAAG,CAAC,CAAC1D,iBACrBD,2BAA2BC;AAE/B;AAUO,SAAS2D,mCACdzB,iBAAoC,EACpC0B,YAAsB;IAEtB,4EAA4E;IAC5E,IAAIA,cAAc;QAChB,OAAOC,mBAAmBC,KAAKC,SAAS,CAAC7B;IAC3C;IAEA,OAAO2B,mBACLC,KAAKC,SAAS,CAACC,yCAAyC9B;AAE5D;AAEA;;;CAGC,GACD,SAAS8B,yCACP9B,iBAAoC;IAEpC,MAAM,CACJzB,SACAwD,gBACAC,MACAC,eACAC,cACAC,mBACD,GAAGnC;IAEJ,mEAAmE;IACnE,0BAA0B;IAC1B,MAAMoC,iBAAiBC,iCAAiC9D;IAExD,sCAAsC;IACtC,MAAM+D,wBAA8D,CAAC;IACrE,KAAK,MAAM,CAACrB,KAAKsB,WAAW,IAAIC,OAAOC,OAAO,CAACV,gBAAiB;QAC9DO,qBAAqB,CAACrB,IAAI,GACxBa,yCAAyCS;IAC7C;IAEA,MAAMG,SAA4B;QAChCN;QACAE;QACA;QACAK,4BAA4BV,iBAAiBA,gBAAgB;KAC9D;IAED,oCAAoC;IACpC,IAAIC,iBAAiBU,WAAW;QAC9BF,MAAM,CAAC,EAAE,GAAGR;IACd;IACA,IAAIC,uBAAuBS,WAAW;QACpCF,MAAM,CAAC,EAAE,GAAGP;IACd;IAEA,OAAOO;AACT;AAEA;;;CAGC,GACD,SAASL,iCAAiC9D,OAAgB;IACxD,IACE,OAAOA,YAAY,YACnBA,QAAQsE,UAAU,CAACvF,6YAAAA,GAAmB,MACtC;QACA,OAAOA,6YAAAA;IACT;IACA,OAAOiB;AACT;AAEA;;;;CAIC,GACD,SAASoE,4BACPV,aAAmC;IAEnC,OAAOa,QAAQb,iBAAiBA,kBAAkB;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/app-build-id.ts"], "sourcesContent": ["// This gets assigned as a side-effect during app initialization. Because it\n// represents the build used to create the JS bundle, it should never change\n// after being set, so we store it in a global variable.\n//\n// When performing RSC requests, if the incoming data has a different build ID,\n// we perform an MPA navigation/refresh to load the updated build and ensure\n// that the client and server in sync.\n\n// Starts as an empty string. In practice, because setAppBuildId is called\n// during initialization before hydration starts, this will always get\n// reassigned to the actual build ID before it's ever needed by a navigation.\n// If for some reasons it didn't, due to a bug or race condition, then on\n// navigation the build comparision would fail and trigger an MPA navigation.\nlet globalBuildId: string = ''\n\nexport function setAppBuildId(buildId: string) {\n  globalBuildId = buildId\n}\n\nexport function getAppBuildId(): string {\n  return globalBuildId\n}\n"], "names": ["globalBuildId", "setAppBuildId", "buildId", "getAppBuildId"], "mappings": "AAAA,4EAA4E;AAC5E,4EAA4E;AAC5E,wDAAwD;AACxD,EAAE;AACF,+EAA+E;AAC/E,4EAA4E;AAC5E,sCAAsC;AAEtC,0EAA0E;AAC1E,sEAAsE;AACtE,6EAA6E;AAC7E,yEAAyE;AACzE,6EAA6E;;;;;;;AAC7E,IAAIA,gBAAwB;AAErB,SAASC,cAAcC,OAAe;IAC3CF,gBAAgBE;AAClB;AAEO,SAASC;IACd,OAAOH;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/hash.ts"], "sourcesContent": ["// http://www.cse.yorku.ca/~oz/hash.html\n// More specifically, 32-bit hash via djbxor\n// (ref: https://gist.github.com/eplawless/52813b1d8ad9af510d85?permalink_comment_id=3367765#gistcomment-3367765)\n// This is due to number type differences between rust for turbopack to js number types,\n// where rust does not have easy way to repreesnt js's 53-bit float number type for the matching\n// overflow behavior. This is more `correct` in terms of having canonical hash across different runtime / implementation\n// as can gaurantee determinstic output from 32bit hash.\nexport function djb2Hash(str: string) {\n  let hash = 5381\n  for (let i = 0; i < str.length; i++) {\n    const char = str.charCodeAt(i)\n    hash = ((hash << 5) + hash + char) & 0xffffffff\n  }\n  return hash >>> 0\n}\n\nexport function hexHash(str: string) {\n  return djb2Hash(str).toString(36).slice(0, 5)\n}\n"], "names": ["djb2Hash", "str", "hash", "i", "length", "char", "charCodeAt", "hexHash", "toString", "slice"], "mappings": "AAAA,wCAAwC;AACxC,4CAA4C;AAC5C,iHAAiH;AACjH,wFAAwF;AACxF,gGAAgG;AAChG,wHAAwH;AACxH,wDAAwD;;;;;;;AACjD,SAASA,SAASC,GAAW;IAClC,IAAIC,OAAO;IACX,IAAK,IAAIC,IAAI,GAAGA,IAAIF,IAAIG,MAAM,EAAED,IAAK;QACnC,MAAME,OAAOJ,IAAIK,UAAU,CAACH;QAC5BD,OAASA,CAAAA,QAAQ,CAAA,IAAKA,OAAOG,OAAQ;IACvC;IACA,OAAOH,SAAS;AAClB;AAEO,SAASK,QAAQN,GAAW;IACjC,OAAOD,SAASC,KAAKO,QAAQ,CAAC,IAAIC,KAAK,CAAC,GAAG;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 983, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/router/utils/cache-busting-search-param.ts"], "sourcesContent": ["import { hexHash } from '../../hash'\n\nexport function computeCacheBustingSearchParam(\n  prefetchHeader: '1' | '2' | '0' | undefined,\n  segmentPrefetchHeader: string | string[] | undefined,\n  stateTreeHeader: string | string[] | undefined,\n  nextUrlHeader: string | string[] | undefined\n): string {\n  if (\n    (prefetchHeader === undefined || prefetchHeader === '0') &&\n    segmentPrefetchHeader === undefined &&\n    stateTreeHeader === undefined &&\n    nextUrlHeader === undefined\n  ) {\n    return ''\n  }\n  return hexHash(\n    [\n      prefetchHeader || '0',\n      segmentPrefetchHeader || '0',\n      stateTreeHeader || '0',\n      nextUrlHeader || '0',\n    ].join(',')\n  )\n}\n"], "names": ["hexHash", "computeCacheBustingSearchParam", "prefetch<PERSON><PERSON><PERSON>", "segmentPrefetchHeader", "stateTreeHeader", "nextUrl<PERSON><PERSON>er", "undefined", "join"], "mappings": ";;;;AAAA,SAASA,OAAO,QAAQ,aAAY;;AAE7B,SAASC,+BACdC,cAA2C,EAC3CC,qBAAoD,EACpDC,eAA8C,EAC9CC,aAA4C;IAE5C,IACGH,CAAAA,mBAAmBI,aAAaJ,mBAAmB,GAAE,KACtDC,0BAA0BG,aAC1BF,oBAAoBE,aACpBD,kBAAkBC,WAClB;QACA,OAAO;IACT;IACA,WAAON,iYAAAA,EACL;QACEE,kBAAkB;QAClBC,yBAAyB;QACzBC,mBAAmB;QACnBC,iBAAiB;KAClB,CAACE,IAAI,CAAC;AAEX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/router-reducer/set-cache-busting-search-param.ts"], "sourcesContent": ["'use client'\n\nimport { computeCacheBustingSearchParam } from '../../../shared/lib/router/utils/cache-busting-search-param'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_URL,\n  NEXT_RSC_UNION_QUERY,\n} from '../app-router-headers'\nimport type { RequestHeaders } from './fetch-server-response'\n\n/**\n * Mutates the provided URL by adding a cache-busting search parameter for CDNs that don't\n * support custom headers. This helps avoid caching conflicts by making each request unique.\n *\n * Rather than relying on the Vary header which some CDNs ignore, we append a search param\n * to create a unique URL that forces a fresh request.\n *\n * Example:\n * URL before: https://example.com/path?query=1\n * URL after: https://example.com/path?query=1&_rsc=abc123\n *\n * Note: This function mutates the input URL directly and does not return anything.\n *\n * TODO: Since we need to use a search param anyway, we could simplify by removing the custom\n * headers approach entirely and just use search params.\n */\nexport const setCacheBustingSearchParam = (\n  url: URL,\n  headers: RequestHeaders\n): void => {\n  const uniqueCacheKey = computeCacheBustingSearchParam(\n    headers[NEXT_ROUTER_PREFETCH_HEADER],\n    headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER],\n    headers[NEXT_ROUTER_STATE_TREE_HEADER],\n    headers[NEXT_URL]\n  )\n  setCacheBustingSearchParamWithHash(url, uniqueCacheKey)\n}\n\n/**\n * Sets a cache-busting search parameter on a URL using a provided hash value.\n *\n * This function performs the same logic as `setCacheBustingSearchParam` but accepts\n * a pre-computed hash instead of computing it from headers.\n *\n * Example:\n * URL before: https://example.com/path?query=1\n * hash: \"abc123\"\n * URL after: https://example.com/path?query=1&_rsc=abc123\n *\n * If the hash is null, we will set `_rsc` search param without a value.\n * Like this: https://example.com/path?query=1&_rsc\n *\n * Note: This function mutates the input URL directly and does not return anything.\n */\nexport const setCacheBustingSearchParamWithHash = (\n  url: URL,\n  hash: string\n): void => {\n  /**\n   * Note that we intentionally do not use `url.searchParams.set` here:\n   *\n   * const url = new URL('https://example.com/search?q=custom%20spacing');\n   * url.searchParams.set('_rsc', 'abc123');\n   * console.log(url.toString()); // Outputs: https://example.com/search?q=custom+spacing&_rsc=abc123\n   *                                                                             ^ <--- this is causing confusion\n   * This is in fact intended based on https://url.spec.whatwg.org/#interface-urlsearchparams, but\n   * we want to preserve the %20 as %20 if that's what the user passed in, hence the custom\n   * logic below.\n   */\n  const existingSearch = url.search\n  const rawQuery = existingSearch.startsWith('?')\n    ? existingSearch.slice(1)\n    : existingSearch\n\n  // Always remove any existing cache busting param and add a fresh one to ensure\n  // we have the correct value based on current request headers\n  const pairs = rawQuery\n    .split('&')\n    .filter((pair) => pair && !pair.startsWith(`${NEXT_RSC_UNION_QUERY}=`))\n\n  if (hash.length > 0) {\n    pairs.push(`${NEXT_RSC_UNION_QUERY}=${hash}`)\n  } else {\n    pairs.push(`${NEXT_RSC_UNION_QUERY}`)\n  }\n  url.search = pairs.length ? `?${pairs.join('&')}` : ''\n}\n"], "names": ["computeCacheBustingSearchParam", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_URL", "NEXT_RSC_UNION_QUERY", "setCacheBustingSearchParam", "url", "headers", "unique<PERSON><PERSON><PERSON><PERSON>", "setCacheBustingSearchParamWithHash", "hash", "existingSearch", "search", "<PERSON><PERSON><PERSON><PERSON>", "startsWith", "slice", "pairs", "split", "filter", "pair", "length", "push", "join"], "mappings": ";;;;;;AAEA,SAASA,8BAA8B,QAAQ,8DAA6D;AAC5G,SACEC,2BAA2B,EAC3BC,mCAAmC,EACnCC,6BAA6B,EAC7BC,QAAQ,EACRC,oBAAoB,QACf,wBAAuB;AAT9B;;;AA4BO,MAAMC,6BAA6B,CACxCC,KACAC;IAEA,MAAMC,qBAAiBT,0cAAAA,EACrBQ,OAAO,CAACP,gbAAAA,CAA4B,EACpCO,OAAO,CAACN,wbAAAA,CAAoC,EAC5CM,OAAO,CAACL,kbAAAA,CAA8B,EACtCK,OAAO,CAACJ,6ZAAAA,CAAS;IAEnBM,mCAAmCH,KAAKE;AAC1C,EAAC;AAkBM,MAAMC,qCAAqC,CAChDH,KACAI;IAEA;;;;;;;;;;GAUC,GACD,MAAMC,iBAAiBL,IAAIM,MAAM;IACjC,MAAMC,WAAWF,eAAeG,UAAU,CAAC,OACvCH,eAAeI,KAAK,CAAC,KACrBJ;IAEJ,+EAA+E;IAC/E,6DAA6D;IAC7D,MAAMK,QAAQH,SACXI,KAAK,CAAC,KACNC,MAAM,CAAC,CAACC,OAASA,QAAQ,CAACA,KAAKL,UAAU,CAAC,GAAGV,yaAAAA,CAAqB,CAAC,CAAC;IAEvE,IAAIM,KAAKU,MAAM,GAAG,GAAG;QACnBJ,MAAMK,IAAI,CAAC,GAAGjB,yaAAAA,CAAqB,CAAC,EAAEM,MAAM;IAC9C,OAAO;QACLM,MAAMK,IAAI,CAAC,GAAGjB,yaAAAA,EAAsB;IACtC;IACAE,IAAIM,MAAM,GAAGI,MAAMI,MAAM,GAAG,CAAC,CAAC,EAAEJ,MAAMM,IAAI,CAAC,MAAM,GAAG;AACtD,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1046, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/router-reducer/fetch-server-response.ts"], "sourcesContent": ["'use client'\n\n// TODO: Explicitly import from client.browser\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport {\n  createFromReadableStream as createFromReadableStreamBrowser,\n  create<PERSON><PERSON><PERSON><PERSON>ch as createFromFetchBrowser,\n} from 'react-server-dom-webpack/client'\n\nimport type {\n  FlightRouterState,\n  NavigationFlightResponse,\n} from '../../../shared/lib/app-router-types'\n\nimport type { NEXT_ROUTER_SEGMENT_PREFETCH_HEADER } from '../app-router-headers'\nimport {\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_RSC_UNION_QUERY,\n  NEXT_URL,\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n  NEXT_HTML_REQUEST_ID_HEADER,\n  NEXT_REQUEST_ID_HEADER,\n} from '../app-router-headers'\nimport { callServer } from '../../app-call-server'\nimport { findSourceMapURL } from '../../app-find-source-map-url'\nimport { PrefetchKind } from './router-reducer-types'\nimport {\n  normalizeFlightData,\n  prepareFlightRouterStateForRequest,\n  type NormalizedFlightData,\n} from '../../flight-data-helpers'\nimport { getAppBuildId } from '../../app-build-id'\nimport { setCacheBustingSearchParam } from './set-cache-busting-search-param'\nimport {\n  getRenderedSearch,\n  urlToUrlWithoutFlightMarker,\n} from '../../route-params'\nimport type { NormalizedSearch } from '../segment-cache'\n\nconst createFromReadableStream =\n  createFromReadableStreamBrowser as (typeof import('react-server-dom-webpack/client.browser'))['createFromReadableStream']\nconst createFromFetch =\n  createFromFetchBrowser as (typeof import('react-server-dom-webpack/client.browser'))['createFromFetch']\n\nlet createDebugChannel:\n  | typeof import('../../dev/debug-channel').createDebugChannel\n  | undefined\n\nif (\n  process.env.NODE_ENV !== 'production' &&\n  process.env.__NEXT_REACT_DEBUG_CHANNEL\n) {\n  createDebugChannel = (\n    require('../../dev/debug-channel') as typeof import('../../dev/debug-channel')\n  ).createDebugChannel\n}\n\nexport interface FetchServerResponseOptions {\n  readonly flightRouterState: FlightRouterState\n  readonly nextUrl: string | null\n  readonly prefetchKind?: PrefetchKind\n  readonly isHmrRefresh?: boolean\n}\n\ntype SpaFetchServerResponseResult = {\n  flightData: NormalizedFlightData[]\n  canonicalUrl: URL\n  renderedSearch: NormalizedSearch\n  couldBeIntercepted: boolean\n  prerendered: boolean\n  postponed: boolean\n  staleTime: number\n  debugInfo: Array<any> | null\n}\n\ntype MpaFetchServerResponseResult = string\n\nexport type FetchServerResponseResult =\n  | MpaFetchServerResponseResult\n  | SpaFetchServerResponseResult\n\nexport type RequestHeaders = {\n  [RSC_HEADER]?: '1'\n  [NEXT_ROUTER_STATE_TREE_HEADER]?: string\n  [NEXT_URL]?: string\n  [NEXT_ROUTER_PREFETCH_HEADER]?: '1' | '2'\n  [NEXT_ROUTER_SEGMENT_PREFETCH_HEADER]?: string\n  'x-deployment-id'?: string\n  [NEXT_HMR_REFRESH_HEADER]?: '1'\n  // A header that is only added in test mode to assert on fetch priority\n  'Next-Test-Fetch-Priority'?: RequestInit['priority']\n  [NEXT_HTML_REQUEST_ID_HEADER]?: string // dev-only\n  [NEXT_REQUEST_ID_HEADER]?: string // dev-only\n}\n\nfunction doMpaNavigation(url: string): FetchServerResponseResult {\n  return urlToUrlWithoutFlightMarker(new URL(url, location.origin)).toString()\n}\n\nlet abortController = new AbortController()\n\nif (typeof window !== 'undefined') {\n  // Abort any in-flight requests when the page is unloaded, e.g. due to\n  // reloading the page or performing hard navigations. This allows us to ignore\n  // what would otherwise be a thrown TypeError when the browser cancels the\n  // requests.\n  window.addEventListener('pagehide', () => {\n    abortController.abort()\n  })\n\n  // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n  // and the JavaScript execution context is restored by the browser.\n  window.addEventListener('pageshow', () => {\n    abortController = new AbortController()\n  })\n}\n\n/**\n * Fetch the flight data for the provided url. Takes in the current router state\n * to decide what to render server-side.\n */\nexport async function fetchServerResponse(\n  url: URL,\n  options: FetchServerResponseOptions\n): Promise<FetchServerResponseResult> {\n  const { flightRouterState, nextUrl, prefetchKind } = options\n\n  const headers: RequestHeaders = {\n    // Enable flight response\n    [RSC_HEADER]: '1',\n    // Provide the current router state\n    [NEXT_ROUTER_STATE_TREE_HEADER]: prepareFlightRouterStateForRequest(\n      flightRouterState,\n      options.isHmrRefresh\n    ),\n  }\n\n  /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */\n  if (prefetchKind === PrefetchKind.AUTO) {\n    headers[NEXT_ROUTER_PREFETCH_HEADER] = '1'\n  }\n\n  if (process.env.NODE_ENV === 'development' && options.isHmrRefresh) {\n    headers[NEXT_HMR_REFRESH_HEADER] = '1'\n  }\n\n  if (nextUrl) {\n    headers[NEXT_URL] = nextUrl\n  }\n\n  // In static export mode, we need to modify the URL to request the .txt file,\n  // but we should preserve the original URL for the canonical URL and error handling.\n  const originalUrl = url\n\n  try {\n    // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n    // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n    // Otherwise, all other prefetches are sent with a \"low\" priority.\n    // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n    const fetchPriority = prefetchKind\n      ? prefetchKind === PrefetchKind.TEMPORARY\n        ? 'high'\n        : 'low'\n      : 'auto'\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        // In \"output: export\" mode, we can't rely on headers to distinguish\n        // between HTML and RSC requests. Instead, we append an extra prefix\n        // to the request.\n        url = new URL(url)\n        if (url.pathname.endsWith('/')) {\n          url.pathname += 'index.txt'\n        } else {\n          url.pathname += '.txt'\n        }\n      }\n    }\n\n    // Typically, during a navigation, we decode the response using Flight's\n    // `createFromFetch` API, which accepts a `fetch` promise.\n    // TODO: Remove this check once the old PPR flag is removed\n    const isLegacyPPR =\n      process.env.__NEXT_PPR && !process.env.__NEXT_CACHE_COMPONENTS\n    const shouldImmediatelyDecode = !isLegacyPPR\n    const res = await createFetch<NavigationFlightResponse>(\n      url,\n      headers,\n      fetchPriority,\n      shouldImmediatelyDecode,\n      abortController.signal\n    )\n\n    const responseUrl = urlToUrlWithoutFlightMarker(new URL(res.url))\n    const canonicalUrl = res.redirected ? responseUrl : originalUrl\n\n    const contentType = res.headers.get('content-type') || ''\n    const interception = !!res.headers.get('vary')?.includes(NEXT_URL)\n    const postponed = !!res.headers.get(NEXT_DID_POSTPONE_HEADER)\n    const staleTimeHeaderSeconds = res.headers.get(\n      NEXT_ROUTER_STALE_TIME_HEADER\n    )\n    const staleTime =\n      staleTimeHeaderSeconds !== null\n        ? parseInt(staleTimeHeaderSeconds, 10) * 1000\n        : -1\n    let isFlightResponse = contentType.startsWith(RSC_CONTENT_TYPE_HEADER)\n\n    if (process.env.NODE_ENV === 'production') {\n      if (process.env.__NEXT_CONFIG_OUTPUT === 'export') {\n        if (!isFlightResponse) {\n          isFlightResponse = contentType.startsWith('text/plain')\n        }\n      }\n    }\n\n    // If fetch returns something different than flight response handle it like a mpa navigation\n    // If the fetch was not 200, we also handle it like a mpa navigation\n    if (!isFlightResponse || !res.ok || !res.body) {\n      // in case the original URL came with a hash, preserve it before redirecting to the new URL\n      if (url.hash) {\n        responseUrl.hash = url.hash\n      }\n\n      return doMpaNavigation(responseUrl.toString())\n    }\n\n    // We may navigate to a page that requires a different Webpack runtime.\n    // In prod, every page will have the same Webpack runtime.\n    // In dev, the Webpack runtime is minimal for each page.\n    // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n    // TODO: This needs to happen in the Flight Client.\n    // Or Webpack needs to include the runtime update in the Flight response as\n    // a blocking script.\n    if (process.env.NODE_ENV !== 'production' && !process.env.TURBOPACK) {\n      await (\n        require('../../dev/hot-reloader/app/hot-reloader-app') as typeof import('../../dev/hot-reloader/app/hot-reloader-app')\n      ).waitForWebpackRuntimeHotUpdate()\n    }\n\n    let flightResponsePromise = res.flightResponse\n    if (flightResponsePromise === null) {\n      // Typically, `createFetch` would have already started decoding the\n      // Flight response. If it hasn't, though, we need to decode it now.\n      // TODO: This should only be reachable if legacy PPR is enabled (i.e. PPR\n      // without Cache Components). Remove this branch once legacy PPR\n      // is deleted.\n      const flightStream = postponed\n        ? createUnclosingPrefetchStream(res.body)\n        : res.body\n      flightResponsePromise =\n        createFromNextReadableStream<NavigationFlightResponse>(\n          flightStream,\n          headers\n        )\n    }\n\n    const flightResponse = await flightResponsePromise\n\n    if (getAppBuildId() !== flightResponse.b) {\n      return doMpaNavigation(res.url)\n    }\n\n    const normalizedFlightData = normalizeFlightData(flightResponse.f)\n    if (typeof normalizedFlightData === 'string') {\n      return doMpaNavigation(normalizedFlightData)\n    }\n\n    return {\n      flightData: normalizedFlightData,\n      canonicalUrl: canonicalUrl,\n      renderedSearch: getRenderedSearch(res),\n      couldBeIntercepted: interception,\n      prerendered: flightResponse.S,\n      postponed,\n      staleTime,\n      debugInfo: flightResponsePromise._debugInfo ?? null,\n    }\n  } catch (err) {\n    if (!abortController.signal.aborted) {\n      console.error(\n        `Failed to fetch RSC payload for ${originalUrl}. Falling back to browser navigation.`,\n        err\n      )\n    }\n\n    // If fetch fails handle it like a mpa navigation\n    // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n    // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n    return originalUrl.toString()\n  }\n}\n\n// This is a subset of the standard Response type. We use a custom type for\n// this so we can limit which details about the response leak into the rest of\n// the codebase. For example, there's some custom logic for manually following\n// redirects, so \"redirected\" in this type could be a composite of multiple\n// browser fetch calls; however, this fact should not leak to the caller.\nexport type RSCResponse<T> = {\n  ok: boolean\n  redirected: boolean\n  headers: Headers\n  body: ReadableStream<Uint8Array> | null\n  status: number\n  url: string\n  flightResponse: (Promise<T> & { _debugInfo?: Array<any> }) | null\n}\n\nexport async function createFetch<T>(\n  url: URL,\n  headers: RequestHeaders,\n  fetchPriority: 'auto' | 'high' | 'low' | null,\n  shouldImmediatelyDecode: boolean,\n  signal?: AbortSignal\n): Promise<RSCResponse<T>> {\n  // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n  // cache busting search param) from the request so they're\n  // maximally cacheable.\n\n  if (process.env.__NEXT_TEST_MODE && fetchPriority !== null) {\n    headers['Next-Test-Fetch-Priority'] = fetchPriority\n  }\n\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    headers['x-deployment-id'] = process.env.NEXT_DEPLOYMENT_ID\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (self.__next_r) {\n      headers[NEXT_HTML_REQUEST_ID_HEADER] = self.__next_r\n    }\n\n    // Create a new request ID for the server action request. The server uses\n    // this to tag debug information sent via WebSocket to the client, which\n    // then routes those chunks to the debug channel associated with this ID.\n    headers[NEXT_REQUEST_ID_HEADER] = crypto\n      .getRandomValues(new Uint32Array(1))[0]\n      .toString(16)\n  }\n\n  const fetchOptions: RequestInit = {\n    // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n    credentials: 'same-origin',\n    headers,\n    priority: fetchPriority || undefined,\n    signal,\n  }\n  // `fetchUrl` is slightly different from `url` because we add a cache-busting\n  // search param to it. This should not leak outside of this function, so we\n  // track them separately.\n  let fetchUrl = new URL(url)\n  setCacheBustingSearchParam(fetchUrl, headers)\n  let fetchPromise = fetch(fetchUrl, fetchOptions)\n  // Immediately pass the fetch promise to the Flight client so that the debug\n  // info includes the latency from the client to the server. The internal timer\n  // in React starts as soon as `createFromFetch` is called.\n  //\n  // The only case where we don't do this is during a prefetch, because we have\n  // to do some extra processing of the response stream (see\n  // `createUnclosingPrefetchStream`). But this is fine, because a top-level\n  // prefetch response never blocks a navigation; if it hasn't already been\n  // written into the cache by the time the navigation happens, the router will\n  // go straight to a dynamic request.\n  let flightResponsePromise = shouldImmediatelyDecode\n    ? createFromNextFetch<T>(fetchPromise, headers)\n    : null\n  let browserResponse = await fetchPromise\n\n  // If the server responds with a redirect (e.g. 307), and the redirected\n  // location does not contain the cache busting search param set in the\n  // original request, the response is likely invalid — when following the\n  // redirect, the browser forwards the request headers, but since the cache\n  // busting search param is missing, the server will reject the request due to\n  // a mismatch.\n  //\n  // Ideally, we would be able to intercept the redirect response and perform it\n  // manually, instead of letting the browser automatically follow it, but this\n  // is not allowed by the fetch API.\n  //\n  // So instead, we must \"replay\" the redirect by fetching the new location\n  // again, but this time we'll append the cache busting search param to prevent\n  // a mismatch.\n  //\n  // TODO: We can optimize Next.js's built-in middleware APIs by returning a\n  // custom status code, to prevent the browser from automatically following it.\n  //\n  // This does not affect Server Action-based redirects; those are encoded\n  // differently, as part of the Flight body. It only affects redirects that\n  // occur in a middleware or a third-party proxy.\n\n  let redirected = browserResponse.redirected\n  if (process.env.__NEXT_CLIENT_VALIDATE_RSC_REQUEST_HEADERS) {\n    // This is to prevent a redirect loop. Same limit used by Chrome.\n    const MAX_REDIRECTS = 20\n    for (let n = 0; n < MAX_REDIRECTS; n++) {\n      if (!browserResponse.redirected) {\n        // The server did not perform a redirect.\n        break\n      }\n      const responseUrl = new URL(browserResponse.url, fetchUrl)\n      if (responseUrl.origin !== fetchUrl.origin) {\n        // The server redirected to an external URL. The rest of the logic below\n        // is not relevant, because it only applies to internal redirects.\n        break\n      }\n      if (\n        responseUrl.searchParams.get(NEXT_RSC_UNION_QUERY) ===\n        fetchUrl.searchParams.get(NEXT_RSC_UNION_QUERY)\n      ) {\n        // The redirected URL already includes the cache busting search param.\n        // This was probably intentional. Regardless, there's no reason to\n        // issue another request to this URL because it already has the param\n        // value that we would have added below.\n        break\n      }\n      // The RSC request was redirected. Assume the response is invalid.\n      //\n      // Append the cache busting search param to the redirected URL and\n      // fetch again.\n      // TODO: We should abort the previous request.\n      fetchUrl = new URL(responseUrl)\n      setCacheBustingSearchParam(fetchUrl, headers)\n      fetchPromise = fetch(fetchUrl, fetchOptions)\n      flightResponsePromise = shouldImmediatelyDecode\n        ? createFromNextFetch<T>(fetchPromise, headers)\n        : null\n      browserResponse = await fetchPromise\n      // We just performed a manual redirect, so this is now true.\n      redirected = true\n    }\n  }\n\n  // Remove the cache busting search param from the response URL, to prevent it\n  // from leaking outside of this function.\n  const responseUrl = new URL(browserResponse.url, fetchUrl)\n  responseUrl.searchParams.delete(NEXT_RSC_UNION_QUERY)\n\n  const rscResponse: RSCResponse<T> = {\n    url: responseUrl.href,\n\n    // This is true if any redirects occurred, either automatically by the\n    // browser, or manually by us. So it's different from\n    // `browserResponse.redirected`, which only tells us whether the browser\n    // followed a redirect, and only for the last response in the chain.\n    redirected,\n\n    // These can be copied from the last browser response we received. We\n    // intentionally only expose the subset of fields that are actually used\n    // elsewhere in the codebase.\n    ok: browserResponse.ok,\n    headers: browserResponse.headers,\n    body: browserResponse.body,\n    status: browserResponse.status,\n\n    // This is the exact promise returned by `createFromFetch`. It contains\n    // debug information that we need to transfer to any derived promises that\n    // are later rendered by React.\n    flightResponse: flightResponsePromise,\n  }\n\n  return rscResponse\n}\n\nexport function createFromNextReadableStream<T>(\n  flightStream: ReadableStream<Uint8Array>,\n  requestHeaders: RequestHeaders\n): Promise<T> {\n  return createFromReadableStream(flightStream, {\n    callServer,\n    findSourceMapURL,\n    debugChannel: createDebugChannel && createDebugChannel(requestHeaders),\n  })\n}\n\nfunction createFromNextFetch<T>(\n  promiseForResponse: Promise<Response>,\n  requestHeaders: RequestHeaders\n): Promise<T> & { _debugInfo?: Array<any> } {\n  return createFromFetch(promiseForResponse, {\n    callServer,\n    findSourceMapURL,\n    debugChannel: createDebugChannel && createDebugChannel(requestHeaders),\n  })\n}\n\nfunction createUnclosingPrefetchStream(\n  originalFlightStream: ReadableStream<Uint8Array>\n): ReadableStream<Uint8Array> {\n  // When PPR is enabled, prefetch streams may contain references that never\n  // resolve, because that's how we encode dynamic data access. In the decoded\n  // object returned by the Flight client, these are reified into hanging\n  // promises that suspend during render, which is effectively what we want.\n  // The UI resolves when it switches to the dynamic data stream\n  // (via useDeferredValue(dynamic, static)).\n  //\n  // However, the Flight implementation currently errors if the server closes\n  // the response before all the references are resolved. As a cheat to work\n  // around this, we wrap the original stream in a new stream that never closes,\n  // and therefore doesn't error.\n  const reader = originalFlightStream.getReader()\n  return new ReadableStream({\n    async pull(controller) {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (!done) {\n          // Pass to the target stream and keep consuming the Flight response\n          // from the server.\n          controller.enqueue(value)\n          continue\n        }\n        // The server stream has closed. Exit, but intentionally do not close\n        // the target stream.\n        return\n      }\n    },\n  })\n}\n"], "names": ["createFromReadableStream", "createFromReadableStreamBrowser", "createFromFetch", "createFromFetchBrowser", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_RSC_UNION_QUERY", "NEXT_URL", "RSC_HEADER", "RSC_CONTENT_TYPE_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_HTML_REQUEST_ID_HEADER", "NEXT_REQUEST_ID_HEADER", "callServer", "findSourceMapURL", "PrefetchKind", "normalizeFlightData", "prepareFlightRouterStateForRequest", "getAppBuildId", "setCacheBustingSearchParam", "getRenderedSearch", "urlToUrlWithoutFlightMarker", "createDebugChannel", "process", "env", "NODE_ENV", "__NEXT_REACT_DEBUG_CHANNEL", "require", "doMpaNavigation", "url", "URL", "location", "origin", "toString", "abortController", "AbortController", "window", "addEventListener", "abort", "fetchServerResponse", "options", "flightRouterState", "nextUrl", "prefetchKind", "headers", "isHmrRefresh", "AUTO", "originalUrl", "fetchPriority", "TEMPORARY", "__NEXT_CONFIG_OUTPUT", "pathname", "endsWith", "isLegacyPPR", "__NEXT_PPR", "__NEXT_CACHE_COMPONENTS", "shouldImmediatelyDecode", "res", "createFetch", "signal", "responseUrl", "canonicalUrl", "redirected", "contentType", "get", "interception", "includes", "postponed", "staleTimeHeaderSeconds", "staleTime", "parseInt", "isFlightResponse", "startsWith", "ok", "body", "hash", "TURBOPACK", "waitForWebpackRuntimeHotUpdate", "flightResponsePromise", "flightResponse", "flightStream", "createUnclosingPrefetchStream", "createFromNextReadableStream", "b", "normalizedFlightData", "f", "flightData", "renderedSearch", "couldBeIntercepted", "prerendered", "S", "debugInfo", "_debugInfo", "err", "aborted", "console", "error", "__NEXT_TEST_MODE", "NEXT_DEPLOYMENT_ID", "self", "__next_r", "crypto", "getRandomValues", "Uint32Array", "fetchOptions", "credentials", "priority", "undefined", "fetchUrl", "fetchPromise", "fetch", "createFromNextFetch", "browserResponse", "__NEXT_CLIENT_VALIDATE_RSC_REQUEST_HEADERS", "MAX_REDIRECTS", "n", "searchParams", "delete", "rscResponse", "href", "status", "requestHeaders", "debugChannel", "promiseForResponse", "originalFlightStream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "pull", "controller", "done", "value", "read", "enqueue"], "mappings": ";;;;;;;;AAEA,8CAA8C;AAC9C,6DAA6D;AAC7D,SACEA,4BAA4BC,+BAA+B,EAC3DC,mBAAmBC,sBAAsB,QACpC,kCAAiC;AAQxC,SACEC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,oBAAoB,EACpBC,QAAQ,EACRC,UAAU,EACVC,uBAAuB,EACvBC,uBAAuB,EACvBC,wBAAwB,EACxBC,6BAA6B,EAC7BC,2BAA2B,EAC3BC,sBAAsB,QACjB,wBAAuB;AAC9B,SAASC,UAAU,QAAQ,wBAAuB;AAClD,SAASC,gBAAgB,QAAQ,gCAA+B;AAChE,SAASC,YAAY,QAAQ,yBAAwB;AACrD,SACEC,mBAAmB,EACnBC,kCAAkC,QAE7B,4BAA2B;AAClC,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,0BAA0B,QAAQ,mCAAkC;AAC7E,SACEC,iBAAiB,EACjBC,2BAA2B,QACtB,qBAAoB;AAzC3B;;;;;;;;;;AA4CA,MAAMvB,2BACJC,meAAAA;AACF,MAAMC,kBACJC,0dAAAA;AAEF,IAAIqB;AAIJ,IACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBF,QAAQC,GAAG,CAACE,0BAA0B,EACtC;;AA4CF,SAASE,gBAAgBC,GAAW;IAClC,OAAOR,6ZAAAA,EAA4B,IAAIS,IAAID,KAAKE,SAASC,MAAM,GAAGC,QAAQ;AAC5E;AAEA,IAAIC,kBAAkB,IAAIC;AAE1B,IAAI,OAAOC,WAAW,aAAa;;AAoB5B,eAAeG,oBACpBV,GAAQ,EACRW,OAAmC;IAEnC,MAAM,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,YAAY,EAAE,GAAGH;IAErD,MAAMI,UAA0B;QAC9B,yBAAyB;QACzB,CAACtC,+ZAAAA,CAAW,EAAE;QACd,mCAAmC;QACnC,CAACH,kbAAAA,CAA8B,MAAEc,0aAAAA,EAC/BwB,mBACAD,QAAQK,YAAY;IAExB;IAEA;;;;;GAKC,GACD,IAAIF,iBAAiB5B,wbAAAA,CAAa+B,IAAI,EAAE;QACtCF,OAAO,CAAC1C,gbAAAA,CAA4B,GAAG;IACzC;IAEA,IAAIqB,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAiBe,QAAQK,YAAY,EAAE;QAClED,OAAO,CAACpC,4aAAAA,CAAwB,GAAG;IACrC;IAEA,IAAIkC,SAAS;QACXE,OAAO,CAACvC,6ZAAAA,CAAS,GAAGqC;IACtB;IAEA,6EAA6E;IAC7E,oFAAoF;IACpF,MAAMK,cAAclB;IAEpB,IAAI;QACF,wHAAwH;QACxH,4HAA4H;QAC5H,kEAAkE;QAClE,yHAAyH;QACzH,MAAMmB,gBAAgBL,eAClBA,iBAAiB5B,wbAAAA,CAAakC,SAAS,GACrC,SACA,QACF;QAEJ,IAAI1B,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;;QAc3C,wEAAwE;QACxE,0DAA0D;QAC1D,2DAA2D;QAC3D,MAAM4B,cACJ9B,QAAQC,GAAG,CAAC8B,UAAU,qBAAI,CAAC/B,QAAQC,GAAG,CAAC+B,uBAAuB;QAChE,MAAMC,0BAA0B,CAACH;QACjC,MAAMI,MAAM,MAAMC,YAChB7B,KACAe,SACAI,eACAQ,yBACAtB,gBAAgByB,MAAM;QAGxB,MAAMC,kBAAcvC,yZAAAA,EAA4B,IAAIS,IAAI2B,IAAI5B,GAAG;QAC/D,MAAMgC,eAAeJ,IAAIK,UAAU,GAAGF,cAAcb;QAEpD,MAAMgB,cAAcN,IAAIb,OAAO,CAACoB,GAAG,CAAC,mBAAmB;QACvD,MAAMC,eAAe,CAAC,CAACR,IAAIb,OAAO,CAACoB,GAAG,CAAC,SAASE,SAAS7D,6ZAAAA;QACzD,MAAM8D,YAAY,CAAC,CAACV,IAAIb,OAAO,CAACoB,GAAG,CAACvD,6aAAAA;QACpC,MAAM2D,yBAAyBX,IAAIb,OAAO,CAACoB,GAAG,CAC5CtD,kbAAAA;QAEF,MAAM2D,YACJD,2BAA2B,OACvBE,SAASF,wBAAwB,MAAM,OACvC,CAAC;QACP,IAAIG,mBAAmBR,YAAYS,UAAU,CAACjE,4aAAAA;QAE9C,IAAIgB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;;QAQ3C,4FAA4F;QAC5F,oEAAoE;QACpE,IAAI,CAAC8C,oBAAoB,CAACd,IAAIgB,EAAE,IAAI,CAAChB,IAAIiB,IAAI,EAAE;YAC7C,2FAA2F;YAC3F,IAAI7C,IAAI8C,IAAI,EAAE;gBACZf,YAAYe,IAAI,GAAG9C,IAAI8C,IAAI;YAC7B;YAEA,OAAO/C,gBAAgBgC,YAAY3B,QAAQ;QAC7C;QAEA,uEAAuE;QACvE,0DAA0D;QAC1D,wDAAwD;QACxD,oGAAoG;QACpG,mDAAmD;QACnD,2EAA2E;QAC3E,qBAAqB;QACrB,IAAIV,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgB,CAACF,QAAQC,GAAG,CAACoD,SAAS,EAAE;;QAMrE,IAAIE,wBAAwBrB,IAAIsB,cAAc;QAC9C,IAAID,0BAA0B,MAAM;YAClC,mEAAmE;YACnE,mEAAmE;YACnE,yEAAyE;YACzE,gEAAgE;YAChE,cAAc;YACd,MAAME,eAAeb,YACjBc,8BAA8BxB,IAAIiB,IAAI,IACtCjB,IAAIiB,IAAI;YACZI,wBACEI,6BACEF,cACApC;QAEN;QAEA,MAAMmC,iBAAiB,MAAMD;QAE7B,QAAI5D,8YAAAA,QAAoB6D,eAAeI,CAAC,EAAE;YACxC,OAAOvD,gBAAgB6B,IAAI5B,GAAG;QAChC;QAEA,MAAMuD,2BAAuBpE,2ZAAAA,EAAoB+D,eAAeM,CAAC;QACjE,IAAI,OAAOD,yBAAyB,UAAU;YAC5C,OAAOxD,gBAAgBwD;QACzB;QAEA,OAAO;YACLE,YAAYF;YACZvB,cAAcA;YACd0B,oBAAgBnE,+YAAAA,EAAkBqC;YAClC+B,oBAAoBvB;YACpBwB,aAAaV,eAAeW,CAAC;YAC7BvB;YACAE;YACAsB,WAAWb,sBAAsBc,UAAU,IAAI;QACjD;IACF,EAAE,OAAOC,KAAK;QACZ,IAAI,CAAC3D,gBAAgByB,MAAM,CAACmC,OAAO,EAAE;YACnCC,QAAQC,KAAK,CACX,CAAC,gCAAgC,EAAEjD,YAAY,qCAAqC,CAAC,EACrF8C;QAEJ;QAEA,iDAAiD;QACjD,qHAAqH;QACrH,iGAAiG;QACjG,OAAO9C,YAAYd,QAAQ;IAC7B;AACF;AAiBO,eAAeyB,YACpB7B,GAAQ,EACRe,OAAuB,EACvBI,aAA6C,EAC7CQ,uBAAgC,EAChCG,MAAoB;IAEpB,6EAA6E;IAC7E,0DAA0D;IAC1D,uBAAuB;IAEvB,IAAIpC,QAAQC,GAAG,CAACyE,gBAAgB,IAAIjD,kBAAkB,MAAM;;IAI5D,IAAIzB,QAAQC,GAAG,CAAC0E,kBAAkB,EAAE;;IAIpC,IAAI3E,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAI0E,KAAKC,QAAQ,EAAE;YACjBxD,OAAO,CAACjC,gbAAAA,CAA4B,GAAGwF,KAAKC,QAAQ;QACtD;QAEA,yEAAyE;QACzE,wEAAwE;QACxE,yEAAyE;QACzExD,OAAO,CAAChC,2aAAAA,CAAuB,GAAGyF,OAC/BC,eAAe,CAAC,IAAIC,YAAY,GAAG,CAAC,EAAE,CACtCtE,QAAQ,CAAC;IACd;IAEA,MAAMuE,eAA4B;QAChC,wFAAwF;QACxFC,aAAa;QACb7D;QACA8D,UAAU1D,iBAAiB2D;QAC3BhD;IACF;IACA,6EAA6E;IAC7E,2EAA2E;IAC3E,yBAAyB;IACzB,IAAIiD,WAAW,IAAI9E,IAAID;QACvBV,sdAAAA,EAA2ByF,UAAUhE;IACrC,IAAIiE,eAAeC,MAAMF,UAAUJ;IACnC,4EAA4E;IAC5E,8EAA8E;IAC9E,0DAA0D;IAC1D,EAAE;IACF,6EAA6E;IAC7E,0DAA0D;IAC1D,0EAA0E;IAC1E,yEAAyE;IACzE,6EAA6E;IAC7E,oCAAoC;IACpC,IAAI1B,wBAAwBtB,0BACxBuD,oBAAuBF,cAAcjE,WACrC;IACJ,IAAIoE,kBAAkB,MAAMH;IAE5B,wEAAwE;IACxE,sEAAsE;IACtE,wEAAwE;IACxE,0EAA0E;IAC1E,6EAA6E;IAC7E,cAAc;IACd,EAAE;IACF,8EAA8E;IAC9E,6EAA6E;IAC7E,mCAAmC;IACnC,EAAE;IACF,yEAAyE;IACzE,8EAA8E;IAC9E,cAAc;IACd,EAAE;IACF,0EAA0E;IAC1E,8EAA8E;IAC9E,EAAE;IACF,wEAAwE;IACxE,0EAA0E;IAC1E,gDAAgD;IAEhD,IAAI/C,aAAakD,gBAAgBlD,UAAU;IAC3C,IAAIvC,QAAQC,GAAG,CAACyF,0CAA0C,EAAE;;IAyC5D,6EAA6E;IAC7E,yCAAyC;IACzC,MAAMrD,cAAc,IAAI9B,IAAIkF,gBAAgBnF,GAAG,EAAE+E;IACjDhD,YAAYwD,YAAY,CAACC,MAAM,CAACjH,yaAAAA;IAEhC,MAAMkH,cAA8B;QAClCzF,KAAK+B,YAAY2D,IAAI;QAErB,sEAAsE;QACtE,qDAAqD;QACrD,wEAAwE;QACxE,oEAAoE;QACpEzD;QAEA,qEAAqE;QACrE,wEAAwE;QACxE,6BAA6B;QAC7BW,IAAIuC,gBAAgBvC,EAAE;QACtB7B,SAASoE,gBAAgBpE,OAAO;QAChC8B,MAAMsC,gBAAgBtC,IAAI;QAC1B8C,QAAQR,gBAAgBQ,MAAM;QAE9B,uEAAuE;QACvE,0EAA0E;QAC1E,+BAA+B;QAC/BzC,gBAAgBD;IAClB;IAEA,OAAOwC;AACT;AAEO,SAASpC,6BACdF,YAAwC,EACxCyC,cAA8B;IAE9B,OAAO3H,yBAAyBkF,cAAc;oBAC5CnE,8YAAAA;0BACAC,kaAAAA;QACA4G,cAAcpG,sBAAsBA,mBAAmBmG;IACzD;AACF;AAEA,SAASV,oBACPY,kBAAqC,EACrCF,cAA8B;IAE9B,OAAOzH,gBAAgB2H,oBAAoB;oBACzC9G,8YAAAA;0BACAC,kaAAAA;QACA4G,cAAcpG,sBAAsBA,mBAAmBmG;IACzD;AACF;AAEA,SAASxC,8BACP2C,oBAAgD;IAEhD,0EAA0E;IAC1E,4EAA4E;IAC5E,uEAAuE;IACvE,0EAA0E;IAC1E,8DAA8D;IAC9D,2CAA2C;IAC3C,EAAE;IACF,2EAA2E;IAC3E,0EAA0E;IAC1E,8EAA8E;IAC9E,+BAA+B;IAC/B,MAAMC,SAASD,qBAAqBE,SAAS;IAC7C,OAAO,IAAIC,eAAe;QACxB,MAAMC,MAAKC,UAAU;YACnB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMN,OAAOO,IAAI;gBACzC,IAAI,CAACF,MAAM;oBACT,mEAAmE;oBACnE,mBAAmB;oBACnBD,WAAWI,OAAO,CAACF;oBACnB;gBACF;gBACA,qEAAqE;gBACrE,qBAAqB;gBACrB;YACF;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1330, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/unresolved-thenable.ts"], "sourcesContent": ["/**\n * Create a \"Thenable\" that does not resolve. This is used to suspend indefinitely when data is not available yet.\n */\nexport const unresolvedThenable = {\n  then: () => {},\n} as PromiseLike<void>\n"], "names": ["unresolvedThenable", "then"], "mappings": "AAAA;;CAEC,GACD;;;;AAAO,MAAMA,qBAAqB;IAChCC,MAAM,KAAO;AACf,EAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1343, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/route-modules/app-page/vendored/contexts/hooks-client-context.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].HooksClientContext\n"], "names": ["module", "exports", "require", "vendored", "HooksClientContext"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,oQACRC,QAAQ,CAAC,WAAW,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1348, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/navigation-untracked.ts"], "sourcesContent": ["import { useContext } from 'react'\nimport { PathnameContext } from '../../shared/lib/hooks-client-context.shared-runtime'\n\n/**\n * This checks to see if the current render has any unknown route parameters that\n * would cause the pathname to be dynamic. It's used to trigger a different\n * render path in the error boundary.\n *\n * @returns true if there are any unknown route parameters, false otherwise\n */\nfunction hasFallbackRouteParams(): boolean {\n  if (typeof window === 'undefined') {\n    // AsyncLocalStorage should not be included in the client bundle.\n    const { workUnitAsyncStorage } =\n      require('../../server/app-render/work-unit-async-storage.external') as typeof import('../../server/app-render/work-unit-async-storage.external')\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (!workUnitStore) return false\n\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        return fallbackParams ? fallbackParams.size > 0 : false\n      case 'prerender-legacy':\n      case 'request':\n      case 'prerender-runtime':\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n\n    return false\n  }\n\n  return false\n}\n\n/**\n * This returns a `null` value if there are any unknown route parameters, and\n * otherwise returns the pathname from the context. This is an alternative to\n * `usePathname` that is used in the error boundary to avoid rendering the\n * error boundary when there are unknown route parameters. This doesn't throw\n * when accessed with unknown route parameters.\n *\n * @returns\n *\n * @internal\n */\nexport function useUntrackedPathname(): string | null {\n  // If there are any unknown route parameters we would typically throw\n  // an error, but this internal method allows us to return a null value instead\n  // for components that do not propagate the pathname to the static shell (like\n  // the error boundary).\n  if (hasFallbackRouteParams()) {\n    return null\n  }\n\n  // This shouldn't cause any issues related to conditional rendering because\n  // the environment will be consistent for the render.\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useContext(PathnameContext)\n}\n"], "names": ["useContext", "PathnameContext", "hasFallbackRouteParams", "window", "workUnitAsyncStorage", "require", "workUnitStore", "getStore", "type", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "useUntrackedPathname"], "mappings": ";;;;AAAA,SAASA,UAAU,QAAQ,QAAO;AAClC,SAASC,eAAe,QAAQ,uDAAsD;;;AAEtF;;;;;;CAMC,GACD,SAASC;IACP,IAAI,OAAOC,WAAW,kBAAa;QACjC,iEAAiE;QACjE,MAAM,EAAEC,oBAAoB,EAAE,GAC5BC,QAAQ;QAEV,MAAMC,gBAAgBF,qBAAqBG,QAAQ;QACnD,IAAI,CAACD,eAAe,OAAO;QAE3B,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAMC,iBAAiBH,cAAcI,mBAAmB;gBACxD,OAAOD,iBAAiBA,eAAeE,IAAI,GAAG,IAAI;YACpD,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEL;QACJ;QAEA,OAAO;IACT;;;AAGF;AAaO,SAASM;IACd,qEAAqE;IACrE,8EAA8E;IAC9E,8EAA8E;IAC9E,uBAAuB;IACvB,IAAIV,0BAA0B;QAC5B,OAAO;IACT;IAEA,2EAA2E;IAC3E,qDAAqD;IACrD,sDAAsD;IACtD,WAAOF,6aAAAA,EAAWC,4cAAAA;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1406, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/http-access-fallback/http-access-fallback.ts"], "sourcesContent": ["export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n"], "names": ["HTTPAccessErrorStatus", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "ALLOWED_CODES", "Set", "Object", "values", "HTTP_ERROR_FALLBACK_ERROR_CODE", "isHTTPAccessFallbackError", "error", "digest", "prefix", "httpStatus", "split", "has", "Number", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "status"], "mappings": ";;;;;;;;;;;;AAAO,MAAMA,wBAAwB;IACnCC,WAAW;IACXC,WAAW;IACXC,cAAc;AAChB,EAAC;AAED,MAAMC,gBAAgB,IAAIC,IAAIC,OAAOC,MAAM,CAACP;AAErC,MAAMQ,iCAAiC,2BAA0B;AAajE,SAASC,0BACdC,KAAc;IAEd,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IACA,MAAM,CAACC,QAAQC,WAAW,GAAGH,MAAMC,MAAM,CAACG,KAAK,CAAC;IAEhD,OACEF,WAAWJ,kCACXJ,cAAcW,GAAG,CAACC,OAAOH;AAE7B;AAEO,SAASI,4BACdP,KAA8B;IAE9B,MAAMG,aAAaH,MAAMC,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC7C,OAAOE,OAAOH;AAChB;AAEO,SAASK,mCACdC,MAAc;IAEd,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE;IACJ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1452, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/redirect-status-code.ts"], "sourcesContent": ["export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n"], "names": ["RedirectStatusCode"], "mappings": ";;;;AAAO,IAAKA,qBAAAA,WAAAA,GAAAA,SAAAA,kBAAAA;;;;WAAAA;MAIX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1466, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/redirect-error.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n"], "names": ["RedirectStatusCode", "REDIRECT_ERROR_CODE", "RedirectType", "isRedirectError", "error", "digest", "split", "errorCode", "type", "destination", "slice", "join", "status", "at", "statusCode", "Number", "isNaN"], "mappings": ";;;;;;;;AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;;AAEpD,MAAMC,sBAAsB,gBAAe;AAE3C,IAAKC,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;WAAAA;MAGX;AAaM,SAASC,gBAAgBC,KAAc;IAC5C,IACE,OAAOA,UAAU,YACjBA,UAAU,QACV,CAAE,CAAA,YAAYA,KAAI,KAClB,OAAOA,MAAMC,MAAM,KAAK,UACxB;QACA,OAAO;IACT;IAEA,MAAMA,SAASD,MAAMC,MAAM,CAACC,KAAK,CAAC;IAClC,MAAM,CAACC,WAAWC,KAAK,GAAGH;IAC1B,MAAMI,cAAcJ,OAAOK,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;IAC7C,MAAMC,SAASP,OAAOQ,EAAE,CAAC,CAAC;IAE1B,MAAMC,aAAaC,OAAOH;IAE1B,OACEL,cAAcN,uBACbO,CAAAA,SAAS,aAAaA,SAAS,MAAK,KACrC,OAAOC,gBAAgB,YACvB,CAACO,MAAMF,eACPA,cAAcd,yaAAAA;AAElB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1497, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/is-next-router-error.ts"], "sourcesContent": ["import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n"], "names": ["isHTTPAccessFallbackError", "isRedirectError", "isNextRouterError", "error"], "mappings": ";;;;AAAA,SACEA,yBAAyB,QAEpB,8CAA6C;AACpD,SAASC,eAAe,QAA4B,mBAAkB;;;AAO/D,SAASC,kBACdC,KAAc;IAEd,WAAOF,6ZAAAA,EAAgBE,cAAUH,8cAAAA,EAA0BG;AAC7D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1512, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/nav-failure-handler.ts"], "sourcesContent": ["import { useEffect } from 'react'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\n\nexport function handleHardNavError(error: unknown): boolean {\n  if (\n    error &&\n    typeof window !== 'undefined' &&\n    window.next.__pendingUrl &&\n    createHrefFromUrl(new URL(window.location.href)) !==\n      createHrefFromUrl(window.next.__pendingUrl)\n  ) {\n    console.error(\n      `Error occurred during navigation, falling back to hard navigation`,\n      error\n    )\n    window.location.href = window.next.__pendingUrl.toString()\n    return true\n  }\n  return false\n}\n\nexport function useNavFailureHandler() {\n  if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n    // this if is only for DCE of the feature flag not conditional\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      const uncaughtExceptionHandler = (\n        evt: ErrorEvent | PromiseRejectionEvent\n      ) => {\n        const error = 'reason' in evt ? evt.reason : evt.error\n        // if we have an unhandled exception/rejection during\n        // a navigation we fall back to a hard navigation to\n        // attempt recovering to a good state\n        handleHardNavError(error)\n      }\n      window.addEventListener('unhandledrejection', uncaughtExceptionHandler)\n      window.addEventListener('error', uncaughtExceptionHandler)\n      return () => {\n        window.removeEventListener('error', uncaughtExceptionHandler)\n        window.removeEventListener(\n          'unhandledrejection',\n          uncaughtExceptionHandler\n        )\n      }\n    }, [])\n  }\n}\n"], "names": ["useEffect", "createHrefFromUrl", "handleHardNavError", "error", "window", "next", "__pendingUrl", "URL", "location", "href", "console", "toString", "useNavFailureHandler", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "uncaughtExceptionHandler", "evt", "reason", "addEventListener", "removeEventListener"], "mappings": ";;;;;;AAAA,SAASA,SAAS,QAAQ,QAAO;AACjC,SAASC,iBAAiB,QAAQ,wCAAuC;;;AAElE,SAASC,mBAAmBC,KAAc;IAC/C,IACEA,SACA,OAAOC,2CAAW,eAClBA,OAAOC,IAAI,CAACC,YAAY,QACxBL,gcAAAA,EAAkB,IAAIM,IAAIH,OAAOI,QAAQ,CAACC,IAAI,WAC5CR,gcAAAA,EAAkBG,OAAOC,IAAI,CAACC,YAAY,GAC5C;;IAQF,OAAO;AACT;AAEO,SAASM;IACd,IAAIC,QAAQC,GAAG,CAACC,4BAA4B,EAAE;;AAwBhD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1535, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/handle-isr-error.tsx"], "sourcesContent": ["const workAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n      ).workAsyncStorage\n    : undefined\n\n// if we are revalidating we want to re-throw the error so the\n// function crashes so we can maintain our previous cache\n// instead of caching the error page\nexport function HandleISRError({ error }: { error: any }) {\n  if (workAsyncStorage) {\n    const store = workAsyncStorage.getStore()\n    if (store?.isStaticGeneration) {\n      if (error) {\n        console.error(error)\n      }\n      throw error\n    }\n  }\n\n  return null\n}\n"], "names": ["workAsyncStorage", "window", "require", "undefined", "HandleISRError", "error", "store", "getStore", "isStaticGeneration", "console"], "mappings": ";;;;AAAA,MAAMA,mBACJ,OAAOC,WAAW,qBAEZC,QAAQ,uKACRF,gBAAgB,GAClBG;AAKC,SAASC,eAAe,EAAEC,KAAK,EAAkB;IACtD,IAAIL,kBAAkB;QACpB,MAAMM,QAAQN,iBAAiBO,QAAQ;QACvC,IAAID,OAAOE,oBAAoB;YAC7B,IAAIH,OAAO;gBACTI,QAAQJ,KAAK,CAACA;YAChB;YACA,MAAMA;QACR;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1556, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/router/utils/html-bots.ts"], "sourcesContent": ["// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\n// Note: The pattern [\\w-]+-Google captures all Google crawlers with \"-Google\" suffix (e.g., Mediapartners-Google, AdsBot-Google, Storebot-Google)\n// as well as crawlers starting with \"Google-\" (e.g., Google-PageRenderer, Google-InspectionTool)\nexport const HTML_LIMITED_BOT_UA_RE =\n  /[\\w-]+-Google|Google-[\\w-]+|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti|googleweblight/i\n"], "names": ["HTML_LIMITED_BOT_UA_RE"], "mappings": "AAAA,6GAA6G;AAC7G,sKAAsK;AACtK,kJAAkJ;AAClJ,iGAAiG;;;;;AAC1F,MAAMA,yBACX,sTAAqT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1569, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/router/utils/is-bot.ts"], "sourcesContent": ["import { HTML_LIMITED_BOT_UA_RE } from './html-bots'\n\n// Bot crawler that will spin up a headless browser and execute JS.\n// Only the main Googlebot search crawler executes JavaScript, not other Google crawlers.\n// x-ref: https://developers.google.com/search/docs/crawling-indexing/google-common-crawlers\n// This regex specifically matches \"Googlebot\" but NOT \"Mediapartners-Google\", \"AdsBot-Google\", etc.\nconst HEADLESS_BROWSER_BOT_UA_RE = /Googlebot(?!-)|Googlebot$/i\n\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source\n\nexport { HTML_LIMITED_BOT_UA_RE }\n\nfunction isDomBotUA(userAgent: string) {\n  return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent)\n}\n\nfunction isHtmlLimitedBotUA(userAgent: string) {\n  return HTML_LIMITED_BOT_UA_RE.test(userAgent)\n}\n\nexport function isBot(userAgent: string): boolean {\n  return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent)\n}\n\nexport function getBotType(userAgent: string): 'dom' | 'html' | undefined {\n  if (isDomBotUA(userAgent)) {\n    return 'dom'\n  }\n  if (isHtmlLimitedBotUA(userAgent)) {\n    return 'html'\n  }\n  return undefined\n}\n"], "names": ["HTML_LIMITED_BOT_UA_RE", "HEADLESS_BROWSER_BOT_UA_RE", "HTML_LIMITED_BOT_UA_RE_STRING", "source", "isDomBotUA", "userAgent", "test", "isHtmlLimitedBotUA", "isBot", "getBotType", "undefined"], "mappings": ";;;;;;;;AAAA,SAASA,sBAAsB,QAAQ,cAAa;;AAEpD,mEAAmE;AACnE,yFAAyF;AACzF,4FAA4F;AAC5F,oGAAoG;AACpG,MAAMC,6BAA6B;AAE5B,MAAMC,gCAAgCF,2aAAAA,CAAuBG,MAAM,CAAA;;AAI1E,SAASC,WAAWC,SAAiB;IACnC,OAAOJ,2BAA2BK,IAAI,CAACD;AACzC;AAEA,SAASE,mBAAmBF,SAAiB;IAC3C,OAAOL,2aAAAA,CAAuBM,IAAI,CAACD;AACrC;AAEO,SAASG,MAAMH,SAAiB;IACrC,OAAOD,WAAWC,cAAcE,mBAAmBF;AACrD;AAEO,SAASI,WAAWJ,SAAiB;IAC1C,IAAID,WAAWC,YAAY;QACzB,OAAO;IACT;IACA,IAAIE,mBAAmBF,YAAY;QACjC,OAAO;IACT;IACA,OAAOK;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1608, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React, { type JSX } from 'react'\nimport { useUntrackedPathname } from './navigation-untracked'\nimport { isNextRouterError } from './is-next-router-error'\nimport { handleHardNavError } from './nav-failure-handler'\nimport { HandleISRError } from './handle-isr-error'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\n\nconst isBotUserAgent =\n  typeof window !== 'undefined' && isBot(window.navigator.userAgent)\n\nexport type ErrorComponent = React.ComponentType<{\n  error: Error\n  // global-error, there's no `reset` function;\n  // regular error boundary, there's a `reset` function.\n  reset?: () => void\n}>\n\nexport interface ErrorBoundaryProps {\n  children?: React.ReactNode\n  errorComponent: ErrorComponent | undefined\n  errorStyles?: React.ReactNode | undefined\n  errorScripts?: React.ReactNode | undefined\n}\n\ninterface ErrorBoundaryHandlerProps extends ErrorBoundaryProps {\n  pathname: string | null\n  errorComponent: ErrorComponent\n}\n\ninterface ErrorBoundaryHandlerState {\n  error: Error | null\n  previousPathname: string | null\n}\n\nexport class ErrorBoundaryHandler extends React.Component<\n  ErrorBoundaryHandlerProps,\n  ErrorBoundaryHandlerState\n> {\n  constructor(props: ErrorBoundaryHandlerProps) {\n    super(props)\n    this.state = { error: null, previousPathname: this.props.pathname }\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    if (isNextRouterError(error)) {\n      // Re-throw if an expected internal Next.js router error occurs\n      // this means it should be handled by a different boundary (such as a NotFound boundary in a parent segment)\n      throw error\n    }\n\n    return { error }\n  }\n\n  static getDerivedStateFromProps(\n    props: ErrorBoundaryHandlerProps,\n    state: ErrorBoundaryHandlerState\n  ): ErrorBoundaryHandlerState | null {\n    const { error } = state\n\n    // if we encounter an error while\n    // a navigation is pending we shouldn't render\n    // the error boundary and instead should fallback\n    // to a hard navigation to attempt recovering\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      if (error && handleHardNavError(error)) {\n        // clear error so we don't render anything\n        return {\n          error: null,\n          previousPathname: props.pathname,\n        }\n      }\n    }\n\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.error) {\n      return {\n        error: null,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      error: state.error,\n      previousPathname: props.pathname,\n    }\n  }\n\n  reset = () => {\n    this.setState({ error: null })\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    //When it's bot request, segment level error boundary will keep rendering the children,\n    // the final error will be caught by the root error boundary and determine wether need to apply graceful degrade.\n    if (this.state.error && !isBotUserAgent) {\n      return (\n        <>\n          <HandleISRError error={this.state.error} />\n          {this.props.errorStyles}\n          {this.props.errorScripts}\n          <this.props.errorComponent\n            error={this.state.error}\n            reset={this.reset}\n          />\n        </>\n      )\n    }\n\n    return this.props.children\n  }\n}\n\n/**\n * Handles errors through `getDerivedStateFromError`.\n * Renders the provided error component and provides a way to `reset` the error boundary state.\n */\n\n/**\n * Renders error boundary with the provided \"errorComponent\" property as the fallback.\n * If no \"errorComponent\" property is provided it renders the children without an error boundary.\n */\nexport function ErrorBoundary({\n  errorComponent,\n  errorStyles,\n  errorScripts,\n  children,\n}: ErrorBoundaryProps & {\n  children: React.ReactNode\n}): JSX.Element {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these errors can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  if (errorComponent) {\n    return (\n      <ErrorBoundaryHandler\n        pathname={pathname}\n        errorComponent={errorComponent}\n        errorStyles={errorStyles}\n        errorScripts={errorScripts}\n      >\n        {children}\n      </ErrorBoundaryHandler>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useUntrackedPathname", "isNextRouterError", "handleHardNavError", "HandleISRError", "isBot", "isBotUserAgent", "window", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "constructor", "props", "reset", "setState", "error", "state", "previousPathname", "pathname", "getDerivedStateFromError", "getDerivedStateFromProps", "process", "env", "__NEXT_APP_NAV_FAIL_HANDLING", "render", "errorStyles", "errorScripts", "this", "errorComponent", "children", "Error<PERSON>ou<PERSON><PERSON>"], "mappings": ";;;;;;;AAEA,OAAOA,WAAyB,QAAO;AACvC,SAASC,oBAAoB,QAAQ,yBAAwB;AAC7D,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,KAAK,QAAQ,uCAAsC;AAP5D;;;;;;;;AASA,MAAMC,iBACJ,OAAOC,2CAAW,mBAAeF,uaAAAA,EAAME,OAAOC,SAAS,CAACC,SAAS;AA0B5D,MAAMC,6BAA6BV,0aAAAA,CAAMW,SAAS;IAIvDC,YAAYC,KAAgC,CAAE;QAC5C,KAAK,CAACA,QAAAA,IAAAA,CAoDRC,KAAAA,GAAQ;YACN,IAAI,CAACC,QAAQ,CAAC;gBAAEC,OAAO;YAAK;QAC9B;QArDE,IAAI,CAACC,KAAK,GAAG;YAAED,OAAO;YAAME,kBAAkB,IAAI,CAACL,KAAK,CAACM,QAAQ;QAAC;IACpE;IAEA,OAAOC,yBAAyBJ,KAAY,EAAE;QAC5C,QAAId,2aAAAA,EAAkBc,QAAQ;YAC5B,+DAA+D;YAC/D,4GAA4G;YAC5G,MAAMA;QACR;QAEA,OAAO;YAAEA;QAAM;IACjB;IAEA,OAAOK,yBACLR,KAAgC,EAChCI,KAAgC,EACE;QAClC,MAAM,EAAED,KAAK,EAAE,GAAGC;QAElB,iCAAiC;QACjC,8CAA8C;QAC9C,iDAAiD;QACjD,6CAA6C;QAC7C,IAAIK,QAAQC,GAAG,CAACC,4BAA4B,EAAE;;QAU9C;;;;;KAKC,GACD,IAAIX,MAAMM,QAAQ,KAAKF,MAAMC,gBAAgB,IAAID,MAAMD,KAAK,EAAE;YAC5D,OAAO;gBACLA,OAAO;gBACPE,kBAAkBL,MAAMM,QAAQ;YAClC;QACF;QACA,OAAO;YACLH,OAAOC,MAAMD,KAAK;YAClBE,kBAAkBL,MAAMM,QAAQ;QAClC;IACF;IAMA,yIAAyI;IACzIM,SAA0B;QACxB,uFAAuF;QACvF,iHAAiH;QACjH,IAAI,IAAI,CAACR,KAAK,CAACD,KAAK,IAAI,CAACV,gBAAgB;YACvC,OAAA,WAAA,OACE,ybAAA,EAAA,6bAAA,EAAA;;sCACE,wbAAA,EAACF,iaAAAA,EAAAA;wBAAeY,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;;oBACtC,IAAI,CAACH,KAAK,CAACa,WAAW;oBACtB,IAAI,CAACb,KAAK,CAACc,YAAY;sCACxB,wbAAA,EAACC,IAAI,CAACf,KAAK,CAACgB,cAAc,EAAA;wBACxBb,OAAO,IAAI,CAACC,KAAK,CAACD,KAAK;wBACvBF,OAAO,IAAI,CAACA,KAAK;;;;QAIzB;QAEA,OAAO,IAAI,CAACD,KAAK,CAACiB,QAAQ;IAC5B;AACF;AAWO,SAASC,cAAc,EAC5BF,cAAc,EACdH,WAAW,EACXC,YAAY,EACZG,QAAQ,EAGT;IACC,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,oEAAoE;IACpE,MAAMX,eAAWlB,waAAAA;IACjB,IAAI4B,gBAAgB;QAClB,OAAA,WAAA,OACE,wbAAA,EAACnB,sBAAAA;YACCS,UAAUA;YACVU,gBAAgBA;YAChBH,aAAaA;YACbC,cAAcA;sBAEbG;;IAGP;IAEA,OAAA,WAAA,OAAO,wbAAA,EAAA,6bAAA,EAAA;kBAAGA;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1721, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/match-segments.ts"], "sourcesContent": ["import type { Segment } from '../../shared/lib/app-router-types'\n\nexport const matchSegment = (\n  existingSegment: Segment,\n  segment: Segment\n): boolean => {\n  // segment is either Array or string\n  if (typeof existingSegment === 'string') {\n    if (typeof segment === 'string') {\n      // Common case: segment is just a string\n      return existingSegment === segment\n    }\n    return false\n  }\n\n  if (typeof segment === 'string') {\n    return false\n  }\n  return existingSegment[0] === segment[0] && existingSegment[1] === segment[1]\n}\n"], "names": ["matchSegment", "existingSegment", "segment"], "mappings": ";;;;AAEO,MAAMA,eAAe,CAC1BC,iBACAC;IAEA,oCAAoC;IACpC,IAAI,OAAOD,oBAAoB,UAAU;QACvC,IAAI,OAAOC,YAAY,UAAU;YAC/B,wCAAwC;YACxC,OAAOD,oBAAoBC;QAC7B;QACA,OAAO;IACT;IAEA,IAAI,OAAOA,YAAY,UAAU;QAC/B,OAAO;IACT;IACA,OAAOD,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE,IAAID,eAAe,CAAC,EAAE,KAAKC,OAAO,CAAC,EAAE;AAC/E,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1743, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/utils/warn-once.ts"], "sourcesContent": ["let warnOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const warnings = new Set<string>()\n  warnOnce = (msg: string) => {\n    if (!warnings.has(msg)) {\n      console.warn(msg)\n    }\n    warnings.add(msg)\n  }\n}\n\nexport { warnOnce }\n"], "names": ["warnOnce", "_", "process", "env", "NODE_ENV", "warnings", "Set", "msg", "has", "console", "warn", "add"], "mappings": ";;;;AAAA,IAAIA,WAAW,CAACC,KAAe;AAC/B,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;IACzC,MAAMC,WAAW,IAAIC;IACrBN,WAAW,CAACO;QACV,IAAI,CAACF,SAASG,GAAG,CAACD,MAAM;YACtBE,QAAQC,IAAI,CAACH;QACf;QACAF,SAASM,GAAG,CAACJ;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1763, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/router/utils/disable-smooth-scroll.ts"], "sourcesContent": ["import { warnOnce } from '../../utils/warn-once'\n\n/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */\nexport function disableSmoothScrollDuringRouteTransition(\n  fn: () => void,\n  options: { dontForceLayout?: boolean; onlyHashChange?: boolean } = {}\n) {\n  // if only the hash is changed, we don't need to disable smooth scrolling\n  // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n  if (options.onlyHashChange) {\n    fn()\n    return\n  }\n\n  const htmlElement = document.documentElement\n  const hasDataAttribute = htmlElement.dataset.scrollBehavior === 'smooth'\n\n  if (!hasDataAttribute) {\n    // Warn if smooth scrolling is detected but no data attribute is present\n    if (\n      process.env.NODE_ENV === 'development' &&\n      getComputedStyle(htmlElement).scrollBehavior === 'smooth'\n    ) {\n      warnOnce(\n        'Detected `scroll-behavior: smooth` on the `<html>` element. To disable smooth scrolling during route transitions, ' +\n          'add `data-scroll-behavior=\"smooth\"` to your <html> element. ' +\n          'Learn more: https://nextjs.org/docs/messages/missing-data-scroll-behavior'\n      )\n    }\n    // No smooth scrolling configured, run directly without style manipulation\n    fn()\n    return\n  }\n\n  // Proceed with temporarily disabling smooth scrolling\n  const existing = htmlElement.style.scrollBehavior\n  htmlElement.style.scrollBehavior = 'auto'\n  if (!options.dontForceLayout) {\n    // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n    // Otherwise it will not pickup the change in scrollBehavior\n    // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n    htmlElement.getClientRects()\n  }\n  fn()\n  htmlElement.style.scrollBehavior = existing\n}\n"], "names": ["warnOnce", "disableSmoothScrollDuringRouteTransition", "fn", "options", "onlyHashChange", "htmlElement", "document", "documentElement", "hasDataAttribute", "dataset", "scroll<PERSON>eh<PERSON>or", "process", "env", "NODE_ENV", "getComputedStyle", "existing", "style", "dontForceLayout", "getClientRects"], "mappings": ";;;;AAAA,SAASA,QAAQ,QAAQ,wBAAuB;;AAMzC,SAASC,yCACdC,EAAc,EACdC,UAAmE,CAAC,CAAC;IAErE,yEAAyE;IACzE,6FAA6F;IAC7F,IAAIA,QAAQC,cAAc,EAAE;QAC1BF;QACA;IACF;IAEA,MAAMG,cAAcC,SAASC,eAAe;IAC5C,MAAMC,mBAAmBH,YAAYI,OAAO,CAACC,cAAc,KAAK;IAEhE,IAAI,CAACF,kBAAkB;QACrB,wEAAwE;QACxE,IACEG,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzBC,iBAAiBT,aAAaK,cAAc,KAAK,UACjD;gBACAV,mZAAAA,EACE,uHACE,iEACA;QAEN;QACA,0EAA0E;QAC1EE;QACA;IACF;IAEA,sDAAsD;IACtD,MAAMa,WAAWV,YAAYW,KAAK,CAACN,cAAc;IACjDL,YAAYW,KAAK,CAACN,cAAc,GAAG;IACnC,IAAI,CAACP,QAAQc,eAAe,EAAE;QAC5B,8EAA8E;QAC9E,4DAA4D;QAC5D,yFAAyF;QACzFZ,YAAYa,cAAc;IAC5B;IACAhB;IACAG,YAAYW,KAAK,CAACN,cAAc,GAAGK;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1803, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/readonly-url-search-params.ts"], "sourcesContent": ["/**\n * ReadonlyURLSearchParams implementation shared between client and server.\n * This file is intentionally not marked as 'use client' or 'use server'\n * so it can be imported by both environments.\n */\n\n/** @internal */\nclass ReadonlyURLSearchParamsError extends Error {\n  constructor() {\n    super(\n      'Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams'\n    )\n  }\n}\n\n/**\n * A read-only version of URLSearchParams that throws errors when mutation methods are called.\n * This ensures that the URLSearchParams returned by useSearchParams() cannot be mutated.\n */\nexport class ReadonlyURLSearchParams extends URLSearchParams {\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  append() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  delete() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  set() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n  /** @deprecated Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams */\n  sort() {\n    throw new ReadonlyURLSearchParamsError()\n  }\n}\n"], "names": ["ReadonlyURLSearchParamsError", "Error", "constructor", "ReadonlyURLSearchParams", "URLSearchParams", "append", "delete", "set", "sort"], "mappings": "AAAA;;;;CAIC,GAED,cAAc;;;;AACd,MAAMA,qCAAqCC;IACzCC,aAAc;QACZ,KAAK,CACH;IAEJ;AACF;AAMO,MAAMC,gCAAgCC;IAC3C,wKAAwK,GACxKC,SAAS;QACP,MAAM,IAAIL;IACZ;IACA,wKAAwK,GACxKM,SAAS;QACP,MAAM,IAAIN;IACZ;IACA,wKAAwK,GACxKO,MAAM;QACJ,MAAM,IAAIP;IACZ;IACA,wKAAwK,GACxKQ,OAAO;QACL,MAAM,IAAIR;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1834, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/route-modules/app-page/vendored/contexts/server-inserted-html.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].ServerInsertedHtml\n"], "names": ["module", "exports", "require", "vendored", "ServerInsertedHtml"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,oQACRC,QAAQ,CAAC,WAAW,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1839, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/unrecognized-action-error.ts"], "sourcesContent": ["export class UnrecognizedActionError extends Error {\n  constructor(...args: ConstructorParameters<typeof Error>) {\n    super(...args)\n    this.name = 'UnrecognizedActionError'\n  }\n}\n\n/**\n * Check whether a server action call failed because the server action was not recognized by the server.\n * This can happen if the client and the server are not from the same deployment.\n *\n * Example usage:\n * ```ts\n * try {\n *   await myServerAction();\n * } catch (err) {\n *   if (unstable_isUnrecognizedActionError(err)) {\n *     // The client is from a different deployment than the server.\n *     // Reloading the page will fix this mismatch.\n *     window.alert(\"Please refresh the page and try again\");\n *     return;\n *   }\n * }\n * ```\n * */\nexport function unstable_isUnrecognizedActionError(\n  error: unknown\n): error is UnrecognizedActionError {\n  return !!(\n    error &&\n    typeof error === 'object' &&\n    error instanceof UnrecognizedActionError\n  )\n}\n"], "names": ["UnrecognizedActionError", "Error", "constructor", "args", "name", "unstable_isUnrecognizedActionError", "error"], "mappings": ";;;;;;AAAO,MAAMA,gCAAgCC;IAC3CC,YAAY,GAAGC,IAAyC,CAAE;QACxD,KAAK,IAAIA;QACT,IAAI,CAACC,IAAI,GAAG;IACd;AACF;AAoBO,SAASC,mCACdC,KAAc;IAEd,OAAO,CAAC,CACNA,CAAAA,SACA,OAAOA,UAAU,YACjBA,iBAAiBN,uBAAsB;AAE3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1858, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/redirect.ts"], "sourcesContent": ["import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n"], "names": ["RedirectStatusCode", "RedirectType", "isRedirectError", "REDIRECT_ERROR_CODE", "actionAsyncStorage", "window", "require", "undefined", "getRedirectError", "url", "type", "statusCode", "TemporaryRedirect", "error", "Error", "digest", "redirect", "getStore", "isAction", "push", "replace", "permanentRedirect", "PermanentRedirect", "getURLFromRedirectError", "split", "slice", "join", "getRedirectTypeFromError", "getRedirectStatusCodeFromError", "Number", "at"], "mappings": ";;;;;;;;;;;;;;AAAA,SAASA,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,YAAY,EAEZC,eAAe,EACfC,mBAAmB,QACd,mBAAkB;;;AAEzB,MAAMC,qBACJ,OAAOC,WAAW,qBAEZC,QAAQ,2KACRF,kBAAkB,GACpBG;AAEC,SAASC,iBACdC,GAAW,EACXC,IAAkB,EAClBC,aAAiCX,yaAAAA,CAAmBY,iBAAiB;IAErE,MAAMC,QAAQ,OAAA,cAA8B,CAA9B,IAAIC,MAAMX,iaAAAA,GAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA6B;IAC3CU,MAAME,MAAM,GAAG,GAAGZ,iaAAAA,CAAoB,CAAC,EAAEO,KAAK,CAAC,EAAED,IAAI,CAAC,EAAEE,WAAW,CAAC,CAAC;IACrE,OAAOE;AACT;AAcO,SAASG,SACd,2BAA2B,GAC3BP,GAAW,EACXC,IAAmB;IAEnBA,SAASN,oBAAoBa,YAAYC,WACrCjB,0ZAAAA,CAAakB,IAAI,GACjBlB,0ZAAAA,CAAamB,OAAO;IAExB,MAAMZ,iBAAiBC,KAAKC,MAAMV,yaAAAA,CAAmBY,iBAAiB;AACxE;AAaO,SAASS,kBACd,2BAA2B,GAC3BZ,GAAW,EACXC,OAAqBT,0ZAAAA,CAAamB,OAAO;IAEzC,MAAMZ,iBAAiBC,KAAKC,MAAMV,yaAAAA,CAAmBsB,iBAAiB;AACxE;AAUO,SAASC,wBAAwBV,KAAc;IACpD,IAAI,KAACX,6ZAAAA,EAAgBW,QAAQ,OAAO;IAEpC,wEAAwE;IACxE,kBAAkB;IAClB,OAAOA,MAAME,MAAM,CAACS,KAAK,CAAC,KAAKC,KAAK,CAAC,GAAG,CAAC,GAAGC,IAAI,CAAC;AACnD;AAEO,SAASC,yBAAyBd,KAAoB;IAC3D,IAAI,KAACX,6ZAAAA,EAAgBW,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOD,MAAME,MAAM,CAACS,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE;AACtC;AAEO,SAASI,+BAA+Bf,KAAoB;IACjE,IAAI,KAACX,6ZAAAA,EAAgBW,QAAQ;QAC3B,MAAM,OAAA,cAAiC,CAAjC,IAAIC,MAAM,yBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAgC;IACxC;IAEA,OAAOe,OAAOhB,MAAME,MAAM,CAACS,KAAK,CAAC,KAAKM,EAAE,CAAC,CAAC;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1923, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/not-found.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n/**\n * This function allows you to render the [not-found.js file](https://nextjs.org/docs/app/api-reference/file-conventions/not-found)\n * within a route segment as well as inject a tag.\n *\n * `notFound()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a `<meta name=\"robots\" content=\"noindex\" />` meta tag and set the status code to 404.\n * - In a Route Handler or Server Action, it will serve a 404 to the caller.\n *\n * Read more: [Next.js Docs: `notFound`](https://nextjs.org/docs/app/api-reference/functions/not-found)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};404`\n\nexport function notFound(): never {\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "notFound", "error", "Error", "digest"], "mappings": ";;;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD;;;;;;;;;;;;;CAaC,GAED,MAAMC,SAAS,GAAGD,mdAAAA,CAA+B,IAAI,CAAC;AAE/C,SAASE;IACd,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAIC,MAAMH,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BE,MAAkCE,MAAM,GAAGJ;IAE7C,MAAME;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1956, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/forbidden.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `forbidden` docs\n/**\n * @experimental\n * This function allows you to render the [forbidden.js file](https://nextjs.org/docs/app/api-reference/file-conventions/forbidden)\n * within a route segment as well as inject a tag.\n *\n * `forbidden()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * Read more: [Next.js Docs: `forbidden`](https://nextjs.org/docs/app/api-reference/functions/forbidden)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};403`\n\nexport function forbidden(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`forbidden()\\` is experimental and only allowed to be enabled when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "forbidden", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD,6BAA6B;AAC7B;;;;;;;;;;;CAWC,GAED,MAAMC,SAAS,GAAGD,mdAAAA,CAA+B,IAAI,CAAC;AAE/C,SAASE;IACd,IAAI,CAACC,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,+GAA+G,CAAC,GAD7G,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1995, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/unauthorized.ts"], "sourcesContent": ["import {\n  HTTP_ERROR_FALLBACK_ERROR_CODE,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\n\n// TODO: Add `unauthorized` docs\n/**\n * @experimental\n * This function allows you to render the [unauthorized.js file](https://nextjs.org/docs/app/api-reference/file-conventions/unauthorized)\n * within a route segment as well as inject a tag.\n *\n * `unauthorized()` can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n *\n * Read more: [Next.js Docs: `unauthorized`](https://nextjs.org/docs/app/api-reference/functions/unauthorized)\n */\n\nconst DIGEST = `${HTTP_ERROR_FALLBACK_ERROR_CODE};401`\n\nexport function unauthorized(): never {\n  if (!process.env.__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS) {\n    throw new Error(\n      `\\`unauthorized()\\` is experimental and only allowed to be used when \\`experimental.authInterrupts\\` is enabled.`\n    )\n  }\n\n  const error = new Error(DIGEST) as HTTPAccessFallbackError\n  ;(error as HTTPAccessFallbackError).digest = DIGEST\n  throw error\n}\n"], "names": ["HTTP_ERROR_FALLBACK_ERROR_CODE", "DIGEST", "unauthorized", "process", "env", "__NEXT_EXPERIMENTAL_AUTH_INTERRUPTS", "Error", "error", "digest"], "mappings": ";;;;AAAA,SACEA,8BAA8B,QAEzB,8CAA6C;;AAEpD,gCAAgC;AAChC;;;;;;;;;;;;CAYC,GAED,MAAMC,SAAS,GAAGD,mdAAAA,CAA+B,IAAI,CAAC;AAE/C,SAASE;IACd,IAAI,CAACC,QAAQC,GAAG,CAACC,uBAAqC,YAAF;QAClD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,+GAA+G,CAAC,GAD7G,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,QAAQ,OAAA,cAAiB,CAAjB,IAAID,MAAML,SAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAgB;IAC5BM,MAAkCC,MAAM,GAAGP;IAC7C,MAAMM;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2035, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/server/dynamic-rendering-utils.ts"], "sourcesContent": ["import type { NonStaticRenderStage } from './app-render/staged-rendering'\nimport type { RequestStore } from './app-render/work-unit-async-storage.external'\n\nexport function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(\n    public readonly route: string,\n    public readonly expression: string\n  ) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route \"${route}\".`\n    )\n  }\n}\n\ntype AbortListeners = Array<(err: unknown) => void>\nconst abortListenersBySignal = new WeakMap<AbortSignal, AbortListeners>()\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for cacheComponents where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  route: string,\n  expression: string\n): Promise<T> {\n  if (signal.aborted) {\n    return Promise.reject(new HangingPromiseRejectionError(route, expression))\n  } else {\n    const hangingPromise = new Promise<T>((_, reject) => {\n      const boundRejection = reject.bind(\n        null,\n        new HangingPromiseRejectionError(route, expression)\n      )\n      let currentListeners = abortListenersBySignal.get(signal)\n      if (currentListeners) {\n        currentListeners.push(boundRejection)\n      } else {\n        const listeners = [boundRejection]\n        abortListenersBySignal.set(signal, listeners)\n        signal.addEventListener(\n          'abort',\n          () => {\n            for (let i = 0; i < listeners.length; i++) {\n              listeners[i]()\n            }\n          },\n          { once: true }\n        )\n      }\n    })\n    // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n    // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n    // your own promise out of it you'll need to ensure you handle the error when it rejects.\n    hangingPromise.catch(ignoreReject)\n    return hangingPromise\n  }\n}\n\nfunction ignoreReject() {}\n\nexport function makeDevtoolsIOAwarePromise<T>(\n  underlying: T,\n  requestStore: RequestStore,\n  stage: NonStaticRenderStage\n): Promise<T> {\n  if (requestStore.stagedRendering) {\n    // We resolve each stage in a timeout, so React DevTools will pick this up as IO.\n    return requestStore.stagedRendering.delayUntilStage(\n      stage,\n      undefined,\n      underlying\n    )\n  }\n  // in React DevTools if we resolve in a setTimeout we will observe\n  // the promise resolution as something that can suspend a boundary or root.\n  return new Promise<T>((resolve) => {\n    // Must use setTimeout to be considered IO React DevTools. setImmediate will not work.\n    setTimeout(() => {\n      resolve(underlying)\n    }, 0)\n  })\n}\n"], "names": ["isHangingPromiseRejectionError", "err", "digest", "HANGING_PROMISE_REJECTION", "HangingPromiseRejectionError", "Error", "constructor", "route", "expression", "abortListenersBySignal", "WeakMap", "makeHangingPromise", "signal", "aborted", "Promise", "reject", "hanging<PERSON>romise", "_", "boundRejection", "bind", "currentListeners", "get", "push", "listeners", "set", "addEventListener", "i", "length", "once", "catch", "ignoreReject", "makeDevtoolsIOAwarePromise", "underlying", "requestStore", "stage", "stagedRendering", "delayUntilStage", "undefined", "resolve", "setTimeout"], "mappings": ";;;;;;;;AAGO,SAASA,+BACdC,GAAY;IAEZ,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIC,MAAM,KAAKC;AACxB;AAEA,MAAMA,4BAA4B;AAElC,MAAMC,qCAAqCC;IAGzCC,YACkBC,KAAa,EACbC,UAAkB,CAClC;QACA,KAAK,CACH,CAAC,qBAAqB,EAAEA,WAAW,qGAAqG,EAAEA,WAAW,8KAA8K,EAAED,MAAM,EAAE,CAAC,GAAA,IAAA,CAJhUA,KAAAA,GAAAA,OAAAA,IAAAA,CACAC,UAAAA,GAAAA,YAAAA,IAAAA,CAJFN,MAAAA,GAASC;IASzB;AACF;AAGA,MAAMM,yBAAyB,IAAIC;AAS5B,SAASC,mBACdC,MAAmB,EACnBL,KAAa,EACbC,UAAkB;IAElB,IAAII,OAAOC,OAAO,EAAE;QAClB,OAAOC,QAAQC,MAAM,CAAC,IAAIX,6BAA6BG,OAAOC;IAChE,OAAO;QACL,MAAMQ,iBAAiB,IAAIF,QAAW,CAACG,GAAGF;YACxC,MAAMG,iBAAiBH,OAAOI,IAAI,CAChC,MACA,IAAIf,6BAA6BG,OAAOC;YAE1C,IAAIY,mBAAmBX,uBAAuBY,GAAG,CAACT;YAClD,IAAIQ,kBAAkB;gBACpBA,iBAAiBE,IAAI,CAACJ;YACxB,OAAO;gBACL,MAAMK,YAAY;oBAACL;iBAAe;gBAClCT,uBAAuBe,GAAG,CAACZ,QAAQW;gBACnCX,OAAOa,gBAAgB,CACrB,SACA;oBACE,IAAK,IAAIC,IAAI,GAAGA,IAAIH,UAAUI,MAAM,EAAED,IAAK;wBACzCH,SAAS,CAACG,EAAE;oBACd;gBACF,GACA;oBAAEE,MAAM;gBAAK;YAEjB;QACF;QACA,2GAA2G;QAC3G,6GAA6G;QAC7G,yFAAyF;QACzFZ,eAAea,KAAK,CAACC;QACrB,OAAOd;IACT;AACF;AAEA,SAASc,gBAAgB;AAElB,SAASC,2BACdC,UAAa,EACbC,YAA0B,EAC1BC,KAA2B;IAE3B,IAAID,aAAaE,eAAe,EAAE;QAChC,iFAAiF;QACjF,OAAOF,aAAaE,eAAe,CAACC,eAAe,CACjDF,OACAG,WACAL;IAEJ;IACA,kEAAkE;IAClE,2EAA2E;IAC3E,OAAO,IAAIlB,QAAW,CAACwB;QACrB,sFAAsF;QACtFC,WAAW;YACTD,QAAQN;QACV,GAAG;IACL;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2105, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/server/lib/router-utils/is-postpone.ts"], "sourcesContent": ["const REACT_POSTPONE_TYPE: symbol = Symbol.for('react.postpone')\n\nexport function isPostpone(error: any): boolean {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    error.$$typeof === REACT_POSTPONE_TYPE\n  )\n}\n"], "names": ["REACT_POSTPONE_TYPE", "Symbol", "for", "isPostpone", "error", "$$typeof"], "mappings": ";;;;AAAA,MAAMA,sBAA8BC,OAAOC,GAAG,CAAC;AAExC,SAASC,WAAWC,KAAU;IACnC,OACE,OAAOA,UAAU,YACjBA,UAAU,QACVA,MAAMC,QAAQ,KAAKL;AAEvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2117, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/lazy-dynamic/bailout-to-csr.ts"], "sourcesContent": ["// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n"], "names": ["BAILOUT_TO_CSR", "BailoutToCSRError", "Error", "constructor", "reason", "digest", "isBailoutToCSRError", "err"], "mappings": "AAAA,+GAA+G;;;;;;;AAC/G,MAAMA,iBAAiB;AAGhB,MAAMC,0BAA0BC;IAGrCC,YAA4BC,MAAc,CAAE;QAC1C,KAAK,CAAC,CAAC,mCAAmC,EAAEA,QAAQ,GAAA,IAAA,CAD1BA,MAAAA,GAAAA,QAAAA,IAAAA,CAFZC,MAAAA,GAASL;IAIzB;AACF;AAGO,SAASM,oBAAoBC,GAAY;IAC9C,IAAI,OAAOA,QAAQ,YAAYA,QAAQ,QAAQ,CAAE,CAAA,YAAYA,GAAE,GAAI;QACjE,OAAO;IACT;IAEA,OAAOA,IAAIF,MAAM,KAAKL;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2140, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/hooks-server-context.ts"], "sourcesContent": ["const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n"], "names": ["DYNAMIC_ERROR_CODE", "DynamicServerError", "Error", "constructor", "description", "digest", "isDynamicServerError", "err"], "mappings": ";;;;;;AAAA,MAAMA,qBAAqB;AAEpB,MAAMC,2BAA2BC;IAGtCC,YAA4BC,WAAmB,CAAE;QAC/C,KAAK,CAAC,CAAC,sBAAsB,EAAEA,aAAa,GAAA,IAAA,CADlBA,WAAAA,GAAAA,aAAAA,IAAAA,CAF5BC,MAAAA,GAAoCL;IAIpC;AACF;AAEO,SAASM,qBAAqBC,GAAY;IAC/C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,CAAE,CAAA,YAAYA,GAAE,KAChB,OAAOA,IAAIF,MAAM,KAAK,UACtB;QACA,OAAO;IACT;IAEA,OAAOE,IAAIF,MAAM,KAAKL;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2162, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/static-generation-bailout.ts"], "sourcesContent": ["const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n"], "names": ["NEXT_STATIC_GEN_BAILOUT", "StaticGenBailoutError", "Error", "code", "isStaticGenBailoutError", "error"], "mappings": ";;;;;;AAAA,MAAMA,0BAA0B;AAEzB,MAAMC,8BAA8BC;;QAApC,KAAA,IAAA,OAAA,IAAA,CACWC,IAAAA,GAAOH;;AACzB;AAEO,SAASI,wBACdC,KAAc;IAEd,IAAI,OAAOA,UAAU,YAAYA,UAAU,QAAQ,CAAE,CAAA,UAAUA,KAAI,GAAI;QACrE,OAAO;IACT;IAEA,OAAOA,MAAMF,IAAI,KAAKH;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2184, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/lib/framework/boundary-constants.tsx"], "sourcesContent": ["export const METADATA_BOUNDARY_NAME = '__next_metadata_boundary__'\nexport const VIEWPORT_BOUNDARY_NAME = '__next_viewport_boundary__'\nexport const OUTLET_BOUNDARY_NAME = '__next_outlet_boundary__'\nexport const ROOT_LAYOUT_BOUNDARY_NAME = '__next_root_layout_boundary__'\n"], "names": ["METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME"], "mappings": ";;;;;;;;;;AAAO,MAAMA,yBAAyB,6BAA4B;AAC3D,MAAMC,yBAAyB,6BAA4B;AAC3D,MAAMC,uBAAuB,2BAA0B;AACvD,MAAMC,4BAA4B,gCAA+B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2202, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/lib/scheduler.ts"], "sourcesContent": ["export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = (cb: ScheduledFn<void>) => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = (cb: ScheduledFn<void>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n"], "names": ["scheduleOnNextTick", "cb", "Promise", "resolve", "then", "process", "env", "NEXT_RUNTIME", "setTimeout", "nextTick", "scheduleImmediate", "setImmediate", "atLeastOneTask", "waitAtLeastOneReactRenderTask", "r"], "mappings": "AAGA;;;;;CAKC,GACD;;;;;;;;;;AAAO,MAAMA,qBAAqB,CAACC;IACjC,6EAA6E;IAC7E,4EAA4E;IAC5E,uCAAuC;IACvC,EAAE;IACF,kLAAkL;IAClL,EAAE;IACFC,QAAQC,OAAO,GAAGC,IAAI,CAAC;QACrB,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;aAElC;YACLF,QAAQI,QAAQ,CAACR;QACnB;IACF;AACF,EAAC;AAQM,MAAMS,oBAAoB,CAACT;IAChC,IAAII,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACLI,aAAaV;IACf;AACF,EAAC;AAOM,SAASW;IACd,OAAO,IAAIV,QAAc,CAACC,UAAYO,kBAAkBP;AAC1D;AAWO,SAASU;IACd,IAAIR,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;SAElC;QACL,OAAO,IAAIL,QAAQ,CAACY,IAAMH,aAAaG;IACzC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2253, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/invariant-error.ts"], "sourcesContent": ["export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n"], "names": ["InvariantError", "Error", "constructor", "message", "options", "endsWith", "name"], "mappings": ";;;;AAAO,MAAMA,uBAAuBC;IAClCC,YAAYC,OAAe,EAAEC,OAAsB,CAAE;QACnD,KAAK,CACH,CAAC,WAAW,EAAED,QAAQE,QAAQ,CAAC,OAAOF,UAAUA,UAAU,IAAI,0BAA0B,CAAC,EACzFC;QAEF,IAAI,CAACE,IAAI,GAAG;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2267, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/promise-with-resolvers.ts"], "sourcesContent": ["export function createPromiseWithResolvers<T>(): PromiseWithResolvers<T> {\n  // Shim of Stage 4 Promise.withResolvers proposal\n  let resolve: (value: T | PromiseLike<T>) => void\n  let reject: (reason: any) => void\n  const promise = new Promise<T>((res, rej) => {\n    resolve = res\n    reject = rej\n  })\n  return { resolve: resolve!, reject: reject!, promise }\n}\n"], "names": ["createPromiseWithResolvers", "resolve", "reject", "promise", "Promise", "res", "rej"], "mappings": ";;;;AAAO,SAASA;IACd,iDAAiD;IACjD,IAAIC;IACJ,IAAIC;IACJ,MAAMC,UAAU,IAAIC,QAAW,CAACC,KAAKC;QACnCL,UAAUI;QACVH,SAASI;IACX;IACA,OAAO;QAAEL,SAASA;QAAUC,QAAQA;QAASC;IAAQ;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2289, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/server/app-render/staged-rendering.ts"], "sourcesContent": ["import { InvariantError } from '../../shared/lib/invariant-error'\nimport { createPromiseWithResolvers } from '../../shared/lib/promise-with-resolvers'\n\nexport enum RenderStage {\n  Static = 1,\n  Runtime = 2,\n  Dynamic = 3,\n}\n\nexport type NonStaticRenderStage = RenderStage.Runtime | RenderStage.Dynamic\n\nexport class StagedRenderingController {\n  currentStage: RenderStage = RenderStage.Static\n\n  private runtimeStagePromise = createPromiseWithResolvers<void>()\n  private dynamicStagePromise = createPromiseWithResolvers<void>()\n\n  constructor(private abortSignal: AbortSignal | null = null) {\n    if (abortSignal) {\n      abortSignal.addEventListener(\n        'abort',\n        () => {\n          const { reason } = abortSignal\n          if (this.currentStage < RenderStage.Runtime) {\n            this.runtimeStagePromise.promise.catch(ignoreReject) // avoid unhandled rejections\n            this.runtimeStagePromise.reject(reason)\n          }\n          if (this.currentStage < RenderStage.Dynamic) {\n            this.dynamicStagePromise.promise.catch(ignoreReject) // avoid unhandled rejections\n            this.dynamicStagePromise.reject(reason)\n          }\n        },\n        { once: true }\n      )\n    }\n  }\n\n  advanceStage(stage: NonStaticRenderStage) {\n    // If we're already at the target stage or beyond, do nothing.\n    // (this can happen e.g. if sync IO advanced us to the dynamic stage)\n    if (this.currentStage >= stage) {\n      return\n    }\n    this.currentStage = stage\n    // Note that we might be going directly from Static to Dynamic,\n    // so we need to resolve the runtime stage as well.\n    if (stage >= RenderStage.Runtime) {\n      this.runtimeStagePromise.resolve()\n    }\n    if (stage >= RenderStage.Dynamic) {\n      this.dynamicStagePromise.resolve()\n    }\n  }\n\n  private getStagePromise(stage: NonStaticRenderStage): Promise<void> {\n    switch (stage) {\n      case RenderStage.Runtime: {\n        return this.runtimeStagePromise.promise\n      }\n      case RenderStage.Dynamic: {\n        return this.dynamicStagePromise.promise\n      }\n      default: {\n        stage satisfies never\n        throw new InvariantError(`Invalid render stage: ${stage}`)\n      }\n    }\n  }\n\n  waitForStage(stage: NonStaticRenderStage) {\n    return this.getStagePromise(stage)\n  }\n\n  delayUntilStage<T>(\n    stage: NonStaticRenderStage,\n    displayName: string | undefined,\n    resolvedValue: T\n  ) {\n    const ioTriggerPromise = this.getStagePromise(stage)\n\n    const promise = makeDevtoolsIOPromiseFromIOTrigger(\n      ioTriggerPromise,\n      displayName,\n      resolvedValue\n    )\n\n    // Analogously to `makeHangingPromise`, we might reject this promise if the signal is invoked.\n    // (e.g. in the case where we don't want want the render to proceed to the dynamic stage and abort it).\n    // We shouldn't consider this an unhandled rejection, so we attach a noop catch handler here to suppress this warning.\n    if (this.abortSignal) {\n      promise.catch(ignoreReject)\n    }\n    return promise\n  }\n}\n\nfunction ignoreReject() {}\n\n// TODO(restart-on-cache-miss): the layering of `delayUntilStage`,\n// `makeDevtoolsIOPromiseFromIOTrigger` and and `makeDevtoolsIOAwarePromise`\n// is confusing, we should clean it up.\nfunction makeDevtoolsIOPromiseFromIOTrigger<T>(\n  ioTrigger: Promise<any>,\n  displayName: string | undefined,\n  resolvedValue: T\n): Promise<T> {\n  // If we create a `new Promise` and give it a displayName\n  // (with no userspace code above us in the stack)\n  // React Devtools will use it as the IO cause when determining \"suspended by\".\n  // In particular, it should shadow any inner IO that resolved/rejected the promise\n  // (in case of staged rendering, this will be the `setTimeout` that triggers the relevant stage)\n  const promise = new Promise<T>((resolve, reject) => {\n    ioTrigger.then(resolve.bind(null, resolvedValue), reject)\n  })\n  if (displayName !== undefined) {\n    // @ts-expect-error\n    promise.displayName = displayName\n  }\n  return promise\n}\n"], "names": ["InvariantError", "createPromiseWithResolvers", "RenderStage", "StagedRenderingController", "constructor", "abortSignal", "currentStage", "runtimeStagePromise", "dynamicStagePromise", "addEventListener", "reason", "promise", "catch", "ignoreReject", "reject", "once", "advanceStage", "stage", "resolve", "getStagePromise", "waitForStage", "delayUntilStage", "displayName", "resolvedValue", "ioTriggerPromise", "makeDevtoolsIOPromiseFromIOTrigger", "ioTrigger", "Promise", "then", "bind", "undefined"], "mappings": ";;;;;;AAAA,SAASA,cAAc,QAAQ,mCAAkC;AACjE,SAASC,0BAA0B,QAAQ,0CAAyC;;;AAE7E,IAAKC,cAAAA,WAAAA,GAAAA,SAAAA,WAAAA;;;;WAAAA;MAIX;AAIM,MAAMC;IAMXC,YAAoBC,cAAkC,IAAI,CAAE;aAAxCA,WAAAA,GAAAA;aALpBC,YAAAA,GAAAA;aAEQC,mBAAAA,OAAsBN,4aAAAA;aACtBO,mBAAAA,OAAsBP,4aAAAA;QAG5B,IAAII,aAAa;YACfA,YAAYI,gBAAgB,CAC1B,SACA;gBACE,MAAM,EAAEC,MAAM,EAAE,GAAGL;gBACnB,IAAI,IAAI,CAACC,YAAY,GAAA,GAAwB;oBAC3C,IAAI,CAACC,mBAAmB,CAACI,OAAO,CAACC,KAAK,CAACC,cAAc,6BAA6B;;oBAClF,IAAI,CAACN,mBAAmB,CAACO,MAAM,CAACJ;gBAClC;gBACA,IAAI,IAAI,CAACJ,YAAY,GAAA,GAAwB;oBAC3C,IAAI,CAACE,mBAAmB,CAACG,OAAO,CAACC,KAAK,CAACC,cAAc,6BAA6B;;oBAClF,IAAI,CAACL,mBAAmB,CAACM,MAAM,CAACJ;gBAClC;YACF,GACA;gBAAEK,MAAM;YAAK;QAEjB;IACF;IAEAC,aAAaC,KAA2B,EAAE;QACxC,8DAA8D;QAC9D,qEAAqE;QACrE,IAAI,IAAI,CAACX,YAAY,IAAIW,OAAO;YAC9B;QACF;QACA,IAAI,CAACX,YAAY,GAAGW;QACpB,+DAA+D;QAC/D,mDAAmD;QACnD,IAAIA,SAAAA,GAA8B;YAChC,IAAI,CAACV,mBAAmB,CAACW,OAAO;QAClC;QACA,IAAID,SAAAA,GAA8B;YAChC,IAAI,CAACT,mBAAmB,CAACU,OAAO;QAClC;IACF;IAEQC,gBAAgBF,KAA2B,EAAiB;QAClE,OAAQA;YACN,KAAA;gBAA0B;oBACxB,OAAO,IAAI,CAACV,mBAAmB,CAACI,OAAO;gBACzC;YACA,KAAA;gBAA0B;oBACxB,OAAO,IAAI,CAACH,mBAAmB,CAACG,OAAO;gBACzC;YACA;gBAAS;oBACPM;oBACA,MAAM,OAAA,cAAoD,CAApD,IAAIjB,sZAAAA,CAAe,CAAC,sBAAsB,EAAEiB,OAAO,GAAnD,qBAAA;+BAAA;oCAAA;sCAAA;oBAAmD;gBAC3D;QACF;IACF;IAEAG,aAAaH,KAA2B,EAAE;QACxC,OAAO,IAAI,CAACE,eAAe,CAACF;IAC9B;IAEAI,gBACEJ,KAA2B,EAC3BK,WAA+B,EAC/BC,aAAgB,EAChB;QACA,MAAMC,mBAAmB,IAAI,CAACL,eAAe,CAACF;QAE9C,MAAMN,UAAUc,mCACdD,kBACAF,aACAC;QAGF,8FAA8F;QAC9F,uGAAuG;QACvG,sHAAsH;QACtH,IAAI,IAAI,CAAClB,WAAW,EAAE;YACpBM,QAAQC,KAAK,CAACC;QAChB;QACA,OAAOF;IACT;AACF;AAEA,SAASE,gBAAgB;AAEzB,kEAAkE;AAClE,4EAA4E;AAC5E,uCAAuC;AACvC,SAASY,mCACPC,SAAuB,EACvBJ,WAA+B,EAC/BC,aAAgB;IAEhB,yDAAyD;IACzD,iDAAiD;IACjD,8EAA8E;IAC9E,kFAAkF;IAClF,gGAAgG;IAChG,MAAMZ,UAAU,IAAIgB,QAAW,CAACT,SAASJ;QACvCY,UAAUE,IAAI,CAACV,QAAQW,IAAI,CAAC,MAAMN,gBAAgBT;IACpD;IACA,IAAIQ,gBAAgBQ,WAAW;QAC7B,mBAAmB;QACnBnB,QAAQW,WAAW,GAAGA;IACxB;IACA,OAAOX;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2404, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/server/app-render/dynamic-rendering.ts"], "sourcesContent": ["/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n  PrerenderStoreModernRuntime,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport {\n  getRuntimeStagePromise,\n  throwForMissingRequestStore,\n  workUnitAsyncStorage,\n} from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n  ROOT_LAYOUT_BOUNDARY_NAME,\n} from '../../lib/framework/boundary-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\nimport { BailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { RenderStage } from './staged-rendering'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicErrorWithStack: null | Error\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspenseAboveBody: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasAllowedDynamic: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspenseAboveBody: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasAllowedDynamic: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'cache':\n      case 'unstable-cache':\n        // Inside cache scopes, marking a scope as dynamic has no effect,\n        // because the outer cache scope creates a cache boundary. This is\n        // subtly different from reading a dynamic data source, which is\n        // forbidden inside a cache scope.\n        return\n      case 'private-cache':\n        // A private cache scope is already dynamic by definition.\n        return\n      case 'prerender-legacy':\n      case 'prerender-ppr':\n      case 'request':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender-ppr':\n        return postponeWithTracking(\n          store.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      case 'prerender-legacy':\n        workUnitStore.revalidate = 0\n\n        // We aren't prerendering, but we are generating a static page. We need\n        // to bail out of static generation.\n        const err = new DynamicServerError(\n          `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n        )\n        store.dynamicUsageDescription = expression\n        store.dynamicUsageStack = err.stack\n\n        throw err\n      case 'request':\n        if (process.env.NODE_ENV !== 'production') {\n          workUnitStore.usedDynamic = true\n        }\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\n/**\n * This function is meant to be used when prerendering without cacheComponents or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(workUnitStore: WorkUnitStore) {\n  switch (workUnitStore.type) {\n    case 'cache':\n    case 'unstable-cache':\n      // Inside cache scopes, marking a scope as dynamic has no effect,\n      // because the outer cache scope creates a cache boundary. This is\n      // subtly different from reading a dynamic data source, which is\n      // forbidden inside a cache scope.\n      return\n    case 'private-cache':\n      // A private cache scope is already dynamic by definition.\n      return\n    case 'prerender':\n    case 'prerender-runtime':\n    case 'prerender-legacy':\n    case 'prerender-ppr':\n    case 'prerender-client':\n      break\n    case 'request':\n      if (process.env.NODE_ENV !== 'production') {\n        workUnitStore.usedDynamic = true\n      }\n      break\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  // It is important that we set this tracking value after aborting. Aborts are executed\n  // synchronously except for the case where you abort during render itself. By setting this\n  // value late we can use it to determine if any of the aborted tasks are the task that\n  // called the sync IO expression in the first place.\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of the prerender stage\n  if (requestStore.stagedRendering) {\n    // TODO: error for sync IO in the runtime stage\n    // (which is not currently covered by the validation render in `spawnDynamicValidationInDev`)\n    requestStore.stagedRendering.advanceStage(RenderStage.Dynamic)\n  }\n}\n\n/**\n * use this function when prerendering with cacheComponents. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in cacheComponents mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const prerenderSignal = prerenderStore.controller.signal\n  if (prerenderSignal.aborted === false) {\n    // TODO it would be better to move this aborted check into the callsite so we can avoid making\n    // the error object when it isn't relevant to the aborting of the prerender however\n    // since we need the throw semantics regardless of whether we abort it is easier to land\n    // this way. See how this was handled with `abortOnSynchronousPlatformIOAccess` for a closer\n    // to ideal implementation\n    abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n    // It is important that we set this tracking value after aborting. Aborts are executed\n    // synchronously except for the case where you abort during render itself. By setting this\n    // value late we can use it to determine if any of the aborted tasks are the task that\n    // called the sync IO expression in the first place.\n    const dynamicTracking = prerenderStore.dynamicTracking\n    if (dynamicTracking) {\n      if (dynamicTracking.syncDynamicErrorWithStack === null) {\n        dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      }\n    }\n  }\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createRenderInBrowserAbortSignal(): AbortSignal {\n  const controller = new AbortController()\n  controller.abort(new BailoutToCSRError('Render in Browser'))\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: WorkUnitStore\n): AbortSignal | undefined {\n  switch (workUnitStore.type) {\n    case 'prerender':\n    case 'prerender-runtime':\n      const controller = new AbortController()\n\n      if (workUnitStore.cacheSignal) {\n        // If we have a cacheSignal it means we're in a prospective render. If\n        // the input we're waiting on is coming from another cache, we do want\n        // to wait for it so that we can resolve this cache entry too.\n        workUnitStore.cacheSignal.inputReady().then(() => {\n          controller.abort()\n        })\n      } else {\n        // Otherwise we're in the final render and we should already have all\n        // our caches filled.\n        // If the prerender uses stages, we have wait until the runtime stage,\n        // at which point all runtime inputs will be resolved.\n        // (otherwise, a runtime prerender might consider `cookies()` hanging\n        //  even though they'd resolve in the next task.)\n        //\n        // We might still be waiting on some microtasks so we\n        // wait one tick before giving up. When we give up, we still want to\n        // render the content of this cache as deeply as we can so that we can\n        // suspend as deeply as possible in the tree or not at all if we don't\n        // end up waiting for the input.\n        const runtimeStagePromise = getRuntimeStagePromise(workUnitStore)\n        if (runtimeStagePromise) {\n          runtimeStagePromise.then(() =>\n            scheduleOnNextTick(() => controller.abort())\n          )\n        } else {\n          scheduleOnNextTick(() => controller.abort())\n        }\n      }\n\n      return controller.signal\n    case 'prerender-client':\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n    case 'request':\n    case 'cache':\n    case 'private-cache':\n    case 'unstable-cache':\n      return undefined\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workStore && workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender-client':\n      case 'prerender': {\n        const fallbackParams = workUnitStore.fallbackRouteParams\n\n        if (fallbackParams && fallbackParams.size > 0) {\n          // We are in a prerender with cacheComponents semantics. We are going to\n          // hang here and never resolve. This will cause the currently\n          // rendering component to effectively be a dynamic hole.\n          React.use(\n            makeHangingPromise(\n              workUnitStore.renderSignal,\n              workStore.route,\n              expression\n            )\n          )\n        }\n        break\n      }\n      case 'prerender-ppr': {\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams && fallbackParams.size > 0) {\n          return postponeWithTracking(\n            workStore.route,\n            expression,\n            workUnitStore.dynamicTracking\n          )\n        }\n        break\n      }\n      case 'prerender-runtime':\n        throw new InvariantError(\n          `\\`${expression}\\` was called during a runtime prerender. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n        )\n      case 'cache':\n      case 'private-cache':\n        throw new InvariantError(\n          `\\`${expression}\\` was called inside a cache scope. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n        )\n      case 'prerender-legacy':\n      case 'request':\n      case 'unstable-cache':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n}\n\nexport function useDynamicSearchParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (!workStore) {\n    // We assume pages router context and just return\n    return\n  }\n\n  if (!workUnitStore) {\n    throwForMissingRequestStore(expression)\n  }\n\n  switch (workUnitStore.type) {\n    case 'prerender-client': {\n      React.use(\n        makeHangingPromise(\n          workUnitStore.renderSignal,\n          workStore.route,\n          expression\n        )\n      )\n      break\n    }\n    case 'prerender-legacy':\n    case 'prerender-ppr': {\n      if (workStore.forceStatic) {\n        return\n      }\n      throw new BailoutToCSRError(expression)\n    }\n    case 'prerender':\n    case 'prerender-runtime':\n      throw new InvariantError(\n        `\\`${expression}\\` was called from a Server Component. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n      )\n    case 'cache':\n    case 'unstable-cache':\n    case 'private-cache':\n      throw new InvariantError(\n        `\\`${expression}\\` was called inside a cache scope. Next.js should be preventing ${expression} from being included in server components statically, but did not in this case.`\n      )\n    case 'request':\n      return\n    default:\n      workUnitStore satisfies never\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\n\n// Common implicit body tags that React will treat as body when placed directly in html\nconst bodyAndImplicitTags =\n  'body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6'\n\n// Detects when RootLayoutBoundary (our framework marker component) appears\n// after Suspense in the component stack, indicating the root layout is wrapped\n// within a Suspense boundary. Ensures no body/html/implicit-body components are in between.\n//\n// Example matches:\n//   at Suspense (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\n//\n// Or with other components in between (but not body/html/implicit-body):\n//   at Suspense (<anonymous>)\n//   at SomeComponent (<anonymous>)\n//   at __next_root_layout_boundary__ (<anonymous>)\nconst hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex = new RegExp(\n  `\\\\n\\\\s+at Suspense \\\\(<anonymous>\\\\)(?:(?!\\\\n\\\\s+at (?:${bodyAndImplicitTags}) \\\\(<anonymous>\\\\))[\\\\s\\\\S])*?\\\\n\\\\s+at ${ROOT_LAYOUT_BOUNDARY_NAME} \\\\([^\\\\n]*\\\\)`\n)\n\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  workStore: WorkStore,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (\n    hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex.test(\n      componentStack\n    )\n  ) {\n    // For Suspense within body, the prelude wouldn't be empty so it wouldn't violate the empty static shells rule.\n    // But if you have Suspense above body, the prelude is empty but we allow that because having Suspense\n    // is an explicit signal from the user that they acknowledge the empty shell and want dynamic rendering.\n    dynamicValidation.hasAllowedDynamic = true\n    dynamicValidation.hasSuspenseAboveBody = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    // this error had a Suspense boundary above it so we don't need to report it as a source\n    // of disallowed\n    dynamicValidation.hasAllowedDynamic = true\n    return\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    // This task was the task that called the sync error.\n    dynamicValidation.dynamicErrors.push(\n      clientDynamic.syncDynamicErrorWithStack\n    )\n    return\n  } else {\n    const message =\n      `Route \"${workStore.route}\": Uncached data was accessed outside of ` +\n      '<Suspense>. This delays the entire page from rendering, resulting in a ' +\n      'slow user experience. Learn more: ' +\n      'https://nextjs.org/docs/messages/blocking-route'\n    const error = createErrorWithComponentOrOwnerStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\n/**\n * In dev mode, we prefer using the owner stack, otherwise the provided\n * component stack is used.\n */\nfunction createErrorWithComponentOrOwnerStack(\n  message: string,\n  componentStack: string\n) {\n  const ownerStack =\n    process.env.NODE_ENV !== 'production' && React.captureOwnerStack\n      ? React.captureOwnerStack()\n      : null\n\n  const error = new Error(message)\n  error.stack = error.name + ': ' + message + (ownerStack ?? componentStack)\n  return error\n}\n\nexport enum PreludeState {\n  Full = 0,\n  Empty = 1,\n  Errored = 2,\n}\n\nexport function logDisallowedDynamicError(\n  workStore: WorkStore,\n  error: Error\n): void {\n  console.error(error)\n\n  if (!workStore.dev) {\n    if (workStore.hasReadableErrorStacks) {\n      console.error(\n        `To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.`\n      )\n    } else {\n      console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:\n  - Start the app in development mode by running \\`next dev\\`, then open \"${workStore.route}\" in your browser to investigate the error.\n  - Rerun the production build with \\`next build --debug-prerender\\` to generate better stack traces.`)\n    }\n  }\n}\n\nexport function throwIfDisallowedDynamic(\n  workStore: WorkStore,\n  prelude: PreludeState,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState\n): void {\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    logDisallowedDynamicError(\n      workStore,\n      serverDynamic.syncDynamicErrorWithStack\n    )\n    throw new StaticGenBailoutError()\n  }\n\n  if (prelude !== PreludeState.Full) {\n    if (dynamicValidation.hasSuspenseAboveBody) {\n      // This route has opted into allowing fully dynamic rendering\n      // by including a Suspense boundary above the body. In this case\n      // a lack of a shell is not considered disallowed so we simply return\n      return\n    }\n\n    // We didn't have any sync bailouts but there may be user code which\n    // blocked the root. We would have captured these during the prerender\n    // and can log them here and then terminate the build/validating render\n    const dynamicErrors = dynamicValidation.dynamicErrors\n    if (dynamicErrors.length > 0) {\n      for (let i = 0; i < dynamicErrors.length; i++) {\n        logDisallowedDynamicError(workStore, dynamicErrors[i])\n      }\n\n      throw new StaticGenBailoutError()\n    }\n\n    // If we got this far then the only other thing that could be blocking\n    // the root is dynamic Viewport. If this is dynamic then\n    // you need to opt into that by adding a Suspense boundary above the body\n    // to indicate your are ok with fully dynamic rendering.\n    if (dynamicValidation.hasDynamicViewport) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`\n      )\n      throw new StaticGenBailoutError()\n    }\n\n    if (prelude === PreludeState.Empty) {\n      // If we ever get this far then we messed up the tracking of invalid dynamic.\n      // We still adhere to the constraint that you must produce a shell but invite the\n      // user to report this as a bug in Next.js.\n      console.error(\n        `Route \"${workStore.route}\" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`\n      )\n      throw new StaticGenBailoutError()\n    }\n  } else {\n    if (\n      dynamicValidation.hasAllowedDynamic === false &&\n      dynamicValidation.hasDynamicMetadata\n    ) {\n      console.error(\n        `Route \"${workStore.route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or uncached external data (\\`fetch(...)\\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`\n      )\n      throw new StaticGenBailoutError()\n    }\n  }\n}\n\nexport function delayUntilRuntimeStage<T>(\n  prerenderStore: PrerenderStoreModernRuntime,\n  result: Promise<T>\n): Promise<T> {\n  if (prerenderStore.runtimeStagePromise) {\n    return prerenderStore.runtimeStagePromise.then(() => result)\n  }\n  return result\n}\n"], "names": ["React", "DynamicServerError", "StaticGenBailoutError", "getRuntimeStagePromise", "throwForMissingRequestStore", "workUnitAsyncStorage", "workAsyncStorage", "makeHangingPromise", "METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME", "scheduleOnNextTick", "BailoutToCSRError", "InvariantError", "RenderStage", "hasPostpone", "unstable_postpone", "createDynamicTrackingState", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicErrorWithStack", "createDynamicValidationState", "hasSuspenseAboveBody", "hasDynamicMetadata", "hasDynamicViewport", "hasAllowedDynamic", "dynamicErrors", "getFirstDynamicReason", "trackingState", "expression", "markCurrentScopeAsDynamic", "store", "workUnitStore", "type", "forceDynamic", "forceStatic", "dynamicShouldError", "route", "postponeWithTracking", "dynamicTracking", "revalidate", "err", "dynamicUsageDescription", "dynamicUsageStack", "stack", "process", "env", "NODE_ENV", "usedDynamic", "throwToInterruptStaticGeneration", "prerenderStore", "trackDynamicDataInDynamicRender", "abortOnSynchronousDynamicDataAccess", "reason", "error", "createPrerenderInterruptedError", "controller", "abort", "push", "Error", "undefined", "abortOnSynchronousPlatformIOAccess", "errorWithStack", "trackSynchronousPlatformIOAccessInDev", "requestStore", "stagedRendering", "advanceStage", "Dynamic", "abortAndThrowOnSynchronousRequestDataAccess", "prerenderSignal", "signal", "aborted", "Postpone", "getStore", "assertPostpone", "createPostponeReason", "isDynamicPostpone", "message", "isDynamicPostponeReason", "includes", "NEXT_PRERENDER_INTERRUPTED", "digest", "isPrerenderInterruptedError", "accessedDynamicData", "length", "consumeDynamicAccess", "serverDynamic", "clientDynamic", "formatDynamicAPIAccesses", "filter", "access", "map", "split", "slice", "line", "join", "createRenderInBrowserAbortSignal", "AbortController", "createHangingInputAbortSignal", "cacheSignal", "inputReady", "then", "runtimeStagePromise", "annotateDynamicAccess", "useDynamicRouteParams", "workStore", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "size", "use", "renderSignal", "useDynamicSearchParams", "hasSuspenseRegex", "bodyAndImplicitTags", "hasSuspenseBeforeRootLayoutWithoutBodyOrImplicitBodyRegex", "RegExp", "hasMetadataRegex", "hasViewportRegex", "hasOutletRegex", "trackAllowedDynamicAccess", "componentStack", "dynamicValidation", "test", "createErrorWithComponentOrOwnerStack", "ownerStack", "captureOwnerStack", "name", "PreludeState", "logDisallowedDynamicError", "console", "dev", "hasReadableErrorStacks", "throwIfDisallowedDynamic", "prelude", "i", "delayUntilRuntimeStage", "result"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;CAoBC,GAWD,wFAAwF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACxF,OAAOA,WAAW,QAAO;AAEzB,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,qBAAqB,QAAQ,oDAAmD;AACzF,SACEC,sBAAsB,EACtBC,2BAA2B,EAC3BC,oBAAoB,QACf,qCAAoC;AAC3C,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SACEC,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,EACpBC,yBAAyB,QACpB,yCAAwC;AAC/C,SAASC,kBAAkB,QAAQ,sBAAqB;AACxD,SAASC,iBAAiB,QAAQ,+CAA8C;AAChF,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SAASC,WAAW,QAAQ,qBAAoB;;;;;;;;;;;;AAEhD,MAAMC,cAAc,OAAOhB,0aAAAA,CAAMiB,iBAAiB,KAAK;AAwChD,SAASC,2BACdC,sBAA2C;IAE3C,OAAO;QACLA;QACAC,iBAAiB,EAAE;QACnBC,2BAA2B;IAC7B;AACF;AAEO,SAASC;IACd,OAAO;QACLC,sBAAsB;QACtBC,oBAAoB;QACpBC,oBAAoB;QACpBC,mBAAmB;QACnBC,eAAe,EAAE;IACnB;AACF;AAEO,SAASC,sBACdC,aAAmC;QAE5BA;IAAP,OAAA,CAAOA,kCAAAA,cAAcT,eAAe,CAAC,EAAE,KAAA,OAAA,KAAA,IAAhCS,gCAAkCC,UAAU;AACrD;AASO,SAASC,0BACdC,KAAgB,EAChBC,aAAuE,EACvEH,UAAkB;IAElB,IAAIG,eAAe;QACjB,OAAQA,cAAcC,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,iEAAiE;gBACjE,kEAAkE;gBAClE,gEAAgE;gBAChE,kCAAkC;gBAClC;YACF,KAAK;gBACH,0DAA0D;gBAC1D;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACED;QACJ;IACF;IAEA,2EAA2E;IAC3E,4EAA4E;IAC5E,2DAA2D;IAC3D,IAAID,MAAMG,YAAY,IAAIH,MAAMI,WAAW,EAAE;IAE7C,IAAIJ,MAAMK,kBAAkB,EAAE;QAC5B,MAAM,OAAA,cAEL,CAFK,IAAInC,ibAAAA,CACR,CAAC,MAAM,EAAE8B,MAAMM,KAAK,CAAC,8EAA8E,EAAER,WAAW,4HAA4H,CAAC,GADzO,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIG,eAAe;QACjB,OAAQA,cAAcC,IAAI;YACxB,KAAK;gBACH,OAAOK,qBACLP,MAAMM,KAAK,EACXR,YACAG,cAAcO,eAAe;YAEjC,KAAK;gBACHP,cAAcQ,UAAU,GAAG;gBAE3B,uEAAuE;gBACvE,oCAAoC;gBACpC,MAAMC,MAAM,OAAA,cAEX,CAFW,IAAIzC,yaAAAA,CACd,CAAC,MAAM,EAAE+B,MAAMM,KAAK,CAAC,iDAAiD,EAAER,WAAW,2EAA2E,CAAC,GADrJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAE,MAAMW,uBAAuB,GAAGb;gBAChCE,MAAMY,iBAAiB,GAAGF,IAAIG,KAAK;gBAEnC,MAAMH;YACR,KAAK;gBACH,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;oBACzCf,cAAcgB,WAAW,GAAG;gBAC9B;gBACA;YACF;gBACEhB;QACJ;IACF;AACF;AAQO,SAASiB,iCACdpB,UAAkB,EAClBE,KAAgB,EAChBmB,cAAoC;IAEpC,uGAAuG;IACvG,MAAMT,MAAM,OAAA,cAEX,CAFW,IAAIzC,yaAAAA,CACd,CAAC,MAAM,EAAE+B,MAAMM,KAAK,CAAC,mDAAmD,EAAER,WAAW,6EAA6E,CAAC,GADzJ,qBAAA;eAAA;oBAAA;sBAAA;IAEZ;IAEAqB,eAAeV,UAAU,GAAG;IAE5BT,MAAMW,uBAAuB,GAAGb;IAChCE,MAAMY,iBAAiB,GAAGF,IAAIG,KAAK;IAEnC,MAAMH;AACR;AASO,SAASU,gCAAgCnB,aAA4B;IAC1E,OAAQA,cAAcC,IAAI;QACxB,KAAK;QACL,KAAK;YACH,iEAAiE;YACjE,kEAAkE;YAClE,gEAAgE;YAChE,kCAAkC;YAClC;QACF,KAAK;YACH,0DAA0D;YAC1D;QACF,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH;QACF,KAAK;YACH,IAAIY,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzCf,cAAcgB,WAAW,GAAG;YAC9B;YACA;QACF;YACEhB;IACJ;AACF;AAEA,SAASoB,oCACPf,KAAa,EACbR,UAAkB,EAClBqB,cAAoC;IAEpC,MAAMG,SAAS,CAAC,MAAM,EAAEhB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;IAE9G,MAAMyB,QAAQC,gCAAgCF;IAE9CH,eAAeM,UAAU,CAACC,KAAK,CAACH;IAEhC,MAAMf,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBpB,eAAe,CAACuC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACfd,OAAOL,gBAAgBrB,sBAAsB,GACzC,IAAIyC,QAAQf,KAAK,GACjBgB;YACJ/B;QACF;IACF;AACF;AAEO,SAASgC,mCACdxB,KAAa,EACbR,UAAkB,EAClBiC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtDa,oCAAoCf,OAAOR,YAAYqB;IACvD,sFAAsF;IACtF,0FAA0F;IAC1F,sFAAsF;IACtF,oDAAoD;IACpD,IAAIX,iBAAiB;QACnB,IAAIA,gBAAgBnB,yBAAyB,KAAK,MAAM;YACtDmB,gBAAgBnB,yBAAyB,GAAG0C;QAC9C;IACF;AACF;AAEO,SAASC,sCACdC,YAA0B;IAE1B,oFAAoF;IACpF,yDAAyD;IACzD,IAAIA,aAAaC,eAAe,EAAE;QAChC,+CAA+C;QAC/C,6FAA6F;QAC7FD,aAAaC,eAAe,CAACC,YAAY,CAACpD,8ZAAAA,CAAYqD,OAAO;IAC/D;AACF;AAYO,SAASC,4CACd/B,KAAa,EACbR,UAAkB,EAClBiC,cAAqB,EACrBZ,cAAoC;IAEpC,MAAMmB,kBAAkBnB,eAAeM,UAAU,CAACc,MAAM;IACxD,IAAID,gBAAgBE,OAAO,KAAK,OAAO;QACrC,8FAA8F;QAC9F,mFAAmF;QACnF,wFAAwF;QACxF,4FAA4F;QAC5F,0BAA0B;QAC1BnB,oCAAoCf,OAAOR,YAAYqB;QACvD,sFAAsF;QACtF,0FAA0F;QAC1F,sFAAsF;QACtF,oDAAoD;QACpD,MAAMX,kBAAkBW,eAAeX,eAAe;QACtD,IAAIA,iBAAiB;YACnB,IAAIA,gBAAgBnB,yBAAyB,KAAK,MAAM;gBACtDmB,gBAAgBnB,yBAAyB,GAAG0C;YAC9C;QACF;IACF;IACA,MAAMP,gCACJ,CAAC,MAAM,EAAElB,MAAM,iEAAiE,EAAER,WAAW,CAAC,CAAC;AAEnG;AASO,SAAS2C,SAAS,EAAEnB,MAAM,EAAEhB,KAAK,EAAiB;IACvD,MAAMa,iBAAiB9C,2SAAAA,CAAqBqE,QAAQ;IACpD,MAAMlC,kBACJW,kBAAkBA,eAAejB,IAAI,KAAK,kBACtCiB,eAAeX,eAAe,GAC9B;IACND,qBAAqBD,OAAOgB,QAAQd;AACtC;AAEO,SAASD,qBACdD,KAAa,EACbR,UAAkB,EAClBU,eAA4C;IAE5CmC;IACA,IAAInC,iBAAiB;QACnBA,gBAAgBpB,eAAe,CAACuC,IAAI,CAAC;YACnC,0EAA0E;YAC1E,eAAe;YACfd,OAAOL,gBAAgBrB,sBAAsB,GACzC,IAAIyC,QAAQf,KAAK,GACjBgB;YACJ/B;QACF;IACF;IAEA9B,0aAAAA,CAAMiB,iBAAiB,CAAC2D,qBAAqBtC,OAAOR;AACtD;AAEA,SAAS8C,qBAAqBtC,KAAa,EAAER,UAAkB;IAC7D,OACE,CAAC,MAAM,EAAEQ,MAAM,iEAAiE,EAAER,WAAW,EAAE,CAAC,GAChG,CAAC,+EAA+E,CAAC,GACjF,CAAC,iFAAiF,CAAC;AAEvF;AAEO,SAAS+C,kBAAkBnC,GAAY;IAC5C,IACE,OAAOA,QAAQ,YACfA,QAAQ,QACR,OAAQA,IAAYoC,OAAO,KAAK,UAChC;QACA,OAAOC,wBAAyBrC,IAAYoC,OAAO;IACrD;IACA,OAAO;AACT;AAEA,SAASC,wBAAwBzB,MAAc;IAC7C,OACEA,OAAO0B,QAAQ,CACb,sEAEF1B,OAAO0B,QAAQ,CACb;AAGN;AAEA,IAAID,wBAAwBH,qBAAqB,OAAO,YAAY,OAAO;IACzE,MAAM,OAAA,cAEL,CAFK,IAAIhB,MACR,2FADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMqB,6BAA6B;AAEnC,SAASzB,gCAAgCsB,OAAe;IACtD,MAAMvB,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMkB,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC7BvB,MAAc2B,MAAM,GAAGD;IACzB,OAAO1B;AACT;AAMO,SAAS4B,4BACd5B,KAAc;IAEd,OACE,OAAOA,UAAU,YACjBA,UAAU,QACTA,MAAc2B,MAAM,KAAKD,8BAC1B,UAAU1B,SACV,aAAaA,SACbA,iBAAiBK;AAErB;AAEO,SAASwB,oBACdhE,eAAqC;IAErC,OAAOA,gBAAgBiE,MAAM,GAAG;AAClC;AAEO,SAASC,qBACdC,aAAmC,EACnCC,aAAmC;IAEnC,oEAAoE;IACpE,0EAA0E;IAC1E,SAAS;IACTD,cAAcnE,eAAe,CAACuC,IAAI,IAAI6B,cAAcpE,eAAe;IACnE,OAAOmE,cAAcnE,eAAe;AACtC;AAEO,SAASqE,yBACdrE,eAAqC;IAErC,OAAOA,gBACJsE,MAAM,CACL,CAACC,SACC,OAAOA,OAAO9C,KAAK,KAAK,YAAY8C,OAAO9C,KAAK,CAACwC,MAAM,GAAG,GAE7DO,GAAG,CAAC,CAAC,EAAE9D,UAAU,EAAEe,KAAK,EAAE;QACzBA,QAAQA,MACLgD,KAAK,CAAC,MACP,wEAAwE;QACxE,qEAAqE;QACrE,uDAAuD;SACtDC,KAAK,CAAC,GACNJ,MAAM,CAAC,CAACK;YACP,kDAAkD;YAClD,IAAIA,KAAKf,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;YACT;YAEA,oDAAoD;YACpD,IAAIe,KAAKf,QAAQ,CAAC,mBAAmB;gBACnC,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAIe,KAAKf,QAAQ,CAAC,YAAY;gBAC5B,OAAO;YACT;YAEA,OAAO;QACT,GACCgB,IAAI,CAAC;QACR,OAAO,CAAC,0BAA0B,EAAElE,WAAW,GAAG,EAAEe,OAAO;IAC7D;AACJ;AAEA,SAAS8B;IACP,IAAI,CAAC3D,aAAa;QAChB,MAAM,OAAA,cAEL,CAFK,IAAI4C,MACR,CAAC,gIAAgI,CAAC,GAD9H,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAMO,SAASqC;IACd,MAAMxC,aAAa,IAAIyC;IACvBzC,WAAWC,KAAK,CAAC,OAAA,cAA0C,CAA1C,IAAI7C,8aAAAA,CAAkB,sBAAtB,qBAAA;eAAA;oBAAA;sBAAA;IAAyC;IAC1D,OAAO4C,WAAWc,MAAM;AAC1B;AAOO,SAAS4B,8BACdlE,aAA4B;IAE5B,OAAQA,cAAcC,IAAI;QACxB,KAAK;QACL,KAAK;YACH,MAAMuB,aAAa,IAAIyC;YAEvB,IAAIjE,cAAcmE,WAAW,EAAE;gBAC7B,sEAAsE;gBACtE,sEAAsE;gBACtE,8DAA8D;gBAC9DnE,cAAcmE,WAAW,CAACC,UAAU,GAAGC,IAAI,CAAC;oBAC1C7C,WAAWC,KAAK;gBAClB;YACF,OAAO;gBACL,qEAAqE;gBACrE,qBAAqB;gBACrB,sEAAsE;gBACtE,sDAAsD;gBACtD,qEAAqE;gBACrE,iDAAiD;gBACjD,EAAE;gBACF,qDAAqD;gBACrD,oEAAoE;gBACpE,sEAAsE;gBACtE,sEAAsE;gBACtE,gCAAgC;gBAChC,MAAM6C,0BAAsBpG,6SAAAA,EAAuB8B;gBACnD,IAAIsE,qBAAqB;oBACvBA,oBAAoBD,IAAI,CAAC,QACvB1F,uYAAAA,EAAmB,IAAM6C,WAAWC,KAAK;gBAE7C,OAAO;wBACL9C,uYAAAA,EAAmB,IAAM6C,WAAWC,KAAK;gBAC3C;YACF;YAEA,OAAOD,WAAWc,MAAM;QAC1B,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAOV;QACT;YACE5B;IACJ;AACF;AAEO,SAASuE,sBACd1E,UAAkB,EAClBqB,cAAoC;IAEpC,MAAMX,kBAAkBW,eAAeX,eAAe;IACtD,IAAIA,iBAAiB;QACnBA,gBAAgBpB,eAAe,CAACuC,IAAI,CAAC;YACnCd,OAAOL,gBAAgBrB,sBAAsB,GACzC,IAAIyC,QAAQf,KAAK,GACjBgB;YACJ/B;QACF;IACF;AACF;AAEO,SAAS2E,sBAAsB3E,UAAkB;IACtD,MAAM4E,YAAYpG,uRAAAA,CAAiBoE,QAAQ;IAC3C,MAAMzC,gBAAgB5B,2SAAAA,CAAqBqE,QAAQ;IACnD,IAAIgC,aAAazE,eAAe;QAC9B,OAAQA,cAAcC,IAAI;YACxB,KAAK;YACL,KAAK;gBAAa;oBAChB,MAAMyE,iBAAiB1E,cAAc2E,mBAAmB;oBAExD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;wBAC7C,wEAAwE;wBACxE,6DAA6D;wBAC7D,wDAAwD;wBACxD7G,0aAAAA,CAAM8G,GAAG,KACPvG,8ZAAAA,EACE0B,cAAc8E,YAAY,EAC1BL,UAAUpE,KAAK,EACfR;oBAGN;oBACA;gBACF;YACA,KAAK;gBAAiB;oBACpB,MAAM6E,iBAAiB1E,cAAc2E,mBAAmB;oBACxD,IAAID,kBAAkBA,eAAeE,IAAI,GAAG,GAAG;wBAC7C,OAAOtE,qBACLmE,UAAUpE,KAAK,EACfR,YACAG,cAAcO,eAAe;oBAEjC;oBACA;gBACF;YACA,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAI1B,sZAAAA,CACR,CAAC,EAAE,EAAEgB,WAAW,uEAAuE,EAAEA,WAAW,+EAA+E,CAAC,GADhL,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIhB,sZAAAA,CACR,CAAC,EAAE,EAAEgB,WAAW,iEAAiE,EAAEA,WAAW,+EAA+E,CAAC,GAD1K,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEG;QACJ;IACF;AACF;AAEO,SAAS+E,uBAAuBlF,UAAkB;IACvD,MAAM4E,YAAYpG,uRAAAA,CAAiBoE,QAAQ;IAC3C,MAAMzC,gBAAgB5B,2SAAAA,CAAqBqE,QAAQ;IAEnD,IAAI,CAACgC,WAAW;QACd,iDAAiD;QACjD;IACF;IAEA,IAAI,CAACzE,eAAe;YAClB7B,kTAAAA,EAA4B0B;IAC9B;IAEA,OAAQG,cAAcC,IAAI;QACxB,KAAK;YAAoB;gBACvBlC,0aAAAA,CAAM8G,GAAG,KACPvG,8ZAAAA,EACE0B,cAAc8E,YAAY,EAC1BL,UAAUpE,KAAK,EACfR;gBAGJ;YACF;QACA,KAAK;QACL,KAAK;YAAiB;gBACpB,IAAI4E,UAAUtE,WAAW,EAAE;oBACzB;gBACF;gBACA,MAAM,OAAA,cAAiC,CAAjC,IAAIvB,8aAAAA,CAAkBiB,aAAtB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAgC;YACxC;QACA,KAAK;QACL,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAIhB,sZAAAA,CACR,CAAC,EAAE,EAAEgB,WAAW,oEAAoE,EAAEA,WAAW,+EAA+E,CAAC,GAD7K,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;QACL,KAAK;QACL,KAAK;YACH,MAAM,OAAA,cAEL,CAFK,IAAIhB,sZAAAA,CACR,CAAC,EAAE,EAAEgB,WAAW,iEAAiE,EAAEA,WAAW,+EAA+E,CAAC,GAD1K,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF,KAAK;YACH;QACF;YACEG;IACJ;AACF;AAEA,MAAMgF,mBAAmB;AAEzB,uFAAuF;AACvF,MAAMC,sBACJ;AAEF,2EAA2E;AAC3E,+EAA+E;AAC/E,4FAA4F;AAC5F,EAAE;AACF,mBAAmB;AACnB,8BAA8B;AAC9B,mDAAmD;AACnD,EAAE;AACF,yEAAyE;AACzE,8BAA8B;AAC9B,mCAAmC;AACnC,mDAAmD;AACnD,MAAMC,4DAA4D,IAAIC,OACpE,CAAC,uDAAuD,EAAEF,oBAAoB,yCAAyC,EAAEvG,uaAAAA,CAA0B,cAAc,CAAC;AAGpK,MAAM0G,mBAAmB,IAAID,OAC3B,CAAC,UAAU,EAAE5G,oaAAAA,CAAuB,QAAQ,CAAC;AAE/C,MAAM8G,mBAAmB,IAAIF,OAC3B,CAAC,UAAU,EAAE3G,oaAAAA,CAAuB,QAAQ,CAAC;AAE/C,MAAM8G,iBAAiB,IAAIH,OAAO,CAAC,UAAU,EAAE1G,kaAAAA,CAAqB,QAAQ,CAAC;AAEtE,SAAS8G,0BACdd,SAAoB,EACpBe,cAAsB,EACtBC,iBAAyC,EACzClC,aAAmC;IAEnC,IAAI+B,eAAeI,IAAI,CAACF,iBAAiB;QACvC,kGAAkG;QAClG;IACF,OAAO,IAAIJ,iBAAiBM,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBlG,kBAAkB,GAAG;QACvC;IACF,OAAO,IAAI8F,iBAAiBK,IAAI,CAACF,iBAAiB;QAChDC,kBAAkBjG,kBAAkB,GAAG;QACvC;IACF,OAAO,IACL0F,0DAA0DQ,IAAI,CAC5DF,iBAEF;QACA,+GAA+G;QAC/G,sGAAsG;QACtG,wGAAwG;QACxGC,kBAAkBhG,iBAAiB,GAAG;QACtCgG,kBAAkBnG,oBAAoB,GAAG;QACzC;IACF,OAAO,IAAI0F,iBAAiBU,IAAI,CAACF,iBAAiB;QAChD,wFAAwF;QACxF,gBAAgB;QAChBC,kBAAkBhG,iBAAiB,GAAG;QACtC;IACF,OAAO,IAAI8D,cAAcnE,yBAAyB,EAAE;QAClD,qDAAqD;QACrDqG,kBAAkB/F,aAAa,CAACgC,IAAI,CAClC6B,cAAcnE,yBAAyB;QAEzC;IACF,OAAO;QACL,MAAMyD,UACJ,CAAC,OAAO,EAAE4B,UAAUpE,KAAK,CAAC,yCAAyC,CAAC,GACpE,4EACA,uCACA;QACF,MAAMiB,QAAQqE,qCAAqC9C,SAAS2C;QAC5DC,kBAAkB/F,aAAa,CAACgC,IAAI,CAACJ;QACrC;IACF;AACF;AAEA;;;CAGC,GACD,SAASqE,qCACP9C,OAAe,EACf2C,cAAsB;IAEtB,MAAMI,aACJ/E,QAAQC,GAAG,CAACC,QAAQ,gCAAK,gBAAgBhD,0aAAAA,CAAM8H,iBAAiB,GAC5D9H,0aAAAA,CAAM8H,iBAAiB,KACvB;IAEN,MAAMvE,QAAQ,OAAA,cAAkB,CAAlB,IAAIK,MAAMkB,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB;IAC/BvB,MAAMV,KAAK,GAAGU,MAAMwE,IAAI,GAAG,OAAOjD,UAAW+C,CAAAA,cAAcJ,cAAa;IACxE,OAAOlE;AACT;AAEO,IAAKyE,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;;;;WAAAA;MAIX;AAEM,SAASC,0BACdvB,SAAoB,EACpBnD,KAAY;IAEZ2E,QAAQ3E,KAAK,CAACA;IAEd,IAAI,CAACmD,UAAUyB,GAAG,EAAE;QAClB,IAAIzB,UAAU0B,sBAAsB,EAAE;YACpCF,QAAQ3E,KAAK,CACX,CAAC,iIAAiI,EAAEmD,UAAUpE,KAAK,CAAC,2CAA2C,CAAC;QAEpM,OAAO;YACL4F,QAAQ3E,KAAK,CAAC,CAAC;0EACqD,EAAEmD,UAAUpE,KAAK,CAAC;qGACS,CAAC;QAClG;IACF;AACF;AAEO,SAAS+F,yBACd3B,SAAoB,EACpB4B,OAAqB,EACrBZ,iBAAyC,EACzCnC,aAAmC;IAEnC,IAAIA,cAAclE,yBAAyB,EAAE;QAC3C4G,0BACEvB,WACAnB,cAAclE,yBAAyB;QAEzC,MAAM,IAAInB,ibAAAA;IACZ;IAEA,IAAIoI,YAAAA,GAA+B;QACjC,IAAIZ,kBAAkBnG,oBAAoB,EAAE;YAC1C,6DAA6D;YAC7D,gEAAgE;YAChE,qEAAqE;YACrE;QACF;QAEA,oEAAoE;QACpE,sEAAsE;QACtE,uEAAuE;QACvE,MAAMI,gBAAgB+F,kBAAkB/F,aAAa;QACrD,IAAIA,cAAc0D,MAAM,GAAG,GAAG;YAC5B,IAAK,IAAIkD,IAAI,GAAGA,IAAI5G,cAAc0D,MAAM,EAAEkD,IAAK;gBAC7CN,0BAA0BvB,WAAW/E,aAAa,CAAC4G,EAAE;YACvD;YAEA,MAAM,IAAIrI,ibAAAA;QACZ;QAEA,sEAAsE;QACtE,wDAAwD;QACxD,yEAAyE;QACzE,wDAAwD;QACxD,IAAIwH,kBAAkBjG,kBAAkB,EAAE;YACxCyG,QAAQ3E,KAAK,CACX,CAAC,OAAO,EAAEmD,UAAUpE,KAAK,CAAC,8QAA8Q,CAAC;YAE3S,MAAM,IAAIpC,ibAAAA;QACZ;QAEA,IAAIoI,YAAAA,GAAgC;YAClC,6EAA6E;YAC7E,iFAAiF;YACjF,2CAA2C;YAC3CJ,QAAQ3E,KAAK,CACX,CAAC,OAAO,EAAEmD,UAAUpE,KAAK,CAAC,wGAAwG,CAAC;YAErI,MAAM,IAAIpC,ibAAAA;QACZ;IACF,OAAO;QACL,IACEwH,kBAAkBhG,iBAAiB,KAAK,SACxCgG,kBAAkBlG,kBAAkB,EACpC;YACA0G,QAAQ3E,KAAK,CACX,CAAC,OAAO,EAAEmD,UAAUpE,KAAK,CAAC,8PAA8P,CAAC;YAE3R,MAAM,IAAIpC,ibAAAA;QACZ;IACF;AACF;AAEO,SAASsI,uBACdrF,cAA2C,EAC3CsF,MAAkB;IAElB,IAAItF,eAAeoD,mBAAmB,EAAE;QACtC,OAAOpD,eAAeoD,mBAAmB,CAACD,IAAI,CAAC,IAAMmC;IACvD;IACA,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3064, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/unstable-rethrow.server.ts"], "sourcesContent": ["import { isHangingPromiseRejectionError } from '../../server/dynamic-rendering-utils'\nimport { isPostpone } from '../../server/lib/router-utils/is-postpone'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isNextRouterError } from './is-next-router-error'\nimport {\n  isDynamicPostpone,\n  isPrerenderInterruptedError,\n} from '../../server/app-render/dynamic-rendering'\nimport { isDynamicServerError } from './hooks-server-context'\n\nexport function unstable_rethrow(error: unknown): void {\n  if (\n    isNextRouterError(error) ||\n    isBailoutToCSRError(error) ||\n    isDynamicServerError(error) ||\n    isDynamicPostpone(error) ||\n    isPostpone(error) ||\n    isHangingPromiseRejectionError(error) ||\n    isPrerenderInterruptedError(error)\n  ) {\n    throw error\n  }\n\n  if (error instanceof Error && 'cause' in error) {\n    unstable_rethrow(error.cause)\n  }\n}\n"], "names": ["isHangingPromiseRejectionError", "isPostpone", "isBailoutToCSRError", "isNextRouterError", "isDynamicPostpone", "isPrerenderInterruptedError", "isDynamicServerError", "unstable_rethrow", "error", "Error", "cause"], "mappings": ";;;;AAAA,SAASA,8BAA8B,QAAQ,uCAAsC;AACrF,SAASC,UAAU,QAAQ,4CAA2C;AACtE,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SACEC,iBAAiB,EACjBC,2BAA2B,QACtB,4CAA2C;AAClD,SAASC,oBAAoB,QAAQ,yBAAwB;;;;;;;AAEtD,SAASC,iBAAiBC,KAAc;IAC7C,QACEL,2aAAAA,EAAkBK,cAClBN,gbAAAA,EAAoBM,cACpBF,2aAAAA,EAAqBE,cACrBJ,qaAAAA,EAAkBI,cAClBP,iaAAAA,EAAWO,cACXR,0aAAAA,EAA+BQ,cAC/BH,+aAAAA,EAA4BG,QAC5B;QACA,MAAMA;IACR;IAEA,IAAIA,iBAAiBC,SAAS,WAAWD,OAAO;QAC9CD,iBAAiBC,MAAME,KAAK;IAC9B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3092, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/unstable-rethrow.ts"], "sourcesContent": ["/**\n * This function should be used to rethrow internal Next.js errors so that they can be handled by the framework.\n * When wrapping an API that uses errors to interrupt control flow, you should use this function before you do any error handling.\n * This function will rethrow the error if it is a Next.js error so it can be handled, otherwise it will do nothing.\n *\n * Read more: [Next.js Docs: `unstable_rethrow`](https://nextjs.org/docs/app/api-reference/functions/unstable_rethrow)\n */\nexport const unstable_rethrow =\n  typeof window === 'undefined'\n    ? (\n        require('./unstable-rethrow.server') as typeof import('./unstable-rethrow.server')\n      ).unstable_rethrow\n    : (\n        require('./unstable-rethrow.browser') as typeof import('./unstable-rethrow.browser')\n      ).unstable_rethrow\n"], "names": ["unstable_rethrow", "window", "require"], "mappings": "AAAA;;;;;;CAMC,GACD;;;;AAAO,MAAMA,mBACX,OAAOC,WAAW,qBAEZC,QAAQ,oQACRF,gBAAgB,GAEhBE,QAAQ,8BACRF,gBAAgB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3107, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/navigation.react-server.ts"], "sourcesContent": ["import { ReadonlyURLSearchParams } from './readonly-url-search-params'\n\nexport function unstable_isUnrecognizedActionError(): boolean {\n  throw new Error(\n    '`unstable_isUnrecognizedActionError` can only be used on the client.'\n  )\n}\n\nexport { redirect, permanentRedirect } from './redirect'\nexport { RedirectType } from './redirect-error'\nexport { notFound } from './not-found'\nexport { forbidden } from './forbidden'\nexport { unauthorized } from './unauthorized'\nexport { unstable_rethrow } from './unstable-rethrow'\nexport { ReadonlyURLSearchParams }\n"], "names": ["ReadonlyURLSearchParams", "unstable_isUnrecognizedActionError", "Error", "redirect", "permanentRedirect", "RedirectType", "notFound", "forbidden", "unauthorized", "unstable_rethrow"], "mappings": ";;;;AAAA,SAASA,uBAAuB,QAAQ,+BAA8B;AAQtE,SAASG,QAAQ,EAAEC,iBAAiB,QAAQ,aAAY;AACxD,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,QAAQ,QAAQ,cAAa;AACtC,SAASC,SAAS,QAAQ,cAAa;AACvC,SAASC,YAAY,QAAQ,iBAAgB;AAC7C,SAASC,gBAAgB,QAAQ,qBAAoB;;AAX9C,SAASR;IACd,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,yEADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3138, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/navigation.ts"], "sourcesContent": ["import type { Params } from '../../server/request/params'\n\nimport React, { useContext, useMemo, use } from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  type AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n  NavigationPromisesContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport {\n  computeSelectedLayoutSegment,\n  getSelectedLayoutSegmentPath,\n} from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './readonly-url-search-params'\n\nconst useDynamicRouteParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicRouteParams\n    : undefined\n\nconst useDynamicSearchParams =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/dynamic-rendering') as typeof import('../../server/app-render/dynamic-rendering')\n      ).useDynamicSearchParams\n    : undefined\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you *read* the current URL's search parameters.\n *\n * Learn more about [`URLSearchParams` on MDN](https://developer.mozilla.org/docs/Web/API/URLSearchParams)\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useSearchParams } from 'next/navigation'\n *\n * export default function Page() {\n *   const searchParams = useSearchParams()\n *   searchParams.get('foo') // returns 'bar' when ?foo=bar\n *   // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSearchParams`](https://nextjs.org/docs/app/api-reference/functions/use-search-params)\n */\n// Client components API\nexport function useSearchParams(): ReadonlyURLSearchParams {\n  useDynamicSearchParams?.('useSearchParams()')\n\n  const searchParams = useContext(SearchParamsContext)\n\n  // In the case where this is `null`, the compat types added in\n  // `next-env.d.ts` will add a new overload that changes the return type to\n  // include `null`.\n  const readonlySearchParams = useMemo(() => {\n    if (!searchParams) {\n      // When the router is not ready in pages, we won't have the search params\n      // available.\n      return null\n    }\n\n    return new ReadonlyURLSearchParams(searchParams)\n  }, [searchParams]) as ReadonlyURLSearchParams\n\n  // Instrument with Suspense DevTools (dev-only)\n  if (process.env.NODE_ENV !== 'production' && 'use' in React) {\n    const navigationPromises = use(NavigationPromisesContext)\n    if (navigationPromises) {\n      return use(navigationPromises.searchParams)\n    }\n  }\n\n  return readonlySearchParams\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the current URL's pathname.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { usePathname } from 'next/navigation'\n *\n * export default function Page() {\n *  const pathname = usePathname() // returns \"/dashboard\" on /dashboard?foo=bar\n *  // ...\n * }\n * ```\n *\n * Read more: [Next.js Docs: `usePathname`](https://nextjs.org/docs/app/api-reference/functions/use-pathname)\n */\n// Client components API\nexport function usePathname(): string {\n  useDynamicRouteParams?.('usePathname()')\n\n  // In the case where this is `null`, the compat types added in `next-env.d.ts`\n  // will add a new overload that changes the return type to include `null`.\n  const pathname = useContext(PathnameContext) as string\n\n  // Instrument with Suspense DevTools (dev-only)\n  if (process.env.NODE_ENV !== 'production' && 'use' in React) {\n    const navigationPromises = use(NavigationPromisesContext)\n    if (navigationPromises) {\n      return use(navigationPromises.pathname)\n    }\n  }\n\n  return pathname\n}\n\n// Client components API\nexport {\n  ServerInsertedHTMLContext,\n  useServerInsertedHTML,\n} from '../../shared/lib/server-inserted-html.shared-runtime'\n\n/**\n *\n * This hook allows you to programmatically change routes inside [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components).\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useRouter } from 'next/navigation'\n *\n * export default function Page() {\n *  const router = useRouter()\n *  // ...\n *  router.push('/dashboard') // Navigate to /dashboard\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useRouter`](https://nextjs.org/docs/app/api-reference/functions/use-router)\n */\n// Client components API\nexport function useRouter(): AppRouterInstance {\n  const router = useContext(AppRouterContext)\n  if (router === null) {\n    throw new Error('invariant expected app router to be mounted')\n  }\n\n  return router\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read a route's dynamic params filled in by the current URL.\n *\n * @example\n * ```ts\n * \"use client\"\n * import { useParams } from 'next/navigation'\n *\n * export default function Page() {\n *   // on /dashboard/[team] where pathname is /dashboard/nextjs\n *   const { team } = useParams() // team === \"nextjs\"\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useParams`](https://nextjs.org/docs/app/api-reference/functions/use-params)\n */\n// Client components API\nexport function useParams<T extends Params = Params>(): T {\n  useDynamicRouteParams?.('useParams()')\n\n  const params = useContext(PathParamsContext) as T\n\n  // Instrument with Suspense DevTools (dev-only)\n  if (process.env.NODE_ENV !== 'production' && 'use' in React) {\n    const navigationPromises = use(NavigationPromisesContext)\n    if (navigationPromises) {\n      return use(navigationPromises.params) as T\n    }\n  }\n\n  return params\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segments **below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n *\n * import { useSelectedLayoutSegments } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segments = useSelectedLayoutSegments()\n *\n *   return (\n *     <ul>\n *       {segments.map((segment, index) => (\n *         <li key={index}>{segment}</li>\n *       ))}\n *     </ul>\n *   )\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegments`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segments)\n */\n// Client components API\nexport function useSelectedLayoutSegments(\n  parallelRouteKey: string = 'children'\n): string[] {\n  useDynamicRouteParams?.('useSelectedLayoutSegments()')\n\n  const context = useContext(LayoutRouterContext)\n  // @ts-expect-error This only happens in `pages`. Type is overwritten in navigation.d.ts\n  if (!context) return null\n\n  // Instrument with Suspense DevTools (dev-only)\n  if (process.env.NODE_ENV !== 'production' && 'use' in React) {\n    const navigationPromises = use(NavigationPromisesContext)\n    if (navigationPromises) {\n      const promise =\n        navigationPromises.selectedLayoutSegmentsPromises?.get(parallelRouteKey)\n      if (promise) {\n        // We should always have a promise here, but if we don't, it's not worth erroring over.\n        // We just won't be able to instrument it, but can still provide the value.\n        return use(promise)\n      }\n    }\n  }\n\n  return getSelectedLayoutSegmentPath(context.parentTree, parallelRouteKey)\n}\n\n/**\n * A [Client Component](https://nextjs.org/docs/app/building-your-application/rendering/client-components) hook\n * that lets you read the active route segment **one level below** the Layout it is called from.\n *\n * @example\n * ```ts\n * 'use client'\n * import { useSelectedLayoutSegment } from 'next/navigation'\n *\n * export default function ExampleClientComponent() {\n *   const segment = useSelectedLayoutSegment()\n *\n *   return <p>Active segment: {segment}</p>\n * }\n * ```\n *\n * Read more: [Next.js Docs: `useSelectedLayoutSegment`](https://nextjs.org/docs/app/api-reference/functions/use-selected-layout-segment)\n */\n// Client components API\nexport function useSelectedLayoutSegment(\n  parallelRouteKey: string = 'children'\n): string | null {\n  useDynamicRouteParams?.('useSelectedLayoutSegment()')\n  const navigationPromises = useContext(NavigationPromisesContext)\n  const selectedLayoutSegments = useSelectedLayoutSegments(parallelRouteKey)\n\n  // Instrument with Suspense DevTools (dev-only)\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    navigationPromises &&\n    'use' in React\n  ) {\n    const promise =\n      navigationPromises.selectedLayoutSegmentPromises?.get(parallelRouteKey)\n    if (promise) {\n      // We should always have a promise here, but if we don't, it's not worth erroring over.\n      // We just won't be able to instrument it, but can still provide the value.\n      return use(promise)\n    }\n  }\n\n  return computeSelectedLayoutSegment(selectedLayoutSegments, parallelRouteKey)\n}\n\nexport { unstable_isUnrecognizedActionError } from './unrecognized-action-error'\n\n// Shared components APIs\nexport {\n  notFound,\n  forbidden,\n  unauthorized,\n  redirect,\n  permanentRedirect,\n  RedirectType,\n  ReadonlyURLSearchParams,\n  unstable_rethrow,\n} from './navigation.react-server'\n"], "names": ["React", "useContext", "useMemo", "use", "AppRouterContext", "LayoutRouterContext", "SearchParamsContext", "PathnameContext", "PathParamsContext", "NavigationPromisesContext", "computeSelectedLayoutSegment", "getSelectedLayoutSegmentPath", "ReadonlyURLSearchParams", "useDynamicRouteParams", "window", "require", "undefined", "useDynamicSearchParams", "useSearchParams", "searchParams", "readonlySearchParams", "process", "env", "NODE_ENV", "navigationPromises", "usePathname", "pathname", "ServerInsertedHTMLContext", "useServerInsertedHTML", "useRouter", "router", "Error", "useParams", "params", "useSelectedLayoutSegments", "parallelRouteKey", "context", "promise", "selectedLayoutSegmentsPromises", "get", "parentTree", "useSelectedLayoutSegment", "selectedLayoutSegments", "selectedLayoutSegmentPromises", "unstable_isUnrecognizedActionError", "notFound", "forbidden", "unauthorized", "redirect", "permanentRedirect", "RedirectType", "unstable_rethrow"], "mappings": ";;;;;;;;;;;;;;AAEA,OAAOA,SAASC,UAAU,EAAEC,OAAO,EAAEC,GAAG,QAAQ,QAAO;AACvD,SACEC,gBAAgB,EAChBC,mBAAmB,QAEd,qDAAoD;AAC3D,SACEC,mBAAmB,EACnBC,eAAe,EACfC,iBAAiB,EACjBC,yBAAyB,QACpB,uDAAsD;AAC7D,SACEC,4BAA4B,EAC5BC,4BAA4B,QACvB,2BAA0B;AACjC,SAASC,uBAAuB,QAAQ,+BAA8B;AAsGtE,wBAAwB;AACxB,SACEe,yBAAyB,EACzBC,qBAAqB,QAChB,uDAAsD;AAgK7D,SAASgB,kCAAkC,QAAQ,8BAA6B;AAEhF,yBAAyB;AACzB,SACEC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,QAAQ,EACRC,iBAAiB,EACjBC,YAAY,EACZtC,uBAAuB,EACvBuC,gBAAgB,QACX,4BAA2B;;;;;;AApRlC,MAAMtC,wBACJ,OAAOC,WAAW,qBAEZC,QAAQ,8PACRF,qBAAqB,GACvBG;AAEN,MAAMC,yBACJ,OAAOH,WAAW,qBAEZC,QAAQ,8PACRE,sBAAsB,GACxBD;AAuBC,SAASE;IACdD,yBAAyB;IAEzB,MAAME,mBAAelB,6aAAAA,EAAWK,gdAAAA;IAEhC,8DAA8D;IAC9D,0EAA0E;IAC1E,kBAAkB;IAClB,MAAMc,2BAAuBlB,0aAAAA,EAAQ;QACnC,IAAI,CAACiB,cAAc;YACjB,yEAAyE;YACzE,aAAa;YACb,OAAO;QACT;QAEA,OAAO,IAAIP,ubAAAA,CAAwBO;IACrC,GAAG;QAACA;KAAa;IAEjB,+CAA+C;IAC/C,IAAIE,QAAQC,GAAG,CAACC,QAAQ,gCAAK,gBAAgB,SAASvB,0aAAAA,EAAO;QAC3D,MAAMwB,yBAAqBrB,saAAAA,EAAIM,sdAAAA;QAC/B,IAAIe,oBAAoB;YACtB,WAAOrB,saAAAA,EAAIqB,mBAAmBL,YAAY;QAC5C;IACF;IAEA,OAAOC;AACT;AAoBO,SAASK;IACdZ,wBAAwB;IAExB,8EAA8E;IAC9E,0EAA0E;IAC1E,MAAMa,eAAWzB,6aAAAA,EAAWM,4cAAAA;IAE5B,+CAA+C;IAC/C,IAAIc,QAAQC,GAAG,CAACC,QAAQ,gCAAK,gBAAgB,SAASvB,0aAAAA,EAAO;QAC3D,MAAMwB,yBAAqBrB,saAAAA,EAAIM,sdAAAA;QAC/B,IAAIe,oBAAoB;YACtB,WAAOrB,saAAAA,EAAIqB,mBAAmBE,QAAQ;QACxC;IACF;IAEA,OAAOA;AACT;;AA2BO,SAASG;IACd,MAAMC,SAAS7B,ibAAAA,EAAWG,2cAAAA;IAC1B,IAAI0B,WAAW,MAAM;QACnB,MAAM,OAAA,cAAwD,CAAxD,IAAIC,MAAM,gDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuD;IAC/D;IAEA,OAAOD;AACT;AAoBO,SAASE;IACdnB,wBAAwB;IAExB,MAAMoB,aAAShC,6aAAAA,EAAWO,8cAAAA;IAE1B,+CAA+C;IAC/C,IAAIa,QAAQC,GAAG,CAACC,QAAQ,gCAAK,gBAAgB,SAASvB,0aAAAA,EAAO;QAC3D,MAAMwB,yBAAqBrB,saAAAA,EAAIM,sdAAAA;QAC/B,IAAIe,oBAAoB;YACtB,OAAOrB,0aAAAA,EAAIqB,mBAAmBS,MAAM;QACtC;IACF;IAEA,OAAOA;AACT;AA4BO,SAASC,0BACdC,mBAA2B,UAAU;IAErCtB,wBAAwB;IAExB,MAAMuB,cAAUnC,6aAAAA,EAAWI,8cAAAA;IAC3B,wFAAwF;IACxF,IAAI,CAAC+B,SAAS,OAAO;IAErB,+CAA+C;IAC/C,IAAIf,QAAQC,GAAG,CAACC,QAAQ,gCAAK,gBAAgB,SAASvB,0aAAAA,EAAO;QAC3D,MAAMwB,yBAAqBrB,saAAAA,EAAIM,sdAAAA;QAC/B,IAAIe,oBAAoB;YACtB,MAAMa,UACJb,mBAAmBc,8BAA8B,EAAEC,IAAIJ;YACzD,IAAIE,SAAS;gBACX,uFAAuF;gBACvF,2EAA2E;gBAC3E,WAAOlC,saAAAA,EAAIkC;YACb;QACF;IACF;IAEA,WAAO1B,yZAAAA,EAA6ByB,QAAQI,UAAU,EAAEL;AAC1D;AAqBO,SAASM,yBACdN,mBAA2B,UAAU;IAErCtB,wBAAwB;IACxB,MAAMW,yBAAqBvB,6aAAAA,EAAWQ,sdAAAA;IACtC,MAAMiC,yBAAyBR,0BAA0BC;IAEzD,+CAA+C;IAC/C,IACEd,QAAQC,GAAG,CAACC,QAAQ,gCAAK,gBACzBC,sBACA,SAASxB,0aAAAA,EACT;QACA,MAAMqC,UACJb,mBAAmBmB,6BAA6B,EAAEJ,IAAIJ;QACxD,IAAIE,SAAS;YACX,uFAAuF;YACvF,2EAA2E;YAC3E,WAAOlC,saAAAA,EAAIkC;QACb;IACF;IAEA,WAAO3B,yZAAAA,EAA6BgC,wBAAwBP;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3272, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/redirect-boundary.tsx"], "sourcesContent": ["'use client'\nimport React, { useEffect } from 'react'\nimport type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useRouter } from './navigation'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { RedirectType, isRedirectError } from './redirect-error'\n\ninterface RedirectBoundaryProps {\n  router: AppRouterInstance\n  children: React.ReactNode\n}\n\nfunction HandleRedirect({\n  redirect,\n  reset,\n  redirectType,\n}: {\n  redirect: string\n  redirectType: RedirectType\n  reset: () => void\n}) {\n  const router = useRouter()\n\n  useEffect(() => {\n    React.startTransition(() => {\n      if (redirectType === RedirectType.push) {\n        router.push(redirect, {})\n      } else {\n        router.replace(redirect, {})\n      }\n      reset()\n    })\n  }, [redirect, redirectType, reset, router])\n\n  return null\n}\n\nexport class RedirectErrorBoundary extends React.Component<\n  RedirectBoundaryProps,\n  { redirect: string | null; redirectType: RedirectType | null }\n> {\n  constructor(props: RedirectBoundaryProps) {\n    super(props)\n    this.state = { redirect: null, redirectType: null }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isRedirectError(error)) {\n      const url = getURLFromRedirectError(error)\n      const redirectType = getRedirectTypeFromError(error)\n      return { redirect: url, redirectType }\n    }\n    // Re-throw if error is not for redirect\n    throw error\n  }\n\n  // Explicit type is needed to avoid the generated `.d.ts` having a wide return type that could be specific to the `@types/react` version.\n  render(): React.ReactNode {\n    const { redirect, redirectType } = this.state\n    if (redirect !== null && redirectType !== null) {\n      return (\n        <HandleRedirect\n          redirect={redirect}\n          redirectType={redirectType}\n          reset={() => this.setState({ redirect: null })}\n        />\n      )\n    }\n\n    return this.props.children\n  }\n}\n\nexport function RedirectBoundary({ children }: { children: React.ReactNode }) {\n  const router = useRouter()\n  return (\n    <RedirectErrorBoundary router={router}>{children}</RedirectErrorBoundary>\n  )\n}\n"], "names": ["React", "useEffect", "useRouter", "getRedirectTypeFromError", "getURLFromRedirectError", "RedirectType", "isRedirectError", "HandleRedirect", "redirect", "reset", "redirectType", "router", "startTransition", "push", "replace", "RedirectErrorBoundary", "Component", "constructor", "props", "state", "getDerivedStateFromError", "error", "url", "render", "setState", "children", "RedirectBoundary"], "mappings": ";;;;;;;AACA,OAAOA,SAASC,SAAS,QAAQ,QAAO;AAExC,SAASC,SAAS,QAAQ,eAAc;AACxC,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,aAAY;AAC9E,SAASC,YAAY,EAAEC,eAAe,QAAQ,mBAAkB;AALhE;;;;;;AAYA,SAASC,eAAe,EACtBC,QAAQ,EACRC,KAAK,EACLC,YAAY,EAKb;IACC,MAAMC,aAAST,gaAAAA;QAEfD,4aAAAA,EAAU;QACRD,0aAAAA,CAAMY,eAAe,CAAC;YACpB,IAAIF,iBAAiBL,0ZAAAA,CAAaQ,IAAI,EAAE;gBACtCF,OAAOE,IAAI,CAACL,UAAU,CAAC;YACzB,OAAO;gBACLG,OAAOG,OAAO,CAACN,UAAU,CAAC;YAC5B;YACAC;QACF;IACF,GAAG;QAACD;QAAUE;QAAcD;QAAOE;KAAO;IAE1C,OAAO;AACT;AAEO,MAAMI,8BAA8Bf,0aAAAA,CAAMgB,SAAS;IAIxDC,YAAYC,KAA4B,CAAE;QACxC,KAAK,CAACA;QACN,IAAI,CAACC,KAAK,GAAG;YAAEX,UAAU;YAAME,cAAc;QAAK;IACpD;IAEA,OAAOU,yBAAyBC,KAAU,EAAE;QAC1C,QAAIf,6ZAAAA,EAAgBe,QAAQ;YAC1B,MAAMC,UAAMlB,4ZAAAA,EAAwBiB;YACpC,MAAMX,mBAAeP,6ZAAAA,EAAyBkB;YAC9C,OAAO;gBAAEb,UAAUc;gBAAKZ;YAAa;QACvC;QACA,wCAAwC;QACxC,MAAMW;IACR;IAEA,yIAAyI;IACzIE,SAA0B;QACxB,MAAM,EAAEf,QAAQ,EAAEE,YAAY,EAAE,GAAG,IAAI,CAACS,KAAK;QAC7C,IAAIX,aAAa,QAAQE,iBAAiB,MAAM;YAC9C,OAAA,WAAA,OACE,wbAAA,EAACH,gBAAAA;gBACCC,UAAUA;gBACVE,cAAcA;gBACdD,OAAO,IAAM,IAAI,CAACe,QAAQ,CAAC;wBAAEhB,UAAU;oBAAK;;QAGlD;QAEA,OAAO,IAAI,CAACU,KAAK,CAACO,QAAQ;IAC5B;AACF;AAEO,SAASC,iBAAiB,EAAED,QAAQ,EAAiC;IAC1E,MAAMd,aAAST,gaAAAA;IACf,OAAA,WAAA,OACE,wbAAA,EAACa,uBAAAA;QAAsBJ,QAAQA;kBAASc;;AAE5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3354, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/http-access-fallback/error-boundary.tsx"], "sourcesContent": ["'use client'\n\n/**\n * HTTPAccessFallbackBoundary is a boundary that catches errors and renders a\n * fallback component for HTTP errors.\n *\n * It receives the status code, and determine if it should render fallbacks for few HTTP 4xx errors.\n *\n * e.g. 404\n * 404 represents not found, and the fallback component pair contains the component and its styles.\n *\n */\n\nimport React, { useContext } from 'react'\nimport { useUntrackedPathname } from '../navigation-untracked'\nimport {\n  HTTPAccessErrorStatus,\n  getAccessFallbackHTTPStatus,\n  getAccessFallbackErrorTypeByStatus,\n  isHTTPAccessFallbackError,\n} from './http-access-fallback'\nimport { warnOnce } from '../../../shared/lib/utils/warn-once'\nimport { MissingSlotContext } from '../../../shared/lib/app-router-context.shared-runtime'\n\ninterface HTTPAccessFallbackBoundaryProps {\n  notFound?: React.ReactNode\n  forbidden?: React.ReactNode\n  unauthorized?: React.ReactNode\n  // TODO: Make this required once `React.createElement` understands that positional args go into children\n  children?: React.ReactNode\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessFallbackErrorBoundaryProps\n  extends HTTPAccessFallbackBoundaryProps {\n  pathname: string | null\n  missingSlots?: Set<string>\n}\n\ninterface HTTPAccessBoundaryState {\n  triggeredStatus: number | undefined\n  previousPathname: string | null\n}\n\nclass HTTPAccessFallbackErrorBoundary extends React.Component<\n  HTTPAccessFallbackErrorBoundaryProps,\n  HTTPAccessBoundaryState\n> {\n  constructor(props: HTTPAccessFallbackErrorBoundaryProps) {\n    super(props)\n    this.state = {\n      triggeredStatus: undefined,\n      previousPathname: props.pathname,\n    }\n  }\n\n  componentDidCatch(): void {\n    if (\n      process.env.NODE_ENV === 'development' &&\n      this.props.missingSlots &&\n      this.props.missingSlots.size > 0 &&\n      // A missing children slot is the typical not-found case, so no need to warn\n      !this.props.missingSlots.has('children')\n    ) {\n      let warningMessage =\n        'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' +\n        'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n'\n\n      const formattedSlots = Array.from(this.props.missingSlots)\n        .sort((a, b) => a.localeCompare(b))\n        .map((slot) => `@${slot}`)\n        .join(', ')\n\n      warningMessage += 'Missing slots: ' + formattedSlots\n\n      warnOnce(warningMessage)\n    }\n  }\n\n  static getDerivedStateFromError(error: any) {\n    if (isHTTPAccessFallbackError(error)) {\n      const httpStatus = getAccessFallbackHTTPStatus(error)\n      return {\n        triggeredStatus: httpStatus,\n      }\n    }\n    // Re-throw if error is not for 404\n    throw error\n  }\n\n  static getDerivedStateFromProps(\n    props: HTTPAccessFallbackErrorBoundaryProps,\n    state: HTTPAccessBoundaryState\n  ): HTTPAccessBoundaryState | null {\n    /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */\n    if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n      return {\n        triggeredStatus: undefined,\n        previousPathname: props.pathname,\n      }\n    }\n    return {\n      triggeredStatus: state.triggeredStatus,\n      previousPathname: props.pathname,\n    }\n  }\n\n  render() {\n    const { notFound, forbidden, unauthorized, children } = this.props\n    const { triggeredStatus } = this.state\n    const errorComponents = {\n      [HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n      [HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n      [HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized,\n    }\n\n    if (triggeredStatus) {\n      const isNotFound =\n        triggeredStatus === HTTPAccessErrorStatus.NOT_FOUND && notFound\n      const isForbidden =\n        triggeredStatus === HTTPAccessErrorStatus.FORBIDDEN && forbidden\n      const isUnauthorized =\n        triggeredStatus === HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized\n\n      // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n      if (!(isNotFound || isForbidden || isUnauthorized)) {\n        return children\n      }\n\n      return (\n        <>\n          <meta name=\"robots\" content=\"noindex\" />\n          {process.env.NODE_ENV === 'development' && (\n            <meta\n              name=\"boundary-next-error\"\n              content={getAccessFallbackErrorTypeByStatus(triggeredStatus)}\n            />\n          )}\n          {errorComponents[triggeredStatus]}\n        </>\n      )\n    }\n\n    return children\n  }\n}\n\nexport function HTTPAccessFallbackBoundary({\n  notFound,\n  forbidden,\n  unauthorized,\n  children,\n}: HTTPAccessFallbackBoundaryProps) {\n  // When we're rendering the missing params shell, this will return null. This\n  // is because we won't be rendering any not found boundaries or error\n  // boundaries for the missing params shell. When this runs on the client\n  // (where these error can occur), we will get the correct pathname.\n  const pathname = useUntrackedPathname()\n  const missingSlots = useContext(MissingSlotContext)\n  const hasErrorFallback = !!(notFound || forbidden || unauthorized)\n\n  if (hasErrorFallback) {\n    return (\n      <HTTPAccessFallbackErrorBoundary\n        pathname={pathname}\n        notFound={notFound}\n        forbidden={forbidden}\n        unauthorized={unauthorized}\n        missingSlots={missingSlots}\n      >\n        {children}\n      </HTTPAccessFallbackErrorBoundary>\n    )\n  }\n\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "useUntrackedPathname", "HTTPAccessErrorStatus", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isHTTPAccessFallbackError", "warnOnce", "MissingSlotContext", "HTTPAccessFallbackErrorBoundary", "Component", "constructor", "props", "state", "triggeredStatus", "undefined", "previousPathname", "pathname", "componentDidCatch", "process", "env", "NODE_ENV", "missingSlots", "size", "has", "warningMessage", "formattedSlots", "Array", "from", "sort", "a", "b", "localeCompare", "map", "slot", "join", "getDerivedStateFromError", "error", "httpStatus", "getDerivedStateFromProps", "render", "notFound", "forbidden", "unauthorized", "children", "errorComponents", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "isNotFound", "isForbidden", "isUnauthorized", "meta", "name", "content", "HTTPAccessFallbackBoundary", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;AAEA;;;;;;;;;CASC,GAED,OAAOA,SAASC,UAAU,QAAQ,QAAO;AACzC,SAASC,oBAAoB,QAAQ,0BAAyB;AAC9D,SACEC,qBAAqB,EACrBC,2BAA2B,EAC3BC,kCAAkC,EAClCC,yBAAyB,QACpB,yBAAwB;AAC/B,SAASC,QAAQ,QAAQ,sCAAqC;AAC9D,SAASC,kBAAkB,QAAQ,wDAAuD;AAtB1F;;;;;;;AA4CA,MAAMC,wCAAwCT,0aAAAA,CAAMU,SAAS;IAI3DC,YAAYC,KAA2C,CAAE;QACvD,KAAK,CAACA;QACN,IAAI,CAACC,KAAK,GAAG;YACXC,iBAAiBC;YACjBC,kBAAkBJ,MAAMK,QAAQ;QAClC;IACF;IAEAC,oBAA0B;QACxB,IACEC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBACzB,IAAI,CAACT,KAAK,CAACU,YAAY,IACvB,IAAI,CAACV,KAAK,CAACU,YAAY,CAACC,IAAI,GAAG,KAC/B,4EAA4E;QAC5E,CAAC,IAAI,CAACX,KAAK,CAACU,YAAY,CAACE,GAAG,CAAC,aAC7B;YACA,IAAIC,iBACF,4HACA;YAEF,MAAMC,iBAAiBC,MAAMC,IAAI,CAAC,IAAI,CAAChB,KAAK,CAACU,YAAY,EACtDO,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEE,aAAa,CAACD,IAC/BE,GAAG,CAAC,CAACC,OAAS,CAAC,CAAC,EAAEA,MAAM,EACxBC,IAAI,CAAC;YAERV,kBAAkB,oBAAoBC;gBAEtCnB,mZAAAA,EAASkB;QACX;IACF;IAEA,OAAOW,yBAAyBC,KAAU,EAAE;QAC1C,QAAI/B,8cAAAA,EAA0B+B,QAAQ;YACpC,MAAMC,aAAalC,odAAAA,EAA4BiC;YAC/C,OAAO;gBACLvB,iBAAiBwB;YACnB;QACF;QACA,mCAAmC;QACnC,MAAMD;IACR;IAEA,OAAOE,yBACL3B,KAA2C,EAC3CC,KAA8B,EACE;QAChC;;;;;KAKC,GACD,IAAID,MAAMK,QAAQ,KAAKJ,MAAMG,gBAAgB,IAAIH,MAAMC,eAAe,EAAE;YACtE,OAAO;gBACLA,iBAAiBC;gBACjBC,kBAAkBJ,MAAMK,QAAQ;YAClC;QACF;QACA,OAAO;YACLH,iBAAiBD,MAAMC,eAAe;YACtCE,kBAAkBJ,MAAMK,QAAQ;QAClC;IACF;IAEAuB,SAAS;QACP,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,QAAQ,EAAE,GAAG,IAAI,CAAChC,KAAK;QAClE,MAAM,EAAEE,eAAe,EAAE,GAAG,IAAI,CAACD,KAAK;QACtC,MAAMgC,kBAAkB;YACtB,CAAC1C,0cAAAA,CAAsB2C,SAAS,CAAC,EAAEL;YACnC,CAACtC,0cAAAA,CAAsB4C,SAAS,CAAC,EAAEL;YACnC,CAACvC,0cAAAA,CAAsB6C,YAAY,CAAC,EAAEL;QACxC;QAEA,IAAI7B,iBAAiB;YACnB,MAAMmC,aACJnC,oBAAoBX,0cAAAA,CAAsB2C,SAAS,IAAIL;YACzD,MAAMS,cACJpC,oBAAoBX,0cAAAA,CAAsB4C,SAAS,IAAIL;YACzD,MAAMS,iBACJrC,oBAAoBX,0cAAAA,CAAsB6C,YAAY,IAAIL;YAE5D,kGAAkG;YAClG,IAAI,CAAEM,CAAAA,cAAcC,eAAeC,cAAa,GAAI;gBAClD,OAAOP;YACT;YAEA,OAAA,WAAA,GACE,6bAAA,EAAA,6bAAA,EAAA;;sCACE,wbAAA,EAACQ,QAAAA;wBAAKC,MAAK;wBAASC,SAAQ;;oBAC3BnC,QAAQC,GAAG,CAACC,QAAQ,gCAAK,iBAAA,WAAA,OACxB,wbAAA,EAAC+B,QAAAA;wBACCC,MAAK;wBACLC,aAASjD,udAAAA,EAAmCS;;oBAG/C+B,eAAe,CAAC/B,gBAAgB;;;QAGvC;QAEA,OAAO8B;IACT;AACF;AAEO,SAASW,2BAA2B,EACzCd,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,QAAQ,EACwB;IAChC,6EAA6E;IAC7E,qEAAqE;IACrE,wEAAwE;IACxE,mEAAmE;IACnE,MAAM3B,eAAWf,waAAAA;IACjB,MAAMoB,mBAAerB,6aAAAA,EAAWO,6cAAAA;IAChC,MAAMgD,mBAAmB,CAAC,CAAEf,CAAAA,YAAYC,aAAaC,YAAW;IAEhE,IAAIa,kBAAkB;QACpB,OAAA,WAAA,OACE,wbAAA,EAAC/C,iCAAAA;YACCQ,UAAUA;YACVwB,UAAUA;YACVC,WAAWA;YACXC,cAAcA;YACdrB,cAAcA;sBAEbsB;;IAGP;IAEA,OAAA,WAAA,OAAO,wbAAA,EAAA,6bAAA,EAAA;kBAAGA;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3483, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/router-reducer/create-router-cache-key.ts"], "sourcesContent": ["import type { Segment } from '../../../shared/lib/app-router-types'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\n\nexport function createRouterCacheKey(\n  segment: Segment,\n  withoutSearchParameters: boolean = false\n) {\n  // if the segment is an array, it means it's a dynamic segment\n  // for example, ['lang', 'en', 'd']. We need to convert it to a string to store it as a cache node key.\n  if (Array.isArray(segment)) {\n    return `${segment[0]}|${segment[1]}|${segment[2]}`\n  }\n\n  // Page segments might have search parameters, ie __PAGE__?foo=bar\n  // When `withoutSearchParameters` is true, we only want to return the page segment\n  if (withoutSearchParameters && segment.startsWith(PAGE_SEGMENT_KEY)) {\n    return PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n"], "names": ["PAGE_SEGMENT_KEY", "createRouterCache<PERSON>ey", "segment", "withoutSearchParameters", "Array", "isArray", "startsWith"], "mappings": ";;;;AACA,SAASA,gBAAgB,QAAQ,8BAA6B;;AAEvD,SAASC,qBACdC,OAAgB,EAChBC,0BAAmC,KAAK;IAExC,8DAA8D;IAC9D,uGAAuG;IACvG,IAAIC,MAAMC,OAAO,CAACH,UAAU;QAC1B,OAAO,GAAGA,OAAO,CAAC,EAAE,CAAC,CAAC,EAAEA,OAAO,CAAC,EAAE,CAAC,CAAC,EAAEA,OAAO,CAAC,EAAE,EAAE;IACpD;IAEA,kEAAkE;IAClE,kFAAkF;IAClF,IAAIC,2BAA2BD,QAAQI,UAAU,CAACN,6YAAAA,GAAmB;QACnE,OAAOA,6YAAAA;IACT;IAEA,OAAOE;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3506, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/page-path/ensure-leading-slash.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n"], "names": ["ensureLeadingSlash", "path", "startsWith"], "mappings": "AAAA;;;CAGC,GACD;;;;AAAO,SAASA,mBAAmBC,IAAY;IAC7C,OAAOA,KAAKC,UAAU,CAAC,OAAOD,OAAO,CAAC,CAAC,EAAEA,MAAM;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3520, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/router/utils/app-paths.ts"], "sourcesContent": ["import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n"], "names": ["ensureLeadingSlash", "isGroupSegment", "normalizeAppPath", "route", "split", "reduce", "pathname", "segment", "index", "segments", "length", "normalizeRscURL", "url", "replace"], "mappings": ";;;;;;AAAA,SAASA,kBAAkB,QAAQ,uCAAsC;AACzE,SAASC,cAAc,QAAQ,gBAAe;;;AAqBvC,SAASC,iBAAiBC,KAAa;IAC5C,WAAOH,kbAAAA,EACLG,MAAMC,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,QAAIL,2YAAAA,EAAeM,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACGC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASC,MAAM,GAAG,GAC5B;YACA,OAAOJ;QACT;QAEA,OAAO,GAAGA,SAAS,CAAC,EAAEC,SAAS;IACjC,GAAG;AAEP;AAMO,SAASI,gBAAgBC,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,AACA,8BAD8B;AAGlC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3558, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/router/utils/interception-routes.ts"], "sourcesContent": ["import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\ntype InterceptionRouteInformation = {\n  /**\n   * The intercepting route. This is the route that is being intercepted or the\n   * route that the user was coming from. This is matched by the Next-Url\n   * header.\n   */\n  interceptingRoute: string\n\n  /**\n   * The intercepted route. This is the route that is being intercepted or the\n   * route that the user is going to. This is matched by the request pathname.\n   */\n  interceptedRoute: string\n}\n\nexport function extractInterceptionRouteInformation(\n  path: string\n): InterceptionRouteInformation {\n  let interceptingRoute: string | undefined\n  let marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined\n  let interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n"], "names": ["normalizeAppPath", "INTERCEPTION_ROUTE_MARKERS", "isInterceptionRouteAppPath", "path", "split", "find", "segment", "m", "startsWith", "undefined", "extractInterceptionRouteInformation", "interceptingRoute", "marker", "interceptedRoute", "Error", "slice", "concat", "join", "splitInterceptingRoute", "length"], "mappings": ";;;;;;;;AAAA,SAASA,gBAAgB,QAAQ,cAAa;;AAGvC,MAAMC,6BAA6B;IACxC;IACA;IACA;IACA;CACD,CAAS;AAEH,SAASC,2BAA2BC,IAAY;IACrD,wCAAwC;IACxC,OACEA,KACGC,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UACLL,2BAA2BI,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD,SACtDE;AAEZ;AAiBO,SAASC,oCACdP,IAAY;IAEZ,IAAIQ;IACJ,IAAIC;IACJ,IAAIC;IAEJ,KAAK,MAAMP,WAAWH,KAAKC,KAAK,CAAC,KAAM;QACrCQ,SAASX,2BAA2BI,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD;QACnE,IAAIK,QAAQ;;YACT,CAACD,mBAAmBE,iBAAiB,GAAGV,KAAKC,KAAK,CAACQ,QAAQ;YAC5D;QACF;IACF;IAEA,IAAI,CAACD,qBAAqB,CAACC,UAAU,CAACC,kBAAkB;QACtD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,4BAA4B,EAAEX,KAAK,iFAAiF,CAAC,GADlH,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAQ,wBAAoBX,qaAAAA,EAAiBW,mBAAmB,iDAAiD;;IAEzG,OAAQC;QACN,KAAK;YACH,oIAAoI;YACpI,IAAID,sBAAsB,KAAK;gBAC7BE,mBAAmB,CAAC,CAAC,EAAEA,kBAAkB;YAC3C,OAAO;gBACLA,mBAAmBF,oBAAoB,MAAME;YAC/C;YACA;QACF,KAAK;YACH,uHAAuH;YACvH,IAAIF,sBAAsB,KAAK;gBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIG,MACR,CAAC,4BAA4B,EAAEX,KAAK,4DAA4D,CAAC,GAD7F,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAU,mBAAmBF,kBAChBP,KAAK,CAAC,KACNW,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACH,kBACPI,IAAI,CAAC;YACR;QACF,KAAK;YACH,kIAAkI;YAClIJ,mBAAmB,MAAMA;YACzB;QACF,KAAK;YACH,iIAAiI;YAEjI,MAAMK,yBAAyBP,kBAAkBP,KAAK,CAAC;YACvD,IAAIc,uBAAuBC,MAAM,IAAI,GAAG;gBACtC,MAAM,OAAA,cAEL,CAFK,IAAIL,MACR,CAAC,4BAA4B,EAAEX,KAAK,+DAA+D,CAAC,GADhG,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAU,mBAAmBK,uBAChBH,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACH,kBACPI,IAAI,CAAC;YACR;QACF;YACE,MAAM,OAAA,cAAyC,CAAzC,IAAIH,MAAM,iCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwC;IAClD;IAEA,OAAO;QAAEH;QAAmBE;IAAiB;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3651, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/router-reducer/reducers/has-interception-route-in-current-tree.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../../../shared/lib/app-router-types'\nimport { isInterceptionRouteAppPath } from '../../../../shared/lib/router/utils/interception-routes'\n\nexport function hasInterceptionRouteInCurrentTree([\n  segment,\n  parallelRoutes,\n]: FlightRouterState): boolean {\n  // If we have a dynamic segment, it's marked as an interception route by the presence of the `i` suffix.\n  if (Array.isArray(segment) && (segment[2] === 'di' || segment[2] === 'ci')) {\n    return true\n  }\n\n  // If segment is not an array, apply the existing string-based check\n  if (typeof segment === 'string' && isInterceptionRouteAppPath(segment)) {\n    return true\n  }\n\n  // Iterate through parallelRoutes if they exist\n  if (parallelRoutes) {\n    for (const key in parallelRoutes) {\n      if (hasInterceptionRouteInCurrentTree(parallelRoutes[key])) {\n        return true\n      }\n    }\n  }\n\n  return false\n}\n"], "names": ["isInterceptionRouteAppPath", "hasInterceptionRouteInCurrentTree", "segment", "parallelRoutes", "Array", "isArray", "key"], "mappings": ";;;;AACA,SAASA,0BAA0B,QAAQ,0DAAyD;;AAE7F,SAASC,kCAAkC,CAChDC,SACAC,eACkB;IAClB,wGAAwG;IACxG,IAAIC,MAAMC,OAAO,CAACH,YAAaA,CAAAA,OAAO,CAAC,EAAE,KAAK,QAAQA,OAAO,CAAC,EAAE,KAAK,IAAG,GAAI;QAC1E,OAAO;IACT;IAEA,oEAAoE;IACpE,IAAI,OAAOA,YAAY,gBAAYF,ybAAAA,EAA2BE,UAAU;QACtE,OAAO;IACT;IAEA,+CAA+C;IAC/C,IAAIC,gBAAgB;QAClB,IAAK,MAAMG,OAAOH,eAAgB;YAChC,IAAIF,kCAAkCE,cAAc,CAACG,IAAI,GAAG;gBAC1D,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3680, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/bfcache.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../shared/lib/app-router-types'\nimport { useState } from 'react'\n\n// When the flag is disabled, only track the currently active tree\nconst MAX_BF_CACHE_ENTRIES = process.env.__NEXT_CACHE_COMPONENTS ? 3 : 1\n\nexport type RouterBFCacheEntry = {\n  tree: FlightRouterState\n  stateKey: string\n  // The entries form a linked list, sorted in order of most recently active.\n  next: RouterBFCacheEntry | null\n}\n\n/**\n * Keeps track of the most recent N trees (FlightRouterStates) that were active\n * at a certain segment level. E.g. for a segment \"/a/b/[param]\", this hook\n * tracks the last N param values that the router rendered for N.\n *\n * The result of this hook precisely determines the number and order of\n * trees that are rendered in parallel at their segment level.\n *\n * The purpose of this cache is to we can preserve the React and DOM state of\n * some number of inactive trees, by rendering them in an <Activity> boundary.\n * That means it would not make sense for the the lifetime of the cache to be\n * any longer than the lifetime of the React tree; e.g. if the hook were\n * unmounted, then the React tree would be, too. So, we use React state to\n * manage it.\n *\n * Note that we don't store the RSC data for the cache entries in this hook —\n * the data for inactive segments is stored in the parent CacheNode, which\n * *does* have a longer lifetime than the React tree. This hook only determines\n * which of those trees should have their *state* preserved, by <Activity>.\n */\nexport function useRouterBFCache(\n  activeTree: FlightRouterState,\n  activeStateKey: string\n): RouterBFCacheEntry {\n  // The currently active entry. The entries form a linked list, sorted in\n  // order of most recently active. This allows us to reuse parts of the list\n  // without cloning, unless there's a reordering or removal.\n  // TODO: Once we start tracking back/forward history at each route level,\n  // we should use the history order instead. In other words, when traversing\n  // to an existing entry as a result of a popstate event, we should maintain\n  // the existing order instead of moving it to the front of the list. I think\n  // an initial implementation of this could be to pass an incrementing id\n  // to history.pushState/replaceState, then use that here for ordering.\n  const [prevActiveEntry, setPrevActiveEntry] = useState<RouterBFCacheEntry>(\n    () => {\n      const initialEntry: RouterBFCacheEntry = {\n        tree: activeTree,\n        stateKey: activeStateKey,\n        next: null,\n      }\n      return initialEntry\n    }\n  )\n\n  if (prevActiveEntry.tree === activeTree) {\n    // Fast path. The active tree hasn't changed, so we can reuse the\n    // existing state.\n    return prevActiveEntry\n  }\n\n  // The route tree changed. Note that this doesn't mean that the tree changed\n  // *at this level* — the change may be due to a child route. Either way, we\n  // need to either add or update the router tree in the bfcache.\n  //\n  // The rest of the code looks more complicated than it actually is because we\n  // can't mutate the state in place; we have to copy-on-write.\n\n  // Create a new entry for the active cache key. This is the head of the new\n  // linked list.\n  const newActiveEntry: RouterBFCacheEntry = {\n    tree: activeTree,\n    stateKey: activeStateKey,\n    next: null,\n  }\n\n  // We need to append the old list onto the new list. If the head of the new\n  // list was already present in the cache, then we'll need to clone everything\n  // that came before it. Then we can reuse the rest.\n  let n = 1\n  let oldEntry: RouterBFCacheEntry | null = prevActiveEntry\n  let clonedEntry: RouterBFCacheEntry = newActiveEntry\n  while (oldEntry !== null && n < MAX_BF_CACHE_ENTRIES) {\n    if (oldEntry.stateKey === activeStateKey) {\n      // Fast path. This entry in the old list that corresponds to the key that\n      // is now active. We've already placed a clone of this entry at the front\n      // of the new list. We can reuse the rest of the old list without cloning.\n      // NOTE: We don't need to worry about eviction in this case because we\n      // haven't increased the size of the cache, and we assume the max size\n      // is constant across renders. If we were to change it to a dynamic limit,\n      // then the implementation would need to account for that.\n      clonedEntry.next = oldEntry.next\n      break\n    } else {\n      // Clone the entry and append it to the list.\n      n++\n      const entry: RouterBFCacheEntry = {\n        tree: oldEntry.tree,\n        stateKey: oldEntry.stateKey,\n        next: null,\n      }\n      clonedEntry.next = entry\n      clonedEntry = entry\n    }\n    oldEntry = oldEntry.next\n  }\n\n  setPrevActiveEntry(newActiveEntry)\n  return newActiveEntry\n}\n"], "names": ["useState", "MAX_BF_CACHE_ENTRIES", "process", "env", "__NEXT_CACHE_COMPONENTS", "useRouterBFCache", "activeTree", "activeStateKey", "prevActiveEntry", "setPrevActiveEntry", "initialEntry", "tree", "stateKey", "next", "newActiveEntry", "n", "oldEntry", "clonedEntry", "entry"], "mappings": ";;;;AACA,SAASA,QAAQ,QAAQ,QAAO;;AAEhC,kEAAkE;AAClE,MAAMC,uBAAuBC,QAAQC,GAAG,CAACC,uBAAuB,GAAG,0BAAI;AA6BhE,SAASC,iBACdC,UAA6B,EAC7BC,cAAsB;IAEtB,wEAAwE;IACxE,2EAA2E;IAC3E,2DAA2D;IAC3D,yEAAyE;IACzE,2EAA2E;IAC3E,2EAA2E;IAC3E,4EAA4E;IAC5E,wEAAwE;IACxE,sEAAsE;IACtE,MAAM,CAACC,iBAAiBC,mBAAmB,OAAGT,2aAAAA,EAC5C;QACE,MAAMU,eAAmC;YACvCC,MAAML;YACNM,UAAUL;YACVM,MAAM;QACR;QACA,OAAOH;IACT;IAGF,IAAIF,gBAAgBG,IAAI,KAAKL,YAAY;QACvC,iEAAiE;QACjE,kBAAkB;QAClB,OAAOE;IACT;IAEA,4EAA4E;IAC5E,2EAA2E;IAC3E,+DAA+D;IAC/D,EAAE;IACF,6EAA6E;IAC7E,6DAA6D;IAE7D,2EAA2E;IAC3E,eAAe;IACf,MAAMM,iBAAqC;QACzCH,MAAML;QACNM,UAAUL;QACVM,MAAM;IACR;IAEA,2EAA2E;IAC3E,6EAA6E;IAC7E,mDAAmD;IACnD,IAAIE,IAAI;IACR,IAAIC,WAAsCR;IAC1C,IAAIS,cAAkCH;IACtC,MAAOE,aAAa,QAAQD,IAAId,qBAAsB;QACpD,IAAIe,SAASJ,QAAQ,KAAKL,gBAAgB;YACxC,yEAAyE;YACzE,yEAAyE;YACzE,0EAA0E;YAC1E,sEAAsE;YACtE,sEAAsE;YACtE,0EAA0E;YAC1E,0DAA0D;YAC1DU,YAAYJ,IAAI,GAAGG,SAASH,IAAI;YAChC;QACF,OAAO;YACL,6CAA6C;YAC7CE;YACA,MAAMG,QAA4B;gBAChCP,MAAMK,SAASL,IAAI;gBACnBC,UAAUI,SAASJ,QAAQ;gBAC3BC,MAAM;YACR;YACAI,YAAYJ,IAAI,GAAGK;YACnBD,cAAcC;QAChB;QACAF,WAAWA,SAASH,IAAI;IAC1B;IAEAJ,mBAAmBK;IACnB,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3761, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/navigation-devtools.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../shared/lib/app-router-types'\nimport type { Params } from '../../server/request/params'\nimport {\n  createDevToolsInstrumentedPromise,\n  type InstrumentedPromise,\n  type NavigationPromises,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport {\n  computeSelectedLayoutSegment,\n  getSelectedLayoutSegmentPath,\n} from '../../shared/lib/segment'\nimport { ReadonlyURLSearchParams } from './readonly-url-search-params'\n\n/**\n * Promises are cached by tree to ensure stability across suspense retries.\n */\ntype LayoutSegmentPromisesCache = {\n  selectedLayoutSegmentPromises: Map<string, InstrumentedPromise<string | null>>\n  selectedLayoutSegmentsPromises: Map<string, InstrumentedPromise<string[]>>\n}\n\nconst layoutSegmentPromisesCache = new WeakMap<\n  FlightRouterState,\n  LayoutSegmentPromisesCache\n>()\n\n/**\n * Creates instrumented promises for layout segment hooks at a given tree level.\n * This is dev-only code for React Suspense DevTools instrumentation.\n */\nexport function createLayoutSegmentPromises(\n  tree: FlightRouterState\n): LayoutSegmentPromisesCache | null {\n  if (process.env.NODE_ENV === 'production') {\n    return null\n  }\n\n  // Check if we already have cached promises for this tree\n  const cached = layoutSegmentPromisesCache.get(tree)\n  if (cached) {\n    return cached\n  }\n\n  // Create new promises and cache them\n  const segmentPromises = new Map<string, InstrumentedPromise<string | null>>()\n  const segmentsPromises = new Map<string, InstrumentedPromise<string[]>>()\n\n  const parallelRoutes = tree[1]\n  for (const parallelRouteKey of Object.keys(parallelRoutes)) {\n    const segments = getSelectedLayoutSegmentPath(tree, parallelRouteKey)\n\n    // Use the shared logic to compute the segment value\n    const segment = computeSelectedLayoutSegment(segments, parallelRouteKey)\n\n    segmentPromises.set(\n      parallelRouteKey,\n      createDevToolsInstrumentedPromise('useSelectedLayoutSegment', segment)\n    )\n    segmentsPromises.set(\n      parallelRouteKey,\n      createDevToolsInstrumentedPromise('useSelectedLayoutSegments', segments)\n    )\n  }\n\n  const result: LayoutSegmentPromisesCache = {\n    selectedLayoutSegmentPromises: segmentPromises,\n    selectedLayoutSegmentsPromises: segmentsPromises,\n  }\n\n  // Cache the result for future renders\n  layoutSegmentPromisesCache.set(tree, result)\n\n  return result\n}\n\nconst rootNavigationPromisesCache = new WeakMap<\n  FlightRouterState,\n  Map<string, NavigationPromises>\n>()\n\n/**\n * Creates instrumented navigation promises for the root app-router.\n */\nexport function createRootNavigationPromises(\n  tree: FlightRouterState,\n  pathname: string,\n  searchParams: URLSearchParams,\n  pathParams: Params\n): NavigationPromises | null {\n  if (process.env.NODE_ENV === 'production') {\n    return null\n  }\n\n  // Create stable cache keys from the values\n  const searchParamsString = searchParams.toString()\n  const pathParamsString = JSON.stringify(pathParams)\n  const cacheKey = `${pathname}:${searchParamsString}:${pathParamsString}`\n\n  // Get or create the cache for this tree\n  let treeCache = rootNavigationPromisesCache.get(tree)\n  if (!treeCache) {\n    treeCache = new Map<string, NavigationPromises>()\n    rootNavigationPromisesCache.set(tree, treeCache)\n  }\n\n  // Check if we have cached promises for this combination\n  const cached = treeCache.get(cacheKey)\n  if (cached) {\n    return cached\n  }\n\n  const readonlySearchParams = new ReadonlyURLSearchParams(searchParams)\n\n  const layoutSegmentPromises = createLayoutSegmentPromises(tree)\n\n  const promises: NavigationPromises = {\n    pathname: createDevToolsInstrumentedPromise('usePathname', pathname),\n    searchParams: createDevToolsInstrumentedPromise(\n      'useSearchParams',\n      readonlySearchParams\n    ),\n    params: createDevToolsInstrumentedPromise('useParams', pathParams),\n    ...layoutSegmentPromises,\n  }\n\n  treeCache.set(cacheKey, promises)\n\n  return promises\n}\n\nconst nestedLayoutPromisesCache = new WeakMap<\n  FlightRouterState,\n  Map<NavigationPromises | null, NavigationPromises>\n>()\n\n/**\n * Creates merged navigation promises for nested layouts.\n * Merges parent promises with layout-specific segment promises.\n */\nexport function createNestedLayoutNavigationPromises(\n  tree: FlightRouterState,\n  parentNavPromises: NavigationPromises | null\n): NavigationPromises | null {\n  if (process.env.NODE_ENV === 'production') {\n    return null\n  }\n\n  const parallelRoutes = tree[1]\n  const parallelRouteKeys = Object.keys(parallelRoutes)\n\n  // Only create promises if there are parallel routes at this level\n  if (parallelRouteKeys.length === 0) {\n    return null\n  }\n\n  // Get or create the cache for this tree\n  let treeCache = nestedLayoutPromisesCache.get(tree)\n  if (!treeCache) {\n    treeCache = new Map<NavigationPromises | null, NavigationPromises>()\n    nestedLayoutPromisesCache.set(tree, treeCache)\n  }\n\n  // Check if we have cached promises for this parent combination\n  const cached = treeCache.get(parentNavPromises)\n  if (cached) {\n    return cached\n  }\n\n  // Create merged promises\n  const layoutSegmentPromises = createLayoutSegmentPromises(tree)\n  const promises: NavigationPromises = {\n    ...parentNavPromises!,\n    ...layoutSegmentPromises,\n  }\n\n  treeCache.set(parentNavPromises, promises)\n\n  return promises\n}\n"], "names": ["createDevToolsInstrumentedPromise", "computeSelectedLayoutSegment", "getSelectedLayoutSegmentPath", "ReadonlyURLSearchParams", "layoutSegmentPromisesCache", "WeakMap", "createLayoutSegmentPromises", "tree", "process", "env", "NODE_ENV", "cached", "get", "segmentPromises", "Map", "segmentsPromises", "parallelRoutes", "parallelRouteKey", "Object", "keys", "segments", "segment", "set", "result", "selectedLayoutSegmentPromises", "selectedLayoutSegmentsPromises", "rootNavigationPromisesCache", "createRootNavigationPromises", "pathname", "searchParams", "pathParams", "searchParamsString", "toString", "pathParamsString", "JSON", "stringify", "cache<PERSON>ey", "treeCache", "readonlySearchParams", "layoutSegmentPromises", "promises", "params", "nestedLayoutPromisesCache", "createNestedLayoutNavigationPromises", "parentNavPromises", "parallelRouteKeys", "length"], "mappings": ";;;;;;;;AAEA,SACEA,iCAAiC,QAG5B,uDAAsD;AAC7D,SACEC,4BAA4B,EAC5BC,4BAA4B,QACvB,2BAA0B;AACjC,SAASC,uBAAuB,QAAQ,+BAA8B;;;;AAUtE,MAAMC,6BAA6B,IAAIC;AAShC,SAASC,4BACdC,IAAuB;IAEvB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;;IAI3C,yDAAyD;IACzD,MAAMC,SAASP,2BAA2BQ,GAAG,CAACL;IAC9C,IAAII,QAAQ;QACV,OAAOA;IACT;IAEA,qCAAqC;IACrC,MAAME,kBAAkB,IAAIC;IAC5B,MAAMC,mBAAmB,IAAID;IAE7B,MAAME,iBAAiBT,IAAI,CAAC,EAAE;IAC9B,KAAK,MAAMU,oBAAoBC,OAAOC,IAAI,CAACH,gBAAiB;QAC1D,MAAMI,eAAWlB,yZAAAA,EAA6BK,MAAMU;QAEpD,oDAAoD;QACpD,MAAMI,cAAUpB,yZAAAA,EAA6BmB,UAAUH;QAEvDJ,gBAAgBS,GAAG,CACjBL,sBACAjB,8dAAAA,EAAkC,4BAA4BqB;QAEhEN,iBAAiBO,GAAG,CAClBL,sBACAjB,8dAAAA,EAAkC,6BAA6BoB;IAEnE;IAEA,MAAMG,SAAqC;QACzCC,+BAA+BX;QAC/BY,gCAAgCV;IAClC;IAEA,sCAAsC;IACtCX,2BAA2BkB,GAAG,CAACf,MAAMgB;IAErC,OAAOA;AACT;AAEA,MAAMG,8BAA8B,IAAIrB;AAQjC,SAASsB,6BACdpB,IAAuB,EACvBqB,QAAgB,EAChBC,YAA6B,EAC7BC,UAAkB;IAElB,IAAItB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;;IAI3C,2CAA2C;IAC3C,MAAMqB,qBAAqBF,aAAaG,QAAQ;IAChD,MAAMC,mBAAmBC,KAAKC,SAAS,CAACL;IACxC,MAAMM,WAAW,GAAGR,SAAS,CAAC,EAAEG,mBAAmB,CAAC,EAAEE,kBAAkB;IAExE,wCAAwC;IACxC,IAAII,YAAYX,4BAA4Bd,GAAG,CAACL;IAChD,IAAI,CAAC8B,WAAW;QACdA,YAAY,IAAIvB;QAChBY,4BAA4BJ,GAAG,CAACf,MAAM8B;IACxC;IAEA,wDAAwD;IACxD,MAAM1B,SAAS0B,UAAUzB,GAAG,CAACwB;IAC7B,IAAIzB,QAAQ;QACV,OAAOA;IACT;IAEA,MAAM2B,uBAAuB,IAAInC,ubAAAA,CAAwB0B;IAEzD,MAAMU,wBAAwBjC,4BAA4BC;IAE1D,MAAMiC,WAA+B;QACnCZ,cAAU5B,8dAAAA,EAAkC,eAAe4B;QAC3DC,kBAAc7B,8dAAAA,EACZ,mBACAsC;QAEFG,YAAQzC,8dAAAA,EAAkC,aAAa8B;QACvD,GAAGS,qBAAqB;IAC1B;IAEAF,UAAUf,GAAG,CAACc,UAAUI;IAExB,OAAOA;AACT;AAEA,MAAME,4BAA4B,IAAIrC;AAS/B,SAASsC,qCACdpC,IAAuB,EACvBqC,iBAA4C;IAE5C,IAAIpC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;;IAI3C,MAAMM,iBAAiBT,IAAI,CAAC,EAAE;IAC9B,MAAMsC,oBAAoB3B,OAAOC,IAAI,CAACH;IAEtC,kEAAkE;IAClE,IAAI6B,kBAAkBC,MAAM,KAAK,GAAG;QAClC,OAAO;IACT;IAEA,wCAAwC;IACxC,IAAIT,YAAYK,0BAA0B9B,GAAG,CAACL;IAC9C,IAAI,CAAC8B,WAAW;QACdA,YAAY,IAAIvB;QAChB4B,0BAA0BpB,GAAG,CAACf,MAAM8B;IACtC;IAEA,+DAA+D;IAC/D,MAAM1B,SAAS0B,UAAUzB,GAAG,CAACgC;IAC7B,IAAIjC,QAAQ;QACV,OAAOA;IACT;IAEA,yBAAyB;IACzB,MAAM4B,wBAAwBjC,4BAA4BC;IAC1D,MAAMiC,WAA+B;QACnC,GAAGI,iBAAiB;QACpB,GAAGL,qBAAqB;IAC1B;IAEAF,UAAUf,GAAG,CAACsB,mBAAmBJ;IAEjC,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3867, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/next-devtools/userspace/app/segment-explorer-node.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactNode } from 'react'\nimport {\n  useState,\n  createContext,\n  useContext,\n  use,\n  useMemo,\n  useCallback,\n} from 'react'\nimport { useLayoutEffect } from 'react'\nimport { dispatcher } from 'next/dist/compiled/next-devtools'\nimport { notFound } from '../../../client/components/not-found'\n\nexport type SegmentBoundaryType =\n  | 'not-found'\n  | 'error'\n  | 'loading'\n  | 'global-error'\n\nexport const SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE =\n  'NEXT_DEVTOOLS_SIMULATED_ERROR'\n\nexport type SegmentNodeState = {\n  type: string\n  pagePath: string\n  boundaryType: string | null\n  setBoundaryType: (type: SegmentBoundaryType | null) => void\n}\n\nfunction SegmentTrieNode({\n  type,\n  pagePath,\n}: {\n  type: string\n  pagePath: string\n}): React.ReactNode {\n  const { boundaryType, setBoundaryType } = useSegmentState()\n  const nodeState: SegmentNodeState = useMemo(() => {\n    return {\n      type,\n      pagePath,\n      boundaryType,\n      setBoundaryType,\n    }\n  }, [type, pagePath, boundaryType, setBoundaryType])\n\n  // Use `useLayoutEffect` to ensure the state is updated during suspense.\n  // `useEffect` won't work as the state is preserved during suspense.\n  useLayoutEffect(() => {\n    dispatcher.segmentExplorerNodeAdd(nodeState)\n    return () => {\n      dispatcher.segmentExplorerNodeRemove(nodeState)\n    }\n  }, [nodeState])\n\n  return null\n}\n\nfunction NotFoundSegmentNode(): React.ReactNode {\n  notFound()\n}\n\nfunction ErrorSegmentNode(): React.ReactNode {\n  throw new Error(SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE)\n}\n\nconst forever = new Promise(() => {})\nfunction LoadingSegmentNode(): React.ReactNode {\n  use(forever)\n  return null\n}\n\nexport function SegmentViewStateNode({ page }: { page: string }) {\n  useLayoutEffect(() => {\n    dispatcher.segmentExplorerUpdateRouteState(page)\n    return () => {\n      dispatcher.segmentExplorerUpdateRouteState('')\n    }\n  }, [page])\n  return null\n}\n\nexport function SegmentBoundaryTriggerNode() {\n  const { boundaryType } = useSegmentState()\n  let segmentNode: React.ReactNode = null\n  if (boundaryType === 'loading') {\n    segmentNode = <LoadingSegmentNode />\n  } else if (boundaryType === 'not-found') {\n    segmentNode = <NotFoundSegmentNode />\n  } else if (boundaryType === 'error') {\n    segmentNode = <ErrorSegmentNode />\n  }\n  return segmentNode\n}\n\nexport function SegmentViewNode({\n  type,\n  pagePath,\n  children,\n}: {\n  type: string\n  pagePath: string\n  children?: ReactNode\n}): React.ReactNode {\n  const segmentNode = (\n    <SegmentTrieNode key={type} type={type} pagePath={pagePath} />\n  )\n\n  return (\n    <>\n      {segmentNode}\n      {children}\n    </>\n  )\n}\n\nconst SegmentStateContext = createContext<{\n  boundaryType: SegmentBoundaryType | null\n  setBoundaryType: (type: SegmentBoundaryType | null) => void\n}>({\n  boundaryType: null,\n  setBoundaryType: () => {},\n})\n\nexport function SegmentStateProvider({ children }: { children: ReactNode }) {\n  const [boundaryType, setBoundaryType] = useState<SegmentBoundaryType | null>(\n    null\n  )\n\n  const [errorBoundaryKey, setErrorBoundaryKey] = useState(0)\n  const reloadBoundary = useCallback(\n    () => setErrorBoundaryKey((prev) => prev + 1),\n    []\n  )\n\n  const setBoundaryTypeAndReload = useCallback(\n    (type: SegmentBoundaryType | null) => {\n      if (type === null) {\n        reloadBoundary()\n      }\n      setBoundaryType(type)\n    },\n    [reloadBoundary]\n  )\n\n  return (\n    <SegmentStateContext.Provider\n      key={errorBoundaryKey}\n      value={{\n        boundaryType,\n        setBoundaryType: setBoundaryTypeAndReload,\n      }}\n    >\n      {children}\n    </SegmentStateContext.Provider>\n  )\n}\n\nexport function useSegmentState() {\n  return useContext(SegmentStateContext)\n}\n"], "names": ["useState", "createContext", "useContext", "use", "useMemo", "useCallback", "useLayoutEffect", "dispatcher", "notFound", "SEGMENT_EXPLORER_SIMULATED_ERROR_MESSAGE", "SegmentTrieNode", "type", "pagePath", "boundaryType", "setBoundaryType", "useSegmentState", "nodeState", "segmentExplorerNodeAdd", "segmentExplorerNodeRemove", "NotFoundSegmentNode", "ErrorSegmentNode", "Error", "forever", "Promise", "LoadingSegmentNode", "SegmentViewStateNode", "page", "segmentExplorerUpdateRouteState", "SegmentBoundaryTriggerNode", "segmentNode", "SegmentViewNode", "children", "SegmentStateContext", "SegmentStateProvider", "error<PERSON>ou<PERSON><PERSON><PERSON><PERSON>", "setError<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reloadBoundary", "prev", "setBoundaryTypeAndReload", "Provider", "value"], "mappings": ";;;;;;;;;;;;;;;AAGA,SACEA,QAAQ,EACRC,aAAa,EACbC,UAAU,EACVC,GAAG,EACHC,OAAO,EACPC,WAAW,QACN,QAAO;AAEd,SAASE,UAAU,QAAQ,mCAAkC;AAC7D,SAASC,QAAQ,QAAQ,uCAAsC;AAb/D;;;;;;AAqBO,MAAMC,2CACX,gCAA+B;AASjC,SAASC,gBAAgB,EACvBC,IAAI,EACJC,QAAQ,EAIT;IACC,MAAM,EAAEC,YAAY,EAAEC,eAAe,EAAE,GAAGC;IAC1C,MAAMC,YAA8BZ,8aAAAA,EAAQ;QAC1C,OAAO;YACLO;YACAC;YACAC;YACAC;QACF;IACF,GAAG;QAACH;QAAMC;QAAUC;QAAcC;KAAgB;IAElD,wEAAwE;IACxE,oEAAoE;QACpER,kbAAAA,EAAgB;QACdC,kZAAAA,CAAWU,sBAAsB,CAACD;QAClC,OAAO;YACLT,kZAAAA,CAAWW,yBAAyB,CAACF;QACvC;IACF,GAAG;QAACA;KAAU;IAEd,OAAO;AACT;AAEA,SAASG;QACPX,iZAAAA;AACF;AAEA,SAASY;IACP,MAAM,OAAA,cAAmD,CAAnD,IAAIC,MAAMZ,2CAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAkD;AAC1D;AAEA,MAAMa,UAAU,IAAIC,QAAQ,KAAO;AACnC,SAASC;QACPrB,saAAAA,EAAImB;IACJ,OAAO;AACT;AAEO,SAASG,qBAAqB,EAAEC,IAAI,EAAoB;QAC7DpB,kbAAAA,EAAgB;QACdC,kZAAAA,CAAWoB,+BAA+B,CAACD;QAC3C,OAAO;YACLnB,kZAAAA,CAAWoB,+BAA+B,CAAC;QAC7C;IACF,GAAG;QAACD;KAAK;IACT,OAAO;AACT;AAEO,SAASE;IACd,MAAM,EAAEf,YAAY,EAAE,GAAGE;IACzB,IAAIc,cAA+B;IACnC,IAAIhB,iBAAiB,WAAW;QAC9BgB,cAAAA,WAAAA,OAAc,wbAAA,EAACL,oBAAAA,CAAAA;IACjB,OAAO,IAAIX,iBAAiB,aAAa;QACvCgB,cAAAA,WAAAA,OAAc,wbAAA,EAACV,qBAAAA,CAAAA;IACjB,OAAO,IAAIN,iBAAiB,SAAS;QACnCgB,cAAAA,WAAAA,OAAc,wbAAA,EAACT,kBAAAA,CAAAA;IACjB;IACA,OAAOS;AACT;AAEO,SAASC,gBAAgB,EAC9BnB,IAAI,EACJC,QAAQ,EACRmB,QAAQ,EAKT;IACC,MAAMF,cAAAA,WAAAA,OACJ,wbAAA,EAACnB,iBAAAA;QAA2BC,MAAMA;QAAMC,UAAUA;OAA5BD;IAGxB,OAAA,WAAA,OACE,ybAAA,EAAA,6bAAA,EAAA;;YACGkB;YACAE;;;AAGP;AAEA,MAAMC,sBAAAA,WAAAA,OAAsB/B,gbAAAA,EAGzB;IACDY,cAAc;IACdC,iBAAiB,KAAO;AAC1B;AAEO,SAASmB,qBAAqB,EAAEF,QAAQ,EAA2B;IACxE,MAAM,CAAClB,cAAcC,gBAAgB,OAAGd,2aAAAA,EACtC;IAGF,MAAM,CAACkC,kBAAkBC,oBAAoB,OAAGnC,2aAAAA,EAAS;IACzD,MAAMoC,qBAAiB/B,8aAAAA,EACrB,IAAM8B,oBAAoB,CAACE,OAASA,OAAO,IAC3C,EAAE;IAGJ,MAAMC,+BAA2BjC,8aAAAA,EAC/B,CAACM;QACC,IAAIA,SAAS,MAAM;YACjByB;QACF;QACAtB,gBAAgBH;IAClB,GACA;QAACyB;KAAe;IAGlB,OAAA,WAAA,OACE,wbAAA,EAACJ,oBAAoBO,QAAQ,EAAA;QAE3BC,OAAO;YACL3B;YACAC,iBAAiBwB;QACnB;kBAECP;OANIG;AASX;AAEO,SAASnB;IACd,WAAOb,6aAAAA,EAAW8B;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4000, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/layout-router.tsx"], "sourcesContent": ["'use client'\n\nimport type {\n  <PERSON><PERSON>N<PERSON>,\n  LazyCacheNode,\n} from '../../shared/lib/app-router-types'\nimport type { LoadingModuleData } from '../../shared/lib/app-router-types'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../../shared/lib/app-router-types'\nimport type { ErrorComponent } from './error-boundary'\nimport {\n  ACTION_SERVER_PATCH,\n  type FocusAndScrollRef,\n} from './router-reducer/router-reducer-types'\n\nimport React, {\n  Activity,\n  useContext,\n  use,\n  startTransition,\n  Suspense,\n  useDeferredValue,\n  type JSX,\n  type ActivityProps,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport {\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n  TemplateContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { fetchServerResponse } from './router-reducer/fetch-server-response'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { ErrorBoundary } from './error-boundary'\nimport { matchSegment } from './match-segments'\nimport { disableSmoothScrollDuringRouteTransition } from '../../shared/lib/router/utils/disable-smooth-scroll'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key'\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree'\nimport { dispatchAppRouterAction } from './use-action-queue'\nimport { useRouterBFCache, type RouterBFCacheEntry } from './bfcache'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport {\n  NavigationPromisesContext,\n  type NavigationPromises,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getParamValueFromCacheKey } from '../route-params'\nimport type { Params } from '../../server/request/params'\n\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */\nfunction walkAddRefetch(\n  segmentPathToWalk: FlightSegmentPath | undefined,\n  treeToRecreate: FlightRouterState\n): FlightRouterState {\n  if (segmentPathToWalk) {\n    const [segment, parallelRouteKey] = segmentPathToWalk\n    const isLast = segmentPathToWalk.length === 2\n\n    if (matchSegment(treeToRecreate[0], segment)) {\n      if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n        if (isLast) {\n          const subTree = walkAddRefetch(\n            undefined,\n            treeToRecreate[1][parallelRouteKey]\n          )\n          return [\n            treeToRecreate[0],\n            {\n              ...treeToRecreate[1],\n              [parallelRouteKey]: [\n                subTree[0],\n                subTree[1],\n                subTree[2],\n                'refetch',\n              ],\n            },\n          ]\n        }\n\n        return [\n          treeToRecreate[0],\n          {\n            ...treeToRecreate[1],\n            [parallelRouteKey]: walkAddRefetch(\n              segmentPathToWalk.slice(2),\n              treeToRecreate[1][parallelRouteKey]\n            ),\n          },\n        ]\n      }\n    }\n  }\n\n  return treeToRecreate\n}\n\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = (\n  ReactDOM as any\n).__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE\n\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */\nfunction findDOMNode(\n  instance: React.ReactInstance | null | undefined\n): Element | Text | null {\n  // Tree-shake for server bundle\n  if (typeof window === 'undefined') return null\n\n  // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n  // We need to lazily reference it.\n  const internal_reactDOMfindDOMNode =\n    __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode\n  return internal_reactDOMfindDOMNode(instance)\n}\n\nconst rectProperties = [\n  'bottom',\n  'height',\n  'left',\n  'right',\n  'top',\n  'width',\n  'x',\n  'y',\n] as const\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */\nfunction shouldSkipElement(element: HTMLElement) {\n  // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n  // and will result in a situation we bail on scroll because of something like a fixed nav,\n  // even though the actual page content is offscreen\n  if (['sticky', 'fixed'].includes(getComputedStyle(element).position)) {\n    return true\n  }\n\n  // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n  // because `offsetParent` doesn't consider document/body\n  const rect = element.getBoundingClientRect()\n  return rectProperties.every((item) => rect[item] === 0)\n}\n\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */\nfunction topOfElementInViewport(element: HTMLElement, viewportHeight: number) {\n  const rect = element.getBoundingClientRect()\n  return rect.top >= 0 && rect.top <= viewportHeight\n}\n\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */\nfunction getHashFragmentDomNode(hashFragment: string) {\n  // If the hash fragment is `top` the page has to scroll to the top of the page.\n  if (hashFragment === 'top') {\n    return document.body\n  }\n\n  // If the hash fragment is an id, the page has to scroll to the element with that id.\n  return (\n    document.getElementById(hashFragment) ??\n    // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0]\n  )\n}\ninterface ScrollAndFocusHandlerProps {\n  focusAndScrollRef: FocusAndScrollRef\n  children: React.ReactNode\n  segmentPath: FlightSegmentPath\n}\nclass InnerScrollAndFocusHandler extends React.Component<ScrollAndFocusHandlerProps> {\n  handlePotentialScroll = () => {\n    // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n    const { focusAndScrollRef, segmentPath } = this.props\n\n    if (focusAndScrollRef.apply) {\n      // segmentPaths is an array of segment paths that should be scrolled to\n      // if the current segment path is not in the array, the scroll is not applied\n      // unless the array is empty, in which case the scroll is always applied\n      if (\n        focusAndScrollRef.segmentPaths.length !== 0 &&\n        !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath) =>\n          segmentPath.every((segment, index) =>\n            matchSegment(segment, scrollRefSegmentPath[index])\n          )\n        )\n      ) {\n        return\n      }\n\n      let domNode:\n        | ReturnType<typeof getHashFragmentDomNode>\n        | ReturnType<typeof findDOMNode> = null\n      const hashFragment = focusAndScrollRef.hashFragment\n\n      if (hashFragment) {\n        domNode = getHashFragmentDomNode(hashFragment)\n      }\n\n      // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n      // This already caused a bug where the first child was a <link/> in head.\n      if (!domNode) {\n        domNode = findDOMNode(this)\n      }\n\n      // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n      if (!(domNode instanceof Element)) {\n        return\n      }\n\n      // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n      // If the element is skipped, try to select the next sibling and try again.\n      while (!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (domNode.parentElement?.localName === 'head') {\n            // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n            // This is always a bug in Next.js and caused by React hoisting metadata.\n            // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n          }\n        }\n\n        // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n        if (domNode.nextElementSibling === null) {\n          return\n        }\n        domNode = domNode.nextElementSibling\n      }\n\n      // State is mutated to ensure that the focus and scroll is applied only once.\n      focusAndScrollRef.apply = false\n      focusAndScrollRef.hashFragment = null\n      focusAndScrollRef.segmentPaths = []\n\n      disableSmoothScrollDuringRouteTransition(\n        () => {\n          // In case of hash scroll, we only need to scroll the element into view\n          if (hashFragment) {\n            ;(domNode as HTMLElement).scrollIntoView()\n\n            return\n          }\n          // Store the current viewport height because reading `clientHeight` causes a reflow,\n          // and it won't change during this function.\n          const htmlElement = document.documentElement\n          const viewportHeight = htmlElement.clientHeight\n\n          // If the element's top edge is already in the viewport, exit early.\n          if (topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            return\n          }\n\n          // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n          // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n          // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n          // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n          htmlElement.scrollTop = 0\n\n          // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n          if (!topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            // Scroll into view doesn't scroll horizontally by default when not needed\n            ;(domNode as HTMLElement).scrollIntoView()\n          }\n        },\n        {\n          // We will force layout by querying domNode position\n          dontForceLayout: true,\n          onlyHashChange: focusAndScrollRef.onlyHashChange,\n        }\n      )\n\n      // Mutate after scrolling so that it can be read by `disableSmoothScrollDuringRouteTransition`\n      focusAndScrollRef.onlyHashChange = false\n\n      // Set focus on the element\n      domNode.focus()\n    }\n  }\n\n  componentDidMount() {\n    this.handlePotentialScroll()\n  }\n\n  componentDidUpdate() {\n    // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n    if (this.props.focusAndScrollRef.apply) {\n      this.handlePotentialScroll()\n    }\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nfunction ScrollAndFocusHandler({\n  segmentPath,\n  children,\n}: {\n  segmentPath: FlightSegmentPath\n  children: React.ReactNode\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  return (\n    <InnerScrollAndFocusHandler\n      segmentPath={segmentPath}\n      focusAndScrollRef={context.focusAndScrollRef}\n    >\n      {children}\n    </InnerScrollAndFocusHandler>\n  )\n}\n\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */\nfunction InnerLayoutRouter({\n  tree,\n  segmentPath,\n  debugNameContext,\n  cacheNode,\n  params,\n  url,\n  isActive,\n}: {\n  tree: FlightRouterState\n  segmentPath: FlightSegmentPath\n  debugNameContext: string\n  cacheNode: CacheNode\n  params: Params\n  url: string\n  isActive: boolean\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  const parentNavPromises = useContext(NavigationPromisesContext)\n\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  const { tree: fullTree } = context\n\n  // `rsc` represents the renderable node for this segment.\n\n  // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n  // We should use that on initial render instead of `rsc`. Then we'll switch\n  // to `rsc` when the dynamic response streams in.\n  //\n  // If no prefetch data is available, then we go straight to rendering `rsc`.\n  const resolvedPrefetchRsc =\n    cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  const rsc: any = useDeferredValue(cacheNode.rsc, resolvedPrefetchRsc)\n\n  // `rsc` is either a React node or a promise for a React node, except we\n  // special case `null` to represent that this segment's data is missing. If\n  // it's a promise, we need to unwrap it so we can determine whether or not the\n  // data is missing.\n  const resolvedRsc: React.ReactNode =\n    typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function'\n      ? use(rsc)\n      : rsc\n\n  if (!resolvedRsc) {\n    // The data for this segment is not available, and there's no pending\n    // navigation that will be able to fulfill it. We need to fetch more from\n    // the server and patch the cache.\n\n    // Only fetch data for the active segment. Inactive segments (rendered\n    // offscreen for bfcache) should not trigger fetches.\n    if (isActive) {\n      // Check if there's already a pending request.\n      let lazyData = cacheNode.lazyData\n      if (lazyData === null) {\n        /**\n         * Router state with refetch marker added\n         */\n        // TODO-APP: remove ''\n        const refetchTree = walkAddRefetch(['', ...segmentPath], fullTree)\n        const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree)\n        const navigatedAt = Date.now()\n        cacheNode.lazyData = lazyData = fetchServerResponse(\n          new URL(url, location.origin),\n          {\n            flightRouterState: refetchTree,\n            nextUrl: includeNextUrl\n              ? // We always send the last next-url, not the current when\n                // performing a dynamic request. This is because we update\n                // the next-url after a navigation, but we want the same\n                // interception route to be matched that used the last\n                // next-url.\n                context.previousNextUrl || context.nextUrl\n              : null,\n          }\n        ).then((serverResponse) => {\n          startTransition(() => {\n            dispatchAppRouterAction({\n              type: ACTION_SERVER_PATCH,\n              previousTree: fullTree,\n              serverResponse,\n              navigatedAt,\n            })\n          })\n\n          return serverResponse\n        })\n\n        // Suspend while waiting for lazyData to resolve\n        use(lazyData)\n      }\n    }\n    // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n    // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n    use(unresolvedThenable) as never\n  }\n\n  // If we get to this point, then we know we have something we can render.\n  let content = resolvedRsc\n\n  // In dev, we create a NavigationPromisesContext containing the instrumented promises that provide\n  // `useSelectedLayoutSegment` and `useSelectedLayoutSegments`.\n  // Promises are cached outside of render to survive suspense retries.\n  let navigationPromises: NavigationPromises | null = null\n  if (process.env.NODE_ENV !== 'production') {\n    const { createNestedLayoutNavigationPromises } =\n      require('./navigation-devtools') as typeof import('./navigation-devtools')\n\n    navigationPromises = createNestedLayoutNavigationPromises(\n      tree,\n      parentNavPromises\n    )\n  }\n\n  if (navigationPromises) {\n    content = (\n      <NavigationPromisesContext.Provider value={navigationPromises}>\n        {resolvedRsc}\n      </NavigationPromisesContext.Provider>\n    )\n  }\n\n  const subtree = (\n    // The layout router context narrows down tree and childNodes at each level.\n    <LayoutRouterContext.Provider\n      value={{\n        parentTree: tree,\n        parentCacheNode: cacheNode,\n        parentSegmentPath: segmentPath,\n        parentParams: params,\n        debugNameContext: debugNameContext,\n\n        // TODO-APP: overriding of url for parallel routes\n        url: url,\n        isActive: isActive,\n      }}\n    >\n      {content}\n    </LayoutRouterContext.Provider>\n  )\n  // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n  return subtree\n}\n\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */\nfunction LoadingBoundary({\n  name,\n  loading,\n  children,\n}: {\n  name: ActivityProps['name']\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  children: React.ReactNode\n}): JSX.Element {\n  // If loading is a promise, unwrap it. This happens in cases where we haven't\n  // yet received the loading data from the server — which includes whether or\n  // not this layout has a loading component at all.\n  //\n  // It's OK to suspend here instead of inside the fallback because this\n  // promise will resolve simultaneously with the data for the segment itself.\n  // So it will never suspend for longer than it would have if we didn't use\n  // a Suspense fallback at all.\n  let loadingModuleData\n  if (\n    typeof loading === 'object' &&\n    loading !== null &&\n    typeof (loading as any).then === 'function'\n  ) {\n    const promiseForLoading = loading as Promise<LoadingModuleData>\n    loadingModuleData = use(promiseForLoading)\n  } else {\n    loadingModuleData = loading as LoadingModuleData\n  }\n\n  if (loadingModuleData) {\n    const loadingRsc = loadingModuleData[0]\n    const loadingStyles = loadingModuleData[1]\n    const loadingScripts = loadingModuleData[2]\n    return (\n      <Suspense\n        name={name}\n        fallback={\n          <>\n            {loadingStyles}\n            {loadingScripts}\n            {loadingRsc}\n          </>\n        }\n      >\n        {children}\n      </Suspense>\n    )\n  }\n\n  return <>{children}</>\n}\n\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */\nexport default function OuterLayoutRouter({\n  parallelRouterKey,\n  error,\n  errorStyles,\n  errorScripts,\n  templateStyles,\n  templateScripts,\n  template,\n  notFound,\n  forbidden,\n  unauthorized,\n  segmentViewBoundaries,\n}: {\n  parallelRouterKey: string\n  error: ErrorComponent | undefined\n  errorStyles: React.ReactNode | undefined\n  errorScripts: React.ReactNode | undefined\n  templateStyles: React.ReactNode | undefined\n  templateScripts: React.ReactNode | undefined\n  template: React.ReactNode\n  notFound: React.ReactNode | undefined\n  forbidden: React.ReactNode | undefined\n  unauthorized: React.ReactNode | undefined\n  segmentViewBoundaries?: React.ReactNode\n}) {\n  const context = useContext(LayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant expected layout router to be mounted')\n  }\n\n  const {\n    parentTree,\n    parentCacheNode,\n    parentSegmentPath,\n    parentParams,\n    url,\n    isActive,\n    debugNameContext,\n  } = context\n\n  // Get the CacheNode for this segment by reading it from the parent segment's\n  // child map.\n  const parentParallelRoutes = parentCacheNode.parallelRoutes\n  let segmentMap = parentParallelRoutes.get(parallelRouterKey)\n  // If the parallel router cache node does not exist yet, create it.\n  // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n  if (!segmentMap) {\n    segmentMap = new Map()\n    parentParallelRoutes.set(parallelRouterKey, segmentMap)\n  }\n  const parentTreeSegment = parentTree[0]\n  const segmentPath =\n    parentSegmentPath === null\n      ? // TODO: The root segment value is currently omitted from the segment\n        // path. This has led to a bunch of special cases scattered throughout\n        // the code. We should clean this up.\n        [parallelRouterKey]\n      : parentSegmentPath.concat([parentTreeSegment, parallelRouterKey])\n\n  // The \"state\" key of a segment is the one passed to React — it represents the\n  // identity of the UI tree. Whenever the state key changes, the tree is\n  // recreated and the state is reset. In the App Router model, search params do\n  // not cause state to be lost, so two segments with the same segment path but\n  // different search params should have the same state key.\n  //\n  // The \"cache\" key of a segment, however, *does* include the search params, if\n  // it's possible that the segment accessed the search params on the server.\n  // (This only applies to page segments; layout segments cannot access search\n  // params on the server.)\n  const activeTree = parentTree[1][parallelRouterKey]\n  const activeSegment = activeTree[0]\n  const activeStateKey = createRouterCacheKey(activeSegment, true) // no search params\n\n  // At each level of the route tree, not only do we render the currently\n  // active segment — we also render the last N segments that were active at\n  // this level inside a hidden <Activity> boundary, to preserve their state\n  // if or when the user navigates to them again.\n  //\n  // bfcacheEntry is a linked list of FlightRouterStates.\n  let bfcacheEntry: RouterBFCacheEntry | null = useRouterBFCache(\n    activeTree,\n    activeStateKey\n  )\n  let children: Array<React.ReactNode> = []\n  do {\n    const tree = bfcacheEntry.tree\n    const stateKey = bfcacheEntry.stateKey\n    const segment = tree[0]\n    const cacheKey = createRouterCacheKey(segment)\n\n    // Read segment path from the parallel router cache node.\n    let cacheNode = segmentMap.get(cacheKey)\n    if (cacheNode === undefined) {\n      // When data is not available during rendering client-side we need to fetch\n      // it from the server.\n      const newLazyCacheNode: LazyCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      }\n\n      // Flight data fetch kicked off during render and put into the cache.\n      cacheNode = newLazyCacheNode\n      segmentMap.set(cacheKey, newLazyCacheNode)\n    }\n\n    /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n      - When gracefully degrade for bots, skip rendering error boundary.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */\n\n    let segmentBoundaryTriggerNode: React.ReactNode = null\n    let segmentViewStateNode: React.ReactNode = null\n    if (process.env.NODE_ENV !== 'production') {\n      const { SegmentBoundaryTriggerNode, SegmentViewStateNode } =\n        require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n\n      const pagePrefix = normalizeAppPath(url)\n      segmentViewStateNode = (\n        <SegmentViewStateNode key={pagePrefix} page={pagePrefix} />\n      )\n\n      segmentBoundaryTriggerNode = (\n        <>\n          <SegmentBoundaryTriggerNode />\n        </>\n      )\n    }\n\n    let params = parentParams\n    if (Array.isArray(segment)) {\n      // This segment contains a route param. Accumulate these as we traverse\n      // down the router tree. The result represents the set of params that\n      // the layout/page components are permitted to access below this point.\n      const paramName = segment[0]\n      const paramCacheKey = segment[1]\n      const paramType = segment[2]\n      const paramValue = getParamValueFromCacheKey(paramCacheKey, paramType)\n      if (paramValue !== null) {\n        params = {\n          ...parentParams,\n          [paramName]: paramValue,\n        }\n      }\n    }\n\n    const debugName = getBoundaryDebugNameFromSegment(segment)\n    // `debugNameContext` represents the nearest non-\"virtual\" parent segment.\n    // `getBoundaryDebugNameFromSegment` returns undefined for virtual segments.\n    // So if `debugName` is undefined, the context is passed through unchanged.\n    const childDebugNameContext = debugName ?? debugNameContext\n\n    // In practical terms, clicking this name in the Suspense DevTools\n    // should select the child slots of that layout.\n    //\n    // So the name we apply to the Activity boundary is actually based on\n    // the nearest parent segments.\n    //\n    // We skip over \"virtual\" parents, i.e. ones inserted by Next.js that\n    // don't correspond to application-defined code.\n    const isVirtual = debugName === undefined\n    const debugNameToDisplay = isVirtual ? undefined : debugNameContext\n\n    // TODO: The loading module data for a segment is stored on the parent, then\n    // applied to each of that parent segment's parallel route slots. In the\n    // simple case where there's only one parallel route (the `children` slot),\n    // this is no different from if the loading module data where stored on the\n    // child directly. But I'm not sure this actually makes sense when there are\n    // multiple parallel routes. It's not a huge issue because you always have\n    // the option to define a narrower loading boundary for a particular slot. But\n    // this sort of smells like an implementation accident to me.\n    const loadingModuleData = parentCacheNode.loading\n    let child = (\n      <TemplateContext.Provider\n        key={stateKey}\n        value={\n          <ScrollAndFocusHandler segmentPath={segmentPath}>\n            <ErrorBoundary\n              errorComponent={error}\n              errorStyles={errorStyles}\n              errorScripts={errorScripts}\n            >\n              <LoadingBoundary\n                name={debugNameToDisplay}\n                loading={loadingModuleData}\n              >\n                <HTTPAccessFallbackBoundary\n                  notFound={notFound}\n                  forbidden={forbidden}\n                  unauthorized={unauthorized}\n                >\n                  <RedirectBoundary>\n                    <InnerLayoutRouter\n                      url={url}\n                      tree={tree}\n                      params={params}\n                      cacheNode={cacheNode}\n                      segmentPath={segmentPath}\n                      debugNameContext={childDebugNameContext}\n                      isActive={isActive && stateKey === activeStateKey}\n                    />\n                    {segmentBoundaryTriggerNode}\n                  </RedirectBoundary>\n                </HTTPAccessFallbackBoundary>\n              </LoadingBoundary>\n            </ErrorBoundary>\n            {segmentViewStateNode}\n          </ScrollAndFocusHandler>\n        }\n      >\n        {templateStyles}\n        {templateScripts}\n        {template}\n      </TemplateContext.Provider>\n    )\n\n    if (process.env.NODE_ENV !== 'production') {\n      const { SegmentStateProvider } =\n        require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n\n      child = (\n        <SegmentStateProvider key={stateKey}>\n          {child}\n          {segmentViewBoundaries}\n        </SegmentStateProvider>\n      )\n    }\n\n    if (process.env.__NEXT_CACHE_COMPONENTS) {\n      child = (\n        <Activity\n          name={debugNameToDisplay}\n          key={stateKey}\n          mode={stateKey === activeStateKey ? 'visible' : 'hidden'}\n        >\n          {child}\n        </Activity>\n      )\n    }\n\n    children.push(child)\n\n    bfcacheEntry = bfcacheEntry.next\n  } while (bfcacheEntry !== null)\n\n  return children\n}\n\nfunction getBoundaryDebugNameFromSegment(segment: Segment): string | undefined {\n  if (segment === '/') {\n    // Reached the root\n    return '/'\n  }\n  if (typeof segment === 'string') {\n    if (isVirtualLayout(segment)) {\n      return undefined\n    } else {\n      return segment + '/'\n    }\n  }\n  const paramCacheKey = segment[1]\n  return paramCacheKey + '/'\n}\n\nfunction isVirtualLayout(segment: string): boolean {\n  return (\n    // This is inserted by the loader. We should consider encoding these\n    // in a more special way instead of checking the name, to distinguish them\n    // from app-defined groups.\n    segment === '(slot)'\n  )\n}\n"], "names": ["ACTION_SERVER_PATCH", "React", "Activity", "useContext", "use", "startTransition", "Suspense", "useDeferredValue", "ReactDOM", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "fetchServerResponse", "unresolvedThenable", "Error<PERSON>ou<PERSON><PERSON>", "matchSegment", "disableSmoothScrollDuringRouteTransition", "RedirectBoundary", "HTTPAccessFallbackBoundary", "createRouterCache<PERSON>ey", "hasInterceptionRouteInCurrentTree", "dispatchAppRouterAction", "useRouterBFCache", "normalizeAppPath", "NavigationPromisesContext", "getParamValueFromCacheKey", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "hasOwnProperty", "subTree", "undefined", "slice", "__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "findDOMNode", "instance", "window", "internal_reactDOMfindDOMNode", "rectProperties", "shouldSkipElement", "element", "includes", "getComputedStyle", "position", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "process", "env", "NODE_ENV", "parentElement", "localName", "nextElement<PERSON><PERSON>ling", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "Error", "InnerLayoutRouter", "tree", "debugNameContext", "cacheNode", "params", "url", "isActive", "parentNavPromises", "fullTree", "resolvedPrefetchRsc", "prefetchRsc", "rsc", "resolvedRsc", "then", "lazyData", "refetchTree", "includeNextUrl", "navigatedAt", "Date", "now", "URL", "location", "origin", "flightRouterState", "nextUrl", "previousNextUrl", "serverResponse", "type", "previousTree", "content", "navigationPromises", "createNestedLayoutNavigationPromises", "require", "Provider", "value", "subtree", "parentTree", "parentCacheNode", "parentSegmentPath", "parentParams", "LoadingBoundary", "name", "loading", "loadingModuleData", "promiseForLoading", "loadingRsc", "loadingStyles", "loadingScripts", "fallback", "OuterLayoutRouter", "parallel<PERSON><PERSON>er<PERSON>ey", "error", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "forbidden", "unauthorized", "segmentViewBoundaries", "parentParallelRoutes", "parallelRoutes", "segmentMap", "get", "Map", "set", "parentTreeSegment", "concat", "activeTree", "activeSegment", "activeStateKey", "bfcacheEntry", "stateKey", "cache<PERSON>ey", "newLazyCacheNode", "head", "prefetchHead", "segmentBoundaryTriggerNode", "segmentViewStateNode", "SegmentBoundaryTriggerNode", "SegmentViewStateNode", "pagePrefix", "page", "Array", "isArray", "paramName", "param<PERSON><PERSON><PERSON><PERSON>", "paramType", "paramValue", "debugName", "getBoundaryDebugNameFromSegment", "childDebugNameContext", "isVirtual", "debugNameToDisplay", "child", "errorComponent", "SegmentStateProvider", "__NEXT_CACHE_COMPONENTS", "mode", "push", "next", "isVirtualLayout"], "mappings": ";;;;;AAaA,SACEA,mBAAmB,QAEd,wCAAuC;AAE9C,OAAOC,SACLC,QAAQ,EACRC,UAAU,EACVC,GAAG,EACHC,eAAe,EACfC,QAAQ,EACRC,gBAAgB,QAGX,QAAO;AACd,OAAOC,cAAc,YAAW;AAChC,SACEC,mBAAmB,EACnBC,yBAAyB,EACzBC,eAAe,QACV,qDAAoD;AAC3D,SAASC,mBAAmB,QAAQ,yCAAwC;AAC5E,SAASC,kBAAkB,QAAQ,wBAAuB;AAC1D,SAASC,aAAa,QAAQ,mBAAkB;AAChD,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,wCAAwC,QAAQ,sDAAqD;AAC9G,SAASC,gBAAgB,QAAQ,sBAAqB;AACtD,SAASC,0BAA0B,QAAQ,wCAAuC;AAClF,SAASC,oBAAoB,QAAQ,2CAA0C;AAC/E,SAASC,iCAAiC,QAAQ,mEAAkE;AACpH,SAASC,uBAAuB,QAAQ,qBAAoB;AAC5D,SAASC,gBAAgB,QAAiC,YAAW;AACrE,SAASC,gBAAgB,QAAQ,0CAAyC;AAC1E,SACEC,yBAAyB,QAEpB,uDAAsD;AAC7D,SAASC,yBAAyB,QAAQ,kBAAiB;AAlD3D;;;;;;;;;;;;;;;;;;;;AAqDA;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,QAAIjB,0ZAAAA,EAAaa,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACK,cAAc,CAACH,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMG,UAAUR,eACdS,WACAP,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBI,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLN,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBS,KAAK,CAAC,IACxBR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,MAAMS,+DACJ7B,ibAAAA,CACA6B,4DAA4D;AAE9D,4FAA4F;AAC5F;;CAEC,GACD,SAASC,YACPC,QAAgD;IAEhD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,kBAAa,OAAO;;;IAE1C,uGAAuG;IACvG,kCAAkC;IAClC,MAAMC,+BACJJ,6DAA6DC,WAAW;AAE5E;AAEA,MAAMI,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACC,QAAQ,CAACC,iBAAiBF,SAASG,QAAQ,GAAG;QACpE,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMC,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOP,eAAeQ,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBR,OAAoB,EAAES,cAAsB;IAC1E,MAAML,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;IAEA,qFAAqF;IACrF,OACED,SAASE,cAAc,CAACH,iBACxB,8FAA8F;IAC9FC,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE;AAE/C;AAMA,MAAMK,mCAAmC5D,0aAAAA,CAAM6D,SAAS;IA4GtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;QAzHF,KAAA,IAAA,OAAA,IAAA,CACEN,qBAAAA,GAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAACxC,MAAM,KAAK,KAC1C,CAACmC,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYrB,KAAK,CAAC,CAACrB,SAAS8C,YAC1B5D,0ZAAAA,EAAac,SAAS6C,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMpB,eAAeW,kBAAkBX,YAAY;gBAEnD,IAAIA,cAAc;oBAChBoB,UAAUrB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACoB,SAAS;oBACZA,UAAUtC,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAEsC,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMnC,kBAAkBiC,SAAU;oBACtE,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;wBACzC,IAAIL,QAAQM,aAAa,EAAEC,cAAc,QAAQ;wBAC/C,2FAA2F;wBAC3F,yEAAyE;wBACzE,iHAAiH;wBACnH;oBACF;oBAEA,uGAAuG;oBACvG,IAAIP,QAAQQ,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAR,UAAUA,QAAQQ,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7EjB,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBX,YAAY,GAAG;gBACjCW,kBAAkBK,YAAY,GAAG,EAAE;oBAEnCxD,4cAAAA,EACE;oBACE,uEAAuE;oBACvE,IAAIwC,cAAc;;wBACdoB,QAAwBS,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAc7B,SAAS8B,eAAe;oBAC5C,MAAMlC,iBAAiBiC,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAIpC,uBAAuBwB,SAAwBvB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7HiC,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAACrC,uBAAuBwB,SAAwBvB,iBAAiB;wBACnE,0EAA0E;;wBACxEuB,QAAwBS,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBxB,kBAAkBwB,cAAc;gBAClD;gBAGF,8FAA8F;gBAC9FxB,kBAAkBwB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3Bf,QAAQgB,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,EAC7BtB,WAAW,EACXD,QAAQ,EAIT;IACC,MAAMwB,cAAU3F,6aAAAA,EAAWO,odAAAA;IAC3B,IAAI,CAACoF,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIC,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,OAAA,WAAA,OACE,wbAAA,EAAClC,4BAAAA;QACCU,aAAaA;QACbJ,mBAAmB2B,QAAQ3B,iBAAiB;kBAE3CG;;AAGP;AAEA;;CAEC,GACD,SAAS0B,kBAAkB,EACzBC,IAAI,EACJ1B,WAAW,EACX2B,gBAAgB,EAChBC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,QAAQ,EAST;IACC,MAAMR,cAAU3F,6aAAAA,EAAWO,odAAAA;IAC3B,MAAM6F,wBAAoBpG,6aAAAA,EAAWqB,sdAAAA;IAErC,IAAI,CAACsE,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIC,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,MAAM,EAAEE,MAAMO,QAAQ,EAAE,GAAGV;IAE3B,yDAAyD;IAEzD,4EAA4E;IAC5E,2EAA2E;IAC3E,iDAAiD;IACjD,EAAE;IACF,4EAA4E;IAC5E,MAAMW,sBACJN,UAAUO,WAAW,KAAK,OAAOP,UAAUO,WAAW,GAAGP,UAAUQ,GAAG;IAExE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,MAAMA,UAAWpG,mbAAAA,EAAiB4F,UAAUQ,GAAG,EAAEF;IAEjD,wEAAwE;IACxE,2EAA2E;IAC3E,8EAA8E;IAC9E,mBAAmB;IACnB,MAAMG,cACJ,OAAOD,QAAQ,YAAYA,QAAQ,QAAQ,OAAOA,IAAIE,IAAI,KAAK,iBAC3DzG,saAAAA,EAAIuG,OACJA;IAEN,IAAI,CAACC,aAAa;QAChB,qEAAqE;QACrE,yEAAyE;QACzE,kCAAkC;QAElC,sEAAsE;QACtE,qDAAqD;QACrD,IAAIN,UAAU;YACZ,8CAA8C;YAC9C,IAAIQ,WAAWX,UAAUW,QAAQ;YACjC,IAAIA,aAAa,MAAM;gBACrB;;SAEC,GACD,sBAAsB;gBACtB,MAAMC,cAAcrF,eAAe;oBAAC;uBAAO6C;iBAAY,EAAEiC;gBACzD,MAAMQ,qBAAiB5F,ofAAAA,EAAkCoF;gBACzD,MAAMS,cAAcC,KAAKC,GAAG;gBAC5BhB,UAAUW,QAAQ,GAAGA,eAAWlG,gcAAAA,EAC9B,IAAIwG,IAAIf,KAAKgB,SAASC,MAAM,GAC5B;oBACEC,mBAAmBR;oBACnBS,SAASR,iBAEL,AACA,wDAAwD,EADE;oBAE1D,sDAAsD;oBACtD,YAAY;oBACZlB,QAAQ2B,eAAe,IAAI3B,QAAQ0B,OAAO,GAC1C;gBACN,GACAX,IAAI,CAAC,CAACa;wBACNrH,kbAAAA,EAAgB;wBACdgB,8aAAAA,EAAwB;4BACtBsG,MAAM3H,+bAAAA;4BACN4H,cAAcpB;4BACdkB;4BACAT;wBACF;oBACF;oBAEA,OAAOS;gBACT;gBAEA,gDAAgD;oBAChDtH,saAAAA,EAAI0G;YACN;QACF;QACA,yGAAyG;QACzG,iIAAiI;YACjI1G,saAAAA,EAAIS,qaAAAA;IACN;IAEA,yEAAyE;IACzE,IAAIgH,UAAUjB;IAEd,kGAAkG;IAClG,8DAA8D;IAC9D,qEAAqE;IACrE,IAAIkB,qBAAgD;IACpD,IAAI/C,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,MAAM,EAAE8C,oCAAoC,EAAE,GAC5CC,QAAQ;QAEVF,qBAAqBC,qCACnB9B,MACAM;IAEJ;IAEA,IAAIuB,oBAAoB;QACtBD,UAAAA,WAAAA,OACE,wbAAA,EAACrG,sdAAAA,CAA0ByG,QAAQ,EAAA;YAACC,OAAOJ;sBACxClB;;IAGP;IAEA,MAAMuB,UACJ,kBACA,0DAD4E,8XAC5E,EAAC1H,8cAAAA,CAAoBwH,QAAQ,EAAA;QAC3BC,OAAO;YACLE,YAAYnC;YACZoC,iBAAiBlC;YACjBmC,mBAAmB/D;YACnBgE,cAAcnC;YACdF,kBAAkBA;YAElB,kDAAkD;YAClDG,KAAKA;YACLC,UAAUA;QACZ;kBAECuB;;IAGL,iFAAiF;IACjF,OAAOM;AACT;AAEA;;;CAGC,GACD,SAASK,gBAAgB,EACvBC,IAAI,EACJC,OAAO,EACPpE,QAAQ,EAKT;IACC,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,EAAE;IACF,sEAAsE;IACtE,4EAA4E;IAC5E,0EAA0E;IAC1E,8BAA8B;IAC9B,IAAIqE;IACJ,IACE,OAAOD,YAAY,YACnBA,YAAY,QACZ,OAAQA,QAAgB7B,IAAI,KAAK,YACjC;QACA,MAAM+B,oBAAoBF;QAC1BC,wBAAoBvI,saAAAA,EAAIwI;IAC1B,OAAO;QACLD,oBAAoBD;IACtB;IAEA,IAAIC,mBAAmB;QACrB,MAAME,aAAaF,iBAAiB,CAAC,EAAE;QACvC,MAAMG,gBAAgBH,iBAAiB,CAAC,EAAE;QAC1C,MAAMI,iBAAiBJ,iBAAiB,CAAC,EAAE;QAC3C,OAAA,WAAA,OACE,wbAAA,EAACrI,2aAAAA,EAAAA;YACCmI,MAAMA;YACNO,UAAAA,WAAAA,OACE,ybAAA,EAAA,6bAAA,EAAA;;oBACGF;oBACAC;oBACAF;;;sBAIJvE;;IAGP;IAEA,OAAA,WAAA,OAAO,wbAAA,EAAA,6bAAA,EAAA;kBAAGA;;AACZ;AAMe,SAAS2E,kBAAkB,EACxCC,iBAAiB,EACjBC,KAAK,EACLC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,qBAAqB,EAatB;IACC,MAAM9D,UAAU3F,ibAAAA,EAAWM,8cAAAA;IAC3B,IAAI,CAACqF,SAAS;QACZ,MAAM,OAAA,cAA2D,CAA3D,IAAIC,MAAM,mDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0D;IAClE;IAEA,MAAM,EACJqC,UAAU,EACVC,eAAe,EACfC,iBAAiB,EACjBC,YAAY,EACZlC,GAAG,EACHC,QAAQ,EACRJ,gBAAgB,EACjB,GAAGJ;IAEJ,6EAA6E;IAC7E,aAAa;IACb,MAAM+D,uBAAuBxB,gBAAgByB,cAAc;IAC3D,IAAIC,aAAaF,qBAAqBG,GAAG,CAACd;IAC1C,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACa,YAAY;QACfA,aAAa,IAAIE;QACjBJ,qBAAqBK,GAAG,CAAChB,mBAAmBa;IAC9C;IACA,MAAMI,oBAAoB/B,UAAU,CAAC,EAAE;IACvC,MAAM7D,cACJ+D,sBAAsB,OAElB,AACA,qCAAqC,iCADiC;IAEtE;QAACY;KAAkB,GACnBZ,kBAAkB8B,MAAM,CAAC;QAACD;QAAmBjB;KAAkB;IAErE,8EAA8E;IAC9E,uEAAuE;IACvE,8EAA8E;IAC9E,6EAA6E;IAC7E,0DAA0D;IAC1D,EAAE;IACF,8EAA8E;IAC9E,2EAA2E;IAC3E,4EAA4E;IAC5E,yBAAyB;IACzB,MAAMmB,aAAajC,UAAU,CAAC,EAAE,CAACc,kBAAkB;IACnD,MAAMoB,gBAAgBD,UAAU,CAAC,EAAE;IACnC,MAAME,iBAAiBpJ,0cAAAA,EAAqBmJ,eAAe,MAAM,mBAAmB;;IAEpF,uEAAuE;IACvE,0EAA0E;IAC1E,0EAA0E;IAC1E,+CAA+C;IAC/C,EAAE;IACF,uDAAuD;IACvD,IAAIE,mBAA0ClJ,oZAAAA,EAC5C+I,YACAE;IAEF,IAAIjG,WAAmC,EAAE;IACzC,GAAG;QACD,MAAM2B,OAAOuE,aAAavE,IAAI;QAC9B,MAAMwE,WAAWD,aAAaC,QAAQ;QACtC,MAAM5I,UAAUoE,IAAI,CAAC,EAAE;QACvB,MAAMyE,eAAWvJ,scAAAA,EAAqBU;QAEtC,yDAAyD;QACzD,IAAIsE,YAAY4D,WAAWC,GAAG,CAACU;QAC/B,IAAIvE,cAAchE,WAAW;YAC3B,2EAA2E;YAC3E,sBAAsB;YACtB,MAAMwI,mBAAkC;gBACtC7D,UAAU;gBACVH,KAAK;gBACLD,aAAa;gBACbkE,MAAM;gBACNC,cAAc;gBACdf,gBAAgB,IAAIG;gBACpBvB,SAAS;gBACTzB,aAAa,CAAC;YAChB;YAEA,qEAAqE;YACrEd,YAAYwE;YACZZ,WAAWG,GAAG,CAACQ,UAAUC;QAC3B;QAEA;;;;;;;;;EASF,GAEE,IAAIG,6BAA8C;QAClD,IAAIC,uBAAwC;QAC5C,IAAIhG,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;YACzC,MAAM,EAAE+F,0BAA0B,EAAEC,oBAAoB,EAAE,GACxDjD,QAAQ;YAEV,MAAMkD,iBAAa3J,qaAAAA,EAAiB8E;YACpC0E,uBAAAA,WAAAA,OACE,wbAAA,EAACE,sBAAAA;gBAAsCE,MAAMD;eAAlBA;YAG7BJ,6BAAAA,WAAAA,OACE,wbAAA,EAAA,6bAAA,EAAA;0BACE,WAAA,OAAA,wbAAA,EAACE,4BAAAA,CAAAA;;QAGP;QAEA,IAAI5E,SAASmC;QACb,IAAI6C,MAAMC,OAAO,CAACxJ,UAAU;YAC1B,uEAAuE;YACvE,qEAAqE;YACrE,uEAAuE;YACvE,MAAMyJ,YAAYzJ,OAAO,CAAC,EAAE;YAC5B,MAAM0J,gBAAgB1J,OAAO,CAAC,EAAE;YAChC,MAAM2J,YAAY3J,OAAO,CAAC,EAAE;YAC5B,MAAM4J,iBAAahK,uZAAAA,EAA0B8J,eAAeC;YAC5D,IAAIC,eAAe,MAAM;gBACvBrF,SAAS;oBACP,GAAGmC,YAAY;oBACf,CAAC+C,UAAU,EAAEG;gBACf;YACF;QACF;QAEA,MAAMC,YAAYC,gCAAgC9J;QAClD,0EAA0E;QAC1E,4EAA4E;QAC5E,2EAA2E;QAC3E,MAAM+J,wBAAwBF,aAAaxF;QAE3C,kEAAkE;QAClE,gDAAgD;QAChD,EAAE;QACF,qEAAqE;QACrE,+BAA+B;QAC/B,EAAE;QACF,qEAAqE;QACrE,gDAAgD;QAChD,MAAM2F,YAAYH,cAAcvJ;QAChC,MAAM2J,qBAAqBD,YAAY1J,YAAY+D;QAEnD,4EAA4E;QAC5E,wEAAwE;QACxE,2EAA2E;QAC3E,2EAA2E;QAC3E,4EAA4E;QAC5E,0EAA0E;QAC1E,8EAA8E;QAC9E,6DAA6D;QAC7D,MAAMyC,oBAAoBN,gBAAgBK,OAAO;QACjD,IAAIqD,QAAAA,WAAAA,OACF,ybAAA,EAACpL,0cAAAA,CAAgBsH,QAAQ,EAAA;YAEvBC,OAAAA,WAAAA,OACE,ybAAA,EAACrC,uBAAAA;gBAAsBtB,aAAaA;;kCAClC,4bAAA,EAACzD,2ZAAAA,EAAAA;wBACCkL,gBAAgB7C;wBAChBC,aAAaA;wBACbC,cAAcA;kCAEd,WAAA,OAAA,wbAAA,EAACb,iBAAAA;4BACCC,MAAMqD;4BACNpD,SAASC;sCAET,WAAA,OAAA,wbAAA,EAACzH,scAAAA,EAAAA;gCACCuI,UAAUA;gCACVC,WAAWA;gCACXC,cAAcA;0CAEd,WAAA,OAAA,ybAAA,EAAC1I,iaAAAA,EAAAA;;0DACC,wbAAA,EAAC+E,mBAAAA;4CACCK,KAAKA;4CACLJ,MAAMA;4CACNG,QAAQA;4CACRD,WAAWA;4CACX5B,aAAaA;4CACb2B,kBAAkB0F;4CAClBtF,UAAUA,YAAYmE,aAAaF;;wCAEpCO;;;;;;oBAKRC;;;;gBAIJzB;gBACAC;gBACAC;;WAtCIiB;QA0CT,IAAI1F,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;YACzC,MAAM,EAAEgH,oBAAoB,EAAE,GAC5BjE,QAAQ;YAEV+D,QAAAA,WAAAA,GACE,6bAAA,EAACE,sBAAAA;;oBACEF;oBACAnC;;eAFwBa;QAK/B;QAEA,IAAI1F,QAAQC,GAAG,CAACkH,uBAAuB,EAAE;;QAYzC5H,SAAS8H,IAAI,CAACL;QAEdvB,eAAeA,aAAa6B,IAAI;IAClC,QAAS7B,iBAAiB,KAAK;IAE/B,OAAOlG;AACT;AAEA,SAASqH,gCAAgC9J,OAAgB;IACvD,IAAIA,YAAY,KAAK;QACnB,mBAAmB;QACnB,OAAO;IACT;IACA,IAAI,OAAOA,YAAY,UAAU;QAC/B,IAAIyK,gBAAgBzK,UAAU;YAC5B,OAAOM;QACT,OAAO;YACL,OAAON,UAAU;QACnB;IACF;IACA,MAAM0J,gBAAgB1J,OAAO,CAAC,EAAE;IAChC,OAAO0J,gBAAgB;AACzB;AAEA,SAASe,gBAAgBzK,OAAe;IACtC,OACE,AACA,oEADoE,MACM;IAC1E,2BAA2B;IAC3BA,YAAY;AAEhB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4611, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/render-from-template-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n"], "names": ["React", "useContext", "TemplateContext", "RenderFromTemplateContext", "children"], "mappings": ";;;;;AAEA,OAAOA,SAASC,UAAU,QAAkB,QAAO;AACnD,SAASC,eAAe,QAAQ,qDAAoD;AAHpF;;;;AAKe,SAASC;IACtB,MAAMC,eAAWH,6aAAAA,EAAWC,0cAAAA;IAC5B,OAAA,WAAA,OAAO,wbAAA,EAAA,6bAAA,EAAA;kBAAGE;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4632, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/server/web/spec-extension/adapters/reflect.ts"], "sourcesContent": ["export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n"], "names": ["ReflectAdapter", "get", "target", "prop", "receiver", "value", "Reflect", "bind", "set", "has", "deleteProperty"], "mappings": ";;;;AAAO,MAAMA;IACX,OAAOC,IACLC,MAAS,EACTC,IAAqB,EACrBC,QAAiB,EACZ;QACL,MAAMC,QAAQC,QAAQL,GAAG,CAACC,QAAQC,MAAMC;QACxC,IAAI,OAAOC,UAAU,YAAY;YAC/B,OAAOA,MAAME,IAAI,CAACL;QACpB;QAEA,OAAOG;IACT;IAEA,OAAOG,IACLN,MAAS,EACTC,IAAqB,EACrBE,KAAU,EACVD,QAAa,EACJ;QACT,OAAOE,QAAQE,GAAG,CAACN,QAAQC,MAAME,OAAOD;IAC1C;IAEA,OAAOK,IAAsBP,MAAS,EAAEC,IAAqB,EAAW;QACtE,OAAOG,QAAQG,GAAG,CAACP,QAAQC;IAC7B;IAEA,OAAOO,eACLR,MAAS,EACTC,IAAqB,EACZ;QACT,OAAOG,QAAQI,cAAc,CAACR,QAAQC;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4658, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/server/create-deduped-by-callsite-server-error-logger.ts"], "sourcesContent": ["import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Cache Components is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_CACHE_COMPONENTS\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n"], "names": ["React", "errorRef", "current", "cache", "fn", "logErrorOrWarn", "process", "env", "__NEXT_CACHE_COMPONENTS", "console", "error", "warn", "flushCurrentErrorIfNew", "key", "createDedupedByCallsiteServerErrorLoggerDev", "getMessage", "logDedupedError", "args", "message", "NODE_ENV", "callStackFrames", "Error", "stack", "split", "undefined", "length"], "mappings": ";;;;AAAA,YAAYA,WAAW,QAAO;;AAE9B,MAAMC,WAAsC;IAAEC,SAAS;AAAK;AAE5D,iFAAiF;AACjF,MAAMC,QACJ,OAAOH,MAAMG,kaAAK,KAAK,aACnBH,MAAMG,kaAAK,GACX,CAACC,KAA+BA;AAEtC,2EAA2E;AAC3E,qEAAqE;AACrE,gBAAgB;AAChB,MAAMC,iBAAiBC,QAAQC,GAAG,CAACC,uBAAuB,GACtDC,QAAQC,KAAK,aACbD,QAAQE,IAAI;AAEhB,2CAA2C;AAC3C,wGAAwG;AACxG,MAAMC,yBAAyBT,MAC7B,AACA,CAACU,yEADyE;IAExE,IAAI;QACFR,eAAeJ,SAASC,OAAO;IACjC,SAAU;QACRD,SAASC,OAAO,GAAG;IACrB;AACF;AAcK,SAASY,4CACdC,UAAoC;IAEpC,OAAO,SAASC,gBAAgB,GAAGC,IAAU;QAC3C,MAAMC,UAAUH,cAAcE;QAE9B,IAAIX,QAAQC,GAAG,CAACY,QAAQ,KAAK,WAAc;gBACjB;YAAxB,MAAMC,kBAAAA,CAAkB,SAAA,IAAIC,QAAQC,KAAK,KAAA,OAAA,KAAA,IAAjB,OAAmBC,KAAK,CAAC;YACjD,IAAIH,oBAAoBI,aAAaJ,gBAAgBK,MAAM,GAAG,GAAG;gBAC/DpB,eAAea;YACjB,OAAO;gBACL,SAAS;gBACT,oBAAoB;gBACpB,uCAAuC;gBACvC,wBAAwB;gBACxB,iEAAiE;gBACjE,MAAML,MAAMO,eAAe,CAAC,EAAE;gBAC9BnB,SAASC,OAAO,GAAGgB;gBACnBN,uBAAuBC;YACzB;QACF,OAAO;;IAGT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4708, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/shared/lib/utils/reflect-utils.ts"], "sourcesContent": ["// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nexport function describeStringPropertyAccess(target: string, prop: string) {\n  if (isDefinitelyAValidIdentifier.test(prop)) {\n    return `\\`${target}.${prop}\\``\n  }\n  return `\\`${target}[${JSON.stringify(prop)}]\\``\n}\n\nexport function describeHasCheckingStringProperty(\n  target: string,\n  prop: string\n) {\n  const stringifiedProp = JSON.stringify(prop)\n  return `\\`Reflect.has(${target}, ${stringifiedProp})\\`, \\`${stringifiedProp} in ${target}\\`, or similar`\n}\n\nexport const wellKnownProperties = new Set([\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toString',\n  'valueOf',\n  'toLocaleString',\n\n  // Promise prototype\n  'then',\n  'catch',\n  'finally',\n\n  // React Promise extension\n  'status',\n  // 'value',\n  // 'error',\n\n  // React introspection\n  'displayName',\n  '_debugInfo',\n\n  // Common tested properties\n  'toJSON',\n  '$$typeof',\n  '__esModule',\n])\n"], "names": ["isDefinitelyAValidIdentifier", "describeStringPropertyAccess", "target", "prop", "test", "JSON", "stringify", "describeHasCheckingStringProperty", "stringifiedProp", "wellKnownProperties", "Set"], "mappings": "AAAA,6EAA6E;AAC7E,iFAAiF;AACjF,0FAA0F;AAC1F,uFAAuF;AACvF,2DAA2D;;;;;;;;;AAC3D,MAAMA,+BAA+B;AAE9B,SAASC,6BAA6BC,MAAc,EAAEC,IAAY;IACvE,IAAIH,6BAA6BI,IAAI,CAACD,OAAO;QAC3C,OAAO,CAAC,EAAE,EAAED,OAAO,CAAC,EAAEC,KAAK,EAAE,CAAC;IAChC;IACA,OAAO,CAAC,EAAE,EAAED,OAAO,CAAC,EAAEG,KAAKC,SAAS,CAACH,MAAM,GAAG,CAAC;AACjD;AAEO,SAASI,kCACdL,MAAc,EACdC,IAAY;IAEZ,MAAMK,kBAAkBH,KAAKC,SAAS,CAACH;IACvC,OAAO,CAAC,cAAc,EAAED,OAAO,EAAE,EAAEM,gBAAgB,OAAO,EAAEA,gBAAgB,IAAI,EAAEN,OAAO,cAAc,CAAC;AAC1G;AAEO,MAAMO,sBAAsB,IAAIC,IAAI;IACzC;IACA;IACA;IACA;IACA;IACA;IAEA,oBAAoB;IACpB;IACA;IACA;IAEA,0BAA0B;IAC1B;IACA,WAAW;IACX,WAAW;IAEX,sBAAsB;IACtB;IACA;IAEA,2BAA2B;IAC3B;IACA;IACA;CACD,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4759, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/server/request/utils.ts"], "sourcesContent": ["import { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\n\nexport function throwWithStaticGenerationBailoutErrorWithDynamicError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwForSearchParamsAccessInUseCache(\n  workStore: WorkStore,\n  constructorOpt: Function\n): never {\n  const error = new Error(\n    `Route ${workStore.route} used \\`searchParams\\` inside \"use cache\". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await \\`searchParams\\` outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n  )\n\n  Error.captureStackTrace(error, constructorOpt)\n  workStore.invalidDynamicUsageError ??= error\n\n  throw error\n}\n\nexport function isRequestAPICallableInsideAfter() {\n  const afterTaskStore = afterTaskAsyncStorage.getStore()\n  return afterTaskStore?.rootTaskSpawnPhase === 'action'\n}\n"], "names": ["StaticGenBailoutError", "afterTaskAsyncStorage", "throwWithStaticGenerationBailoutErrorWithDynamicError", "route", "expression", "throwForSearchParamsAccessInUseCache", "workStore", "constructorOpt", "error", "Error", "captureStackTrace", "invalidDynamicUsageError", "isRequestAPICallableInsideAfter", "afterTaskStore", "getStore", "rootTaskSpawnPhase"], "mappings": ";;;;;;;;AAAA,SAASA,qBAAqB,QAAQ,oDAAmD;AACzF,SAASC,qBAAqB,QAAQ,kDAAiD;;;AAGhF,SAASC,sDACdC,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIJ,ibAAAA,CACR,CAAC,MAAM,EAAEG,MAAM,4EAA4E,EAAEC,WAAW,0HAA0H,CAAC,GAD/N,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASC,qCACdC,SAAoB,EACpBC,cAAwB;IAExB,MAAMC,QAAQ,OAAA,cAEb,CAFa,IAAIC,MAChB,CAAC,MAAM,EAAEH,UAAUH,KAAK,CAAC,2XAA2X,CAAC,GADzY,qBAAA;eAAA;oBAAA;sBAAA;IAEd;IAEAM,MAAMC,iBAAiB,CAACF,OAAOD;IAC/BD,UAAUK,wBAAwB,KAAKH;IAEvC,MAAMA;AACR;AAEO,SAASI;IACd,MAAMC,iBAAiBZ,8SAAAA,CAAsBa,QAAQ;IACrD,OAAOD,CAAAA,kBAAAA,OAAAA,KAAAA,IAAAA,eAAgBE,kBAAkB,MAAK;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4796, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/server/request/search-params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  annotateDynamicAccess,\n  delayUntilRuntimeStage,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n  type PrerenderStoreModern,\n  type PrerenderStoreModernRuntime,\n  type StaticPrerenderStore,\n  throwInvariantForMissingStore,\n  type RequestStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  throwWithStaticGenerationBailoutErrorWithDynamicError,\n  throwForSearchParamsAccessInUseCache,\n} from './utils'\nimport { RenderStage } from '../app-render/staged-rendering'\n\nexport type SearchParams = { [key: string]: string | string[] | undefined }\n\nexport function createSearchParamsFromClient(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderSearchParams(workStore, workUnitStore)\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createSearchParamsFromClient should not be called in a runtime prerender.'\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createSearchParamsFromClient should not be called in cache contexts.'\n        )\n      case 'request':\n        return createRenderSearchParams(\n          underlyingSearchParams,\n          workStore,\n          workUnitStore\n        )\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport const createServerSearchParamsForMetadata =\n  createServerSearchParamsForServerPage\n\nexport function createServerSearchParamsForServerPage(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderSearchParams(workStore, workUnitStore)\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerSearchParamsForServerPage should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderSearchParams(\n          underlyingSearchParams,\n          workUnitStore\n        )\n      case 'request':\n        return createRenderSearchParams(\n          underlyingSearchParams,\n          workStore,\n          workUnitStore\n        )\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createPrerenderSearchParamsForClientPage(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n        // We're prerendering in a mode that aborts (cacheComponents) and should stall\n        // the promise to ensure the RSC side is considered dynamic\n        return makeHangingPromise(\n          workUnitStore.renderSignal,\n          workStore.route,\n          '`searchParams`'\n        )\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createPrerenderSearchParamsForClientPage should not be called in a runtime prerender.'\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createPrerenderSearchParamsForClientPage should not be called in cache contexts.'\n        )\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'request':\n        return Promise.resolve({})\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nfunction createStaticPrerenderSearchParams(\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  switch (prerenderStore.type) {\n    case 'prerender':\n    case 'prerender-client':\n      // We are in a cacheComponents (PPR or otherwise) prerender\n      return makeHangingSearchParams(workStore, prerenderStore)\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // We are in a legacy static generation and need to interrupt the\n      // prerender when search params are accessed.\n      return makeErroringSearchParams(workStore, prerenderStore)\n    default:\n      return prerenderStore satisfies never\n  }\n}\n\nfunction createRuntimePrerenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workUnitStore: PrerenderStoreModernRuntime\n): Promise<SearchParams> {\n  return delayUntilRuntimeStage(\n    workUnitStore,\n    makeUntrackedSearchParams(underlyingSearchParams)\n  )\n}\n\nfunction createRenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      // Semantically we only need the dev tracking when running in `next dev`\n      // but since you would never use next dev with production NODE_ENV we use this\n      // as a proxy so we can statically exclude this code from production builds.\n      return makeUntrackedSearchParamsWithDevWarnings(\n        underlyingSearchParams,\n        workStore,\n        requestStore\n      )\n    } else {\n      return makeUntrackedSearchParams(underlyingSearchParams)\n    }\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nconst CachedSearchParamsForUseCache = new WeakMap<\n  CacheLifetime,\n  Promise<SearchParams>\n>()\n\nfunction makeHangingSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreModern\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(prerenderStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = makeHangingPromise<SearchParams>(\n    prerenderStore.renderSignal,\n    workStore.route,\n    '`searchParams`'\n  )\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        default: {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n  })\n\n  CachedSearchParams.set(prerenderStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeErroringSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreLegacy | PrerenderStorePPR\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const underlyingSearchParams = {}\n  // For search params we don't construct a ReactPromise because we want to interrupt\n  // rendering on any property access that was not set from outside and so we only want\n  // to have properties like value and status if React sets them.\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      if (typeof prop === 'string' && prop === 'then') {\n        const expression =\n          '`await searchParams`, `searchParams.then`, or similar'\n        if (workStore.dynamicShouldError) {\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        } else if (prerenderStore.type === 'prerender-ppr') {\n          // PPR Prerender (no cacheComponents)\n          postponeWithTracking(\n            workStore.route,\n            expression,\n            prerenderStore.dynamicTracking\n          )\n        } else {\n          // Legacy Prerender\n          throwToInterruptStaticGeneration(\n            expression,\n            workStore,\n            prerenderStore\n          )\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  CachedSearchParams.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\n/**\n * This is a variation of `makeErroringSearchParams` that always throws an\n * error on access, because accessing searchParams inside of `\"use cache\"` is\n * not allowed.\n */\nexport function makeErroringSearchParamsForUseCache(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParamsForUseCache.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve({})\n\n  const proxiedPromise = new Proxy(promise, {\n    get: function get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it. We know it\n        // isn't a dynamic access because it can only be something that was\n        // previously written to the promise and thus not an underlying\n        // searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore, get)\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  CachedSearchParamsForUseCache.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeUntrackedSearchParams(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  return promise\n}\n\nfunction makeUntrackedSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<SearchParams> {\n  if (requestStore.asyncApiPromises) {\n    // Do not cache the resulting promise. If we do, we'll only show the first \"awaited at\"\n    // across all segments that receive searchParams.\n    return makeUntrackedSearchParamsWithDevWarningsImpl(\n      underlyingSearchParams,\n      workStore,\n      requestStore\n    )\n  } else {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n    if (cachedSearchParams) {\n      return cachedSearchParams\n    }\n    const promise = makeUntrackedSearchParamsWithDevWarningsImpl(\n      underlyingSearchParams,\n      workStore,\n      requestStore\n    )\n    CachedSearchParams.set(requestStore, promise)\n    return promise\n  }\n}\n\nfunction makeUntrackedSearchParamsWithDevWarningsImpl(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<SearchParams> {\n  const promiseInitialized = { current: false }\n  const proxiedUnderlying = instrumentSearchParamsObjectWithDevWarnings(\n    underlyingSearchParams,\n    workStore,\n    promiseInitialized\n  )\n\n  let promise: Promise<SearchParams>\n  if (requestStore.asyncApiPromises) {\n    // We wrap each instance of searchParams in a `new Promise()`.\n    // This is important when all awaits are in third party which would otherwise\n    // track all the way to the internal params.\n    const sharedSearchParamsParent =\n      requestStore.asyncApiPromises.sharedSearchParamsParent\n    promise = new Promise((resolve, reject) => {\n      sharedSearchParamsParent.then(() => resolve(proxiedUnderlying), reject)\n    })\n    // @ts-expect-error\n    promise.displayName = 'searchParams'\n  } else {\n    promise = makeDevtoolsIOAwarePromise(\n      proxiedUnderlying,\n      requestStore,\n      RenderStage.Runtime\n    )\n  }\n  promise.then(\n    () => {\n      promiseInitialized.current = true\n    },\n    // If we're in staged rendering, this promise will reject if the render\n    // is aborted before it can reach the runtime stage.\n    // In that case, we have to prevent an unhandled rejection from the promise\n    // created by this `.then()` call.\n    // This does not affect the `promiseInitialized` logic above,\n    // because `proxiedUnderlying` will not be used to resolve the promise,\n    // so there's no risk of any of its properties being accessed and triggering\n    // an undesireable warning.\n    ignoreReject\n  )\n\n  return instrumentSearchParamsPromiseWithDevWarnings(\n    underlyingSearchParams,\n    promise,\n    workStore\n  )\n}\n\nfunction ignoreReject() {}\n\nfunction instrumentSearchParamsObjectWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore,\n  promiseInitialized: { current: boolean }\n) {\n  // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n  // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n  // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n  // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n  // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n  // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n  return new Proxy(underlyingSearchParams, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string' && promiseInitialized.current) {\n        if (workStore.dynamicShouldError) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (workStore.dynamicShouldError) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      if (workStore.dynamicShouldError) {\n        const expression =\n          '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          workStore.route,\n          expression\n        )\n      }\n      return Reflect.ownKeys(target)\n    },\n  })\n}\n\nfunction instrumentSearchParamsPromiseWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  promise: Promise<SearchParams>,\n  workStore: WorkStore\n) {\n  // Track which properties we should warn for.\n  const proxiedProperties = new Set<string>()\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  return new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (prop === 'then' && workStore.dynamicShouldError) {\n        const expression = '`searchParams.then`'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          workStore.route,\n          expression\n        )\n      }\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(workStore.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(workStore.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      warnForSyncAccess(workStore.route, expression)\n      return Reflect.ownKeys(target)\n    },\n  })\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createSearchAccessError\n)\n\nfunction createSearchAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` is a Promise and must be unwrapped with \\`await\\` or \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n"], "names": ["ReflectAdapter", "throwToInterruptStaticGeneration", "postponeWithTracking", "annotateDynamicAccess", "delayUntilRuntimeStage", "workUnitAsyncStorage", "throwInvariantForMissingStore", "InvariantError", "makeDevtoolsIOAwarePromise", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "describeStringPropertyAccess", "describeHasCheckingStringProperty", "wellKnownProperties", "throwWithStaticGenerationBailoutErrorWithDynamicError", "throwForSearchParamsAccessInUseCache", "RenderStage", "createSearchParamsFromClient", "underlyingSearchParams", "workStore", "workUnitStore", "getStore", "type", "createStaticPrerenderSearchParams", "createRenderSearchParams", "createServerSearchParamsForMetadata", "createServerSearchParamsForServerPage", "createRuntimePrerenderSearchParams", "createPrerenderSearchParamsForClientPage", "forceStatic", "Promise", "resolve", "renderSignal", "route", "prerenderStore", "makeHangingSearchParams", "makeErroringSearchParams", "makeUntrackedSearchParams", "requestStore", "process", "env", "NODE_ENV", "makeUntrackedSearchParamsWithDevWarnings", "CachedSearchParams", "WeakMap", "CachedSearchParamsForUseCache", "cachedSearchParams", "get", "promise", "proxiedPromise", "Proxy", "target", "prop", "receiver", "Object", "hasOwn", "expression", "set", "dynamicShouldError", "dynamicTracking", "makeErroringSearchParamsForUseCache", "has", "asyncApiPromises", "makeUntrackedSearchParamsWithDevWarningsImpl", "promiseInitialized", "current", "proxiedUnderlying", "instrumentSearchParamsObjectWithDevWarnings", "sharedSearchParamsParent", "reject", "then", "displayName", "Runtime", "ignoreReject", "instrumentSearchParamsPromiseWithDevWarnings", "Reflect", "ownKeys", "proxiedProperties", "Set", "keys", "for<PERSON>ach", "add", "warnForSyncAccess", "value", "delete", "createSearchAccessError", "prefix", "Error"], "mappings": ";;;;;;;;;;;;AAEA,SAASA,cAAc,QAAQ,yCAAwC;AACvE,SACEC,gCAAgC,EAChCC,oBAAoB,EACpBC,qBAAqB,EACrBC,sBAAsB,QACjB,kCAAiC;AAExC,SACEC,oBAAoB,EAMpBC,6BAA6B,QAExB,iDAAgD;AACvD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SACEC,0BAA0B,EAC1BC,kBAAkB,QACb,6BAA4B;AACnC,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SACEC,4BAA4B,EAC5BC,iCAAiC,EACjCC,mBAAmB,QACd,uCAAsC;AAC7C,SACEC,qDAAqD,EACrDC,oCAAoC,QAC/B,UAAS;AAChB,SAASC,WAAW,QAAQ,iCAAgC;;;;;;;;;;AAIrD,SAASC,6BACdC,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBf,2SAAAA,CAAqBgB,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,kCAAkCJ,WAAWC;YACtD,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIb,sZAAAA,CACR,8EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIA,sZAAAA,CACR,yEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOiB,yBACLN,wBACAC,WACAC;YAEJ;gBACEA;QACJ;IACF;QACAd,oTAAAA;AACF;AAGO,MAAMmB,sCACXC,sCAAqC;AAEhC,SAASA,sCACdR,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBf,2SAAAA,CAAqBgB,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,kCAAkCJ,WAAWC;YACtD,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIb,sZAAAA,CACR,kFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOoB,mCACLT,wBACAE;YAEJ,KAAK;gBACH,OAAOI,yBACLN,wBACAC,WACAC;YAEJ;gBACEA;QACJ;IACF;QACAd,oTAAAA;AACF;AAEO,SAASsB,yCACdT,SAAoB;IAEpB,IAAIA,UAAUU,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,MAAMX,gBAAgBf,2SAAAA,CAAqBgB,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,8EAA8E;gBAC9E,2DAA2D;gBAC3D,WAAOb,8ZAAAA,EACLW,cAAcY,YAAY,EAC1Bb,UAAUc,KAAK,EACf;YAEJ,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAI1B,sZAAAA,CACR,0FADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIA,sZAAAA,CACR,qFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOuB,QAAQC,OAAO,CAAC,CAAC;YAC1B;gBACEX;QACJ;IACF;QACAd,oTAAAA;AACF;AAEA,SAASiB,kCACPJ,SAAoB,EACpBe,cAAoC;IAEpC,IAAIf,UAAUU,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,OAAQG,eAAeZ,IAAI;QACzB,KAAK;QACL,KAAK;YACH,2DAA2D;YAC3D,OAAOa,wBAAwBhB,WAAWe;QAC5C,KAAK;QACL,KAAK;YACH,iEAAiE;YACjE,6CAA6C;YAC7C,OAAOE,yBAAyBjB,WAAWe;QAC7C;YACE,OAAOA;IACX;AACF;AAEA,SAASP,mCACPT,sBAAoC,EACpCE,aAA0C;IAE1C,WAAOhB,0aAAAA,EACLgB,eACAiB,0BAA0BnB;AAE9B;AAEA,SAASM,yBACPN,sBAAoC,EACpCC,SAAoB,EACpBmB,YAA0B;IAE1B,IAAInB,UAAUU,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B,OAAO;QACL,IAAIQ,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,wEAAwE;YACxE,8EAA8E;YAC9E,4EAA4E;YAC5E,OAAOC,yCACLxB,wBACAC,WACAmB;QAEJ,OAAO;;IAGT;AACF;AAGA,MAAMK,qBAAqB,IAAIC;AAE/B,MAAMC,gCAAgC,IAAID;AAK1C,SAAST,wBACPhB,SAAoB,EACpBe,cAAoC;IAEpC,MAAMY,qBAAqBH,mBAAmBI,GAAG,CAACb;IAClD,IAAIY,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,cAAUvC,8ZAAAA,EACdyB,eAAeF,YAAY,EAC3Bb,UAAUc,KAAK,EACf;IAGF,MAAMgB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOpD,4aAAAA,CAAe+C,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMI,aACJ;4BACFrD,yaAAAA,EAAsBqD,YAAYtB;wBAClC,OAAOlC,4aAAAA,CAAe+C,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBACA,KAAK;oBAAU;wBACb,MAAMG,aACJ;4BACFrD,yaAAAA,EAAsBqD,YAAYtB;wBAClC,OAAOlC,4aAAAA,CAAe+C,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBAEA;oBAAS;wBACP,OAAOrD,4aAAAA,CAAe+C,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;IACF;IAEAV,mBAAmBc,GAAG,CAACvB,gBAAgBe;IACvC,OAAOA;AACT;AAEA,SAASb,yBACPjB,SAAoB,EACpBe,cAAwD;IAExD,MAAMY,qBAAqBH,mBAAmBI,GAAG,CAAC5B;IAClD,IAAI2B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAM5B,yBAAyB,CAAC;IAChC,mFAAmF;IACnF,qFAAqF;IACrF,+DAA+D;IAC/D,MAAM8B,UAAUlB,QAAQC,OAAO,CAACb;IAEhC,MAAM+B,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOpD,4aAAAA,CAAe+C,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,IAAI,OAAOD,SAAS,YAAYA,SAAS,QAAQ;gBAC/C,MAAMI,aACJ;gBACF,IAAIrC,UAAUuC,kBAAkB,EAAE;wBAChC5C,obAAAA,EACEK,UAAUc,KAAK,EACfuB;gBAEJ,OAAO,IAAItB,eAAeZ,IAAI,KAAK,iBAAiB;oBAClD,qCAAqC;wBACrCpB,waAAAA,EACEiB,UAAUc,KAAK,EACfuB,YACAtB,eAAeyB,eAAe;gBAElC,OAAO;oBACL,mBAAmB;wBACnB1D,obAAAA,EACEuD,YACArC,WACAe;gBAEJ;YACF;YACA,OAAOlC,4aAAAA,CAAe+C,GAAG,CAACI,QAAQC,MAAMC;QAC1C;IACF;IAEAV,mBAAmBc,GAAG,CAACtC,WAAW8B;IAClC,OAAOA;AACT;AAOO,SAASW,oCACdzC,SAAoB;IAEpB,MAAM2B,qBAAqBD,8BAA8BE,GAAG,CAAC5B;IAC7D,IAAI2B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUlB,QAAQC,OAAO,CAAC,CAAC;IAEjC,MAAMkB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAK,SAASA,IAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACtC,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,wEAAwE;gBACxE,mEAAmE;gBACnE,+DAA+D;gBAC/D,oBAAoB;gBACpB,OAAOpD,4aAAAA,CAAe+C,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,IACE,OAAOD,SAAS,YACfA,CAAAA,SAAS,UAAU,CAACvC,kaAAAA,CAAoBgD,GAAG,CAACT,KAAI,GACjD;oBACArC,maAAAA,EAAqCI,WAAW4B;YAClD;YAEA,OAAO/C,4aAAAA,CAAe+C,GAAG,CAACI,QAAQC,MAAMC;QAC1C;IACF;IAEAR,8BAA8BY,GAAG,CAACtC,WAAW8B;IAC7C,OAAOA;AACT;AAEA,SAASZ,0BACPnB,sBAAoC;IAEpC,MAAM4B,qBAAqBH,mBAAmBI,GAAG,CAAC7B;IAClD,IAAI4B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUlB,QAAQC,OAAO,CAACb;IAChCyB,mBAAmBc,GAAG,CAACvC,wBAAwB8B;IAE/C,OAAOA;AACT;AAEA,SAASN,yCACPxB,sBAAoC,EACpCC,SAAoB,EACpBmB,YAA0B;IAE1B,IAAIA,aAAawB,gBAAgB,EAAE;QACjC,uFAAuF;QACvF,iDAAiD;QACjD,OAAOC,6CACL7C,wBACAC,WACAmB;IAEJ,OAAO;QACL,MAAMQ,qBAAqBH,mBAAmBI,GAAG,CAAC7B;QAClD,IAAI4B,oBAAoB;YACtB,OAAOA;QACT;QACA,MAAME,UAAUe,6CACd7C,wBACAC,WACAmB;QAEFK,mBAAmBc,GAAG,CAACnB,cAAcU;QACrC,OAAOA;IACT;AACF;AAEA,SAASe,6CACP7C,sBAAoC,EACpCC,SAAoB,EACpBmB,YAA0B;IAE1B,MAAM0B,qBAAqB;QAAEC,SAAS;IAAM;IAC5C,MAAMC,oBAAoBC,4CACxBjD,wBACAC,WACA6C;IAGF,IAAIhB;IACJ,IAAIV,aAAawB,gBAAgB,EAAE;QACjC,8DAA8D;QAC9D,6EAA6E;QAC7E,4CAA4C;QAC5C,MAAMM,2BACJ9B,aAAawB,gBAAgB,CAACM,wBAAwB;QACxDpB,UAAU,IAAIlB,QAAQ,CAACC,SAASsC;YAC9BD,yBAAyBE,IAAI,CAAC,IAAMvC,QAAQmC,oBAAoBG;QAClE;QACA,mBAAmB;QACnBrB,QAAQuB,WAAW,GAAG;IACxB,OAAO;QACLvB,cAAUxC,saAAAA,EACR0D,mBACA5B,cACAtB,8ZAAAA,CAAYwD,OAAO;IAEvB;IACAxB,QAAQsB,IAAI,CACV;QACEN,mBAAmBC,OAAO,GAAG;IAC/B,GACA,AACA,oDAAoD,mBADmB;IAEvE,2EAA2E;IAC3E,kCAAkC;IAClC,6DAA6D;IAC7D,uEAAuE;IACvE,4EAA4E;IAC5E,2BAA2B;IAC3BQ;IAGF,OAAOC,6CACLxD,wBACA8B,SACA7B;AAEJ;AAEA,SAASsD,gBAAgB;AAEzB,SAASN,4CACPjD,sBAAoC,EACpCC,SAAoB,EACpB6C,kBAAwC;IAExC,0HAA0H;IAC1H,uIAAuI;IACvI,wIAAwI;IACxI,8IAA8I;IAC9I,6IAA6I;IAC7I,+GAA+G;IAC/G,OAAO,IAAId,MAAMhC,wBAAwB;QACvC6B,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,YAAYY,mBAAmBC,OAAO,EAAE;gBAC1D,IAAI9C,UAAUuC,kBAAkB,EAAE;oBAChC,MAAMF,iBAAa7C,2aAAAA,EAA6B,gBAAgByC;wBAChEtC,obAAAA,EACEK,UAAUc,KAAK,EACfuB;gBAEJ;YACF;YACA,OAAOxD,4aAAAA,CAAe+C,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAQ,KAAIV,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IAAIjC,UAAUuC,kBAAkB,EAAE;oBAChC,MAAMF,iBAAa5C,gbAAAA,EACjB,gBACAwC;wBAEFtC,obAAAA,EACEK,UAAUc,KAAK,EACfuB;gBAEJ;YACF;YACA,OAAOmB,QAAQd,GAAG,CAACV,QAAQC;QAC7B;QACAwB,SAAQzB,MAAM;YACZ,IAAIhC,UAAUuC,kBAAkB,EAAE;gBAChC,MAAMF,aACJ;oBACF1C,obAAAA,EACEK,UAAUc,KAAK,EACfuB;YAEJ;YACA,OAAOmB,QAAQC,OAAO,CAACzB;QACzB;IACF;AACF;AAEA,SAASuB,6CACPxD,sBAAoC,EACpC8B,OAA8B,EAC9B7B,SAAoB;IAEpB,6CAA6C;IAC7C,MAAM0D,oBAAoB,IAAIC;IAE9BxB,OAAOyB,IAAI,CAAC7D,wBAAwB8D,OAAO,CAAC,CAAC5B;QAC3C,IAAIvC,kaAAAA,CAAoBgD,GAAG,CAACT,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLyB,kBAAkBI,GAAG,CAAC7B;QACxB;IACF;IAEA,OAAO,IAAIF,MAAMF,SAAS;QACxBD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAID,SAAS,UAAUjC,UAAUuC,kBAAkB,EAAE;gBACnD,MAAMF,aAAa;oBACnB1C,obAAAA,EACEK,UAAUc,KAAK,EACfuB;YAEJ;YACA,IAAI,OAAOJ,SAAS,UAAU;gBAC5B,IACE,CAACvC,kaAAAA,CAAoBgD,GAAG,CAACT,SACxByB,CAAAA,kBAAkBhB,GAAG,CAACT,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BuB,QAAQd,GAAG,CAACV,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMI,iBAAa7C,2aAAAA,EAA6B,gBAAgByC;oBAChE8B,kBAAkB/D,UAAUc,KAAK,EAAEuB;gBACrC;YACF;YACA,OAAOxD,4aAAAA,CAAe+C,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAI,KAAIN,MAAM,EAAEC,IAAI,EAAE+B,KAAK,EAAE9B,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5ByB,kBAAkBO,MAAM,CAAChC;YAC3B;YACA,OAAOuB,QAAQlB,GAAG,CAACN,QAAQC,MAAM+B,OAAO9B;QAC1C;QACAQ,KAAIV,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACvC,kaAAAA,CAAoBgD,GAAG,CAACT,SACxByB,CAAAA,kBAAkBhB,GAAG,CAACT,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BuB,QAAQd,GAAG,CAACV,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMI,iBAAa5C,gbAAAA,EACjB,gBACAwC;oBAEF8B,kBAAkB/D,UAAUc,KAAK,EAAEuB;gBACrC;YACF;YACA,OAAOmB,QAAQd,GAAG,CAACV,QAAQC;QAC7B;QACAwB,SAAQzB,MAAM;YACZ,MAAMK,aAAa;YACnB0B,kBAAkB/D,UAAUc,KAAK,EAAEuB;YACnC,OAAOmB,QAAQC,OAAO,CAACzB;QACzB;IACF;AACF;AAEA,MAAM+B,wBAAoBxE,0dAAAA,EACxB2E;AAGF,SAASA,wBACPpD,KAAyB,EACzBuB,UAAkB;IAElB,MAAM8B,SAASrD,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAIsD,MACT,GAAGD,OAAO,KAAK,EAAE9B,WAAW,EAAE,CAAC,GAC7B,CAAC,uHAAuH,CAAC,GACzH,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5215, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/server/request/params.ts"], "sourcesContent": ["import {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type { OpaqueFallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  delayUntilRuntimeStage,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type StaticPrerenderStoreModern,\n  type StaticPrerenderStore,\n  throwInvariantForMissingStore,\n  type PrerenderStoreModernRuntime,\n  type RequestStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { dynamicAccessAsyncStorage } from '../app-render/dynamic-access-async-storage.external'\nimport { RenderStage } from '../app-render/staged-rendering'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createParamsFromClient should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createParamsFromClient should not be called in a runtime prerender.'\n        )\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore,\n            workUnitStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerParamsForRoute should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderParams(underlyingParams, workUnitStore)\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore,\n            workUnitStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerParamsForServerSegment should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderParams(underlyingParams, workUnitStore)\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore,\n            workUnitStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params\n): Promise<Params> {\n  const workStore = workAsyncStorage.getStore()\n  if (!workStore) {\n    throw new InvariantError(\n      'Missing workStore in createPrerenderParamsForClientSegment'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams) {\n          for (let key in underlyingParams) {\n            if (fallbackParams.has(key)) {\n              // This params object has one or more fallback params, so we need\n              // to consider the awaiting of this params object \"dynamic\". Since\n              // we are in cacheComponents mode we encode this as a promise that never\n              // resolves.\n              return makeHangingPromise(\n                workUnitStore.renderSignal,\n                workStore.route,\n                '`params`'\n              )\n            }\n          }\n        }\n        break\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createPrerenderParamsForClientSegment should not be called in cache contexts.'\n        )\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'prerender-runtime':\n      case 'request':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createStaticPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<Params> {\n  switch (prerenderStore.type) {\n    case 'prerender':\n    case 'prerender-client': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            // This params object has one or more fallback params, so we need\n            // to consider the awaiting of this params object \"dynamic\". Since\n            // we are in cacheComponents mode we encode this as a promise that never\n            // resolves.\n            return makeHangingParams(\n              underlyingParams,\n              workStore,\n              prerenderStore\n            )\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-ppr': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            return makeErroringParams(\n              underlyingParams,\n              fallbackParams,\n              workStore,\n              prerenderStore\n            )\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-legacy':\n      break\n    default:\n      prerenderStore satisfies never\n  }\n\n  return makeUntrackedParams(underlyingParams)\n}\n\nfunction createRuntimePrerenderParams(\n  underlyingParams: Params,\n  workUnitStore: PrerenderStoreModernRuntime\n): Promise<Params> {\n  return delayUntilRuntimeStage(\n    workUnitStore,\n    makeUntrackedParams(underlyingParams)\n  )\n}\n\nfunction createRenderParamsInProd(underlyingParams: Params): Promise<Params> {\n  return makeUntrackedParams(underlyingParams)\n}\n\nfunction createRenderParamsInDev(\n  underlyingParams: Params,\n  devFallbackParams: OpaqueFallbackRouteParams | null | undefined,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<Params> {\n  let hasFallbackParams = false\n  if (devFallbackParams) {\n    for (let key in underlyingParams) {\n      if (devFallbackParams.has(key)) {\n        hasFallbackParams = true\n        break\n      }\n    }\n  }\n\n  return makeDynamicallyTrackedParamsWithDevWarnings(\n    underlyingParams,\n    hasFallbackParams,\n    workStore,\n    requestStore\n  )\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nconst fallbackParamsProxyHandler: ProxyHandler<Promise<Params>> = {\n  get: function get(target, prop, receiver) {\n    if (prop === 'then' || prop === 'catch' || prop === 'finally') {\n      const originalMethod = ReflectAdapter.get(target, prop, receiver)\n\n      return {\n        [prop]: (...args: unknown[]) => {\n          const store = dynamicAccessAsyncStorage.getStore()\n\n          if (store) {\n            store.abortController.abort(\n              new Error(`Accessed fallback \\`params\\` during prerendering.`)\n            )\n          }\n\n          return new Proxy(\n            originalMethod.apply(target, args),\n            fallbackParamsProxyHandler\n          )\n        },\n      }[prop]\n    }\n\n    return ReflectAdapter.get(target, prop, receiver)\n  },\n}\n\nfunction makeHangingParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = new Proxy(\n    makeHangingPromise<Params>(\n      prerenderStore.renderSignal,\n      workStore.route,\n      '`params`'\n    ),\n    fallbackParamsProxyHandler\n  )\n\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeErroringParams(\n  underlyingParams: Params,\n  fallbackParams: OpaqueFallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when cacheComponents is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no cacheComponents)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedParamsWithDevWarnings(\n  underlyingParams: Params,\n  hasFallbackParams: boolean,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<Params> {\n  if (requestStore.asyncApiPromises && hasFallbackParams) {\n    // We wrap each instance of params in a `new Promise()`, because deduping\n    // them across requests doesn't work anyway and this let us show each\n    // await a different set of values. This is important when all awaits\n    // are in third party which would otherwise track all the way to the\n    // internal params.\n    const sharedParamsParent = requestStore.asyncApiPromises.sharedParamsParent\n    const promise: Promise<Params> = new Promise((resolve, reject) => {\n      sharedParamsParent.then(() => resolve(underlyingParams), reject)\n    })\n    // @ts-expect-error\n    promise.displayName = 'params'\n    return instrumentParamsPromiseWithDevWarnings(\n      underlyingParams,\n      promise,\n      workStore\n    )\n  }\n\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = hasFallbackParams\n    ? makeDevtoolsIOAwarePromise(\n        underlyingParams,\n        requestStore,\n        RenderStage.Runtime\n      )\n    : // We don't want to force an environment transition when this params is not part of the fallback params set\n      Promise.resolve(underlyingParams)\n\n  const proxiedPromise = instrumentParamsPromiseWithDevWarnings(\n    underlyingParams,\n    promise,\n    workStore\n  )\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction instrumentParamsPromiseWithDevWarnings(\n  underlyingParams: Params,\n  promise: Promise<Params>,\n  workStore: WorkStore\n): Promise<Params> {\n  // Track which properties we should warn for.\n  const proxiedProperties = new Set<string>()\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  return new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(workStore.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      warnForSyncAccess(workStore.route, expression)\n      return Reflect.ownKeys(target)\n    },\n  })\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` is a Promise and must be unwrapped with \\`await\\` or \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n"], "names": ["workAsyncStorage", "ReflectAdapter", "throwToInterruptStaticGeneration", "postponeWithTracking", "delayUntilRuntimeStage", "workUnitAsyncStorage", "throwInvariantForMissingStore", "InvariantError", "describeStringPropertyAccess", "wellKnownProperties", "makeDevtoolsIOAwarePromise", "makeHangingPromise", "createDedupedByCallsiteServerErrorLoggerDev", "dynamicAccessAsyncStorage", "RenderStage", "createParamsFromClient", "underlyingParams", "workStore", "workUnitStore", "getStore", "type", "createStaticPrerenderParams", "process", "env", "NODE_ENV", "devFallbackParams", "createRenderParamsInDev", "createRenderParamsInProd", "createServerParamsForMetadata", "createServerParamsForServerSegment", "createServerParamsForRoute", "createRuntimePrerenderParams", "createPrerenderParamsForClientSegment", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "key", "has", "renderSignal", "route", "Promise", "resolve", "prerenderStore", "makeHangingParams", "makeErroringParams", "makeUntrackedParams", "requestStore", "hasFallbackParams", "makeDynamicallyTrackedParamsWithDevWarnings", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "fallbackParamsProxyHandler", "get", "target", "prop", "receiver", "originalMethod", "args", "store", "abortController", "abort", "Error", "Proxy", "apply", "cachedParams", "promise", "set", "augmentedUnderlying", "Object", "keys", "for<PERSON>ach", "defineProperty", "expression", "dynamicTracking", "enumerable", "asyncApiPromises", "sharedParamsParent", "reject", "then", "displayName", "instrumentParamsPromiseWithDevWarnings", "Runtime", "proxiedPromise", "proxiedProperties", "Set", "add", "warnForSyncAccess", "value", "delete", "ownKeys", "Reflect", "createParamsAccessError", "prefix"], "mappings": ";;;;;;;;;;;;AAAA,SACEA,gBAAgB,QAEX,4CAA2C;AAGlD,SAASC,cAAc,QAAQ,yCAAwC;AACvE,SACEC,gCAAgC,EAChCC,oBAAoB,EACpBC,sBAAsB,QACjB,kCAAiC;AAExC,SACEC,oBAAoB,EAKpBC,6BAA6B,QAGxB,iDAAgD;AACvD,SAASC,cAAc,QAAQ,mCAAkC;AACjE,SACEC,4BAA4B,EAC5BC,mBAAmB,QACd,uCAAsC;AAC7C,SACEC,0BAA0B,EAC1BC,kBAAkB,QACb,6BAA4B;AACnC,SAASC,2CAA2C,QAAQ,oDAAmD;AAC/G,SAASC,yBAAyB,QAAQ,sDAAqD;AAC/F,SAASC,WAAW,QAAQ,iCAAgC;;;;;;;;;;;AAKrD,SAASC,uBACdC,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBb,2SAAAA,CAAqBc,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BACLL,kBACAC,WACAC;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIX,sZAAAA,CACR,mEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIA,sZAAAA,CACR,wEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,IAAIe,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;oBAC1C,wEAAwE;oBACxE,8EAA8E;oBAC9E,4EAA4E;oBAC5E,MAAMC,oBAAoBP,cAAcO,iBAAiB;oBACzD,OAAOC,wBACLV,kBACAS,mBACAR,WACAC;gBAEJ,OAAO;;YAGT;gBACEA;QACJ;IACF;QACAZ,oTAAAA;AACF;AAIO,MAAMsB,gCAAgCC,mCAAkC;AAGxE,SAASC,2BACdd,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBb,2SAAAA,CAAqBc,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BACLL,kBACAC,WACAC;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIX,sZAAAA,CACR,uEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOwB,6BAA6Bf,kBAAkBE;YACxD,KAAK;gBACH,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;oBAC1C,wEAAwE;oBACxE,8EAA8E;oBAC9E,4EAA4E;oBAC5E,MAAMC,oBAAoBP,cAAcO,iBAAiB;oBACzD,OAAOC,wBACLV,kBACAS,mBACAR,WACAC;gBAEJ,OAAO;;YAGT;gBACEA;QACJ;IACF;QACAZ,oTAAAA;AACF;AAEO,SAASuB,mCACdb,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBb,2SAAAA,CAAqBc,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BACLL,kBACAC,WACAC;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIX,sZAAAA,CACR,+EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOwB,6BAA6Bf,kBAAkBE;YACxD,KAAK;gBACH,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;oBAC1C,wEAAwE;oBACxE,8EAA8E;oBAC9E,4EAA4E;oBAC5E,MAAMC,oBAAoBP,cAAcO,iBAAiB;oBACzD,OAAOC,wBACLV,kBACAS,mBACAR,WACAC;gBAEJ,OAAO;;YAGT;gBACEA;QACJ;IACF;QACAZ,oTAAAA;AACF;AAEO,SAAS0B,sCACdhB,gBAAwB;IAExB,MAAMC,YAAYjB,uRAAAA,CAAiBmB,QAAQ;IAC3C,IAAI,CAACF,WAAW;QACd,MAAM,OAAA,cAEL,CAFK,IAAIV,sZAAAA,CACR,+DADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMW,gBAAgBb,2SAAAA,CAAqBc,QAAQ;IACnD,IAAID,eAAe;QACjB,OAAQA,cAAcE,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,MAAMa,iBAAiBf,cAAcgB,mBAAmB;gBACxD,IAAID,gBAAgB;oBAClB,IAAK,IAAIE,OAAOnB,iBAAkB;wBAChC,IAAIiB,eAAeG,GAAG,CAACD,MAAM;4BAC3B,iEAAiE;4BACjE,kEAAkE;4BAClE,wEAAwE;4BACxE,YAAY;4BACZ,WAAOxB,8ZAAAA,EACLO,cAAcmB,YAAY,EAC1BpB,UAAUqB,KAAK,EACf;wBAEJ;oBACF;gBACF;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAI/B,sZAAAA,CACR,kFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEW;QACJ;IACF;IACA,mFAAmF;IACnF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOqB,QAAQC,OAAO,CAACxB;AACzB;AAEA,SAASK,4BACPL,gBAAwB,EACxBC,SAAoB,EACpBwB,cAAoC;IAEpC,OAAQA,eAAerB,IAAI;QACzB,KAAK;QACL,KAAK;YAAoB;gBACvB,MAAMa,iBAAiBQ,eAAeP,mBAAmB;gBACzD,IAAID,gBAAgB;oBAClB,IAAK,MAAME,OAAOnB,iBAAkB;wBAClC,IAAIiB,eAAeG,GAAG,CAACD,MAAM;4BAC3B,iEAAiE;4BACjE,kEAAkE;4BAClE,wEAAwE;4BACxE,YAAY;4BACZ,OAAOO,kBACL1B,kBACAC,WACAwB;wBAEJ;oBACF;gBACF;gBACA;YACF;QACA,KAAK;YAAiB;gBACpB,MAAMR,iBAAiBQ,eAAeP,mBAAmB;gBACzD,IAAID,gBAAgB;oBAClB,IAAK,MAAME,OAAOnB,iBAAkB;wBAClC,IAAIiB,eAAeG,GAAG,CAACD,MAAM;4BAC3B,OAAOQ,mBACL3B,kBACAiB,gBACAhB,WACAwB;wBAEJ;oBACF;gBACF;gBACA;YACF;QACA,KAAK;YACH;QACF;YACEA;IACJ;IAEA,OAAOG,oBAAoB5B;AAC7B;AAEA,SAASe,6BACPf,gBAAwB,EACxBE,aAA0C;IAE1C,WAAOd,0aAAAA,EACLc,eACA0B,oBAAoB5B;AAExB;AAEA,SAASW,yBAAyBX,gBAAwB;IACxD,OAAO4B,oBAAoB5B;AAC7B;AAEA,SAASU,wBACPV,gBAAwB,EACxBS,iBAA+D,EAC/DR,SAAoB,EACpB4B,YAA0B;IAE1B,IAAIC,oBAAoB;IACxB,IAAIrB,mBAAmB;QACrB,IAAK,IAAIU,OAAOnB,iBAAkB;YAChC,IAAIS,kBAAkBW,GAAG,CAACD,MAAM;gBAC9BW,oBAAoB;gBACpB;YACF;QACF;IACF;IAEA,OAAOC,4CACL/B,kBACA8B,mBACA7B,WACA4B;AAEJ;AAGA,MAAMG,eAAe,IAAIC;AAEzB,MAAMC,6BAA4D;IAChEC,KAAK,SAASA,IAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;QACtC,IAAID,SAAS,UAAUA,SAAS,WAAWA,SAAS,WAAW;YAC7D,MAAME,iBAAiBtD,4aAAAA,CAAekD,GAAG,CAACC,QAAQC,MAAMC;YAExD,OAAO,CAAA;gBACL,CAACD,KAAK,EAAE,CAAC,GAAGG;oBACV,MAAMC,QAAQ5C,0TAAAA,CAA0BM,QAAQ;oBAEhD,IAAIsC,OAAO;wBACTA,MAAMC,eAAe,CAACC,KAAK,CACzB,OAAA,cAA8D,CAA9D,IAAIC,MAAM,CAAC,iDAAiD,CAAC,GAA7D,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6D;oBAEjE;oBAEA,OAAO,IAAIC,MACTN,eAAeO,KAAK,CAACV,QAAQI,OAC7BN;gBAEJ;YACF,CAAA,CAAC,CAACG,KAAK;QACT;QAEA,OAAOpD,4aAAAA,CAAekD,GAAG,CAACC,QAAQC,MAAMC;IAC1C;AACF;AAEA,SAASZ,kBACP1B,gBAAwB,EACxBC,SAAoB,EACpBwB,cAA0C;IAE1C,MAAMsB,eAAef,aAAaG,GAAG,CAACnC;IACtC,IAAI+C,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMC,UAAU,IAAIH,UAClBlD,8ZAAAA,EACE8B,eAAeJ,YAAY,EAC3BpB,UAAUqB,KAAK,EACf,aAEFY;IAGFF,aAAaiB,GAAG,CAACjD,kBAAkBgD;IAEnC,OAAOA;AACT;AAEA,SAASrB,mBACP3B,gBAAwB,EACxBiB,cAAyC,EACzChB,SAAoB,EACpBwB,cAAwD;IAExD,MAAMsB,eAAef,aAAaG,GAAG,CAACnC;IACtC,IAAI+C,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMG,sBAAsB;QAAE,GAAGlD,gBAAgB;IAAC;IAElD,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMgD,UAAUzB,QAAQC,OAAO,CAAC0B;IAChClB,aAAaiB,GAAG,CAACjD,kBAAkBgD;IAEnCG,OAAOC,IAAI,CAACpD,kBAAkBqD,OAAO,CAAC,CAAChB;QACrC,IAAI5C,kaAAAA,CAAoB2B,GAAG,CAACiB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL,IAAIpB,eAAeG,GAAG,CAACiB,OAAO;gBAC5Bc,OAAOG,cAAc,CAACJ,qBAAqBb,MAAM;oBAC/CF;wBACE,MAAMoB,iBAAa/D,2aAAAA,EAA6B,UAAU6C;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,2EAA2E;wBAC3E,iCAAiC;wBACjC,IAAIZ,eAAerB,IAAI,KAAK,iBAAiB;4BAC3C,qCAAqC;gCACrCjB,waAAAA,EACEc,UAAUqB,KAAK,EACfiC,YACA9B,eAAe+B,eAAe;wBAElC,OAAO;4BACL,mBAAmB;gCACnBtE,obAAAA,EACEqE,YACAtD,WACAwB;wBAEJ;oBACF;oBACAgC,YAAY;gBACd;YACF;QACF;IACF;IAEA,OAAOT;AACT;AAEA,SAASpB,oBAAoB5B,gBAAwB;IACnD,MAAM+C,eAAef,aAAaG,GAAG,CAACnC;IACtC,IAAI+C,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMC,UAAUzB,QAAQC,OAAO,CAACxB;IAChCgC,aAAaiB,GAAG,CAACjD,kBAAkBgD;IAEnC,OAAOA;AACT;AAEA,SAASjB,4CACP/B,gBAAwB,EACxB8B,iBAA0B,EAC1B7B,SAAoB,EACpB4B,YAA0B;IAE1B,IAAIA,aAAa6B,gBAAgB,IAAI5B,mBAAmB;QACtD,yEAAyE;QACzE,qEAAqE;QACrE,qEAAqE;QACrE,oEAAoE;QACpE,mBAAmB;QACnB,MAAM6B,qBAAqB9B,aAAa6B,gBAAgB,CAACC,kBAAkB;QAC3E,MAAMX,UAA2B,IAAIzB,QAAQ,CAACC,SAASoC;YACrDD,mBAAmBE,IAAI,CAAC,IAAMrC,QAAQxB,mBAAmB4D;QAC3D;QACA,mBAAmB;QACnBZ,QAAQc,WAAW,GAAG;QACtB,OAAOC,uCACL/D,kBACAgD,SACA/C;IAEJ;IAEA,MAAM8C,eAAef,aAAaG,GAAG,CAACnC;IACtC,IAAI+C,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMC,UAAUlB,wBACZpC,saAAAA,EACEM,kBACA6B,cACA/B,8ZAAAA,CAAYkE,OAAO,IAGrBzC,QAAQC,OAAO,CAACxB;IAEpB,MAAMiE,iBAAiBF,uCACrB/D,kBACAgD,SACA/C;IAEF+B,aAAaiB,GAAG,CAACjD,kBAAkBiE;IACnC,OAAOA;AACT;AAEA,SAASF,uCACP/D,gBAAwB,EACxBgD,OAAwB,EACxB/C,SAAoB;IAEpB,6CAA6C;IAC7C,MAAMiE,oBAAoB,IAAIC;IAE9BhB,OAAOC,IAAI,CAACpD,kBAAkBqD,OAAO,CAAC,CAAChB;QACrC,IAAI5C,kaAAAA,CAAoB2B,GAAG,CAACiB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL6B,kBAAkBE,GAAG,CAAC/B;QACxB;IACF;IAEA,OAAO,IAAIQ,MAAMG,SAAS;QACxBb,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,AACA6B,kBAAkB9C,GAAG,CAACiB,OACtB,0CAFuE;oBAGvE,MAAMkB,iBAAa/D,2aAAAA,EAA6B,UAAU6C;oBAC1DgC,kBAAkBpE,UAAUqB,KAAK,EAAEiC;gBACrC;YACF;YACA,OAAOtE,4aAAAA,CAAekD,GAAG,CAACC,QAAQC,MAAMC;QAC1C;QACAW,KAAIb,MAAM,EAAEC,IAAI,EAAEiC,KAAK,EAAEhC,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5B6B,kBAAkBK,MAAM,CAAClC;YAC3B;YACA,OAAOpD,4aAAAA,CAAegE,GAAG,CAACb,QAAQC,MAAMiC,OAAOhC;QACjD;QACAkC,SAAQpC,MAAM;YACZ,MAAMmB,aAAa;YACnBc,kBAAkBpE,UAAUqB,KAAK,EAAEiC;YACnC,OAAOkB,QAAQD,OAAO,CAACpC;QACzB;IACF;AACF;AAEA,MAAMiC,wBAAoBzE,0dAAAA,EACxB8E;AAGF,SAASA,wBACPpD,KAAyB,EACzBiC,UAAkB;IAElB,MAAMoB,SAASrD,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAIsB,MACT,GAAG+B,OAAO,KAAK,EAAEpB,WAAW,EAAE,CAAC,GAC7B,CAAC,iHAAiH,CAAC,GACnH,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5616, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/client-page.tsx"], "sourcesContent": ["'use client'\n\nimport type { ParsedUrlQuery } from 'querystring'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\nimport { LayoutRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { use } from 'react'\nimport { urlSearchParamsToParsedUrlQuery } from '../route-params'\nimport { SearchParamsContext } from '../../shared/lib/hooks-client-context.shared-runtime'\n\n/**\n * When the Page is a client component we send the params and searchParams to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Page component.\n *\n * additionally we may send promises representing the params and searchParams. We don't ever use these passed\n * values but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations.\n * It is up to the caller to decide if the promises are needed.\n */\nexport function ClientPageRoot({\n  Component,\n  serverProvidedParams,\n}: {\n  Component: React.ComponentType<any>\n  serverProvidedParams: null | {\n    searchParams: ParsedUrlQuery\n    params: Params\n    promises: Array<Promise<any>> | null\n  }\n}) {\n  let searchParams: ParsedUrlQuery\n  let params: Params\n  if (serverProvidedParams !== null) {\n    searchParams = serverProvidedParams.searchParams\n    params = serverProvidedParams.params\n  } else {\n    // When Cache Components is enabled, the server does not pass the params as\n    // props; they are parsed on the client and passed via context.\n    const layoutRouterContext = use(LayoutRouterContext)\n    params =\n      layoutRouterContext !== null ? layoutRouterContext.parentParams : {}\n\n    // This is an intentional behavior change: when Cache Components is enabled,\n    // client segments receive the \"canonical\" search params, not the\n    // rewritten ones. Users should either call useSearchParams directly or pass\n    // the rewritten ones in from a Server Component.\n    // TODO: Log a deprecation error when this object is accessed\n    searchParams = urlSearchParamsToParsedUrlQuery(use(SearchParamsContext)!)\n  }\n\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientSearchParams: Promise<ParsedUrlQuery>\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling searchParams in a client Page.'\n      )\n    }\n\n    const { createSearchParamsFromClient } =\n      require('../../server/request/search-params') as typeof import('../../server/request/search-params')\n    clientSearchParams = createSearchParamsFromClient(searchParams, store)\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  } else {\n    const { createRenderSearchParamsFromClient } =\n      require('../request/search-params.browser') as typeof import('../request/search-params.browser')\n    const clientSearchParams = createRenderSearchParamsFromClient(searchParams)\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  }\n}\n"], "names": ["InvariantError", "LayoutRouterContext", "use", "urlSearchParamsToParsedUrlQuery", "SearchParamsContext", "ClientPageRoot", "Component", "serverProvidedParams", "searchParams", "params", "layoutRouterContext", "parentParams", "window", "workAsyncStorage", "require", "clientSearchParams", "clientParams", "store", "getStore", "createSearchParamsFromClient", "createParamsFromClient", "createRenderSearchParamsFromClient", "createRenderParamsFromClient"], "mappings": ";;;;;AAGA,SAASA,cAAc,QAAQ,mCAAkC;AAGjE,SAASC,mBAAmB,QAAQ,qDAAoD;AACxF,SAASC,GAAG,QAAQ,QAAO;AAC3B,SAASC,+BAA+B,QAAQ,kBAAiB;AACjE,SAASC,mBAAmB,QAAQ,uDAAsD;AAT1F;;;;;;;AAmBO,SAASC,eAAe,EAC7BC,SAAS,EACTC,oBAAoB,EAQrB;IACC,IAAIC;IACJ,IAAIC;IACJ,IAAIF,yBAAyB,MAAM;QACjCC,eAAeD,qBAAqBC,YAAY;QAChDC,SAASF,qBAAqBE,MAAM;IACtC,OAAO;QACL,2EAA2E;QAC3E,+DAA+D;QAC/D,MAAMC,0BAAsBR,saAAAA,EAAID,8cAAAA;QAChCQ,SACEC,wBAAwB,OAAOA,oBAAoBC,YAAY,GAAG,CAAC;QAErE,4EAA4E;QAC5E,iEAAiE;QACjE,4EAA4E;QAC5E,iDAAiD;QACjD,6DAA6D;QAC7DH,mBAAeL,6ZAAAA,MAAgCD,saAAAA,EAAIE,gdAAAA;IACrD;IAEA,IAAI,OAAOQ,WAAW,kBAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQJ,iBAAiBK,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,IAAIjB,sZAAAA,CACR,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEmB,4BAA4B,EAAE,GACpCL,QAAQ;QACVC,qBAAqBI,6BAA6BX,cAAcS;QAEhE,MAAM,EAAEG,sBAAsB,EAAE,GAC9BN,QAAQ;QACVE,eAAeI,uBAAuBX,QAAQQ;QAE9C,OAAA,WAAA,OAAO,wbAAA,EAACX,WAAAA;YAAUG,QAAQO;YAAcR,cAAcO;;IACxD,OAAO;;AAUT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5680, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/client/components/client-segment.tsx"], "sourcesContent": ["'use client'\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\nimport { LayoutRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { use } from 'react'\n\n/**\n * When the Page is a client component we send the params to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Segment component.\n *\n * additionally we may send a promise representing params. We don't ever use this passed\n * value but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations\n * such as when cacheComponents is enabled. It is up to the caller to decide if the promises are needed.\n */\nexport function ClientSegmentRoot({\n  Component,\n  slots,\n  serverProvidedParams,\n}: {\n  Component: React.ComponentType<any>\n  slots: { [key: string]: React.ReactNode }\n  serverProvidedParams: null | {\n    params: Params\n    promises: Array<Promise<any>> | null\n  }\n}) {\n  let params: Params\n  if (serverProvidedParams !== null) {\n    params = serverProvidedParams.params\n  } else {\n    // When Cache Components is enabled, the server does not pass the params\n    // as props; they are parsed on the client and passed via context.\n    const layoutRouterContext = use(LayoutRouterContext)\n    params =\n      layoutRouterContext !== null ? layoutRouterContext.parentParams : {}\n  }\n\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling params in a client segment such as a Layout or Template.'\n      )\n    }\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component {...slots} params={clientParams} />\n  } else {\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n    return <Component {...slots} params={clientParams} />\n  }\n}\n"], "names": ["InvariantError", "LayoutRouterContext", "use", "ClientSegmentRoot", "Component", "slots", "serverProvidedParams", "params", "layoutRouterContext", "parentParams", "window", "workAsyncStorage", "require", "clientParams", "store", "getStore", "createParamsFromClient", "createRenderParamsFromClient"], "mappings": ";;;;;AAEA,SAASA,cAAc,QAAQ,mCAAkC;AAGjE,SAASC,mBAAmB,QAAQ,qDAAoD;AACxF,SAASC,GAAG,QAAQ,QAAO;AAN3B;;;;;AAgBO,SAASC,kBAAkB,EAChCC,SAAS,EACTC,KAAK,EACLC,oBAAoB,EAQrB;IACC,IAAIC;IACJ,IAAID,yBAAyB,MAAM;QACjCC,SAASD,qBAAqBC,MAAM;IACtC,OAAO;QACL,wEAAwE;QACxE,kEAAkE;QAClE,MAAMC,0BAAsBN,saAAAA,EAAID,8cAAAA;QAChCM,SACEC,wBAAwB,OAAOA,oBAAoBC,YAAY,GAAG,CAAC;IACvE;IAEA,IAAI,OAAOC,WAAW,kBAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQH,iBAAiBI,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,IAAId,sZAAAA,CACR,uGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEgB,sBAAsB,EAAE,GAC9BJ,QAAQ;QACVC,eAAeG,uBAAuBT,QAAQO;QAE9C,OAAA,WAAA,OAAO,wbAAA,EAACV,WAAAA;YAAW,GAAGC,KAAK;YAAEE,QAAQM;;IACvC,OAAO;;AAMT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5729, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/lib/metadata/generate/icon-mark.tsx"], "sourcesContent": ["'use client'\n\n// This is a client component that only renders during SSR,\n// but will be replaced during streaming with an icon insertion script tag.\n// We don't want it to be presented anywhere so it's only visible during streaming,\n// right after the icon meta tags so that browser can pick it up as soon as it's rendered.\n// Note: we don't just emit the script here because we only need the script if it's not in the head,\n// and we need it to be hoistable alongside the other metadata but sync scripts are not hoistable.\nexport const IconMark = () => {\n  if (typeof window !== 'undefined') {\n    return null\n  }\n  return <meta name=\"«nxt-icon»\" />\n}\n"], "names": ["IconMark", "window", "meta", "name"], "mappings": ";;;;;AAAA;;AAQO,MAAMA,WAAW;IACtB,IAAI,OAAOC,WAAW,aAAa;;IAGnC,OAAA,WAAA,OAAO,wbAAA,EAACC,QAAAA;QAAKC,MAAK;;AACpB,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5747, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/dist/src/lib/framework/boundary-components.tsx"], "sourcesContent": ["'use client'\n\nimport type { ReactNode } from 'react'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n  ROOT_LAYOUT_BOUNDARY_NAME,\n} from './boundary-constants'\n\n// We use a namespace object to allow us to recover the name of the function\n// at runtime even when production bundling/minification is used.\nconst NameSpace = {\n  [METADATA_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [VIEWPORT_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [OUTLET_BOUNDARY_NAME]: function ({ children }: { children: ReactNode }) {\n    return children\n  },\n  [ROOT_LAYOUT_BOUNDARY_NAME]: function ({\n    children,\n  }: {\n    children: ReactNode\n  }) {\n    return children\n  },\n}\n\nexport const MetadataBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[METADATA_BOUNDARY_NAME.slice(0) as typeof METADATA_BOUNDARY_NAME]\n\nexport const ViewportBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[VIEWPORT_BOUNDARY_NAME.slice(0) as typeof VIEWPORT_BOUNDARY_NAME]\n\nexport const OutletBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[OUTLET_BOUNDARY_NAME.slice(0) as typeof OUTLET_BOUNDARY_NAME]\n\nexport const RootLayoutBoundary =\n  // We use slice(0) to trick the bundler into not inlining/minifying the function\n  // so it retains the name inferred from the namespace object\n  NameSpace[\n    ROOT_LAYOUT_BOUNDARY_NAME.slice(0) as typeof ROOT_LAYOUT_BOUNDARY_NAME\n  ]\n"], "names": ["METADATA_BOUNDARY_NAME", "VIEWPORT_BOUNDARY_NAME", "OUTLET_BOUNDARY_NAME", "ROOT_LAYOUT_BOUNDARY_NAME", "NameSpace", "children", "MetadataBoundary", "slice", "ViewportBoundary", "OutletBoundary", "RootLayoutBoundary"], "mappings": ";;;;;;;;;;AAGA,SACEA,sBAAsB,EACtBC,sBAAsB,EACtBC,oBAAoB,EACpBC,yBAAyB,QACpB,uBAAsB;AAR7B;;AAUA,4EAA4E;AAC5E,iEAAiE;AACjE,MAAMC,YAAY;IAChB,CAACJ,oaAAAA,CAAuB,EAAE,SAAU,EAAEK,QAAQ,EAA2B;QACvE,OAAOA;IACT;IACA,CAACJ,oaAAAA,CAAuB,EAAE,SAAU,EAAEI,QAAQ,EAA2B;QACvE,OAAOA;IACT;IACA,CAACH,kaAAAA,CAAqB,EAAE,SAAU,EAAEG,QAAQ,EAA2B;QACrE,OAAOA;IACT;IACA,CAACF,uaAAAA,CAA0B,EAAE,SAAU,EACrCE,QAAQ,EAGT;QACC,OAAOA;IACT;AACF;AAEO,MAAMC,mBAEX,AADA,4DAC4D,oBADoB;AAEhFF,SAAS,CAACJ,oaAAAA,CAAuBO,KAAK,CAAC,GAAoC,CAAA;AAEtE,MAAMC,mBACX,AACA,4DAA4D,oBADoB;AAEhFJ,SAAS,CAACH,oaAAAA,CAAuBM,KAAK,CAAC,GAAoC,CAAA;AAEtE,MAAME,iBACX,AACA,4DAA4D,oBADoB;AAEhFL,SAAS,CAACF,kaAAAA,CAAqBK,KAAK,CAAC,GAAkC,CAAA;AAElE,MAAMG,qBACX,AACA,4DAA4D,oBADoB;AAEhFN,SAAS,CACPD,uaAAAA,CAA0BI,KAAK,CAAC,GACjC,CAAA", "ignoreList": [0], "debugId": null}}]}