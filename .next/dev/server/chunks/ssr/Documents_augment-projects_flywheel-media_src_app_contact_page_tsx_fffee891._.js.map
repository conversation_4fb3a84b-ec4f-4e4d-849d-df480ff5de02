{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/app/contact/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Button } from \"@/components/ui/button\"\n\nexport default function ContactPage() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    phone: \"\",\n    skype: \"\",\n    email: \"\",\n    comment: \"\"\n  })\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    // Handle form submission here\n    console.log(\"Form submitted:\", formData)\n    // You can add form submission logic here\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-20\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header Section */}\n        <div className=\"text-center mb-16\">\n          <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Don't hesitate to reach out\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Ready to take the next step? Get in touch with us today and let's start crafting your success story together\n          </p>\n        </div>\n\n        {/* Contact Form */}\n        <div className=\"bg-white rounded-lg shadow-lg p-8 md:p-12\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Name Field */}\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  placeholder=\"Full Name\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  required\n                />\n              </div>\n\n              {/* Phone Field */}\n              <div>\n                <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Phone No.\n                </label>\n                <input\n                  type=\"tel\"\n                  id=\"phone\"\n                  name=\"phone\"\n                  value={formData.phone}\n                  onChange={handleChange}\n                  placeholder=\"Phone No.\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  required\n                />\n              </div>\n\n              {/* Skype Field */}\n              <div>\n                <label htmlFor=\"skype\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Skype\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"skype\"\n                  name=\"skype\"\n                  value={formData.skype}\n                  onChange={handleChange}\n                  placeholder=\"Skype ID/No.\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                />\n              </div>\n\n              {/* Email Field */}\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Email\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  placeholder=\"Email\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  required\n                />\n              </div>\n            </div>\n\n            {/* Comment Field */}\n            <div>\n              <label htmlFor=\"comment\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Comment\n              </label>\n              <textarea\n                id=\"comment\"\n                name=\"comment\"\n                value={formData.comment}\n                onChange={handleChange}\n                placeholder=\"Enter your comment/message...\"\n                rows={6}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-vertical\"\n                required\n              />\n            </div>\n\n            {/* Submit Button */}\n            <div className=\"text-center\">\n              <Button type=\"submit\" size=\"lg\" className=\"px-12 py-3\">\n                Send Message\n              </Button>\n            </div>\n          </form>\n        </div>\n\n        {/* Additional Contact Info */}\n        <div className=\"mt-16 text-center\">\n          <h3 className=\"text-2xl font-semibold text-gray-900 mb-8\">\n            Other Ways to Reach Us\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <div className=\"text-blue-600 text-2xl mb-4\">📧</div>\n              <h4 className=\"font-semibold text-gray-900 mb-2\">Email</h4>\n              <p className=\"text-gray-600\"><EMAIL></p>\n            </div>\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <div className=\"text-blue-600 text-2xl mb-4\">📱</div>\n              <h4 className=\"font-semibold text-gray-900 mb-2\">Phone</h4>\n              <p className=\"text-gray-600\">+****************</p>\n            </div>\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <div className=\"text-blue-600 text-2xl mb-4\">💬</div>\n              <h4 className=\"font-semibold text-gray-900 mb-2\">Live Chat</h4>\n              <p className=\"text-gray-600\">Available 24/7</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,2aAAQ,EAAC;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,mBAAmB;IAC/B,yCAAyC;IAC3C;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,wcAAC;QAAI,WAAU;kBACb,cAAA,wcAAC;YAAI,WAAU;;8BAEb,wcAAC;oBAAI,WAAU;;sCACb,wcAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,wcAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,wcAAC;oBAAI,WAAU;8BACb,cAAA,wcAAC;wBAAK,UAAU;wBAAc,WAAU;;0CACtC,wcAAC;gCAAI,WAAU;;kDAEb,wcAAC;;0DACC,wcAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA+C;;;;;;0DAG/E,wcAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU;gDACV,aAAY;gDACZ,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAKZ,wcAAC;;0DACC,wcAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAA+C;;;;;;0DAGhF,wcAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,aAAY;gDACZ,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAKZ,wcAAC;;0DACC,wcAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAA+C;;;;;;0DAGhF,wcAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAKd,wcAAC;;0DACC,wcAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAA+C;;;;;;0DAGhF,wcAAC;gDACC,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,aAAY;gDACZ,WAAU;gDACV,QAAQ;;;;;;;;;;;;;;;;;;0CAMd,wcAAC;;kDACC,wcAAC;wCAAM,SAAQ;wCAAU,WAAU;kDAA+C;;;;;;kDAGlF,wcAAC;wCACC,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,OAAO;wCACvB,UAAU;wCACV,aAAY;wCACZ,MAAM;wCACN,WAAU;wCACV,QAAQ;;;;;;;;;;;;0CAKZ,wcAAC;gCAAI,WAAU;0CACb,cAAA,wcAAC,qMAAM;oCAAC,MAAK;oCAAS,MAAK;oCAAK,WAAU;8CAAa;;;;;;;;;;;;;;;;;;;;;;8BAQ7D,wcAAC;oBAAI,WAAU;;sCACb,wcAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,wcAAC;4BAAI,WAAU;;8CACb,wcAAC;oCAAI,WAAU;;sDACb,wcAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,wcAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,wcAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,wcAAC;oCAAI,WAAU;;sDACb,wcAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,wcAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,wcAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,wcAAC;oCAAI,WAAU;;sDACb,wcAAC;4CAAI,WAAU;sDAA8B;;;;;;sDAC7C,wcAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,wcAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C", "debugId": null}}]}