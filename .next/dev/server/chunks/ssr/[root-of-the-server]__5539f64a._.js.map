{"version": 3, "sources": [], "sections": [{"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,6RAAO,EAAC,IAAA,uPAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?:\n    | 'default'\n    | 'destructive'\n    | 'outline'\n    | 'secondary'\n    | 'ghost'\n    | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    const baseClasses =\n      'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50';\n\n    const variants = {\n      default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n      destructive:\n        'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n      outline:\n        'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n      ghost: 'hover:bg-accent hover:text-accent-foreground',\n      link: 'text-primary underline-offset-4 hover:underline',\n    };\n\n    const sizes = {\n      default: 'h-10 px-4 py-2',\n      sm: 'h-9 rounded-md px-3',\n      lg: 'h-11 rounded-md px-8',\n      icon: 'h-10 w-10',\n    };\n\n    return (\n      <button\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAeA,MAAM,uBAAS,6aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,MAAM,cACJ;IAEF,MAAM,WAAW;QACf,SAAS;QACT,aACE;QACF,SACE;QACF,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,wcAAC;QACC,WAAW,IAAA,kLAAE,EAAC,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/app/about/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Button } from '@/components/ui/button';\n\nexport default function AboutPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              A little about us\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Unleashing the power of Affiliate Marketing tactics\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Process Section */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-12\">\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-2xl font-bold text-blue-600\">01</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                Tailored Affiliate Strategies\n              </h3>\n              <p className=\"text-gray-600\">\n                Optimizing your website for each of the main components to put a\n                good and fit in house strategy in place.\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-2xl font-bold text-blue-600\">02</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                Building your site\n              </h3>\n              <p className=\"text-gray-600\">\n                Optimizing your website for each of the main components to put a\n                good and fit in house strategy in place.\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-2xl font-bold text-blue-600\">03</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                Promoting your site\n              </h3>\n              <p className=\"text-gray-600\">\n                Optimizing your website for each of the main components to put a\n                good and fit in house strategy in place.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Mission Statement */}\n      <section className=\"bg-gray-50 py-20\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-8\">\n            Our Mission\n          </h2>\n          <p className=\"text-xl text-gray-600 mb-8\">\n            It's time to shift the focus back to the creators. Let's work\n            together to ensure that quality content is recognized and rewarded.\n            With flywheel-media, you can transform your passion into profit\n            while making a positive impact.\n          </p>\n          <Link href=\"/contact\">\n            <Button size=\"lg\">Get Started Today</Button>\n          </Link>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">2k</div>\n              <div className=\"text-gray-600\">Project Done</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">2k+</div>\n              <div className=\"text-gray-600\">Satisfied Clients</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">48K</div>\n              <div className=\"text-gray-600\">Supported locations</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">2024</div>\n              <div className=\"text-gray-600\">Year founded</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-blue-600 py-20\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-6\">\n            It's time to tell the world about it\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8\">\n            It's time to tell the world about what you do. Let's work together\n            to share your story in a way that captivates and inspires.\n          </p>\n          <Link href=\"/contact\">\n            <Button variant=\"secondary\" size=\"lg\">\n              Start Your Journey\n            </Button>\n          </Link>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,wcAAC;QAAI,WAAU;;0BAEb,wcAAC;gBAAQ,WAAU;0BACjB,cAAA,wcAAC;oBAAI,WAAU;8BACb,cAAA,wcAAC;wBAAI,WAAU;;0CACb,wcAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,wcAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;0BAQ7D,wcAAC;gBAAQ,WAAU;0BACjB,cAAA,wcAAC;oBAAI,WAAU;8BACb,cAAA,wcAAC;wBAAI,WAAU;;0CACb,wcAAC;gCAAI,WAAU;;kDACb,wcAAC;wCAAI,WAAU;kDACb,cAAA,wcAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;kDAErD,wcAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,wcAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAM/B,wcAAC;gCAAI,WAAU;;kDACb,wcAAC;wCAAI,WAAU;kDACb,cAAA,wcAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;kDAErD,wcAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,wcAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAM/B,wcAAC;gCAAI,WAAU;;kDACb,wcAAC;wCAAI,WAAU;kDACb,cAAA,wcAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;kDAErD,wcAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAGzD,wcAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUrC,wcAAC;gBAAQ,WAAU;0BACjB,cAAA,wcAAC;oBAAI,WAAU;;sCACb,wcAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,wcAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAM1C,wcAAC,oZAAI;4BAAC,MAAK;sCACT,cAAA,wcAAC,qMAAM;gCAAC,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;;;0BAMxB,wcAAC;gBAAQ,WAAU;0BACjB,cAAA,wcAAC;oBAAI,WAAU;8BACb,cAAA,wcAAC;wBAAI,WAAU;;0CACb,wcAAC;;kDACC,wcAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,wcAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,wcAAC;;kDACC,wcAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,wcAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,wcAAC;;kDACC,wcAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,wcAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,wcAAC;;kDACC,wcAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,wcAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,wcAAC;gBAAQ,WAAU;0BACjB,cAAA,wcAAC;oBAAI,WAAU;;sCACb,wcAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,wcAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,wcAAC,oZAAI;4BAAC,MAAK;sCACT,cAAA,wcAAC,qMAAM;gCAAC,SAAQ;gCAAY,MAAK;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD", "debugId": null}}]}