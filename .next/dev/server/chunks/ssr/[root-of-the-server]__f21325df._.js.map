{"version": 3, "sources": [], "sections": [{"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,6RAAO,EAAC,IAAA,uPAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?:\n    | 'default'\n    | 'destructive'\n    | 'outline'\n    | 'secondary'\n    | 'ghost'\n    | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    const baseClasses =\n      'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50';\n\n    const variants = {\n      default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n      destructive:\n        'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n      outline:\n        'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n      ghost: 'hover:bg-accent hover:text-accent-foreground',\n      link: 'text-primary underline-offset-4 hover:underline',\n    };\n\n    const sizes = {\n      default: 'h-10 px-4 py-2',\n      sm: 'h-9 rounded-md px-3',\n      lg: 'h-11 rounded-md px-8',\n      icon: 'h-10 w-10',\n    };\n\n    return (\n      <button\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAeA,MAAM,uBAAS,6aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,MAAM,cACJ;IAEF,MAAM,WAAW;QACf,SAAS;QACT,aACE;QACF,SACE;QACF,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,wcAAC;QACC,WAAW,IAAA,kLAAE,EAAC,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/components/sections/hero.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Button } from '@/components/ui/button';\n\nexport function Hero() {\n  return (\n    <section className=\"relative bg-gradient-to-br from-blue-50 to-indigo-100 py-20 lg:py-32\">\n      {/* Background decorative elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-32 w-80 h-80 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-32 w-80 h-80 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n            Driving Growth with Smart{' '}\n            <span className=\"text-blue-600\">Affiliate Marketing</span>\n          </h1>\n          <p className=\"text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n            Join us in forging powerful connections between brands and customers\n            through strategic affiliate marketing solutions.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/contact\">\n              <Button size=\"lg\">Get Started Now</Button>\n            </Link>\n            <Link href=\"/services\">\n              <Button variant=\"outline\" size=\"lg\">\n                Learn More\n              </Button>\n            </Link>\n          </div>\n        </div>\n\n        {/* Trust indicators */}\n        <div className=\"mt-16\">\n          <p className=\"text-center text-gray-500 mb-8\">\n            Trusted By Top Clients\n          </p>\n          <div className=\"flex flex-wrap justify-center items-center gap-8 opacity-60\">\n            {/* Placeholder for client logos */}\n            <div className=\"bg-gray-200 h-12 w-32 rounded flex items-center justify-center\">\n              <span className=\"text-gray-500 text-sm\">Client Logo</span>\n            </div>\n            <div className=\"bg-gray-200 h-12 w-32 rounded flex items-center justify-center\">\n              <span className=\"text-gray-500 text-sm\">Client Logo</span>\n            </div>\n            <div className=\"bg-gray-200 h-12 w-32 rounded flex items-center justify-center\">\n              <span className=\"text-gray-500 text-sm\">Client Logo</span>\n            </div>\n            <div className=\"bg-gray-200 h-12 w-32 rounded flex items-center justify-center\">\n              <span className=\"text-gray-500 text-sm\">Client Logo</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8\">\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-blue-600\">2k</div>\n            <div className=\"text-gray-600\">Projects Done</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-blue-600\">2k+</div>\n            <div className=\"text-gray-600\">Satisfied Clients</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-blue-600\">48K</div>\n            <div className=\"text-gray-600\">Supported Locations</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-blue-600\">2024</div>\n            <div className=\"text-gray-600\">Year Founded</div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,wcAAC;QAAQ,WAAU;;0BAEjB,wcAAC;gBAAI,WAAU;;kCACb,wcAAC;wBAAI,WAAU;;;;;;kCACf,wcAAC;wBAAI,WAAU;;;;;;kCACf,wcAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,wcAAC;gBAAI,WAAU;;kCACb,wcAAC;wBAAI,WAAU;;0CACb,wcAAC;gCAAG,WAAU;;oCAAoD;oCACtC;kDAC1B,wcAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,wcAAC;gCAAE,WAAU;0CAA2D;;;;;;0CAIxE,wcAAC;gCAAI,WAAU;;kDACb,wcAAC,oZAAI;wCAAC,MAAK;kDACT,cAAA,wcAAC,qMAAM;4CAAC,MAAK;sDAAK;;;;;;;;;;;kDAEpB,wcAAC,oZAAI;wCAAC,MAAK;kDACT,cAAA,wcAAC,qMAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAQ1C,wcAAC;wBAAI,WAAU;;0CACb,wcAAC;gCAAE,WAAU;0CAAiC;;;;;;0CAG9C,wcAAC;gCAAI,WAAU;;kDAEb,wcAAC;wCAAI,WAAU;kDACb,cAAA,wcAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;kDAE1C,wcAAC;wCAAI,WAAU;kDACb,cAAA,wcAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;kDAE1C,wcAAC;wCAAI,WAAU;kDACb,cAAA,wcAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;kDAE1C,wcAAC;wCAAI,WAAU;kDACb,cAAA,wcAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAM9C,wcAAC;wBAAI,WAAU;;0CACb,wcAAC;gCAAI,WAAU;;kDACb,wcAAC;wCAAI,WAAU;kDAAmC;;;;;;kDAClD,wcAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,wcAAC;gCAAI,WAAU;;kDACb,wcAAC;wCAAI,WAAU;kDAAmC;;;;;;kDAClD,wcAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,wcAAC;gCAAI,WAAU;;kDACb,wcAAC;wCAAI,WAAU;kDAAmC;;;;;;kDAClD,wcAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,wcAAC;gCAAI,WAAU;;kDACb,wcAAC;wCAAI,WAAU;kDAAmC;;;;;;kDAClD,wcAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/components/sections/services.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Button } from '@/components/ui/button';\n\nconst services = [\n  {\n    icon: '🎯',\n    title: 'Tailored Affiliate Strategies',\n    description:\n      \"We understand that one size doesn't fit all. Our team of experts crafts bespoke performance marketing strategies that align with your brand's unique goals and market dynamics, ensuring a personalized approach for success to your business.\",\n    features: [\n      'Creating Targeted Affiliate Content',\n      'Choosing the Right Affiliate Programs',\n      'Proven Techniques for Effective Affiliate Marketing',\n    ],\n  },\n  {\n    icon: '📊',\n    title: 'Advanced Analytics and Reporting',\n    description:\n      'Transparency and insight are central to our operations. Our advanced analytics platform offers real-time data and detailed reports, which helps in making it easy to track campaign performance and ROI for informed decision-making',\n    features: [\n      'Data-Driven Insights for Strategic Growth',\n      'Mastering Data-Driven Decision Making',\n      'Leveraging Analytics for Continuous Improvement',\n    ],\n  },\n  {\n    icon: '🌐',\n    title: 'Global Network',\n    description:\n      'Leverage our extensive global network to reach audiences beyond borders. With partners spanning multiple industries and regions, your brand gains unparalleled exposure and the ability to tap into new, lucrative markets.',\n    features: [\n      'Leverage a Global Network for Success',\n      'Partnerships for Enhanced Growth',\n      'Maximizing Opportunities Through Network Leverage',\n    ],\n  },\n  {\n    icon: '🚀',\n    title: 'Innovative Technology Solutions',\n    description:\n      'Stay ahead of the curve with our cutting-edge adtech solutions. We equip your campaigns with the tools they need to succeed in a competitive digital landscape.',\n  },\n  {\n    icon: '🔍',\n    title: 'Transparency & Accountability',\n    description:\n      \"You'll always be in the loop with comprehensive reports on your program's performance. We maintain full transparency so you can see exactly how your campaigns are performing.\",\n  },\n  {\n    icon: '👥',\n    title: 'Dedicated Support Team',\n    description:\n      'Success is a team effort. Our experienced dedicated account managers work closely with you to ensure your campaigns are running smoothly and optimally.',\n  },\n];\n\nexport function Services() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-sm font-semibold text-blue-600 uppercase tracking-wide mb-2\">\n            Our main services\n          </h2>\n          <h3 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Our solutions that help you grow up\n          </h3>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {services.map((service, index) => (\n            <div\n              key={index}\n              className=\"bg-white rounded-lg border border-gray-200 p-8 hover:shadow-lg transition-shadow duration-300\"\n            >\n              <div className=\"text-4xl mb-4\">{service.icon}</div>\n              <h4 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                {service.title}\n              </h4>\n              <p className=\"text-gray-600 mb-6\">{service.description}</p>\n\n              {service.features && (\n                <ul className=\"space-y-2 mb-6\">\n                  {service.features.map((feature, featureIndex) => (\n                    <li\n                      key={featureIndex}\n                      className=\"text-sm text-gray-500 flex items-start\"\n                    >\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n              )}\n\n              <Link href=\"/contact\" className=\"w-full\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  Get Started\n                </Button>\n              </Link>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEA,MAAM,WAAW;IACf;QACE,MAAM;QACN,OAAO;QACP,aACE;QACF,UAAU;YACR;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aACE;QACF,UAAU;YACR;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aACE;QACF,UAAU;YACR;YACA;YACA;SACD;IACH;IACA;QACE,MAAM;QACN,OAAO;QACP,aACE;IACJ;IACA;QACE,MAAM;QACN,OAAO;QACP,aACE;IACJ;IACA;QACE,MAAM;QACN,OAAO;QACP,aACE;IACJ;CACD;AAEM,SAAS;IACd,qBACE,wcAAC;QAAQ,WAAU;kBACjB,cAAA,wcAAC;YAAI,WAAU;;8BAEb,wcAAC;oBAAI,WAAU;;sCACb,wcAAC;4BAAG,WAAU;sCAAmE;;;;;;sCAGjF,wcAAC;4BAAG,WAAU;sCAAoD;;;;;;;;;;;;8BAMpE,wcAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,wcAAC;4BAEC,WAAU;;8CAEV,wcAAC;oCAAI,WAAU;8CAAiB,QAAQ,IAAI;;;;;;8CAC5C,wcAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,wcAAC;oCAAE,WAAU;8CAAsB,QAAQ,WAAW;;;;;;gCAErD,QAAQ,QAAQ,kBACf,wcAAC;oCAAG,WAAU;8CACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC9B,wcAAC;4CAEC,WAAU;;8DAEV,wcAAC;oDAAK,WAAU;8DAAqB;;;;;;gDACpC;;2CAJI;;;;;;;;;;8CAUb,wcAAC,oZAAI;oCAAC,MAAK;oCAAW,WAAU;8CAC9B,cAAA,wcAAC,qMAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAAS;;;;;;;;;;;;2BAxB1C;;;;;;;;;;;;;;;;;;;;;AAkCnB", "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/app/page.tsx"], "sourcesContent": ["import { Hero } from '@/components/sections/hero';\nimport { Services } from '@/components/sections/services';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <Hero />\n      <Services />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,wcAAC;QAAI,WAAU;;0BACb,wcAAC,uMAAI;;;;;0BACL,wcAAC,+MAAQ;;;;;;;;;;;AAGf", "debugId": null}}]}