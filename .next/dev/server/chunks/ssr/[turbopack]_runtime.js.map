{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared/runtime-utils.ts"], "sourcesContent": ["/**\n * This file contains runtime types and functions that are shared between all\n * TurboPack ECMAScript runtimes.\n *\n * It will be prepended to the runtime code of each runtime.\n */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"./runtime-types.d.ts\" />\n\ntype EsmNamespaceObject = Record<string, any>\n\n// @ts-ignore Defined in `dev-base.ts`\ndeclare function getOrInstantiateModuleFromParent<M>(\n  id: ModuleId,\n  sourceModule: M\n): M\n\nconst REEXPORTED_OBJECTS = new WeakMap<Module, ReexportedObjects>()\n\n/**\n * Constructs the `__turbopack_context__` object for a module.\n */\nfunction Context(\n  this: TurbopackBaseContext<Module>,\n  module: Module,\n  exports: Exports\n) {\n  this.m = module\n  // We need to store this here instead of accessing it from the module object to:\n  // 1. Make it available to factories directly, since we rewrite `this` to\n  //    `__turbopack_context__.e` in CJS modules.\n  // 2. Support async modules which rewrite `module.exports` to a promise, so we\n  //    can still access the original exports object from functions like\n  //    `esmExport`\n  // Ideally we could find a new approach for async modules and drop this property altogether.\n  this.e = exports\n}\nconst contextPrototype = Context.prototype as TurbopackBaseContext<Module>\n\ntype ModuleContextMap = Record<ModuleId, ModuleContextEntry>\n\ninterface ModuleContextEntry {\n  id: () => ModuleId\n  module: () => any\n}\n\ninterface ModuleContext {\n  // require call\n  (moduleId: ModuleId): Exports | EsmNamespaceObject\n\n  // async import call\n  import(moduleId: ModuleId): Promise<Exports | EsmNamespaceObject>\n\n  keys(): ModuleId[]\n\n  resolve(moduleId: ModuleId): ModuleId\n}\n\ntype GetOrInstantiateModuleFromParent<M extends Module> = (\n  moduleId: M['id'],\n  parentModule: M\n) => M\n\ndeclare function getOrInstantiateRuntimeModule(\n  chunkPath: ChunkPath,\n  moduleId: ModuleId\n): Module\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty\nconst toStringTag = typeof Symbol !== 'undefined' && Symbol.toStringTag\n\nfunction defineProp(\n  obj: any,\n  name: PropertyKey,\n  options: PropertyDescriptor & ThisType<any>\n) {\n  if (!hasOwnProperty.call(obj, name)) Object.defineProperty(obj, name, options)\n}\n\nfunction getOverwrittenModule(\n  moduleCache: ModuleCache<Module>,\n  id: ModuleId\n): Module {\n  let module = moduleCache[id]\n  if (!module) {\n    // This is invoked when a module is merged into another module, thus it wasn't invoked via\n    // instantiateModule and the cache entry wasn't created yet.\n    module = createModuleObject(id)\n    moduleCache[id] = module\n  }\n  return module\n}\n\n/**\n * Creates the module object. Only done here to ensure all module objects have the same shape.\n */\nfunction createModuleObject(id: ModuleId): Module {\n  return {\n    exports: {},\n    error: undefined,\n    id,\n    namespaceObject: undefined,\n  }\n}\n\ntype BindingTag = 0\nconst BindingTag_Value = 0 as BindingTag\n\n// an arbitrary sequence of bindings as\n// - a prop name\n// - BindingTag_Value, a value to be bound directly, or\n// - 1 or 2 functions to bind as getters and sdetters\ntype EsmBindings = Array<\n  string | BindingTag | (() => unknown) | ((v: unknown) => void) | unknown\n>\n\n/**\n * Adds the getters to the exports object.\n */\nfunction esm(exports: Exports, bindings: EsmBindings) {\n  defineProp(exports, '__esModule', { value: true })\n  if (toStringTag) defineProp(exports, toStringTag, { value: 'Module' })\n  let i = 0\n  while (i < bindings.length) {\n    const propName = bindings[i++] as string\n    const tagOrFunction = bindings[i++]\n    if (typeof tagOrFunction === 'number') {\n      if (tagOrFunction === BindingTag_Value) {\n        defineProp(exports, propName, {\n          value: bindings[i++],\n          enumerable: true,\n          writable: false,\n        })\n      } else {\n        throw new Error(`unexpected tag: ${tagOrFunction}`)\n      }\n    } else {\n      const getterFn = tagOrFunction as () => unknown\n      if (typeof bindings[i] === 'function') {\n        const setterFn = bindings[i++] as (v: unknown) => void\n        defineProp(exports, propName, {\n          get: getterFn,\n          set: setterFn,\n          enumerable: true,\n        })\n      } else {\n        defineProp(exports, propName, {\n          get: getterFn,\n          enumerable: true,\n        })\n      }\n    }\n  }\n  Object.seal(exports)\n}\n\n/**\n * Makes the module an ESM with exports\n */\nfunction esmExport(\n  this: TurbopackBaseContext<Module>,\n  bindings: EsmBindings,\n  id: ModuleId | undefined\n) {\n  let module: Module\n  let exports: Module['exports']\n  if (id != null) {\n    module = getOverwrittenModule(this.c, id)\n    exports = module.exports\n  } else {\n    module = this.m\n    exports = this.e\n  }\n  module.namespaceObject = exports\n  esm(exports, bindings)\n}\ncontextPrototype.s = esmExport\n\ntype ReexportedObjects = Record<PropertyKey, unknown>[]\nfunction ensureDynamicExports(\n  module: Module,\n  exports: Exports\n): ReexportedObjects {\n  let reexportedObjects: ReexportedObjects | undefined =\n    REEXPORTED_OBJECTS.get(module)\n\n  if (!reexportedObjects) {\n    REEXPORTED_OBJECTS.set(module, (reexportedObjects = []))\n    module.exports = module.namespaceObject = new Proxy(exports, {\n      get(target, prop) {\n        if (\n          hasOwnProperty.call(target, prop) ||\n          prop === 'default' ||\n          prop === '__esModule'\n        ) {\n          return Reflect.get(target, prop)\n        }\n        for (const obj of reexportedObjects!) {\n          const value = Reflect.get(obj, prop)\n          if (value !== undefined) return value\n        }\n        return undefined\n      },\n      ownKeys(target) {\n        const keys = Reflect.ownKeys(target)\n        for (const obj of reexportedObjects!) {\n          for (const key of Reflect.ownKeys(obj)) {\n            if (key !== 'default' && !keys.includes(key)) keys.push(key)\n          }\n        }\n        return keys\n      },\n    })\n  }\n  return reexportedObjects\n}\n\n/**\n * Dynamically exports properties from an object\n */\nfunction dynamicExport(\n  this: TurbopackBaseContext<Module>,\n  object: Record<string, any>,\n  id: ModuleId | undefined\n) {\n  let module: Module\n  let exports: Exports\n  if (id != null) {\n    module = getOverwrittenModule(this.c, id)\n    exports = module.exports\n  } else {\n    module = this.m\n    exports = this.e\n  }\n  const reexportedObjects = ensureDynamicExports(module, exports)\n\n  if (typeof object === 'object' && object !== null) {\n    reexportedObjects.push(object)\n  }\n}\ncontextPrototype.j = dynamicExport\n\nfunction exportValue(\n  this: TurbopackBaseContext<Module>,\n  value: any,\n  id: ModuleId | undefined\n) {\n  let module: Module\n  if (id != null) {\n    module = getOverwrittenModule(this.c, id)\n  } else {\n    module = this.m\n  }\n  module.exports = value\n}\ncontextPrototype.v = exportValue\n\nfunction exportNamespace(\n  this: TurbopackBaseContext<Module>,\n  namespace: any,\n  id: ModuleId | undefined\n) {\n  let module: Module\n  if (id != null) {\n    module = getOverwrittenModule(this.c, id)\n  } else {\n    module = this.m\n  }\n  module.exports = module.namespaceObject = namespace\n}\ncontextPrototype.n = exportNamespace\n\nfunction createGetter(obj: Record<string | symbol, any>, key: string | symbol) {\n  return () => obj[key]\n}\n\n/**\n * @returns prototype of the object\n */\nconst getProto: (obj: any) => any = Object.getPrototypeOf\n  ? (obj) => Object.getPrototypeOf(obj)\n  : (obj) => obj.__proto__\n\n/** Prototypes that are not expanded for exports */\nconst LEAF_PROTOTYPES = [null, getProto({}), getProto([]), getProto(getProto)]\n\n/**\n * @param raw\n * @param ns\n * @param allowExportDefault\n *   * `false`: will have the raw module as default export\n *   * `true`: will have the default property as default export\n */\nfunction interopEsm(\n  raw: Exports,\n  ns: EsmNamespaceObject,\n  allowExportDefault?: boolean\n) {\n  const bindings: EsmBindings = []\n  let defaultLocation = -1\n  for (\n    let current = raw;\n    (typeof current === 'object' || typeof current === 'function') &&\n    !LEAF_PROTOTYPES.includes(current);\n    current = getProto(current)\n  ) {\n    for (const key of Object.getOwnPropertyNames(current)) {\n      bindings.push(key, createGetter(raw, key))\n      if (defaultLocation === -1 && key === 'default') {\n        defaultLocation = bindings.length - 1\n      }\n    }\n  }\n\n  // this is not really correct\n  // we should set the `default` getter if the imported module is a `.cjs file`\n  if (!(allowExportDefault && defaultLocation >= 0)) {\n    // Replace the binding with one for the namespace itself in order to preserve iteration order.\n    if (defaultLocation >= 0) {\n      // Replace the getter with the value\n      bindings.splice(defaultLocation, 1, BindingTag_Value, raw)\n    } else {\n      bindings.push('default', BindingTag_Value, raw)\n    }\n  }\n\n  esm(ns, bindings)\n  return ns\n}\n\nfunction createNS(raw: Module['exports']): EsmNamespaceObject {\n  if (typeof raw === 'function') {\n    return function (this: any, ...args: any[]) {\n      return raw.apply(this, args)\n    }\n  } else {\n    return Object.create(null)\n  }\n}\n\nfunction esmImport(\n  this: TurbopackBaseContext<Module>,\n  id: ModuleId\n): Exclude<Module['namespaceObject'], undefined> {\n  const module = getOrInstantiateModuleFromParent(id, this.m)\n\n  // any ES module has to have `module.namespaceObject` defined.\n  if (module.namespaceObject) return module.namespaceObject\n\n  // only ESM can be an async module, so we don't need to worry about exports being a promise here.\n  const raw = module.exports\n  return (module.namespaceObject = interopEsm(\n    raw,\n    createNS(raw),\n    raw && (raw as any).__esModule\n  ))\n}\ncontextPrototype.i = esmImport\n\nfunction asyncLoader(\n  this: TurbopackBaseContext<Module>,\n  moduleId: ModuleId\n): Promise<Exports> {\n  const loader = this.r(moduleId) as (\n    importFunction: EsmImport\n  ) => Promise<Exports>\n  return loader(esmImport.bind(this))\n}\ncontextPrototype.A = asyncLoader\n\n// Add a simple runtime require so that environments without one can still pass\n// `typeof require` CommonJS checks so that exports are correctly registered.\nconst runtimeRequire =\n  // @ts-ignore\n  typeof require === 'function'\n    ? // @ts-ignore\n      require\n    : function require() {\n        throw new Error('Unexpected use of runtime require')\n      }\ncontextPrototype.t = runtimeRequire\n\nfunction commonJsRequire(\n  this: TurbopackBaseContext<Module>,\n  id: ModuleId\n): Exports {\n  return getOrInstantiateModuleFromParent(id, this.m).exports\n}\ncontextPrototype.r = commonJsRequire\n\n/**\n * `require.context` and require/import expression runtime.\n */\nfunction moduleContext(map: ModuleContextMap): ModuleContext {\n  function moduleContext(id: ModuleId): Exports {\n    if (hasOwnProperty.call(map, id)) {\n      return map[id].module()\n    }\n\n    const e = new Error(`Cannot find module '${id}'`)\n    ;(e as any).code = 'MODULE_NOT_FOUND'\n    throw e\n  }\n\n  moduleContext.keys = (): ModuleId[] => {\n    return Object.keys(map)\n  }\n\n  moduleContext.resolve = (id: ModuleId): ModuleId => {\n    if (hasOwnProperty.call(map, id)) {\n      return map[id].id()\n    }\n\n    const e = new Error(`Cannot find module '${id}'`)\n    ;(e as any).code = 'MODULE_NOT_FOUND'\n    throw e\n  }\n\n  moduleContext.import = async (id: ModuleId) => {\n    return await (moduleContext(id) as Promise<Exports>)\n  }\n\n  return moduleContext\n}\ncontextPrototype.f = moduleContext\n\n/**\n * Returns the path of a chunk defined by its data.\n */\nfunction getChunkPath(chunkData: ChunkData): ChunkPath {\n  return typeof chunkData === 'string' ? chunkData : chunkData.path\n}\n\nfunction isPromise<T = any>(maybePromise: any): maybePromise is Promise<T> {\n  return (\n    maybePromise != null &&\n    typeof maybePromise === 'object' &&\n    'then' in maybePromise &&\n    typeof maybePromise.then === 'function'\n  )\n}\n\nfunction isAsyncModuleExt<T extends {}>(obj: T): obj is AsyncModuleExt & T {\n  return turbopackQueues in obj\n}\n\nfunction createPromise<T>() {\n  let resolve: (value: T | PromiseLike<T>) => void\n  let reject: (reason?: any) => void\n\n  const promise = new Promise<T>((res, rej) => {\n    reject = rej\n    resolve = res\n  })\n\n  return {\n    promise,\n    resolve: resolve!,\n    reject: reject!,\n  }\n}\n\n// Load the CompressedmoduleFactories of a chunk into the `moduleFactories` Map.\n// The CompressedModuleFactories format is\n// - 1 or more module ids\n// - a module factory function\n// So walking this is a little complex but the flat structure is also fast to\n// traverse, we can use `typeof` operators to distinguish the two cases.\nfunction installCompressedModuleFactories(\n  chunkModules: CompressedModuleFactories,\n  offset: number,\n  moduleFactories: ModuleFactories,\n  newModuleId?: (id: ModuleId) => void\n) {\n  let i = offset\n  while (i < chunkModules.length) {\n    let moduleId = chunkModules[i] as ModuleId\n    let end = i + 1\n    // Find our factory function\n    while (\n      end < chunkModules.length &&\n      typeof chunkModules[end] !== 'function'\n    ) {\n      end++\n    }\n    if (end === chunkModules.length) {\n      throw new Error('malformed chunk format, expected a factory function')\n    }\n    // Each chunk item has a 'primary id' and optional additional ids. If the primary id is already\n    // present we know all the additional ids are also present, so we don't need to check.\n    if (!moduleFactories.has(moduleId)) {\n      const moduleFactoryFn = chunkModules[end] as Function\n      applyModuleFactoryName(moduleFactoryFn)\n      newModuleId?.(moduleId)\n      for (; i < end; i++) {\n        moduleId = chunkModules[i] as ModuleId\n        moduleFactories.set(moduleId, moduleFactoryFn)\n      }\n    }\n    i = end + 1 // end is pointing at the last factory advance to the next id or the end of the array.\n  }\n}\n\n// everything below is adapted from webpack\n// https://github.com/webpack/webpack/blob/6be4065ade1e252c1d8dcba4af0f43e32af1bdc1/lib/runtime/AsyncModuleRuntimeModule.js#L13\n\nconst turbopackQueues = Symbol('turbopack queues')\nconst turbopackExports = Symbol('turbopack exports')\nconst turbopackError = Symbol('turbopack error')\n\nconst enum QueueStatus {\n  Unknown = -1,\n  Unresolved = 0,\n  Resolved = 1,\n}\n\ntype AsyncQueueFn = (() => void) & { queueCount: number }\ntype AsyncQueue = AsyncQueueFn[] & {\n  status: QueueStatus\n}\n\nfunction resolveQueue(queue?: AsyncQueue) {\n  if (queue && queue.status !== QueueStatus.Resolved) {\n    queue.status = QueueStatus.Resolved\n    queue.forEach((fn) => fn.queueCount--)\n    queue.forEach((fn) => (fn.queueCount-- ? fn.queueCount++ : fn()))\n  }\n}\n\ntype Dep = Exports | AsyncModulePromise | Promise<Exports>\n\ntype AsyncModuleExt = {\n  [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => void\n  [turbopackExports]: Exports\n  [turbopackError]?: any\n}\n\ntype AsyncModulePromise<T = Exports> = Promise<T> & AsyncModuleExt\n\nfunction wrapDeps(deps: Dep[]): AsyncModuleExt[] {\n  return deps.map((dep): AsyncModuleExt => {\n    if (dep !== null && typeof dep === 'object') {\n      if (isAsyncModuleExt(dep)) return dep\n      if (isPromise(dep)) {\n        const queue: AsyncQueue = Object.assign([], {\n          status: QueueStatus.Unresolved,\n        })\n\n        const obj: AsyncModuleExt = {\n          [turbopackExports]: {},\n          [turbopackQueues]: (fn: (queue: AsyncQueue) => void) => fn(queue),\n        }\n\n        dep.then(\n          (res) => {\n            obj[turbopackExports] = res\n            resolveQueue(queue)\n          },\n          (err) => {\n            obj[turbopackError] = err\n            resolveQueue(queue)\n          }\n        )\n\n        return obj\n      }\n    }\n\n    return {\n      [turbopackExports]: dep,\n      [turbopackQueues]: () => {},\n    }\n  })\n}\n\nfunction asyncModule(\n  this: TurbopackBaseContext<Module>,\n  body: (\n    handleAsyncDependencies: (\n      deps: Dep[]\n    ) => Exports[] | Promise<() => Exports[]>,\n    asyncResult: (err?: any) => void\n  ) => void,\n  hasAwait: boolean\n) {\n  const module = this.m\n  const queue: AsyncQueue | undefined = hasAwait\n    ? Object.assign([], { status: QueueStatus.Unknown })\n    : undefined\n\n  const depQueues: Set<AsyncQueue> = new Set()\n\n  const { resolve, reject, promise: rawPromise } = createPromise<Exports>()\n\n  const promise: AsyncModulePromise = Object.assign(rawPromise, {\n    [turbopackExports]: module.exports,\n    [turbopackQueues]: (fn) => {\n      queue && fn(queue)\n      depQueues.forEach(fn)\n      promise['catch'](() => {})\n    },\n  } satisfies AsyncModuleExt)\n\n  const attributes: PropertyDescriptor = {\n    get(): any {\n      return promise\n    },\n    set(v: any) {\n      // Calling `esmExport` leads to this.\n      if (v !== promise) {\n        promise[turbopackExports] = v\n      }\n    },\n  }\n\n  Object.defineProperty(module, 'exports', attributes)\n  Object.defineProperty(module, 'namespaceObject', attributes)\n\n  function handleAsyncDependencies(deps: Dep[]) {\n    const currentDeps = wrapDeps(deps)\n\n    const getResult = () =>\n      currentDeps.map((d) => {\n        if (d[turbopackError]) throw d[turbopackError]\n        return d[turbopackExports]\n      })\n\n    const { promise, resolve } = createPromise<() => Exports[]>()\n\n    const fn: AsyncQueueFn = Object.assign(() => resolve(getResult), {\n      queueCount: 0,\n    })\n\n    function fnQueue(q: AsyncQueue) {\n      if (q !== queue && !depQueues.has(q)) {\n        depQueues.add(q)\n        if (q && q.status === QueueStatus.Unresolved) {\n          fn.queueCount++\n          q.push(fn)\n        }\n      }\n    }\n\n    currentDeps.map((dep) => dep[turbopackQueues](fnQueue))\n\n    return fn.queueCount ? promise : getResult()\n  }\n\n  function asyncResult(err?: any) {\n    if (err) {\n      reject((promise[turbopackError] = err))\n    } else {\n      resolve(promise[turbopackExports])\n    }\n\n    resolveQueue(queue)\n  }\n\n  body(handleAsyncDependencies, asyncResult)\n\n  if (queue && queue.status === QueueStatus.Unknown) {\n    queue.status = QueueStatus.Unresolved\n  }\n}\ncontextPrototype.a = asyncModule\n\n/**\n * A pseudo \"fake\" URL object to resolve to its relative path.\n *\n * When UrlRewriteBehavior is set to relative, calls to the `new URL()` will construct url without base using this\n * runtime function to generate context-agnostic urls between different rendering context, i.e ssr / client to avoid\n * hydration mismatch.\n *\n * This is based on webpack's existing implementation:\n * https://github.com/webpack/webpack/blob/87660921808566ef3b8796f8df61bd79fc026108/lib/runtime/RelativeUrlRuntimeModule.js\n */\nconst relativeURL = function relativeURL(this: any, inputUrl: string) {\n  const realUrl = new URL(inputUrl, 'x:/')\n  const values: Record<string, any> = {}\n  for (const key in realUrl) values[key] = (realUrl as any)[key]\n  values.href = inputUrl\n  values.pathname = inputUrl.replace(/[?#].*/, '')\n  values.origin = values.protocol = ''\n  values.toString = values.toJSON = (..._args: Array<any>) => inputUrl\n  for (const key in values)\n    Object.defineProperty(this, key, {\n      enumerable: true,\n      configurable: true,\n      value: values[key],\n    })\n}\nrelativeURL.prototype = URL.prototype\ncontextPrototype.U = relativeURL\n\n/**\n * Utility function to ensure all variants of an enum are handled.\n */\nfunction invariant(never: never, computeMessage: (arg: any) => string): never {\n  throw new Error(`Invariant: ${computeMessage(never)}`)\n}\n\n/**\n * A stub function to make `require` available but non-functional in ESM.\n */\nfunction requireStub(_moduleId: ModuleId): never {\n  throw new Error('dynamic usage of require is not supported')\n}\ncontextPrototype.z = requireStub\n\n// Make `globalThis` available to the module in a way that cannot be shadowed by a local variable.\ncontextPrototype.g = globalThis\n\ntype ContextConstructor<M> = {\n  new (module: Module, exports: Exports): TurbopackBaseContext<M>\n}\n\nfunction applyModuleFactoryName(factory: Function) {\n  // Give the module factory a nice name to improve stack traces.\n  Object.defineProperty(factory, 'name', {\n    value: 'module evaluation',\n  })\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,oDAAoD,GAEpD,6CAA6C;AAU7C,MAAM,qBAAqB,IAAI;AAE/B;;CAEC,GACD,SAAS,QAEP,MAAc,EACd,OAAgB;IAEhB,IAAI,CAAC,CAAC,GAAG;IACT,gFAAgF;IAChF,yEAAyE;IACzE,+CAA+C;IAC/C,8EAA8E;IAC9E,sEAAsE;IACtE,iBAAiB;IACjB,4FAA4F;IAC5F,IAAI,CAAC,CAAC,GAAG;AACX;AACA,MAAM,mBAAmB,QAAQ,SAAS;AA+B1C,MAAM,iBAAiB,OAAO,SAAS,CAAC,cAAc;AACtD,MAAM,cAAc,OAAO,WAAW,eAAe,OAAO,WAAW;AAEvE,SAAS,WACP,GAAQ,EACR,IAAiB,EACjB,OAA2C;IAE3C,IAAI,CAAC,eAAe,IAAI,CAAC,KAAK,OAAO,OAAO,cAAc,CAAC,KAAK,MAAM;AACxE;AAEA,SAAS,qBACP,WAAgC,EAChC,EAAY;IAEZ,IAAI,SAAS,WAAW,CAAC,GAAG;IAC5B,IAAI,CAAC,QAAQ;QACX,0FAA0F;QAC1F,4DAA4D;QAC5D,SAAS,mBAAmB;QAC5B,WAAW,CAAC,GAAG,GAAG;IACpB;IACA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,mBAAmB,EAAY;IACtC,OAAO;QACL,SAAS,CAAC;QACV,OAAO;QACP;QACA,iBAAiB;IACnB;AACF;AAGA,MAAM,mBAAmB;AAUzB;;CAEC,GACD,SAAS,IAAI,OAAgB,EAAE,QAAqB;IAClD,WAAW,SAAS,cAAc;QAAE,OAAO;IAAK;IAChD,IAAI,aAAa,WAAW,SAAS,aAAa;QAAE,OAAO;IAAS;IACpE,IAAI,IAAI;IACR,MAAO,IAAI,SAAS,MAAM,CAAE;QAC1B,MAAM,WAAW,QAAQ,CAAC,IAAI;QAC9B,MAAM,gBAAgB,QAAQ,CAAC,IAAI;QACnC,IAAI,OAAO,kBAAkB,UAAU;YACrC,IAAI,kBAAkB,kBAAkB;gBACtC,WAAW,SAAS,UAAU;oBAC5B,OAAO,QAAQ,CAAC,IAAI;oBACpB,YAAY;oBACZ,UAAU;gBACZ;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,eAAe;YACpD;QACF,OAAO;YACL,MAAM,WAAW;YACjB,IAAI,OAAO,QAAQ,CAAC,EAAE,KAAK,YAAY;gBACrC,MAAM,WAAW,QAAQ,CAAC,IAAI;gBAC9B,WAAW,SAAS,UAAU;oBAC5B,KAAK;oBACL,KAAK;oBACL,YAAY;gBACd;YACF,OAAO;gBACL,WAAW,SAAS,UAAU;oBAC5B,KAAK;oBACL,YAAY;gBACd;YACF;QACF;IACF;IACA,OAAO,IAAI,CAAC;AACd;AAEA;;CAEC,GACD,SAAS,UAEP,QAAqB,EACrB,EAAwB;IAExB,IAAI;IACJ,IAAI;IACJ,IAAI,MAAM,MAAM;QACd,SAAS,qBAAqB,IAAI,CAAC,CAAC,EAAE;QACtC,UAAU,OAAO,OAAO;IAC1B,OAAO;QACL,SAAS,IAAI,CAAC,CAAC;QACf,UAAU,IAAI,CAAC,CAAC;IAClB;IACA,OAAO,eAAe,GAAG;IACzB,IAAI,SAAS;AACf;AACA,iBAAiB,CAAC,GAAG;AAGrB,SAAS,qBACP,MAAc,EACd,OAAgB;IAEhB,IAAI,oBACF,mBAAmB,GAAG,CAAC;IAEzB,IAAI,CAAC,mBAAmB;QACtB,mBAAmB,GAAG,CAAC,QAAS,oBAAoB,EAAE;QACtD,OAAO,OAAO,GAAG,OAAO,eAAe,GAAG,IAAI,MAAM,SAAS;YAC3D,KAAI,MAAM,EAAE,IAAI;gBACd,IACE,eAAe,IAAI,CAAC,QAAQ,SAC5B,SAAS,aACT,SAAS,cACT;oBACA,OAAO,QAAQ,GAAG,CAAC,QAAQ;gBAC7B;gBACA,KAAK,MAAM,OAAO,kBAAoB;oBACpC,MAAM,QAAQ,QAAQ,GAAG,CAAC,KAAK;oBAC/B,IAAI,UAAU,WAAW,OAAO;gBAClC;gBACA,OAAO;YACT;YACA,SAAQ,MAAM;gBACZ,MAAM,OAAO,QAAQ,OAAO,CAAC;gBAC7B,KAAK,MAAM,OAAO,kBAAoB;oBACpC,KAAK,MAAM,OAAO,QAAQ,OAAO,CAAC,KAAM;wBACtC,IAAI,QAAQ,aAAa,CAAC,KAAK,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC;oBAC1D;gBACF;gBACA,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,cAEP,MAA2B,EAC3B,EAAwB;IAExB,IAAI;IACJ,IAAI;IACJ,IAAI,MAAM,MAAM;QACd,SAAS,qBAAqB,IAAI,CAAC,CAAC,EAAE;QACtC,UAAU,OAAO,OAAO;IAC1B,OAAO;QACL,SAAS,IAAI,CAAC,CAAC;QACf,UAAU,IAAI,CAAC,CAAC;IAClB;IACA,MAAM,oBAAoB,qBAAqB,QAAQ;IAEvD,IAAI,OAAO,WAAW,YAAY,WAAW,MAAM;QACjD,kBAAkB,IAAI,CAAC;IACzB;AACF;AACA,iBAAiB,CAAC,GAAG;AAErB,SAAS,YAEP,KAAU,EACV,EAAwB;IAExB,IAAI;IACJ,IAAI,MAAM,MAAM;QACd,SAAS,qBAAqB,IAAI,CAAC,CAAC,EAAE;IACxC,OAAO;QACL,SAAS,IAAI,CAAC,CAAC;IACjB;IACA,OAAO,OAAO,GAAG;AACnB;AACA,iBAAiB,CAAC,GAAG;AAErB,SAAS,gBAEP,SAAc,EACd,EAAwB;IAExB,IAAI;IACJ,IAAI,MAAM,MAAM;QACd,SAAS,qBAAqB,IAAI,CAAC,CAAC,EAAE;IACxC,OAAO;QACL,SAAS,IAAI,CAAC,CAAC;IACjB;IACA,OAAO,OAAO,GAAG,OAAO,eAAe,GAAG;AAC5C;AACA,iBAAiB,CAAC,GAAG;AAErB,SAAS,aAAa,GAAiC,EAAE,GAAoB;IAC3E,OAAO,IAAM,GAAG,CAAC,IAAI;AACvB;AAEA;;CAEC,GACD,MAAM,WAA8B,OAAO,cAAc,GACrD,CAAC,MAAQ,OAAO,cAAc,CAAC,OAC/B,CAAC,MAAQ,IAAI,SAAS;AAE1B,iDAAiD,GACjD,MAAM,kBAAkB;IAAC;IAAM,SAAS,CAAC;IAAI,SAAS,EAAE;IAAG,SAAS;CAAU;AAE9E;;;;;;CAMC,GACD,SAAS,WACP,GAAY,EACZ,EAAsB,EACtB,kBAA4B;IAE5B,MAAM,WAAwB,EAAE;IAChC,IAAI,kBAAkB,CAAC;IACvB,IACE,IAAI,UAAU,KACd,CAAC,OAAO,YAAY,YAAY,OAAO,YAAY,UAAU,KAC7D,CAAC,gBAAgB,QAAQ,CAAC,UAC1B,UAAU,SAAS,SACnB;QACA,KAAK,MAAM,OAAO,OAAO,mBAAmB,CAAC,SAAU;YACrD,SAAS,IAAI,CAAC,KAAK,aAAa,KAAK;YACrC,IAAI,oBAAoB,CAAC,KAAK,QAAQ,WAAW;gBAC/C,kBAAkB,SAAS,MAAM,GAAG;YACtC;QACF;IACF;IAEA,6BAA6B;IAC7B,6EAA6E;IAC7E,IAAI,CAAC,CAAC,sBAAsB,mBAAmB,CAAC,GAAG;QACjD,8FAA8F;QAC9F,IAAI,mBAAmB,GAAG;YACxB,oCAAoC;YACpC,SAAS,MAAM,CAAC,iBAAiB,GAAG,kBAAkB;QACxD,OAAO;YACL,SAAS,IAAI,CAAC,WAAW,kBAAkB;QAC7C;IACF;IAEA,IAAI,IAAI;IACR,OAAO;AACT;AAEA,SAAS,SAAS,GAAsB;IACtC,IAAI,OAAO,QAAQ,YAAY;QAC7B,OAAO,SAAqB,GAAG,IAAW;YACxC,OAAO,IAAI,KAAK,CAAC,IAAI,EAAE;QACzB;IACF,OAAO;QACL,OAAO,OAAO,MAAM,CAAC;IACvB;AACF;AAEA,SAAS,UAEP,EAAY;IAEZ,MAAM,SAAS,iCAAiC,IAAI,IAAI,CAAC,CAAC;IAE1D,8DAA8D;IAC9D,IAAI,OAAO,eAAe,EAAE,OAAO,OAAO,eAAe;IAEzD,iGAAiG;IACjG,MAAM,MAAM,OAAO,OAAO;IAC1B,OAAQ,OAAO,eAAe,GAAG,WAC/B,KACA,SAAS,MACT,OAAO,AAAC,IAAY,UAAU;AAElC;AACA,iBAAiB,CAAC,GAAG;AAErB,SAAS,YAEP,QAAkB;IAElB,MAAM,SAAS,IAAI,CAAC,CAAC,CAAC;IAGtB,OAAO,OAAO,UAAU,IAAI,CAAC,IAAI;AACnC;AACA,iBAAiB,CAAC,GAAG;AAErB,+EAA+E;AAC/E,6EAA6E;AAC7E,MAAM,iBACJ,aAAa;AACb,OAAO,YAAY,aAEf,UACA,SAAS;IACP,MAAM,IAAI,MAAM;AAClB;AACN,iBAAiB,CAAC,GAAG;AAErB,SAAS,gBAEP,EAAY;IAEZ,OAAO,iCAAiC,IAAI,IAAI,CAAC,CAAC,EAAE,OAAO;AAC7D;AACA,iBAAiB,CAAC,GAAG;AAErB;;CAEC,GACD,SAAS,cAAc,GAAqB;IAC1C,SAAS,cAAc,EAAY;QACjC,IAAI,eAAe,IAAI,CAAC,KAAK,KAAK;YAChC,OAAO,GAAG,CAAC,GAAG,CAAC,MAAM;QACvB;QAEA,MAAM,IAAI,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QAC9C,EAAU,IAAI,GAAG;QACnB,MAAM;IACR;IAEA,cAAc,IAAI,GAAG;QACnB,OAAO,OAAO,IAAI,CAAC;IACrB;IAEA,cAAc,OAAO,GAAG,CAAC;QACvB,IAAI,eAAe,IAAI,CAAC,KAAK,KAAK;YAChC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE;QACnB;QAEA,MAAM,IAAI,IAAI,MAAM,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;QAC9C,EAAU,IAAI,GAAG;QACnB,MAAM;IACR;IAEA,cAAc,MAAM,GAAG,OAAO;QAC5B,OAAO,MAAO,cAAc;IAC9B;IAEA,OAAO;AACT;AACA,iBAAiB,CAAC,GAAG;AAErB;;CAEC,GACD,SAAS,aAAa,SAAoB;IACxC,OAAO,OAAO,cAAc,WAAW,YAAY,UAAU,IAAI;AACnE;AAEA,SAAS,UAAmB,YAAiB;IAC3C,OACE,gBAAgB,QAChB,OAAO,iBAAiB,YACxB,UAAU,gBACV,OAAO,aAAa,IAAI,KAAK;AAEjC;AAEA,SAAS,iBAA+B,GAAM;IAC5C,OAAO,mBAAmB;AAC5B;AAEA,SAAS;IACP,IAAI;IACJ,IAAI;IAEJ,MAAM,UAAU,IAAI,QAAW,CAAC,KAAK;QACnC,SAAS;QACT,UAAU;IACZ;IAEA,OAAO;QACL;QACA,SAAS;QACT,QAAQ;IACV;AACF;AAEA,gFAAgF;AAChF,0CAA0C;AAC1C,yBAAyB;AACzB,8BAA8B;AAC9B,6EAA6E;AAC7E,wEAAwE;AACxE,SAAS,iCACP,YAAuC,EACvC,MAAc,EACd,eAAgC,EAChC,WAAoC;IAEpC,IAAI,IAAI;IACR,MAAO,IAAI,aAAa,MAAM,CAAE;QAC9B,IAAI,WAAW,YAAY,CAAC,EAAE;QAC9B,IAAI,MAAM,IAAI;QACd,4BAA4B;QAC5B,MACE,MAAM,aAAa,MAAM,IACzB,OAAO,YAAY,CAAC,IAAI,KAAK,WAC7B;YACA;QACF;QACA,IAAI,QAAQ,aAAa,MAAM,EAAE;YAC/B,MAAM,IAAI,MAAM;QAClB;QACA,+FAA+F;QAC/F,sFAAsF;QACtF,IAAI,CAAC,gBAAgB,GAAG,CAAC,WAAW;YAClC,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,uBAAuB;YACvB,cAAc;YACd,MAAO,IAAI,KAAK,IAAK;gBACnB,WAAW,YAAY,CAAC,EAAE;gBAC1B,gBAAgB,GAAG,CAAC,UAAU;YAChC;QACF;QACA,IAAI,MAAM,GAAE,sFAAsF;IACpG;AACF;AAEA,2CAA2C;AAC3C,+HAA+H;AAE/H,MAAM,kBAAkB,OAAO;AAC/B,MAAM,mBAAmB,OAAO;AAChC,MAAM,iBAAiB,OAAO;AAa9B,SAAS,aAAa,KAAkB;IACtC,IAAI,SAAS,MAAM,MAAM,QAA2B;QAClD,MAAM,MAAM;QACZ,MAAM,OAAO,CAAC,CAAC,KAAO,GAAG,UAAU;QACnC,MAAM,OAAO,CAAC,CAAC,KAAQ,GAAG,UAAU,KAAK,GAAG,UAAU,KAAK;IAC7D;AACF;AAYA,SAAS,SAAS,IAAW;IAC3B,OAAO,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;YAC3C,IAAI,iBAAiB,MAAM,OAAO;YAClC,IAAI,UAAU,MAAM;gBAClB,MAAM,QAAoB,OAAO,MAAM,CAAC,EAAE,EAAE;oBAC1C,MAAM;gBACR;gBAEA,MAAM,MAAsB;oBAC1B,CAAC,iBAAiB,EAAE,CAAC;oBACrB,CAAC,gBAAgB,EAAE,CAAC,KAAoC,GAAG;gBAC7D;gBAEA,IAAI,IAAI,CACN,CAAC;oBACC,GAAG,CAAC,iBAAiB,GAAG;oBACxB,aAAa;gBACf,GACA,CAAC;oBACC,GAAG,CAAC,eAAe,GAAG;oBACtB,aAAa;gBACf;gBAGF,OAAO;YACT;QACF;QAEA,OAAO;YACL,CAAC,iBAAiB,EAAE;YACpB,CAAC,gBAAgB,EAAE,KAAO;QAC5B;IACF;AACF;AAEA,SAAS,YAEP,IAKS,EACT,QAAiB;IAEjB,MAAM,SAAS,IAAI,CAAC,CAAC;IACrB,MAAM,QAAgC,WAClC,OAAO,MAAM,CAAC,EAAE,EAAE;QAAE,MAAM;IAAsB,KAChD;IAEJ,MAAM,YAA6B,IAAI;IAEvC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,UAAU,EAAE,GAAG;IAEjD,MAAM,UAA8B,OAAO,MAAM,CAAC,YAAY;QAC5D,CAAC,iBAAiB,EAAE,OAAO,OAAO;QAClC,CAAC,gBAAgB,EAAE,CAAC;YAClB,SAAS,GAAG;YACZ,UAAU,OAAO,CAAC;YAClB,OAAO,CAAC,QAAQ,CAAC,KAAO;QAC1B;IACF;IAEA,MAAM,aAAiC;QACrC;YACE,OAAO;QACT;QACA,KAAI,CAAM;YACR,qCAAqC;YACrC,IAAI,MAAM,SAAS;gBACjB,OAAO,CAAC,iBAAiB,GAAG;YAC9B;QACF;IACF;IAEA,OAAO,cAAc,CAAC,QAAQ,WAAW;IACzC,OAAO,cAAc,CAAC,QAAQ,mBAAmB;IAEjD,SAAS,wBAAwB,IAAW;QAC1C,MAAM,cAAc,SAAS;QAE7B,MAAM,YAAY,IAChB,YAAY,GAAG,CAAC,CAAC;gBACf,IAAI,CAAC,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,eAAe;gBAC9C,OAAO,CAAC,CAAC,iBAAiB;YAC5B;QAEF,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAE7B,MAAM,KAAmB,OAAO,MAAM,CAAC,IAAM,QAAQ,YAAY;YAC/D,YAAY;QACd;QAEA,SAAS,QAAQ,CAAa;YAC5B,IAAI,MAAM,SAAS,CAAC,UAAU,GAAG,CAAC,IAAI;gBACpC,UAAU,GAAG,CAAC;gBACd,IAAI,KAAK,EAAE,MAAM,QAA6B;oBAC5C,GAAG,UAAU;oBACb,EAAE,IAAI,CAAC;gBACT;YACF;QACF;QAEA,YAAY,GAAG,CAAC,CAAC,MAAQ,GAAG,CAAC,gBAAgB,CAAC;QAE9C,OAAO,GAAG,UAAU,GAAG,UAAU;IACnC;IAEA,SAAS,YAAY,GAAS;QAC5B,IAAI,KAAK;YACP,OAAQ,OAAO,CAAC,eAAe,GAAG;QACpC,OAAO;YACL,QAAQ,OAAO,CAAC,iBAAiB;QACnC;QAEA,aAAa;IACf;IAEA,KAAK,yBAAyB;IAE9B,IAAI,SAAS,MAAM,MAAM,SAA0B;QACjD,MAAM,MAAM;IACd;AACF;AACA,iBAAiB,CAAC,GAAG;AAErB;;;;;;;;;CASC,GACD,MAAM,cAAc,SAAS,YAAuB,QAAgB;IAClE,MAAM,UAAU,IAAI,IAAI,UAAU;IAClC,MAAM,SAA8B,CAAC;IACrC,IAAK,MAAM,OAAO,QAAS,MAAM,CAAC,IAAI,GAAG,AAAC,OAAe,CAAC,IAAI;IAC9D,OAAO,IAAI,GAAG;IACd,OAAO,QAAQ,GAAG,SAAS,OAAO,CAAC,UAAU;IAC7C,OAAO,MAAM,GAAG,OAAO,QAAQ,GAAG;IAClC,OAAO,QAAQ,GAAG,OAAO,MAAM,GAAG,CAAC,GAAG,QAAsB;IAC5D,IAAK,MAAM,OAAO,OAChB,OAAO,cAAc,CAAC,IAAI,EAAE,KAAK;QAC/B,YAAY;QACZ,cAAc;QACd,OAAO,MAAM,CAAC,IAAI;IACpB;AACJ;AACA,YAAY,SAAS,GAAG,IAAI,SAAS;AACrC,iBAAiB,CAAC,GAAG;AAErB;;CAEC,GACD,SAAS,UAAU,KAAY,EAAE,cAAoC;IACnE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,eAAe,QAAQ;AACvD;AAEA;;CAEC,GACD,SAAS,YAAY,SAAmB;IACtC,MAAM,IAAI,MAAM;AAClB;AACA,iBAAiB,CAAC,GAAG;AAErB,kGAAkG;AAClG,iBAAiB,CAAC,GAAG;AAMrB,SAAS,uBAAuB,OAAiB;IAC/C,+DAA+D;IAC/D,OAAO,cAAc,CAAC,SAAS,QAAQ;QACrC,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared-node/base-externals-utils.ts"], "sourcesContent": ["/// <reference path=\"../shared/runtime-utils.ts\" />\n\n/// A 'base' utilities to support runtime can have externals.\n/// Currently this is for node.js / edge runtime both.\n/// If a fn requires node.js specific behavior, it should be placed in `node-external-utils` instead.\n\nasync function externalImport(id: DependencySpecifier) {\n  let raw\n  try {\n    raw = await import(id)\n  } catch (err) {\n    // TODO(alexkirsz) This can happen when a client-side module tries to load\n    // an external module we don't provide a shim for (e.g. querystring, url).\n    // For now, we fail semi-silently, but in the future this should be a\n    // compilation error.\n    throw new Error(`Failed to load external module ${id}: ${err}`)\n  }\n\n  if (raw && raw.__esModule && raw.default && 'default' in raw.default) {\n    return interopEsm(raw.default, createNS(raw), true)\n  }\n\n  return raw\n}\ncontextPrototype.y = externalImport\n\nfunction externalRequire(\n  id: ModuleId,\n  thunk: () => any,\n  esm: boolean = false\n): Exports | EsmNamespaceObject {\n  let raw\n  try {\n    raw = thunk()\n  } catch (err) {\n    // TODO(alexkirsz) This can happen when a client-side module tries to load\n    // an external module we don't provide a shim for (e.g. querystring, url).\n    // For now, we fail semi-silently, but in the future this should be a\n    // compilation error.\n    throw new Error(`Failed to load external module ${id}: ${err}`)\n  }\n\n  if (!esm || raw.__esModule) {\n    return raw\n  }\n\n  return interopEsm(raw, createNS(raw), true)\n}\n\nexternalRequire.resolve = (\n  id: string,\n  options?: {\n    paths?: string[]\n  }\n) => {\n  return require.resolve(id, options)\n}\ncontextPrototype.x = externalRequire\n"], "names": [], "mappings": "AAAA,mDAAmD;AAEnD,6DAA6D;AAC7D,sDAAsD;AACtD,qGAAqG;AAErG,eAAe,eAAe,EAAuB;IACnD,IAAI;IACJ,IAAI;QACF,MAAM,MAAM,MAAM,CAAC;IACrB,EAAE,OAAO,KAAK;QACZ,0EAA0E;QAC1E,0EAA0E;QAC1E,qEAAqE;QACrE,qBAAqB;QACrB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,GAAG,EAAE,EAAE,KAAK;IAChE;IAEA,IAAI,OAAO,IAAI,UAAU,IAAI,IAAI,OAAO,IAAI,aAAa,IAAI,OAAO,EAAE;QACpE,OAAO,WAAW,IAAI,OAAO,EAAE,SAAS,MAAM;IAChD;IAEA,OAAO;AACT;AACA,iBAAiB,CAAC,GAAG;AAErB,SAAS,gBACP,EAAY,EACZ,KAAgB,EAChB,MAAe,KAAK;IAEpB,IAAI;IACJ,IAAI;QACF,MAAM;IACR,EAAE,OAAO,KAAK;QACZ,0EAA0E;QAC1E,0EAA0E;QAC1E,qEAAqE;QACrE,qBAAqB;QACrB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,GAAG,EAAE,EAAE,KAAK;IAChE;IAEA,IAAI,CAAC,OAAO,IAAI,UAAU,EAAE;QAC1B,OAAO;IACT;IAEA,OAAO,WAAW,KAAK,SAAS,MAAM;AACxC;AAEA,gBAAgB,OAAO,GAAG,CACxB,IACA;IAIA,OAAO,QAAQ,OAAO,CAAC,IAAI;AAC7B;AACA,iBAAiB,CAAC,GAAG", "ignoreList": [0]}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared-node/node-externals-utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n\ndeclare var RUNTIME_PUBLIC_PATH: string\ndeclare var RELATIVE_ROOT_PATH: string\ndeclare var ASSET_PREFIX: string\n\nconst path = require('path')\n\nconst relativePathToRuntimeRoot = path.relative(RUNTIME_PUBLIC_PATH, '.')\n// Compute the relative path to the `distDir`.\nconst relativePathToDistRoot = path.join(\n  relativePathToRuntimeRoot,\n  RELATIVE_ROOT_PATH\n)\nconst RUNTIME_ROOT = path.resolve(__filename, relativePathToRuntimeRoot)\n// Compute the absolute path to the root, by stripping distDir from the absolute path to this file.\nconst ABSOLUTE_ROOT = path.resolve(__filename, relativePathToDistRoot)\n\n/**\n * Returns an absolute path to the given module path.\n * Module path should be relative, either path to a file or a directory.\n *\n * This fn allows to calculate an absolute path for some global static values, such as\n * `__dirname` or `import.meta.url` that <PERSON><PERSON> will not embeds in compile time.\n * See ImportMetaBinding::code_generation for the usage.\n */\nfunction resolveAbsolutePath(modulePath?: string): string {\n  if (modulePath) {\n    return path.join(ABSOLUTE_ROOT, modulePath)\n  }\n  return ABSOLUTE_ROOT\n}\nContext.prototype.P = resolveAbsolutePath\n"], "names": [], "mappings": "AAAA,oDAAoD,GAMpD,MAAM,OAAO,QAAQ;AAErB,MAAM,4BAA4B,KAAK,QAAQ,CAAC,qBAAqB;AACrE,8CAA8C;AAC9C,MAAM,yBAAyB,KAAK,IAAI,CACtC,2BACA;AAEF,MAAM,eAAe,KAAK,OAAO,CAAC,YAAY;AAC9C,mGAAmG;AACnG,MAAM,gBAAgB,KAAK,OAAO,CAAC,YAAY;AAE/C;;;;;;;CAOC,GACD,SAAS,oBAAoB,UAAmB;IAC9C,IAAI,YAAY;QACd,OAAO,KAAK,IAAI,CAAC,eAAe;IAClC;IACA,OAAO;AACT;AACA,QAAQ,SAAS,CAAC,CAAC,GAAG", "ignoreList": [0]}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/shared-node/node-wasm-utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"../shared/runtime-utils.ts\" />\n\nfunction readWebAssemblyAsResponse(path: string) {\n  const { createReadStream } = require('fs') as typeof import('fs')\n  const { Readable } = require('stream') as typeof import('stream')\n\n  const stream = createReadStream(path)\n\n  // @ts-ignore unfortunately there's a slight type mismatch with the stream.\n  return new Response(Readable.toWeb(stream), {\n    headers: {\n      'content-type': 'application/wasm',\n    },\n  })\n}\n\nasync function compileWebAssemblyFromPath(\n  path: string\n): Promise<WebAssembly.Module> {\n  const response = readWebAssemblyAsResponse(path)\n\n  return await WebAssembly.compileStreaming(response)\n}\n\nasync function instantiateWebAssemblyFromPath(\n  path: string,\n  importsObj: WebAssembly.Imports\n): Promise<Exports> {\n  const response = readWebAssemblyAsResponse(path)\n\n  const { instance } = await WebAssembly.instantiateStreaming(\n    response,\n    importsObj\n  )\n\n  return instance.exports\n}\n"], "names": [], "mappings": "AAAA,oDAAoD,GAEpD,mDAAmD;AAEnD,SAAS,0BAA0B,IAAY;IAC7C,MAAM,EAAE,gBAAgB,EAAE,GAAG,QAAQ;IACrC,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ;IAE7B,MAAM,SAAS,iBAAiB;IAEhC,2EAA2E;IAC3E,OAAO,IAAI,SAAS,SAAS,KAAK,CAAC,SAAS;QAC1C,SAAS;YACP,gBAAgB;QAClB;IACF;AACF;AAEA,eAAe,2BACb,IAAY;IAEZ,MAAM,WAAW,0BAA0B;IAE3C,OAAO,MAAM,YAAY,gBAAgB,CAAC;AAC5C;AAEA,eAAe,+BACb,IAAY,EACZ,UAA+B;IAE/B,MAAM,WAAW,0BAA0B;IAE3C,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,YAAY,oBAAoB,CACzD,UACA;IAGF,OAAO,SAAS,OAAO;AACzB", "ignoreList": [0]}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack]/nodejs/runtime.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/// <reference path=\"../shared/runtime-utils.ts\" />\n/// <reference path=\"../shared-node/base-externals-utils.ts\" />\n/// <reference path=\"../shared-node/node-externals-utils.ts\" />\n/// <reference path=\"../shared-node/node-wasm-utils.ts\" />\n\nenum SourceType {\n  /**\n   * The module was instantiated because it was included in an evaluated chunk's\n   * runtime.\n   * SourceData is a ChunkPath.\n   */\n  Runtime = 0,\n  /**\n   * The module was instantiated because a parent module imported it.\n   * SourceData is a ModuleId.\n   */\n  Parent = 1,\n}\n\ntype SourceData = ChunkPath | ModuleId\n\nprocess.env.TURBOPACK = '1'\n\ninterface TurbopackNodeBuildContext extends TurbopackBaseContext<Module> {\n  R: ResolvePathFromModule\n  x: ExternalRequire\n  y: ExternalImport\n}\n\nconst nodeContextPrototype = Context.prototype as TurbopackNodeBuildContext\n\ntype ModuleFactory = (\n  this: Module['exports'],\n  context: TurbopackNodeBuildContext\n) => unknown\n\nconst url = require('url') as typeof import('url')\n\nconst moduleFactories: ModuleFactories = new Map()\nnodeContextPrototype.M = moduleFactories\nconst moduleCache: ModuleCache<Module> = Object.create(null)\nnodeContextPrototype.c = moduleCache\n\n/**\n * Returns an absolute path to the given module's id.\n */\nfunction resolvePathFromModule(\n  this: TurbopackBaseContext<Module>,\n  moduleId: string\n): string {\n  const exported = this.r(moduleId)\n  const exportedPath = exported?.default ?? exported\n  if (typeof exportedPath !== 'string') {\n    return exported as any\n  }\n\n  const strippedAssetPrefix = exportedPath.slice(ASSET_PREFIX.length)\n  const resolved = path.resolve(RUNTIME_ROOT, strippedAssetPrefix)\n\n  return url.pathToFileURL(resolved).href\n}\nnodeContextPrototype.R = resolvePathFromModule\n\nfunction loadRuntimeChunk(sourcePath: ChunkPath, chunkData: ChunkData): void {\n  if (typeof chunkData === 'string') {\n    loadRuntimeChunkPath(sourcePath, chunkData)\n  } else {\n    loadRuntimeChunkPath(sourcePath, chunkData.path)\n  }\n}\n\nconst loadedChunks = new Set<ChunkPath>()\nconst unsupportedLoadChunk = Promise.resolve(undefined)\nconst loadedChunk: Promise<void> = Promise.resolve(undefined)\nconst chunkCache = new Map<ChunkPath, Promise<void>>()\n\nfunction clearChunkCache() {\n  chunkCache.clear()\n}\n\nfunction loadRuntimeChunkPath(\n  sourcePath: ChunkPath,\n  chunkPath: ChunkPath\n): void {\n  if (!isJs(chunkPath)) {\n    // We only support loading JS chunks in Node.js.\n    // This branch can be hit when trying to load a CSS chunk.\n    return\n  }\n\n  if (loadedChunks.has(chunkPath)) {\n    return\n  }\n\n  try {\n    const resolved = path.resolve(RUNTIME_ROOT, chunkPath)\n    const chunkModules: CompressedModuleFactories = require(resolved)\n    installCompressedModuleFactories(chunkModules, 0, moduleFactories)\n    loadedChunks.add(chunkPath)\n  } catch (e) {\n    let errorMessage = `Failed to load chunk ${chunkPath}`\n\n    if (sourcePath) {\n      errorMessage += ` from runtime for chunk ${sourcePath}`\n    }\n\n    throw new Error(errorMessage, {\n      cause: e,\n    })\n  }\n}\n\nfunction loadChunkAsync(\n  this: TurbopackBaseContext<Module>,\n  chunkData: ChunkData\n): Promise<void> {\n  const chunkPath = typeof chunkData === 'string' ? chunkData : chunkData.path\n  if (!isJs(chunkPath)) {\n    // We only support loading JS chunks in Node.js.\n    // This branch can be hit when trying to load a CSS chunk.\n    return unsupportedLoadChunk\n  }\n\n  let entry = chunkCache.get(chunkPath)\n  if (entry === undefined) {\n    try {\n      // resolve to an absolute path to simplify `require` handling\n      const resolved = path.resolve(RUNTIME_ROOT, chunkPath)\n      // TODO: consider switching to `import()` to enable concurrent chunk loading and async file io\n      // However this is incompatible with hot reloading (since `import` doesn't use the require cache)\n      const chunkModules: CompressedModuleFactories = require(resolved)\n      installCompressedModuleFactories(chunkModules, 0, moduleFactories)\n      entry = loadedChunk\n    } catch (e) {\n      const errorMessage = `Failed to load chunk ${chunkPath} from module ${this.m.id}`\n\n      // Cache the failure promise, future requests will also get this same rejection\n      entry = Promise.reject(\n        new Error(errorMessage, {\n          cause: e,\n        })\n      )\n    }\n    chunkCache.set(chunkPath, entry)\n  }\n  // TODO: Return an instrumented Promise that React can use instead of relying on referential equality.\n  return entry\n}\ncontextPrototype.l = loadChunkAsync\n\nfunction loadChunkAsyncByUrl(\n  this: TurbopackBaseContext<Module>,\n  chunkUrl: string\n) {\n  const path = url.fileURLToPath(new URL(chunkUrl, RUNTIME_ROOT)) as ChunkPath\n  return loadChunkAsync.call(this, path)\n}\ncontextPrototype.L = loadChunkAsyncByUrl\n\nfunction loadWebAssembly(\n  chunkPath: ChunkPath,\n  _edgeModule: () => WebAssembly.Module,\n  imports: WebAssembly.Imports\n) {\n  const resolved = path.resolve(RUNTIME_ROOT, chunkPath)\n\n  return instantiateWebAssemblyFromPath(resolved, imports)\n}\ncontextPrototype.w = loadWebAssembly\n\nfunction loadWebAssemblyModule(\n  chunkPath: ChunkPath,\n  _edgeModule: () => WebAssembly.Module\n) {\n  const resolved = path.resolve(RUNTIME_ROOT, chunkPath)\n\n  return compileWebAssemblyFromPath(resolved)\n}\ncontextPrototype.u = loadWebAssemblyModule\n\nfunction getWorkerBlobURL(_chunks: ChunkPath[]): string {\n  throw new Error('Worker blobs are not implemented yet for Node.js')\n}\n\nnodeContextPrototype.b = getWorkerBlobURL\n\nfunction instantiateModule(\n  id: ModuleId,\n  sourceType: SourceType,\n  sourceData: SourceData\n): Module {\n  const moduleFactory = moduleFactories.get(id)\n  if (typeof moduleFactory !== 'function') {\n    // This can happen if modules incorrectly handle HMR disposes/updates,\n    // e.g. when they keep a `setTimeout` around which still executes old code\n    // and contains e.g. a `require(\"something\")` call.\n    let instantiationReason: string\n    switch (sourceType) {\n      case SourceType.Runtime:\n        instantiationReason = `as a runtime entry of chunk ${sourceData}`\n        break\n      case SourceType.Parent:\n        instantiationReason = `because it was required from module ${sourceData}`\n        break\n      default:\n        invariant(\n          sourceType,\n          (sourceType) => `Unknown source type: ${sourceType}`\n        )\n    }\n    throw new Error(\n      `Module ${id} was instantiated ${instantiationReason}, but the module factory is not available.`\n    )\n  }\n\n  const module: Module = createModuleObject(id)\n  const exports = module.exports\n  moduleCache[id] = module\n\n  const context = new (Context as any as ContextConstructor<Module>)(\n    module,\n    exports\n  )\n  // NOTE(alexkirsz) This can fail when the module encounters a runtime error.\n  try {\n    moduleFactory(context, module, exports)\n  } catch (error) {\n    module.error = error as any\n    throw error\n  }\n\n  module.loaded = true\n  if (module.namespaceObject && module.exports !== module.namespaceObject) {\n    // in case of a circular dependency: cjs1 -> esm2 -> cjs1\n    interopEsm(module.exports, module.namespaceObject)\n  }\n\n  return module\n}\n\n/**\n * Retrieves a module from the cache, or instantiate it if it is not cached.\n */\n// @ts-ignore\nfunction getOrInstantiateModuleFromParent(\n  id: ModuleId,\n  sourceModule: Module\n): Module {\n  const module = moduleCache[id]\n\n  if (module) {\n    if (module.error) {\n      throw module.error\n    }\n\n    return module\n  }\n\n  return instantiateModule(id, SourceType.Parent, sourceModule.id)\n}\n\n/**\n * Instantiates a runtime module.\n */\nfunction instantiateRuntimeModule(\n  chunkPath: ChunkPath,\n  moduleId: ModuleId\n): Module {\n  return instantiateModule(moduleId, SourceType.Runtime, chunkPath)\n}\n\n/**\n * Retrieves a module from the cache, or instantiate it as a runtime module if it is not cached.\n */\n// @ts-ignore TypeScript doesn't separate this module space from the browser runtime\nfunction getOrInstantiateRuntimeModule(\n  chunkPath: ChunkPath,\n  moduleId: ModuleId\n): Module {\n  const module = moduleCache[moduleId]\n  if (module) {\n    if (module.error) {\n      throw module.error\n    }\n    return module\n  }\n\n  return instantiateRuntimeModule(chunkPath, moduleId)\n}\n\nconst regexJsUrl = /\\.js(?:\\?[^#]*)?(?:#.*)?$/\n/**\n * Checks if a given path/URL ends with .js, optionally followed by ?query or #fragment.\n */\nfunction isJs(chunkUrlOrPath: ChunkUrl | ChunkPath): boolean {\n  return regexJsUrl.test(chunkUrlOrPath)\n}\n\nmodule.exports = (sourcePath: ChunkPath) => ({\n  m: (id: ModuleId) => getOrInstantiateRuntimeModule(sourcePath, id),\n  c: (chunkData: ChunkData) => loadRuntimeChunk(sourcePath, chunkData),\n})\n"], "names": [], "mappings": "AAAA,oDAAoD,GAEpD,mDAAmD;AACnD,+DAA+D;AAC/D,+DAA+D;AAC/D,0DAA0D;AAE1D,IAAA,AAAK,oCAAA;IACH;;;;GAIC;IAED;;;GAGC;WAVE;EAAA;AAgBL,QAAQ,GAAG,CAAC,SAAS,GAAG;AAQxB,MAAM,uBAAuB,QAAQ,SAAS;AAO9C,MAAM,MAAM,QAAQ;AAEpB,MAAM,kBAAmC,IAAI;AAC7C,qBAAqB,CAAC,GAAG;AACzB,MAAM,cAAmC,OAAO,MAAM,CAAC;AACvD,qBAAqB,CAAC,GAAG;AAEzB;;CAEC,GACD,SAAS,sBAEP,QAAgB;IAEhB,MAAM,WAAW,IAAI,CAAC,CAAC,CAAC;IACxB,MAAM,eAAe,UAAU,WAAW;IAC1C,IAAI,OAAO,iBAAiB,UAAU;QACpC,OAAO;IACT;IAEA,MAAM,sBAAsB,aAAa,KAAK,CAAC,aAAa,MAAM;IAClE,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;IAE5C,OAAO,IAAI,aAAa,CAAC,UAAU,IAAI;AACzC;AACA,qBAAqB,CAAC,GAAG;AAEzB,SAAS,iBAAiB,UAAqB,EAAE,SAAoB;IACnE,IAAI,OAAO,cAAc,UAAU;QACjC,qBAAqB,YAAY;IACnC,OAAO;QACL,qBAAqB,YAAY,UAAU,IAAI;IACjD;AACF;AAEA,MAAM,eAAe,IAAI;AACzB,MAAM,uBAAuB,QAAQ,OAAO,CAAC;AAC7C,MAAM,cAA6B,QAAQ,OAAO,CAAC;AACnD,MAAM,aAAa,IAAI;AAEvB,SAAS;IACP,WAAW,KAAK;AAClB;AAEA,SAAS,qBACP,UAAqB,EACrB,SAAoB;IAEpB,IAAI,CAAC,KAAK,YAAY;QACpB,gDAAgD;QAChD,0DAA0D;QAC1D;IACF;IAEA,IAAI,aAAa,GAAG,CAAC,YAAY;QAC/B;IACF;IAEA,IAAI;QACF,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;QAC5C,MAAM,eAA0C,QAAQ;QACxD,iCAAiC,cAAc,GAAG;QAClD,aAAa,GAAG,CAAC;IACnB,EAAE,OAAO,GAAG;QACV,IAAI,eAAe,CAAC,qBAAqB,EAAE,WAAW;QAEtD,IAAI,YAAY;YACd,gBAAgB,CAAC,wBAAwB,EAAE,YAAY;QACzD;QAEA,MAAM,IAAI,MAAM,cAAc;YAC5B,OAAO;QACT;IACF;AACF;AAEA,SAAS,eAEP,SAAoB;IAEpB,MAAM,YAAY,OAAO,cAAc,WAAW,YAAY,UAAU,IAAI;IAC5E,IAAI,CAAC,KAAK,YAAY;QACpB,gDAAgD;QAChD,0DAA0D;QAC1D,OAAO;IACT;IAEA,IAAI,QAAQ,WAAW,GAAG,CAAC;IAC3B,IAAI,UAAU,WAAW;QACvB,IAAI;YACF,6DAA6D;YAC7D,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;YAC5C,8FAA8F;YAC9F,iGAAiG;YACjG,MAAM,eAA0C,QAAQ;YACxD,iCAAiC,cAAc,GAAG;YAClD,QAAQ;QACV,EAAE,OAAO,GAAG;YACV,MAAM,eAAe,CAAC,qBAAqB,EAAE,UAAU,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE;YAEjF,+EAA+E;YAC/E,QAAQ,QAAQ,MAAM,CACpB,IAAI,MAAM,cAAc;gBACtB,OAAO;YACT;QAEJ;QACA,WAAW,GAAG,CAAC,WAAW;IAC5B;IACA,sGAAsG;IACtG,OAAO;AACT;AACA,iBAAiB,CAAC,GAAG;AAErB,SAAS,oBAEP,QAAgB;IAEhB,MAAM,QAAO,IAAI,aAAa,CAAC,IAAI,IAAI,UAAU;IACjD,OAAO,eAAe,IAAI,CAAC,IAAI,EAAE;AACnC;AACA,iBAAiB,CAAC,GAAG;AAErB,SAAS,gBACP,SAAoB,EACpB,WAAqC,EACrC,OAA4B;IAE5B,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;IAE5C,OAAO,+BAA+B,UAAU;AAClD;AACA,iBAAiB,CAAC,GAAG;AAErB,SAAS,sBACP,SAAoB,EACpB,WAAqC;IAErC,MAAM,WAAW,KAAK,OAAO,CAAC,cAAc;IAE5C,OAAO,2BAA2B;AACpC;AACA,iBAAiB,CAAC,GAAG;AAErB,SAAS,iBAAiB,OAAoB;IAC5C,MAAM,IAAI,MAAM;AAClB;AAEA,qBAAqB,CAAC,GAAG;AAEzB,SAAS,kBACP,EAAY,EACZ,UAAsB,EACtB,UAAsB;IAEtB,MAAM,gBAAgB,gBAAgB,GAAG,CAAC;IAC1C,IAAI,OAAO,kBAAkB,YAAY;QACvC,sEAAsE;QACtE,0EAA0E;QAC1E,mDAAmD;QACnD,IAAI;QACJ,OAAQ;YACN;gBACE,sBAAsB,CAAC,4BAA4B,EAAE,YAAY;gBACjE;YACF;gBACE,sBAAsB,CAAC,oCAAoC,EAAE,YAAY;gBACzE;YACF;gBACE,UACE,YACA,CAAC,aAAe,CAAC,qBAAqB,EAAE,YAAY;QAE1D;QACA,MAAM,IAAI,MACR,CAAC,OAAO,EAAE,GAAG,kBAAkB,EAAE,oBAAoB,0CAA0C,CAAC;IAEpG;IAEA,MAAM,UAAiB,mBAAmB;IAC1C,MAAM,UAAU,QAAO,OAAO;IAC9B,WAAW,CAAC,GAAG,GAAG;IAElB,MAAM,UAAU,IAAK,QACnB,SACA;IAEF,4EAA4E;IAC5E,IAAI;QACF,cAAc,SAAS,SAAQ;IACjC,EAAE,OAAO,OAAO;QACd,QAAO,KAAK,GAAG;QACf,MAAM;IACR;IAEA,QAAO,MAAM,GAAG;IAChB,IAAI,QAAO,eAAe,IAAI,QAAO,OAAO,KAAK,QAAO,eAAe,EAAE;QACvE,yDAAyD;QACzD,WAAW,QAAO,OAAO,EAAE,QAAO,eAAe;IACnD;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,aAAa;AACb,SAAS,iCACP,EAAY,EACZ,YAAoB;IAEpB,MAAM,UAAS,WAAW,CAAC,GAAG;IAE9B,IAAI,SAAQ;QACV,IAAI,QAAO,KAAK,EAAE;YAChB,MAAM,QAAO,KAAK;QACpB;QAEA,OAAO;IACT;IAEA,OAAO,kBAAkB,OAAuB,aAAa,EAAE;AACjE;AAEA;;CAEC,GACD,SAAS,yBACP,SAAoB,EACpB,QAAkB;IAElB,OAAO,kBAAkB,aAA8B;AACzD;AAEA;;CAEC,GACD,oFAAoF;AACpF,SAAS,8BACP,SAAoB,EACpB,QAAkB;IAElB,MAAM,UAAS,WAAW,CAAC,SAAS;IACpC,IAAI,SAAQ;QACV,IAAI,QAAO,KAAK,EAAE;YAChB,MAAM,QAAO,KAAK;QACpB;QACA,OAAO;IACT;IAEA,OAAO,yBAAyB,WAAW;AAC7C;AAEA,MAAM,aAAa;AACnB;;CAEC,GACD,SAAS,KAAK,cAAoC;IAChD,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA,OAAO,OAAO,GAAG,CAAC,aAA0B,CAAC;QAC3C,GAAG,CAAC,KAAiB,8BAA8B,YAAY;QAC/D,GAAG,CAAC,YAAyB,iBAAiB,YAAY;IAC5D,CAAC", "ignoreList": [0]}}]}