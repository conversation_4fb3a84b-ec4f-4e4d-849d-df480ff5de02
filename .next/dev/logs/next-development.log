[00:00:00.915] Server  LOG      ✓ Ready in 669ms
[00:00:04.396] Server  LOG      ○ Compiling / ...
[00:00:17.298] Server  WARN     ⚠ [next]/internal/font/google/inter_5901b7c6.module.css
Error while requesting resource
There was an issue establishing a connection while requesting https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap

Import trace:
  Server Component:
    [next]/internal/font/google/inter_5901b7c6.module.css
    [next]/internal/font/google/inter_5901b7c6.js
    ./Documents/augment-projects/flywheel-media/src/app/layout.tsx


[00:00:17.299] Server  WARN     ⚠ [next]/internal/font/google/inter_5901b7c6.module.css
next/font: warning:
Failed to download `Inter` from Google Fonts. Using fallback font instead.

Import trace:
  Server Component:
    [next]/internal/font/google/inter_5901b7c6.module.css
    [next]/internal/font/google/inter_5901b7c6.js
    ./Documents/augment-projects/flywheel-media/src/app/layout.tsx


[00:00:18.130] Server  WARN     ⚠ [next]/internal/font/google/inter_5901b7c6.module.css
Error while requesting resource
There was an issue establishing a connection while requesting https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap

Import trace:
  Server Component:
    [next]/internal/font/google/inter_5901b7c6.module.css
    [next]/internal/font/google/inter_5901b7c6.js
    ./Documents/augment-projects/flywheel-media/src/app/layout.tsx


[00:00:18.130] Server  WARN     ⚠ [next]/internal/font/google/inter_5901b7c6.module.css
next/font: warning:
Failed to download `Inter` from Google Fonts. Using fallback font instead.

Import trace:
  Server Component:
    [next]/internal/font/google/inter_5901b7c6.module.css
    [next]/internal/font/google/inter_5901b7c6.js
    ./Documents/augment-projects/flywheel-media/src/app/layout.tsx


[00:00:18.199] Browser INFO    %cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold
[00:00:42.606] Server  WARN     ⚠ [next]/internal/font/google/inter_5901b7c6.module.css
Error while requesting resource
There was an issue establishing a connection while requesting https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap

Import trace:
  Server Component:
    [next]/internal/font/google/inter_5901b7c6.module.css
    [next]/internal/font/google/inter_5901b7c6.js
    ./Documents/augment-projects/flywheel-media/src/app/layout.tsx


[00:00:42.607] Server  WARN     ⚠ [next]/internal/font/google/inter_5901b7c6.module.css
next/font: warning:
Failed to download `Inter` from Google Fonts. Using fallback font instead.

Import trace:
  Server Component:
    [next]/internal/font/google/inter_5901b7c6.module.css
    [next]/internal/font/google/inter_5901b7c6.js
    ./Documents/augment-projects/flywheel-media/src/app/layout.tsx


[00:00:42.818] Browser WARN    Detected `scroll-behavior: smooth` on the `<html>` element. To disable smooth scrolling during route transitions, add `data-scroll-behavior="smooth"` to your <html> element. Learn more: https://nextjs.org/docs/messages/missing-data-scroll-behavior
[00:00:43.675] Server  WARN     ⚠ [next]/internal/font/google/inter_5901b7c6.module.css
Error while requesting resource
There was an issue establishing a connection while requesting https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap

Import trace:
  Server Component:
    [next]/internal/font/google/inter_5901b7c6.module.css
    [next]/internal/font/google/inter_5901b7c6.js
    ./Documents/augment-projects/flywheel-media/src/app/layout.tsx


[00:00:43.675] Server  WARN     ⚠ [next]/internal/font/google/inter_5901b7c6.module.css
next/font: warning:
Failed to download `Inter` from Google Fonts. Using fallback font instead.

Import trace:
  Server Component:
    [next]/internal/font/google/inter_5901b7c6.module.css
    [next]/internal/font/google/inter_5901b7c6.js
    ./Documents/augment-projects/flywheel-media/src/app/layout.tsx


[00:00:47.261] Server  WARN     ⚠ [next]/internal/font/google/inter_5901b7c6.module.css
Error while requesting resource
There was an issue establishing a connection while requesting https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap

Import trace:
  Server Component:
    [next]/internal/font/google/inter_5901b7c6.module.css
    [next]/internal/font/google/inter_5901b7c6.js
    ./Documents/augment-projects/flywheel-media/src/app/layout.tsx


[00:00:47.261] Server  WARN     ⚠ [next]/internal/font/google/inter_5901b7c6.module.css
next/font: warning:
Failed to download `Inter` from Google Fonts. Using fallback font instead.

Import trace:
  Server Component:
    [next]/internal/font/google/inter_5901b7c6.module.css
    [next]/internal/font/google/inter_5901b7c6.js
    ./Documents/augment-projects/flywheel-media/src/app/layout.tsx


[00:00:58.026] Browser LOG     Form submitted: {"comment":"423424","email":"<EMAIL>","name":"Mike MI","phone":"44324324","skype":"342"}
[00:01:02.702] Browser LOG     Form submitted: {"comment":"423424","email":"<EMAIL>","name":"Mike MI","phone":"44324324","skype":"342"}
[00:01:19.711] Browser LOG     Form submitted: {"comment":"423424","email":"<EMAIL>","name":"Mike MI","phone":"44324324","skype":"342"}
[00:01:50.809] Browser LOG     Form submitted: {"comment":"423424","email":"<EMAIL>","name":"Mike MI","phone":"44324324","skype":"342"}
