{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/shared/lib/router/utils/disable-smooth-scroll.ts"], "sourcesContent": ["import { warnOnce } from '../../utils/warn-once'\n\n/**\n * Run function with `scroll-behavior: auto` applied to `<html/>`.\n * This css change will be reverted after the function finishes.\n */\nexport function disableSmoothScrollDuringRouteTransition(\n  fn: () => void,\n  options: { dontForceLayout?: boolean; onlyHashChange?: boolean } = {}\n) {\n  // if only the hash is changed, we don't need to disable smooth scrolling\n  // we only care to prevent smooth scrolling when navigating to a new page to avoid jarring UX\n  if (options.onlyHashChange) {\n    fn()\n    return\n  }\n\n  const htmlElement = document.documentElement\n  const hasDataAttribute = htmlElement.dataset.scrollBehavior === 'smooth'\n\n  if (!hasDataAttribute) {\n    // Warn if smooth scrolling is detected but no data attribute is present\n    if (\n      process.env.NODE_ENV === 'development' &&\n      getComputedStyle(htmlElement).scrollBehavior === 'smooth'\n    ) {\n      warnOnce(\n        'Detected `scroll-behavior: smooth` on the `<html>` element. To disable smooth scrolling during route transitions, ' +\n          'add `data-scroll-behavior=\"smooth\"` to your <html> element. ' +\n          'Learn more: https://nextjs.org/docs/messages/missing-data-scroll-behavior'\n      )\n    }\n    // No smooth scrolling configured, run directly without style manipulation\n    fn()\n    return\n  }\n\n  // Proceed with temporarily disabling smooth scrolling\n  const existing = htmlElement.style.scrollBehavior\n  htmlElement.style.scrollBehavior = 'auto'\n  if (!options.dontForceLayout) {\n    // In Chrome-based browsers we need to force reflow before calling `scrollTo`.\n    // Otherwise it will not pickup the change in scrollBehavior\n    // More info here: https://github.com/vercel/next.js/issues/40719#issuecomment-1336248042\n    htmlElement.getClientRects()\n  }\n  fn()\n  htmlElement.style.scrollBehavior = existing\n}\n"], "names": ["disableSmoothScrollDuringRouteTransition", "fn", "options", "onlyHashChange", "htmlElement", "document", "documentElement", "hasDataAttribute", "dataset", "scroll<PERSON>eh<PERSON>or", "process", "env", "NODE_ENV", "getComputedStyle", "warnOnce", "existing", "style", "dontForceLayout", "getClientRects"], "mappings": "AAuBMU,QAAQC,GAAG,CAACC,QAAQ;;;;;+BAjBVZ,4CAAAA;;;eAAAA;;;0BANS;AAMlB,SAASA,yCACdC,EAAc,EACdC,UAAmE,CAAC,CAAC;IAErE,yEAAyE;IACzE,6FAA6F;IAC7F,IAAIA,QAAQC,cAAc,EAAE;QAC1BF;QACA;IACF;IAEA,MAAMG,cAAcC,SAASC,eAAe;IAC5C,MAAMC,mBAAmBH,YAAYI,OAAO,CAACC,cAAc,KAAK;IAEhE,IAAI,CAACF,kBAAkB;QACrB,wEAAwE;QACxE,wDAC2B,iBACzBM,iBAAiBT,aAAaK,cAAc,KAAK,UACjD;YACAK,CAAAA,GAAAA,UAAAA,QAAQ,EACN,uHACE,iEACA;QAEN;QACA,0EAA0E;QAC1Eb;QACA;IACF;IAEA,sDAAsD;IACtD,MAAMc,WAAWX,YAAYY,KAAK,CAACP,cAAc;IACjDL,YAAYY,KAAK,CAACP,cAAc,GAAG;IACnC,IAAI,CAACP,QAAQe,eAAe,EAAE;QAC5B,8EAA8E;QAC9E,4DAA4D;QAC5D,yFAAyF;QACzFb,YAAYc,cAAc;IAC5B;IACAjB;IACAG,YAAYY,KAAK,CAACP,cAAc,GAAGM;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/client/components/bfcache.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../shared/lib/app-router-types'\nimport { useState } from 'react'\n\n// When the flag is disabled, only track the currently active tree\nconst MAX_BF_CACHE_ENTRIES = process.env.__NEXT_CACHE_COMPONENTS ? 3 : 1\n\nexport type RouterBFCacheEntry = {\n  tree: FlightRouterState\n  stateKey: string\n  // The entries form a linked list, sorted in order of most recently active.\n  next: RouterBFCacheEntry | null\n}\n\n/**\n * Keeps track of the most recent N trees (FlightRouterStates) that were active\n * at a certain segment level. E.g. for a segment \"/a/b/[param]\", this hook\n * tracks the last N param values that the router rendered for N.\n *\n * The result of this hook precisely determines the number and order of\n * trees that are rendered in parallel at their segment level.\n *\n * The purpose of this cache is to we can preserve the React and DOM state of\n * some number of inactive trees, by rendering them in an <Activity> boundary.\n * That means it would not make sense for the the lifetime of the cache to be\n * any longer than the lifetime of the React tree; e.g. if the hook were\n * unmounted, then the React tree would be, too. So, we use React state to\n * manage it.\n *\n * Note that we don't store the RSC data for the cache entries in this hook —\n * the data for inactive segments is stored in the parent CacheNode, which\n * *does* have a longer lifetime than the React tree. This hook only determines\n * which of those trees should have their *state* preserved, by <Activity>.\n */\nexport function useRouterBFCache(\n  activeTree: FlightRouterState,\n  activeStateKey: string\n): RouterBFCacheEntry {\n  // The currently active entry. The entries form a linked list, sorted in\n  // order of most recently active. This allows us to reuse parts of the list\n  // without cloning, unless there's a reordering or removal.\n  // TODO: Once we start tracking back/forward history at each route level,\n  // we should use the history order instead. In other words, when traversing\n  // to an existing entry as a result of a popstate event, we should maintain\n  // the existing order instead of moving it to the front of the list. I think\n  // an initial implementation of this could be to pass an incrementing id\n  // to history.pushState/replaceState, then use that here for ordering.\n  const [prevActiveEntry, setPrevActiveEntry] = useState<RouterBFCacheEntry>(\n    () => {\n      const initialEntry: RouterBFCacheEntry = {\n        tree: activeTree,\n        stateKey: activeStateKey,\n        next: null,\n      }\n      return initialEntry\n    }\n  )\n\n  if (prevActiveEntry.tree === activeTree) {\n    // Fast path. The active tree hasn't changed, so we can reuse the\n    // existing state.\n    return prevActiveEntry\n  }\n\n  // The route tree changed. Note that this doesn't mean that the tree changed\n  // *at this level* — the change may be due to a child route. Either way, we\n  // need to either add or update the router tree in the bfcache.\n  //\n  // The rest of the code looks more complicated than it actually is because we\n  // can't mutate the state in place; we have to copy-on-write.\n\n  // Create a new entry for the active cache key. This is the head of the new\n  // linked list.\n  const newActiveEntry: RouterBFCacheEntry = {\n    tree: activeTree,\n    stateKey: activeStateKey,\n    next: null,\n  }\n\n  // We need to append the old list onto the new list. If the head of the new\n  // list was already present in the cache, then we'll need to clone everything\n  // that came before it. Then we can reuse the rest.\n  let n = 1\n  let oldEntry: RouterBFCacheEntry | null = prevActiveEntry\n  let clonedEntry: RouterBFCacheEntry = newActiveEntry\n  while (oldEntry !== null && n < MAX_BF_CACHE_ENTRIES) {\n    if (oldEntry.stateKey === activeStateKey) {\n      // Fast path. This entry in the old list that corresponds to the key that\n      // is now active. We've already placed a clone of this entry at the front\n      // of the new list. We can reuse the rest of the old list without cloning.\n      // NOTE: We don't need to worry about eviction in this case because we\n      // haven't increased the size of the cache, and we assume the max size\n      // is constant across renders. If we were to change it to a dynamic limit,\n      // then the implementation would need to account for that.\n      clonedEntry.next = oldEntry.next\n      break\n    } else {\n      // Clone the entry and append it to the list.\n      n++\n      const entry: RouterBFCacheEntry = {\n        tree: oldEntry.tree,\n        stateKey: oldEntry.stateKey,\n        next: null,\n      }\n      clonedEntry.next = entry\n      clonedEntry = entry\n    }\n    oldEntry = oldEntry.next\n  }\n\n  setPrevActiveEntry(newActiveEntry)\n  return newActiveEntry\n}\n"], "names": ["useRouterBFCache", "MAX_BF_CACHE_ENTRIES", "process", "env", "__NEXT_CACHE_COMPONENTS", "activeTree", "activeStateKey", "prevActiveEntry", "setPrevActiveEntry", "useState", "initialEntry", "tree", "stateKey", "next", "newActiveEntry", "n", "oldEntry", "clonedEntry", "entry"], "mappings": "AAI6BE,QAAQC,GAAG,CAACC,uBAAuB;;;;;+BA6BhDJ,oBAAAA;;;eAAAA;;;uBAhCS;AAEzB,kEAAkE;AAClE,MAAMC,6DAA6D,0BAAI;AA6BhE,SAASD,iBACdK,UAA6B,EAC7BC,cAAsB;IAEtB,wEAAwE;IACxE,2EAA2E;IAC3E,2DAA2D;IAC3D,yEAAyE;IACzE,2EAA2E;IAC3E,2EAA2E;IAC3E,4EAA4E;IAC5E,wEAAwE;IACxE,sEAAsE;IACtE,MAAM,CAACC,iBAAiBC,mBAAmB,GAAGC,CAAAA,GAAAA,OAAAA,QAAQ,EACpD;QACE,MAAMC,eAAmC;YACvCC,MAAMN;YACNO,UAAUN;YACVO,MAAM;QACR;QACA,OAAOH;IACT;IAGF,IAAIH,gBAAgBI,IAAI,KAAKN,YAAY;QACvC,iEAAiE;QACjE,kBAAkB;QAClB,OAAOE;IACT;IAEA,4EAA4E;IAC5E,2EAA2E;IAC3E,+DAA+D;IAC/D,EAAE;IACF,6EAA6E;IAC7E,6DAA6D;IAE7D,2EAA2E;IAC3E,eAAe;IACf,MAAMO,iBAAqC;QACzCH,MAAMN;QACNO,UAAUN;QACVO,MAAM;IACR;IAEA,2EAA2E;IAC3E,6EAA6E;IAC7E,mDAAmD;IACnD,IAAIE,IAAI;IACR,IAAIC,WAAsCT;IAC1C,IAAIU,cAAkCH;IACtC,MAAOE,aAAa,QAAQD,IAAId,qBAAsB;QACpD,IAAIe,SAASJ,QAAQ,KAAKN,gBAAgB;YACxC,yEAAyE;YACzE,yEAAyE;YACzE,0EAA0E;YAC1E,sEAAsE;YACtE,sEAAsE;YACtE,0EAA0E;YAC1E,0DAA0D;YAC1DW,YAAYJ,IAAI,GAAGG,SAASH,IAAI;YAChC;QACF,OAAO;YACL,6CAA6C;YAC7CE;YACA,MAAMG,QAA4B;gBAChCP,MAAMK,SAASL,IAAI;gBACnBC,UAAUI,SAASJ,QAAQ;gBAC3BC,MAAM;YACR;YACAI,YAAYJ,IAAI,GAAGK;YACnBD,cAAcC;QAChB;QACAF,WAAWA,SAASH,IAAI;IAC1B;IAEAL,mBAAmBM;IACnB,OAAOA;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/client/components/layout-router.tsx"], "sourcesContent": ["'use client'\n\nimport type {\n  <PERSON><PERSON>N<PERSON>,\n  LazyCacheNode,\n} from '../../shared/lib/app-router-types'\nimport type { LoadingModuleData } from '../../shared/lib/app-router-types'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n  Segment,\n} from '../../shared/lib/app-router-types'\nimport type { ErrorComponent } from './error-boundary'\nimport {\n  ACTION_SERVER_PATCH,\n  type FocusAndScrollRef,\n} from './router-reducer/router-reducer-types'\n\nimport React, {\n  Activity,\n  useContext,\n  use,\n  startTransition,\n  Suspense,\n  useDeferredValue,\n  type JSX,\n  type ActivityProps,\n} from 'react'\nimport ReactDOM from 'react-dom'\nimport {\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n  TemplateContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport { fetchServerResponse } from './router-reducer/fetch-server-response'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { ErrorBoundary } from './error-boundary'\nimport { matchSegment } from './match-segments'\nimport { disableSmoothScrollDuringRouteTransition } from '../../shared/lib/router/utils/disable-smooth-scroll'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { HTTPAccessFallbackBoundary } from './http-access-fallback/error-boundary'\nimport { createRouterCacheKey } from './router-reducer/create-router-cache-key'\nimport { hasInterceptionRouteInCurrentTree } from './router-reducer/reducers/has-interception-route-in-current-tree'\nimport { dispatchAppRouterAction } from './use-action-queue'\nimport { useRouterBFCache, type RouterBFCacheEntry } from './bfcache'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport {\n  NavigationPromisesContext,\n  type NavigationPromises,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { getParamValueFromCacheKey } from '../route-params'\nimport type { Params } from '../../server/request/params'\n\n/**\n * Add refetch marker to router state at the point of the current layout segment.\n * This ensures the response returned is not further down than the current layout segment.\n */\nfunction walkAddRefetch(\n  segmentPathToWalk: FlightSegmentPath | undefined,\n  treeToRecreate: FlightRouterState\n): FlightRouterState {\n  if (segmentPathToWalk) {\n    const [segment, parallelRouteKey] = segmentPathToWalk\n    const isLast = segmentPathToWalk.length === 2\n\n    if (matchSegment(treeToRecreate[0], segment)) {\n      if (treeToRecreate[1].hasOwnProperty(parallelRouteKey)) {\n        if (isLast) {\n          const subTree = walkAddRefetch(\n            undefined,\n            treeToRecreate[1][parallelRouteKey]\n          )\n          return [\n            treeToRecreate[0],\n            {\n              ...treeToRecreate[1],\n              [parallelRouteKey]: [\n                subTree[0],\n                subTree[1],\n                subTree[2],\n                'refetch',\n              ],\n            },\n          ]\n        }\n\n        return [\n          treeToRecreate[0],\n          {\n            ...treeToRecreate[1],\n            [parallelRouteKey]: walkAddRefetch(\n              segmentPathToWalk.slice(2),\n              treeToRecreate[1][parallelRouteKey]\n            ),\n          },\n        ]\n      }\n    }\n  }\n\n  return treeToRecreate\n}\n\nconst __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE = (\n  ReactDOM as any\n).__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE\n\n// TODO-APP: Replace with new React API for finding dom nodes without a `ref` when available\n/**\n * Wraps ReactDOM.findDOMNode with additional logic to hide React Strict Mode warning\n */\nfunction findDOMNode(\n  instance: React.ReactInstance | null | undefined\n): Element | Text | null {\n  // Tree-shake for server bundle\n  if (typeof window === 'undefined') return null\n\n  // __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode is null during module init.\n  // We need to lazily reference it.\n  const internal_reactDOMfindDOMNode =\n    __DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE.findDOMNode\n  return internal_reactDOMfindDOMNode(instance)\n}\n\nconst rectProperties = [\n  'bottom',\n  'height',\n  'left',\n  'right',\n  'top',\n  'width',\n  'x',\n  'y',\n] as const\n/**\n * Check if a HTMLElement is hidden or fixed/sticky position\n */\nfunction shouldSkipElement(element: HTMLElement) {\n  // we ignore fixed or sticky positioned elements since they'll likely pass the \"in-viewport\" check\n  // and will result in a situation we bail on scroll because of something like a fixed nav,\n  // even though the actual page content is offscreen\n  if (['sticky', 'fixed'].includes(getComputedStyle(element).position)) {\n    return true\n  }\n\n  // Uses `getBoundingClientRect` to check if the element is hidden instead of `offsetParent`\n  // because `offsetParent` doesn't consider document/body\n  const rect = element.getBoundingClientRect()\n  return rectProperties.every((item) => rect[item] === 0)\n}\n\n/**\n * Check if the top corner of the HTMLElement is in the viewport.\n */\nfunction topOfElementInViewport(element: HTMLElement, viewportHeight: number) {\n  const rect = element.getBoundingClientRect()\n  return rect.top >= 0 && rect.top <= viewportHeight\n}\n\n/**\n * Find the DOM node for a hash fragment.\n * If `top` the page has to scroll to the top of the page. This mirrors the browser's behavior.\n * If the hash fragment is an id, the page has to scroll to the element with that id.\n * If the hash fragment is a name, the page has to scroll to the first element with that name.\n */\nfunction getHashFragmentDomNode(hashFragment: string) {\n  // If the hash fragment is `top` the page has to scroll to the top of the page.\n  if (hashFragment === 'top') {\n    return document.body\n  }\n\n  // If the hash fragment is an id, the page has to scroll to the element with that id.\n  return (\n    document.getElementById(hashFragment) ??\n    // If the hash fragment is a name, the page has to scroll to the first element with that name.\n    document.getElementsByName(hashFragment)[0]\n  )\n}\ninterface ScrollAndFocusHandlerProps {\n  focusAndScrollRef: FocusAndScrollRef\n  children: React.ReactNode\n  segmentPath: FlightSegmentPath\n}\nclass InnerScrollAndFocusHandler extends React.Component<ScrollAndFocusHandlerProps> {\n  handlePotentialScroll = () => {\n    // Handle scroll and focus, it's only applied once in the first useEffect that triggers that changed.\n    const { focusAndScrollRef, segmentPath } = this.props\n\n    if (focusAndScrollRef.apply) {\n      // segmentPaths is an array of segment paths that should be scrolled to\n      // if the current segment path is not in the array, the scroll is not applied\n      // unless the array is empty, in which case the scroll is always applied\n      if (\n        focusAndScrollRef.segmentPaths.length !== 0 &&\n        !focusAndScrollRef.segmentPaths.some((scrollRefSegmentPath) =>\n          segmentPath.every((segment, index) =>\n            matchSegment(segment, scrollRefSegmentPath[index])\n          )\n        )\n      ) {\n        return\n      }\n\n      let domNode:\n        | ReturnType<typeof getHashFragmentDomNode>\n        | ReturnType<typeof findDOMNode> = null\n      const hashFragment = focusAndScrollRef.hashFragment\n\n      if (hashFragment) {\n        domNode = getHashFragmentDomNode(hashFragment)\n      }\n\n      // `findDOMNode` is tricky because it returns just the first child if the component is a fragment.\n      // This already caused a bug where the first child was a <link/> in head.\n      if (!domNode) {\n        domNode = findDOMNode(this)\n      }\n\n      // If there is no DOM node this layout-router level is skipped. It'll be handled higher-up in the tree.\n      if (!(domNode instanceof Element)) {\n        return\n      }\n\n      // Verify if the element is a HTMLElement and if we want to consider it for scroll behavior.\n      // If the element is skipped, try to select the next sibling and try again.\n      while (!(domNode instanceof HTMLElement) || shouldSkipElement(domNode)) {\n        if (process.env.NODE_ENV !== 'production') {\n          if (domNode.parentElement?.localName === 'head') {\n            // TODO: We enter this state when metadata was rendered as part of the page or via Next.js.\n            // This is always a bug in Next.js and caused by React hoisting metadata.\n            // We need to replace `findDOMNode` in favor of Fragment Refs (when available) so that we can skip over metadata.\n          }\n        }\n\n        // No siblings found that match the criteria are found, so handle scroll higher up in the tree instead.\n        if (domNode.nextElementSibling === null) {\n          return\n        }\n        domNode = domNode.nextElementSibling\n      }\n\n      // State is mutated to ensure that the focus and scroll is applied only once.\n      focusAndScrollRef.apply = false\n      focusAndScrollRef.hashFragment = null\n      focusAndScrollRef.segmentPaths = []\n\n      disableSmoothScrollDuringRouteTransition(\n        () => {\n          // In case of hash scroll, we only need to scroll the element into view\n          if (hashFragment) {\n            ;(domNode as HTMLElement).scrollIntoView()\n\n            return\n          }\n          // Store the current viewport height because reading `clientHeight` causes a reflow,\n          // and it won't change during this function.\n          const htmlElement = document.documentElement\n          const viewportHeight = htmlElement.clientHeight\n\n          // If the element's top edge is already in the viewport, exit early.\n          if (topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            return\n          }\n\n          // Otherwise, try scrolling go the top of the document to be backward compatible with pages\n          // scrollIntoView() called on `<html/>` element scrolls horizontally on chrome and firefox (that shouldn't happen)\n          // We could use it to scroll horizontally following RTL but that also seems to be broken - it will always scroll left\n          // scrollLeft = 0 also seems to ignore RTL and manually checking for RTL is too much hassle so we will scroll just vertically\n          htmlElement.scrollTop = 0\n\n          // Scroll to domNode if domNode is not in viewport when scrolled to top of document\n          if (!topOfElementInViewport(domNode as HTMLElement, viewportHeight)) {\n            // Scroll into view doesn't scroll horizontally by default when not needed\n            ;(domNode as HTMLElement).scrollIntoView()\n          }\n        },\n        {\n          // We will force layout by querying domNode position\n          dontForceLayout: true,\n          onlyHashChange: focusAndScrollRef.onlyHashChange,\n        }\n      )\n\n      // Mutate after scrolling so that it can be read by `disableSmoothScrollDuringRouteTransition`\n      focusAndScrollRef.onlyHashChange = false\n\n      // Set focus on the element\n      domNode.focus()\n    }\n  }\n\n  componentDidMount() {\n    this.handlePotentialScroll()\n  }\n\n  componentDidUpdate() {\n    // Because this property is overwritten in handlePotentialScroll it's fine to always run it when true as it'll be set to false for subsequent renders.\n    if (this.props.focusAndScrollRef.apply) {\n      this.handlePotentialScroll()\n    }\n  }\n\n  render() {\n    return this.props.children\n  }\n}\n\nfunction ScrollAndFocusHandler({\n  segmentPath,\n  children,\n}: {\n  segmentPath: FlightSegmentPath\n  children: React.ReactNode\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  return (\n    <InnerScrollAndFocusHandler\n      segmentPath={segmentPath}\n      focusAndScrollRef={context.focusAndScrollRef}\n    >\n      {children}\n    </InnerScrollAndFocusHandler>\n  )\n}\n\n/**\n * InnerLayoutRouter handles rendering the provided segment based on the cache.\n */\nfunction InnerLayoutRouter({\n  tree,\n  segmentPath,\n  debugNameContext,\n  cacheNode,\n  params,\n  url,\n  isActive,\n}: {\n  tree: FlightRouterState\n  segmentPath: FlightSegmentPath\n  debugNameContext: string\n  cacheNode: CacheNode\n  params: Params\n  url: string\n  isActive: boolean\n}) {\n  const context = useContext(GlobalLayoutRouterContext)\n  const parentNavPromises = useContext(NavigationPromisesContext)\n\n  if (!context) {\n    throw new Error('invariant global layout router not mounted')\n  }\n\n  const { tree: fullTree } = context\n\n  // `rsc` represents the renderable node for this segment.\n\n  // If this segment has a `prefetchRsc`, it's the statically prefetched data.\n  // We should use that on initial render instead of `rsc`. Then we'll switch\n  // to `rsc` when the dynamic response streams in.\n  //\n  // If no prefetch data is available, then we go straight to rendering `rsc`.\n  const resolvedPrefetchRsc =\n    cacheNode.prefetchRsc !== null ? cacheNode.prefetchRsc : cacheNode.rsc\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  const rsc: any = useDeferredValue(cacheNode.rsc, resolvedPrefetchRsc)\n\n  // `rsc` is either a React node or a promise for a React node, except we\n  // special case `null` to represent that this segment's data is missing. If\n  // it's a promise, we need to unwrap it so we can determine whether or not the\n  // data is missing.\n  const resolvedRsc: React.ReactNode =\n    typeof rsc === 'object' && rsc !== null && typeof rsc.then === 'function'\n      ? use(rsc)\n      : rsc\n\n  if (!resolvedRsc) {\n    // The data for this segment is not available, and there's no pending\n    // navigation that will be able to fulfill it. We need to fetch more from\n    // the server and patch the cache.\n\n    // Only fetch data for the active segment. Inactive segments (rendered\n    // offscreen for bfcache) should not trigger fetches.\n    if (isActive) {\n      // Check if there's already a pending request.\n      let lazyData = cacheNode.lazyData\n      if (lazyData === null) {\n        /**\n         * Router state with refetch marker added\n         */\n        // TODO-APP: remove ''\n        const refetchTree = walkAddRefetch(['', ...segmentPath], fullTree)\n        const includeNextUrl = hasInterceptionRouteInCurrentTree(fullTree)\n        const navigatedAt = Date.now()\n        cacheNode.lazyData = lazyData = fetchServerResponse(\n          new URL(url, location.origin),\n          {\n            flightRouterState: refetchTree,\n            nextUrl: includeNextUrl\n              ? // We always send the last next-url, not the current when\n                // performing a dynamic request. This is because we update\n                // the next-url after a navigation, but we want the same\n                // interception route to be matched that used the last\n                // next-url.\n                context.previousNextUrl || context.nextUrl\n              : null,\n          }\n        ).then((serverResponse) => {\n          startTransition(() => {\n            dispatchAppRouterAction({\n              type: ACTION_SERVER_PATCH,\n              previousTree: fullTree,\n              serverResponse,\n              navigatedAt,\n            })\n          })\n\n          return serverResponse\n        })\n\n        // Suspend while waiting for lazyData to resolve\n        use(lazyData)\n      }\n    }\n    // Suspend infinitely as `changeByServerResponse` will cause a different part of the tree to be rendered.\n    // A falsey `resolvedRsc` indicates missing data -- we should not commit that branch, and we need to wait for the data to arrive.\n    use(unresolvedThenable) as never\n  }\n\n  // If we get to this point, then we know we have something we can render.\n  let content = resolvedRsc\n\n  // In dev, we create a NavigationPromisesContext containing the instrumented promises that provide\n  // `useSelectedLayoutSegment` and `useSelectedLayoutSegments`.\n  // Promises are cached outside of render to survive suspense retries.\n  let navigationPromises: NavigationPromises | null = null\n  if (process.env.NODE_ENV !== 'production') {\n    const { createNestedLayoutNavigationPromises } =\n      require('./navigation-devtools') as typeof import('./navigation-devtools')\n\n    navigationPromises = createNestedLayoutNavigationPromises(\n      tree,\n      parentNavPromises\n    )\n  }\n\n  if (navigationPromises) {\n    content = (\n      <NavigationPromisesContext.Provider value={navigationPromises}>\n        {resolvedRsc}\n      </NavigationPromisesContext.Provider>\n    )\n  }\n\n  const subtree = (\n    // The layout router context narrows down tree and childNodes at each level.\n    <LayoutRouterContext.Provider\n      value={{\n        parentTree: tree,\n        parentCacheNode: cacheNode,\n        parentSegmentPath: segmentPath,\n        parentParams: params,\n        debugNameContext: debugNameContext,\n\n        // TODO-APP: overriding of url for parallel routes\n        url: url,\n        isActive: isActive,\n      }}\n    >\n      {content}\n    </LayoutRouterContext.Provider>\n  )\n  // Ensure root layout is not wrapped in a div as the root layout renders `<html>`\n  return subtree\n}\n\n/**\n * Renders suspense boundary with the provided \"loading\" property as the fallback.\n * If no loading property is provided it renders the children without a suspense boundary.\n */\nfunction LoadingBoundary({\n  name,\n  loading,\n  children,\n}: {\n  name: ActivityProps['name']\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n  children: React.ReactNode\n}): JSX.Element {\n  // If loading is a promise, unwrap it. This happens in cases where we haven't\n  // yet received the loading data from the server — which includes whether or\n  // not this layout has a loading component at all.\n  //\n  // It's OK to suspend here instead of inside the fallback because this\n  // promise will resolve simultaneously with the data for the segment itself.\n  // So it will never suspend for longer than it would have if we didn't use\n  // a Suspense fallback at all.\n  let loadingModuleData\n  if (\n    typeof loading === 'object' &&\n    loading !== null &&\n    typeof (loading as any).then === 'function'\n  ) {\n    const promiseForLoading = loading as Promise<LoadingModuleData>\n    loadingModuleData = use(promiseForLoading)\n  } else {\n    loadingModuleData = loading as LoadingModuleData\n  }\n\n  if (loadingModuleData) {\n    const loadingRsc = loadingModuleData[0]\n    const loadingStyles = loadingModuleData[1]\n    const loadingScripts = loadingModuleData[2]\n    return (\n      <Suspense\n        name={name}\n        fallback={\n          <>\n            {loadingStyles}\n            {loadingScripts}\n            {loadingRsc}\n          </>\n        }\n      >\n        {children}\n      </Suspense>\n    )\n  }\n\n  return <>{children}</>\n}\n\n/**\n * OuterLayoutRouter handles the current segment as well as <Offscreen> rendering of other segments.\n * It can be rendered next to each other with a different `parallelRouterKey`, allowing for Parallel routes.\n */\nexport default function OuterLayoutRouter({\n  parallelRouterKey,\n  error,\n  errorStyles,\n  errorScripts,\n  templateStyles,\n  templateScripts,\n  template,\n  notFound,\n  forbidden,\n  unauthorized,\n  segmentViewBoundaries,\n}: {\n  parallelRouterKey: string\n  error: ErrorComponent | undefined\n  errorStyles: React.ReactNode | undefined\n  errorScripts: React.ReactNode | undefined\n  templateStyles: React.ReactNode | undefined\n  templateScripts: React.ReactNode | undefined\n  template: React.ReactNode\n  notFound: React.ReactNode | undefined\n  forbidden: React.ReactNode | undefined\n  unauthorized: React.ReactNode | undefined\n  segmentViewBoundaries?: React.ReactNode\n}) {\n  const context = useContext(LayoutRouterContext)\n  if (!context) {\n    throw new Error('invariant expected layout router to be mounted')\n  }\n\n  const {\n    parentTree,\n    parentCacheNode,\n    parentSegmentPath,\n    parentParams,\n    url,\n    isActive,\n    debugNameContext,\n  } = context\n\n  // Get the CacheNode for this segment by reading it from the parent segment's\n  // child map.\n  const parentParallelRoutes = parentCacheNode.parallelRoutes\n  let segmentMap = parentParallelRoutes.get(parallelRouterKey)\n  // If the parallel router cache node does not exist yet, create it.\n  // This writes to the cache when there is no item in the cache yet. It never *overwrites* existing cache items which is why it's safe in concurrent mode.\n  if (!segmentMap) {\n    segmentMap = new Map()\n    parentParallelRoutes.set(parallelRouterKey, segmentMap)\n  }\n  const parentTreeSegment = parentTree[0]\n  const segmentPath =\n    parentSegmentPath === null\n      ? // TODO: The root segment value is currently omitted from the segment\n        // path. This has led to a bunch of special cases scattered throughout\n        // the code. We should clean this up.\n        [parallelRouterKey]\n      : parentSegmentPath.concat([parentTreeSegment, parallelRouterKey])\n\n  // The \"state\" key of a segment is the one passed to React — it represents the\n  // identity of the UI tree. Whenever the state key changes, the tree is\n  // recreated and the state is reset. In the App Router model, search params do\n  // not cause state to be lost, so two segments with the same segment path but\n  // different search params should have the same state key.\n  //\n  // The \"cache\" key of a segment, however, *does* include the search params, if\n  // it's possible that the segment accessed the search params on the server.\n  // (This only applies to page segments; layout segments cannot access search\n  // params on the server.)\n  const activeTree = parentTree[1][parallelRouterKey]\n  const activeSegment = activeTree[0]\n  const activeStateKey = createRouterCacheKey(activeSegment, true) // no search params\n\n  // At each level of the route tree, not only do we render the currently\n  // active segment — we also render the last N segments that were active at\n  // this level inside a hidden <Activity> boundary, to preserve their state\n  // if or when the user navigates to them again.\n  //\n  // bfcacheEntry is a linked list of FlightRouterStates.\n  let bfcacheEntry: RouterBFCacheEntry | null = useRouterBFCache(\n    activeTree,\n    activeStateKey\n  )\n  let children: Array<React.ReactNode> = []\n  do {\n    const tree = bfcacheEntry.tree\n    const stateKey = bfcacheEntry.stateKey\n    const segment = tree[0]\n    const cacheKey = createRouterCacheKey(segment)\n\n    // Read segment path from the parallel router cache node.\n    let cacheNode = segmentMap.get(cacheKey)\n    if (cacheNode === undefined) {\n      // When data is not available during rendering client-side we need to fetch\n      // it from the server.\n      const newLazyCacheNode: LazyCacheNode = {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1,\n      }\n\n      // Flight data fetch kicked off during render and put into the cache.\n      cacheNode = newLazyCacheNode\n      segmentMap.set(cacheKey, newLazyCacheNode)\n    }\n\n    /*\n    - Error boundary\n      - Only renders error boundary if error component is provided.\n      - Rendered for each segment to ensure they have their own error state.\n      - When gracefully degrade for bots, skip rendering error boundary.\n    - Loading boundary\n      - Only renders suspense boundary if loading components is provided.\n      - Rendered for each segment to ensure they have their own loading state.\n      - Passed to the router during rendering to ensure it can be immediately rendered when suspending on a Flight fetch.\n  */\n\n    let segmentBoundaryTriggerNode: React.ReactNode = null\n    let segmentViewStateNode: React.ReactNode = null\n    if (process.env.NODE_ENV !== 'production') {\n      const { SegmentBoundaryTriggerNode, SegmentViewStateNode } =\n        require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n\n      const pagePrefix = normalizeAppPath(url)\n      segmentViewStateNode = (\n        <SegmentViewStateNode key={pagePrefix} page={pagePrefix} />\n      )\n\n      segmentBoundaryTriggerNode = (\n        <>\n          <SegmentBoundaryTriggerNode />\n        </>\n      )\n    }\n\n    let params = parentParams\n    if (Array.isArray(segment)) {\n      // This segment contains a route param. Accumulate these as we traverse\n      // down the router tree. The result represents the set of params that\n      // the layout/page components are permitted to access below this point.\n      const paramName = segment[0]\n      const paramCacheKey = segment[1]\n      const paramType = segment[2]\n      const paramValue = getParamValueFromCacheKey(paramCacheKey, paramType)\n      if (paramValue !== null) {\n        params = {\n          ...parentParams,\n          [paramName]: paramValue,\n        }\n      }\n    }\n\n    const debugName = getBoundaryDebugNameFromSegment(segment)\n    // `debugNameContext` represents the nearest non-\"virtual\" parent segment.\n    // `getBoundaryDebugNameFromSegment` returns undefined for virtual segments.\n    // So if `debugName` is undefined, the context is passed through unchanged.\n    const childDebugNameContext = debugName ?? debugNameContext\n\n    // In practical terms, clicking this name in the Suspense DevTools\n    // should select the child slots of that layout.\n    //\n    // So the name we apply to the Activity boundary is actually based on\n    // the nearest parent segments.\n    //\n    // We skip over \"virtual\" parents, i.e. ones inserted by Next.js that\n    // don't correspond to application-defined code.\n    const isVirtual = debugName === undefined\n    const debugNameToDisplay = isVirtual ? undefined : debugNameContext\n\n    // TODO: The loading module data for a segment is stored on the parent, then\n    // applied to each of that parent segment's parallel route slots. In the\n    // simple case where there's only one parallel route (the `children` slot),\n    // this is no different from if the loading module data where stored on the\n    // child directly. But I'm not sure this actually makes sense when there are\n    // multiple parallel routes. It's not a huge issue because you always have\n    // the option to define a narrower loading boundary for a particular slot. But\n    // this sort of smells like an implementation accident to me.\n    const loadingModuleData = parentCacheNode.loading\n    let child = (\n      <TemplateContext.Provider\n        key={stateKey}\n        value={\n          <ScrollAndFocusHandler segmentPath={segmentPath}>\n            <ErrorBoundary\n              errorComponent={error}\n              errorStyles={errorStyles}\n              errorScripts={errorScripts}\n            >\n              <LoadingBoundary\n                name={debugNameToDisplay}\n                loading={loadingModuleData}\n              >\n                <HTTPAccessFallbackBoundary\n                  notFound={notFound}\n                  forbidden={forbidden}\n                  unauthorized={unauthorized}\n                >\n                  <RedirectBoundary>\n                    <InnerLayoutRouter\n                      url={url}\n                      tree={tree}\n                      params={params}\n                      cacheNode={cacheNode}\n                      segmentPath={segmentPath}\n                      debugNameContext={childDebugNameContext}\n                      isActive={isActive && stateKey === activeStateKey}\n                    />\n                    {segmentBoundaryTriggerNode}\n                  </RedirectBoundary>\n                </HTTPAccessFallbackBoundary>\n              </LoadingBoundary>\n            </ErrorBoundary>\n            {segmentViewStateNode}\n          </ScrollAndFocusHandler>\n        }\n      >\n        {templateStyles}\n        {templateScripts}\n        {template}\n      </TemplateContext.Provider>\n    )\n\n    if (process.env.NODE_ENV !== 'production') {\n      const { SegmentStateProvider } =\n        require('../../next-devtools/userspace/app/segment-explorer-node') as typeof import('../../next-devtools/userspace/app/segment-explorer-node')\n\n      child = (\n        <SegmentStateProvider key={stateKey}>\n          {child}\n          {segmentViewBoundaries}\n        </SegmentStateProvider>\n      )\n    }\n\n    if (process.env.__NEXT_CACHE_COMPONENTS) {\n      child = (\n        <Activity\n          name={debugNameToDisplay}\n          key={stateKey}\n          mode={stateKey === activeStateKey ? 'visible' : 'hidden'}\n        >\n          {child}\n        </Activity>\n      )\n    }\n\n    children.push(child)\n\n    bfcacheEntry = bfcacheEntry.next\n  } while (bfcacheEntry !== null)\n\n  return children\n}\n\nfunction getBoundaryDebugNameFromSegment(segment: Segment): string | undefined {\n  if (segment === '/') {\n    // Reached the root\n    return '/'\n  }\n  if (typeof segment === 'string') {\n    if (isVirtualLayout(segment)) {\n      return undefined\n    } else {\n      return segment + '/'\n    }\n  }\n  const paramCacheKey = segment[1]\n  return paramCacheKey + '/'\n}\n\nfunction isVirtualLayout(segment: string): boolean {\n  return (\n    // This is inserted by the loader. We should consider encoding these\n    // in a more special way instead of checking the name, to distinguish them\n    // from app-defined groups.\n    segment === '(slot)'\n  )\n}\n"], "names": ["OuterLayoutRouter", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "matchSegment", "hasOwnProperty", "subTree", "undefined", "slice", "__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "ReactDOM", "findDOMNode", "instance", "window", "internal_reactDOMfindDOMNode", "rectProperties", "shouldSkipElement", "element", "includes", "getComputedStyle", "position", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "React", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "process", "env", "NODE_ENV", "parentElement", "localName", "nextElement<PERSON><PERSON>ling", "disableSmoothScrollDuringRouteTransition", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "useContext", "GlobalLayoutRouterContext", "Error", "InnerLayoutRouter", "tree", "debugNameContext", "cacheNode", "params", "url", "isActive", "parentNavPromises", "NavigationPromisesContext", "fullTree", "resolvedPrefetchRsc", "prefetchRsc", "rsc", "useDeferredValue", "resolvedRsc", "then", "use", "lazyData", "refetchTree", "includeNextUrl", "hasInterceptionRouteInCurrentTree", "navigatedAt", "Date", "now", "fetchServerResponse", "URL", "location", "origin", "flightRouterState", "nextUrl", "previousNextUrl", "serverResponse", "startTransition", "dispatchAppRouterAction", "type", "ACTION_SERVER_PATCH", "previousTree", "unresolvedThenable", "content", "navigationPromises", "createNestedLayoutNavigationPromises", "require", "Provider", "value", "subtree", "LayoutRouterContext", "parentTree", "parentCacheNode", "parentSegmentPath", "parentParams", "LoadingBoundary", "name", "loading", "loadingModuleData", "promiseForLoading", "loadingRsc", "loadingStyles", "loadingScripts", "Suspense", "fallback", "parallel<PERSON><PERSON>er<PERSON>ey", "error", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "forbidden", "unauthorized", "segmentViewBoundaries", "parentParallelRoutes", "parallelRoutes", "segmentMap", "get", "Map", "set", "parentTreeSegment", "concat", "activeTree", "activeSegment", "activeStateKey", "createRouterCache<PERSON>ey", "bfcacheEntry", "useRouterBFCache", "stateKey", "cache<PERSON>ey", "newLazyCacheNode", "head", "prefetchHead", "segmentBoundaryTriggerNode", "segmentViewStateNode", "SegmentBoundaryTriggerNode", "SegmentViewStateNode", "pagePrefix", "normalizeAppPath", "page", "Array", "isArray", "paramName", "param<PERSON><PERSON><PERSON><PERSON>", "paramType", "paramValue", "getParamValueFromCacheKey", "debugName", "getBoundaryDebugNameFromSegment", "childDebugNameContext", "isVirtual", "debugNameToDisplay", "child", "TemplateContext", "Error<PERSON>ou<PERSON><PERSON>", "errorComponent", "HTTPAccessFallbackBoundary", "RedirectBoundary", "SegmentStateProvider", "__NEXT_CACHE_COMPONENTS", "Activity", "mode", "push", "next", "isVirtualLayout"], "mappings": "AAkOYyD,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAlOrC;;;;;+BA0hBA;;;CAGC,GACD,WAAA;;;eAAwB3D;;;;;;oCA9gBjB;iEAWA;mEACc;+CAKd;qCAC6B;oCACD;+BACL;+BACD;qCAC4B;kCACxB;gCACU;sCACN;mDACa;gCACV;yBACkB;0BACzB;iDAI1B;6BACmC;AAG1C;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,IAAIC,CAAAA,GAAAA,eAAAA,YAAY,EAACL,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACM,cAAc,CAACJ,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMI,UAAUT,eACdU,WACAR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBK,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLP,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBU,KAAK,CAAC,IACxBT,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,MAAMU,+DACJC,UAAAA,OAAQ,CACRD,4DAA4D;AAE9D,4FAA4F;AAC5F;;CAEC,GACD,SAASE,YACPC,QAAgD;IAEhD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;IAE1C,uGAAuG;IACvG,kCAAkC;IAClC,MAAMC,+BACJL,6DAA6DE,WAAW;IAC1E,OAAOG,6BAA6BF;AACtC;AAEA,MAAMG,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACC,QAAQ,CAACC,iBAAiBF,SAASG,QAAQ,GAAG;QACpE,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMC,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOP,eAAeQ,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBR,OAAoB,EAAES,cAAsB;IAC1E,MAAML,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;IAEA,qFAAqF;IACrF,OACED,SAASE,cAAc,CAACH,iBACxB,8FAA8F;IAC9FC,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE;AAE/C;AAMA,MAAMK,mCAAmCC,OAAAA,OAAK,CAACC,SAAS;IA4GtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;QAzHF,KAAA,IAAA,OAAA,IAAA,CACEN,qBAAAA,GAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAAC3C,MAAM,KAAK,KAC1C,CAACsC,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYtB,KAAK,CAAC,CAACvB,SAASiD,QAC1B7C,CAAAA,GAAAA,eAAAA,YAAY,EAACJ,SAASgD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMrB,eAAeY,kBAAkBZ,YAAY;gBAEnD,IAAIA,cAAc;oBAChBqB,UAAUtB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACqB,SAAS;oBACZA,UAAUvC,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAEuC,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMpC,kBAAkBkC,SAAU;oBACtE,wCAA2C;wBACzC,IAAIA,QAAQM,aAAa,EAAEC,cAAc,QAAQ;wBAC/C,2FAA2F;wBAC3F,yEAAyE;wBACzE,iHAAiH;wBACnH;oBACF;oBAEA,uGAAuG;oBACvG,IAAIP,QAAQQ,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAR,UAAUA,QAAQQ,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7EjB,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBZ,YAAY,GAAG;gBACjCY,kBAAkBK,YAAY,GAAG,EAAE;gBAEnCa,CAAAA,GAAAA,qBAAAA,wCAAwC,EACtC;oBACE,uEAAuE;oBACvE,IAAI9B,cAAc;;wBACdqB,QAAwBU,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAc/B,SAASgC,eAAe;oBAC5C,MAAMpC,iBAAiBmC,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAItC,uBAAuByB,SAAwBxB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7HmC,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAACvC,uBAAuByB,SAAwBxB,iBAAiB;wBACnE,0EAA0E;;wBACxEwB,QAAwBU,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBzB,kBAAkByB,cAAc;gBAClD;gBAGF,8FAA8F;gBAC9FzB,kBAAkByB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3BhB,QAAQiB,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,EAC7BvB,WAAW,EACXD,QAAQ,EAIT;IACC,MAAMyB,UAAUC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,+BAAAA,yBAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIG,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACtC,4BAAAA;QACCW,aAAaA;QACbJ,mBAAmB4B,QAAQ5B,iBAAiB;kBAE3CG;;AAGP;AAEA;;CAEC,GACD,SAAS6B,kBAAkB,EACzBC,IAAI,EACJ7B,WAAW,EACX8B,gBAAgB,EAChBC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,QAAQ,EAST;IACC,MAAMV,UAAUC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,+BAAAA,yBAAyB;IACpD,MAAMS,oBAAoBV,CAAAA,GAAAA,OAAAA,UAAU,EAACW,iCAAAA,yBAAyB;IAE9D,IAAI,CAACZ,SAAS;QACZ,MAAM,OAAA,cAAuD,CAAvD,IAAIG,MAAM,+CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAsD;IAC9D;IAEA,MAAM,EAAEE,MAAMQ,QAAQ,EAAE,GAAGb;IAE3B,yDAAyD;IAEzD,4EAA4E;IAC5E,2EAA2E;IAC3E,iDAAiD;IACjD,EAAE;IACF,4EAA4E;IAC5E,MAAMc,sBACJP,UAAUQ,WAAW,KAAK,OAAOR,UAAUQ,WAAW,GAAGR,UAAUS,GAAG;IAExE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,MAAMA,MAAWC,CAAAA,GAAAA,OAAAA,gBAAgB,EAACV,UAAUS,GAAG,EAAEF;IAEjD,wEAAwE;IACxE,2EAA2E;IAC3E,8EAA8E;IAC9E,mBAAmB;IACnB,MAAMI,cACJ,OAAOF,QAAQ,YAAYA,QAAQ,QAAQ,OAAOA,IAAIG,IAAI,KAAK,aAC3DC,CAAAA,GAAAA,OAAAA,GAAG,EAACJ,OACJA;IAEN,IAAI,CAACE,aAAa;QAChB,qEAAqE;QACrE,yEAAyE;QACzE,kCAAkC;QAElC,sEAAsE;QACtE,qDAAqD;QACrD,IAAIR,UAAU;YACZ,8CAA8C;YAC9C,IAAIW,WAAWd,UAAUc,QAAQ;YACjC,IAAIA,aAAa,MAAM;gBACrB;;SAEC,GACD,sBAAsB;gBACtB,MAAMC,cAAc9F,eAAe;oBAAC;uBAAOgD;iBAAY,EAAEqC;gBACzD,MAAMU,iBAAiBC,CAAAA,GAAAA,mCAAAA,iCAAiC,EAACX;gBACzD,MAAMY,cAAcC,KAAKC,GAAG;gBAC5BpB,UAAUc,QAAQ,GAAGA,WAAWO,CAAAA,GAAAA,qBAAAA,mBAAmB,EACjD,IAAIC,IAAIpB,KAAKqB,SAASC,MAAM,GAC5B;oBACEC,mBAAmBV;oBACnBW,SAASV,iBAEL,AACA,wDAAwD,EADE;oBAE1D,sDAAsD;oBACtD,YAAY;oBACZvB,QAAQkC,eAAe,IAAIlC,QAAQiC,OAAO,GAC1C;gBACN,GACAd,IAAI,CAAC,CAACgB;oBACNC,CAAAA,GAAAA,OAAAA,eAAe,EAAC;wBACdC,CAAAA,GAAAA,gBAAAA,uBAAuB,EAAC;4BACtBC,MAAMC,oBAAAA,mBAAmB;4BACzBC,cAAc3B;4BACdsB;4BACAV;wBACF;oBACF;oBAEA,OAAOU;gBACT;gBAEA,gDAAgD;gBAChDf,CAAAA,GAAAA,OAAAA,GAAG,EAACC;YACN;QACF;QACA,yGAAyG;QACzG,iIAAiI;QACjID,CAAAA,GAAAA,OAAAA,GAAG,EAACqB,oBAAAA,kBAAkB;IACxB;IAEA,yEAAyE;IACzE,IAAIC,UAAUxB;IAEd,kGAAkG;IAClG,8DAA8D;IAC9D,qEAAqE;IACrE,IAAIyB,qBAAgD;IACpD,IAAI3D,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,MAAM,EAAE0D,oCAAoC,EAAE,GAC5CC,QAAQ;QAEVF,qBAAqBC,qCACnBvC,MACAM;IAEJ;IAEA,IAAIgC,oBAAoB;QACtBD,UAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAC9B,iCAAAA,yBAAyB,CAACkC,QAAQ,EAAA;YAACC,OAAOJ;sBACxCzB;;IAGP;IAEA,MAAM8B,UACJ,cACA,CAAA,GAAA,YAAA,GAAA,EAACC,+BAAAA,UAD2E,SACxD,CAACH,QAAQ,EAAA;QAC3BC,OAAO;YACLG,YAAY7C;YACZ8C,iBAAiB5C;YACjB6C,mBAAmB5E;YACnB6E,cAAc7C;YACdF,kBAAkBA;YAElB,kDAAkD;YAClDG,KAAKA;YACLC,UAAUA;QACZ;kBAECgC;;IAGL,iFAAiF;IACjF,OAAOM;AACT;AAEA;;;CAGC,GACD,SAASM,gBAAgB,EACvBC,IAAI,EACJC,OAAO,EACPjF,QAAQ,EAKT;IACC,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,EAAE;IACF,sEAAsE;IACtE,4EAA4E;IAC5E,0EAA0E;IAC1E,8BAA8B;IAC9B,IAAIkF;IACJ,IACE,OAAOD,YAAY,YACnBA,YAAY,QACZ,OAAQA,QAAgBrC,IAAI,KAAK,YACjC;QACA,MAAMuC,oBAAoBF;QAC1BC,oBAAoBrC,CAAAA,GAAAA,OAAAA,GAAG,EAACsC;IAC1B,OAAO;QACLD,oBAAoBD;IACtB;IAEA,IAAIC,mBAAmB;QACrB,MAAME,aAAaF,iBAAiB,CAAC,EAAE;QACvC,MAAMG,gBAAgBH,iBAAiB,CAAC,EAAE;QAC1C,MAAMI,iBAAiBJ,iBAAiB,CAAC,EAAE;QAC3C,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACK,OAAAA,QAAQ,EAAA;YACPP,MAAMA;YACNQ,UAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAAA,YAAA,QAAA,EAAA;;oBACGH;oBACAC;oBACAF;;;sBAIJpF;;IAGP;IAEA,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBAAGA;;AACZ;AAMe,SAAShD,kBAAkB,EACxCyI,iBAAiB,EACjBC,KAAK,EACLC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,qBAAqB,EAatB;IACC,MAAM1E,UAAUC,CAAAA,GAAAA,OAAAA,UAAU,EAACgD,+BAAAA,mBAAmB;IAC9C,IAAI,CAACjD,SAAS;QACZ,MAAM,OAAA,cAA2D,CAA3D,IAAIG,MAAM,mDAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAA0D;IAClE;IAEA,MAAM,EACJ+C,UAAU,EACVC,eAAe,EACfC,iBAAiB,EACjBC,YAAY,EACZ5C,GAAG,EACHC,QAAQ,EACRJ,gBAAgB,EACjB,GAAGN;IAEJ,6EAA6E;IAC7E,aAAa;IACb,MAAM2E,uBAAuBxB,gBAAgByB,cAAc;IAC3D,IAAIC,aAAaF,qBAAqBG,GAAG,CAACd;IAC1C,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACa,YAAY;QACfA,aAAa,IAAIE;QACjBJ,qBAAqBK,GAAG,CAAChB,mBAAmBa;IAC9C;IACA,MAAMI,oBAAoB/B,UAAU,CAAC,EAAE;IACvC,MAAM1E,cACJ4E,sBAAsB,OAGlB,AADA,qCACqC,iCADiC;IAEtE;QAACY;KAAkB,GACnBZ,kBAAkB8B,MAAM,CAAC;QAACD;QAAmBjB;KAAkB;IAErE,8EAA8E;IAC9E,uEAAuE;IACvE,8EAA8E;IAC9E,6EAA6E;IAC7E,0DAA0D;IAC1D,EAAE;IACF,8EAA8E;IAC9E,2EAA2E;IAC3E,4EAA4E;IAC5E,yBAAyB;IACzB,MAAMmB,aAAajC,UAAU,CAAC,EAAE,CAACc,kBAAkB;IACnD,MAAMoB,gBAAgBD,UAAU,CAAC,EAAE;IACnC,MAAME,iBAAiBC,CAAAA,GAAAA,sBAAAA,oBAAoB,EAACF,eAAe,MAAM,mBAAmB;;IAEpF,uEAAuE;IACvE,0EAA0E;IAC1E,0EAA0E;IAC1E,+CAA+C;IAC/C,EAAE;IACF,uDAAuD;IACvD,IAAIG,eAA0CC,CAAAA,GAAAA,SAAAA,gBAAgB,EAC5DL,YACAE;IAEF,IAAI9G,WAAmC,EAAE;IACzC,GAAG;QACD,MAAM8B,OAAOkF,aAAalF,IAAI;QAC9B,MAAMoF,WAAWF,aAAaE,QAAQ;QACtC,MAAM9J,UAAU0E,IAAI,CAAC,EAAE;QACvB,MAAMqF,WAAWJ,CAAAA,GAAAA,sBAAAA,oBAAoB,EAAC3J;QAEtC,yDAAyD;QACzD,IAAI4E,YAAYsE,WAAWC,GAAG,CAACY;QAC/B,IAAInF,cAAcrE,WAAW;YAC3B,2EAA2E;YAC3E,sBAAsB;YACtB,MAAMyJ,mBAAkC;gBACtCtE,UAAU;gBACVL,KAAK;gBACLD,aAAa;gBACb6E,MAAM;gBACNC,cAAc;gBACdjB,gBAAgB,IAAIG;gBACpBvB,SAAS;gBACT/B,aAAa,CAAC;YAChB;YAEA,qEAAqE;YACrElB,YAAYoF;YACZd,WAAWG,GAAG,CAACU,UAAUC;QAC3B;QAEA;;;;;;;;;EASF,GAEE,IAAIG,6BAA8C;QAClD,IAAIC,uBAAwC;QAC5C,IAAI/G,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;YACzC,MAAM,EAAE8G,0BAA0B,EAAEC,oBAAoB,EAAE,GACxDpD,QAAQ;YAEV,MAAMqD,aAAaC,CAAAA,GAAAA,UAAAA,gBAAgB,EAAC1F;YACpCsF,uBAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACE,sBAAAA;gBAAsCG,MAAMF;eAAlBA;YAG7BJ,6BAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;0BACE,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACE,4BAAAA,CAAAA;;QAGP;QAEA,IAAIxF,SAAS6C;QACb,IAAIgD,MAAMC,OAAO,CAAC3K,UAAU;YAC1B,uEAAuE;YACvE,qEAAqE;YACrE,uEAAuE;YACvE,MAAM4K,YAAY5K,OAAO,CAAC,EAAE;YAC5B,MAAM6K,gBAAgB7K,OAAO,CAAC,EAAE;YAChC,MAAM8K,YAAY9K,OAAO,CAAC,EAAE;YAC5B,MAAM+K,aAAaC,CAAAA,GAAAA,aAAAA,yBAAyB,EAACH,eAAeC;YAC5D,IAAIC,eAAe,MAAM;gBACvBlG,SAAS;oBACP,GAAG6C,YAAY;oBACf,CAACkD,UAAU,EAAEG;gBACf;YACF;QACF;QAEA,MAAME,YAAYC,gCAAgClL;QAClD,0EAA0E;QAC1E,4EAA4E;QAC5E,2EAA2E;QAC3E,MAAMmL,wBAAwBF,aAAatG;QAE3C,kEAAkE;QAClE,gDAAgD;QAChD,EAAE;QACF,qEAAqE;QACrE,+BAA+B;QAC/B,EAAE;QACF,qEAAqE;QACrE,gDAAgD;QAChD,MAAMyG,YAAYH,cAAc1K;QAChC,MAAM8K,qBAAqBD,YAAY7K,YAAYoE;QAEnD,4EAA4E;QAC5E,wEAAwE;QACxE,2EAA2E;QAC3E,2EAA2E;QAC3E,4EAA4E;QAC5E,0EAA0E;QAC1E,8EAA8E;QAC9E,6DAA6D;QAC7D,MAAMmD,oBAAoBN,gBAAgBK,OAAO;QACjD,IAAIyD,QAAAA,WAAAA,GACF,CAAA,GAAA,YAAA,IAAA,EAACC,+BAAAA,eAAe,CAACpE,QAAQ,EAAA;YAEvBC,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAAChD,uBAAAA;gBAAsBvB,aAAaA;;kCAClC,CAAA,GAAA,YAAA,GAAA,EAAC2I,eAAAA,aAAa,EAAA;wBACZC,gBAAgBnD;wBAChBC,aAAaA;wBACbC,cAAcA;kCAEd,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAACb,iBAAAA;4BACCC,MAAMyD;4BACNxD,SAASC;sCAET,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,EAAC4D,gBAAAA,0BAA0B,EAAA;gCACzB9C,UAAUA;gCACVC,WAAWA;gCACXC,cAAcA;0CAEd,WAAA,GAAA,CAAA,GAAA,YAAA,IAAA,EAAC6C,kBAAAA,gBAAgB,EAAA;;sDACf,CAAA,GAAA,YAAA,GAAA,EAAClH,mBAAAA;4CACCK,KAAKA;4CACLJ,MAAMA;4CACNG,QAAQA;4CACRD,WAAWA;4CACX/B,aAAaA;4CACb8B,kBAAkBwG;4CAClBpG,UAAUA,YAAY+E,aAAaJ;;wCAEpCS;;;;;;oBAKRC;;;;gBAIJ3B;gBACAC;gBACAC;;WAtCImB;QA0CT,IAAIzG,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;YACzC,MAAM,EAAEqI,oBAAoB,EAAE,GAC5B1E,QAAQ;YAEVoE,QAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,IAAA,EAACM,sBAAAA;;oBACEN;oBACAvC;;eAFwBe;QAK/B;QAEA,IAAIzG,QAAQC,GAAG,CAACuI,uBAAuB,EAAE;;QAYzCjJ,SAASoJ,IAAI,CAACV;QAEd1B,eAAeA,aAAaqC,IAAI;IAClC,QAASrC,iBAAiB,KAAK;IAE/B,OAAOhH;AACT;AAEA,SAASsI,gCAAgClL,OAAgB;IACvD,IAAIA,YAAY,KAAK;QACnB,mBAAmB;QACnB,OAAO;IACT;IACA,IAAI,OAAOA,YAAY,UAAU;QAC/B,IAAIkM,gBAAgBlM,UAAU;YAC5B,OAAOO;QACT,OAAO;YACL,OAAOP,UAAU;QACnB;IACF;IACA,MAAM6K,gBAAgB7K,OAAO,CAAC,EAAE;IAChC,OAAO6K,gBAAgB;AACzB;AAEA,SAASqB,gBAAgBlM,OAAe;IACtC,OACE,AACA,oEADoE,MACM;IAC1E,2BAA2B;IAC3BA,YAAY;AAEhB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 754, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/client/components/render-from-template-context.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useContext, type JSX } from 'react'\nimport { TemplateContext } from '../../shared/lib/app-router-context.shared-runtime'\n\nexport default function RenderFromTemplateContext(): JSX.Element {\n  const children = useContext(TemplateContext)\n  return <>{children}</>\n}\n"], "names": ["RenderFromTemplateContext", "children", "useContext", "TemplateContext"], "mappings": ";;;+BAKA,WAAA;;;eAAwBA;;;;;iEAHoB;+CACZ;AAEjB,SAASA;IACtB,MAAMC,WAAWC,CAAAA,GAAAA,OAAAA,UAAU,EAACC,+BAAAA,eAAe;IAC3C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAAA,YAAA,QAAA,EAAA;kBAAGF;;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/web/spec-extension/adapters/reflect.ts"], "sourcesContent": ["export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n"], "names": ["ReflectAdapter", "get", "target", "prop", "receiver", "value", "Reflect", "bind", "set", "has", "deleteProperty"], "mappings": ";;;+BAAaA,kBAAAA;;;eAAAA;;;AAAN,MAAMA;IACX,OAAOC,IACLC,MAAS,EACTC,IAAqB,EACrBC,QAAiB,EACZ;QACL,MAAMC,QAAQC,QAAQL,GAAG,CAACC,QAAQC,MAAMC;QACxC,IAAI,OAAOC,UAAU,YAAY;YAC/B,OAAOA,MAAME,IAAI,CAACL;QACpB;QAEA,OAAOG;IACT;IAEA,OAAOG,IACLN,MAAS,EACTC,IAAqB,EACrBE,KAAU,EACVD,QAAa,EACJ;QACT,OAAOE,QAAQE,GAAG,CAACN,QAAQC,MAAME,OAAOD;IAC1C;IAEA,OAAOK,IAAsBP,MAAS,EAAEC,IAAqB,EAAW;QACtE,OAAOG,QAAQG,GAAG,CAACP,QAAQC;IAC7B;IAEA,OAAOO,eACLR,MAAS,EACTC,IAAqB,EACZ;QACT,OAAOG,QAAQI,cAAc,CAACR,QAAQC;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/shared/lib/utils/reflect-utils.ts"], "sourcesContent": ["// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nexport function describeStringPropertyAccess(target: string, prop: string) {\n  if (isDefinitelyAValidIdentifier.test(prop)) {\n    return `\\`${target}.${prop}\\``\n  }\n  return `\\`${target}[${JSON.stringify(prop)}]\\``\n}\n\nexport function describeHasCheckingStringProperty(\n  target: string,\n  prop: string\n) {\n  const stringifiedProp = JSON.stringify(prop)\n  return `\\`Reflect.has(${target}, ${stringifiedProp})\\`, \\`${stringifiedProp} in ${target}\\`, or similar`\n}\n\nexport const wellKnownProperties = new Set([\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toString',\n  'valueOf',\n  'toLocaleString',\n\n  // Promise prototype\n  'then',\n  'catch',\n  'finally',\n\n  // React Promise extension\n  'status',\n  // 'value',\n  // 'error',\n\n  // React introspection\n  'displayName',\n  '_debugInfo',\n\n  // Common tested properties\n  'toJSON',\n  '$$typeof',\n  '__esModule',\n])\n"], "names": ["describeHasCheckingStringProperty", "describeStringPropertyAccess", "wellKnownProperties", "isDefinitelyAValidIdentifier", "target", "prop", "test", "JSON", "stringify", "stringifiedProp", "Set"], "mappings": "AAAA,6EAA6E;AAC7E,iFAAiF;AACjF,0FAA0F;AAC1F,uFAAuF;AACvF,2DAA2D;;;;;;;;;;;;;;;;IAU3CA,iCAAiC,EAAA;eAAjCA;;IAPAC,4BAA4B,EAAA;eAA5BA;;IAeHC,mBAAmB,EAAA;eAAnBA;;;AAjBb,MAAMC,+BAA+B;AAE9B,SAASF,6BAA6BG,MAAc,EAAEC,IAAY;IACvE,IAAIF,6BAA6BG,IAAI,CAACD,OAAO;QAC3C,OAAO,CAAC,EAAE,EAAED,OAAO,CAAC,EAAEC,KAAK,EAAE,CAAC;IAChC;IACA,OAAO,CAAC,EAAE,EAAED,OAAO,CAAC,EAAEG,KAAKC,SAAS,CAACH,MAAM,GAAG,CAAC;AACjD;AAEO,SAASL,kCACdI,MAAc,EACdC,IAAY;IAEZ,MAAMI,kBAAkBF,KAAKC,SAAS,CAACH;IACvC,OAAO,CAAC,cAAc,EAAED,OAAO,EAAE,EAAEK,gBAAgB,OAAO,EAAEA,gBAAgB,IAAI,EAAEL,OAAO,cAAc,CAAC;AAC1G;AAEO,MAAMF,sBAAsB,IAAIQ,IAAI;IACzC;IACA;IACA;IACA;IACA;IACA;IAEA,oBAAoB;IACpB;IACA;IACA;IAEA,0BAA0B;IAC1B;IACA,WAAW;IACX,WAAW;IAEX,sBAAsB;IACtB;IACA;IAEA,2BAA2B;IAC3B;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/client/request/search-params.browser.dev.ts"], "sourcesContent": ["import type { SearchParams } from '../../server/request/search-params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nfunction makeUntrackedSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const proxiedProperties = new Set<string>()\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      warnForSyncSpread()\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedSearchParams.set(underlyingSearchParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A searchParam property was accessed directly with ${expression}. ` +\n      `\\`searchParams\\` is a Promise and must be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction warnForSyncSpread() {\n  console.error(\n    `The keys of \\`searchParams\\` were accessed directly. ` +\n      `\\`searchParams\\` is a Promise and must be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nexport function createRenderSearchParamsFromClient(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  return makeUntrackedSearchParamsWithDevWarnings(underlyingSearchParams)\n}\n"], "names": ["createRenderSearchParamsFromClient", "CachedSearchParams", "WeakMap", "makeUntrackedSearchParamsWithDevWarnings", "underlyingSearchParams", "cachedSearchParams", "get", "proxiedProperties", "Set", "promise", "Promise", "resolve", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "has", "add", "proxiedPromise", "Proxy", "target", "receiver", "Reflect", "expression", "describeStringPropertyAccess", "warnForSyncAccess", "ReflectAdapter", "set", "value", "delete", "describeHasCheckingStringProperty", "ownKeys", "warnForSyncSpread", "console", "error"], "mappings": ";;;+BAkGgBA,sCAAAA;;;eAAAA;;;yBAhGe;8BAKxB;AAGP,MAAMC,qBAAqB,IAAIC;AAE/B,SAASC,yCACPC,sBAAoC;IAEpC,MAAMC,qBAAqBJ,mBAAmBK,GAAG,CAACF;IAClD,IAAIC,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,oBAAoB,IAAIC;IAC9B,MAAMC,UAAUC,QAAQC,OAAO,CAACP;IAEhCQ,OAAOC,IAAI,CAACT,wBAAwBU,OAAO,CAAC,CAACC;QAC3C,IAAIC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLR,kBAAkBW,GAAG,CAACH;QACxB;IACF;IAEA,MAAMI,iBAAiB,IAAIC,MAAMX,SAAS;QACxCH,KAAIe,MAAM,EAAEN,IAAI,EAAEO,QAAQ;YACxB,IAAI,OAAOP,SAAS,UAAU;gBAC5B,IACE,CAACC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,SACxBR,CAAAA,kBAAkBU,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BQ,QAAQN,GAAG,CAACI,QAAQN,UAAU,KAAI,GACpC;oBACA,MAAMS,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgBV;oBAChEW,kBAAkBF;gBACpB;YACF;YACA,OAAOG,SAAAA,cAAc,CAACrB,GAAG,CAACe,QAAQN,MAAMO;QAC1C;QACAM,KAAIP,MAAM,EAAEN,IAAI,EAAEc,KAAK,EAAEP,QAAQ;YAC/B,IAAI,OAAOP,SAAS,UAAU;gBAC5BR,kBAAkBuB,MAAM,CAACf;YAC3B;YACA,OAAOQ,QAAQK,GAAG,CAACP,QAAQN,MAAMc,OAAOP;QAC1C;QACAL,KAAII,MAAM,EAAEN,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,SACxBR,CAAAA,kBAAkBU,GAAG,CAACF,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BQ,QAAQN,GAAG,CAACI,QAAQN,UAAU,KAAI,GACpC;oBACA,MAAMS,aAAaO,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAhB;oBAEFW,kBAAkBF;gBACpB;YACF;YACA,OAAOD,QAAQN,GAAG,CAACI,QAAQN;QAC7B;QACAiB,SAAQX,MAAM;YACZY;YACA,OAAOV,QAAQS,OAAO,CAACX;QACzB;IACF;IAEApB,mBAAmB2B,GAAG,CAACxB,wBAAwBe;IAC/C,OAAOA;AACT;AAEA,SAASO,kBAAkBF,UAAkB;IAC3CU,QAAQC,KAAK,CACX,CAAC,kDAAkD,EAAEX,WAAW,EAAE,CAAC,GACjE,CAAC,0GAA0G,CAAC,GAC5G,CAAC,8DAA8D,CAAC;AAEtE;AAEA,SAASS;IACPC,QAAQC,KAAK,CACX,CAAC,qDAAqD,CAAC,GACrD,CAAC,0GAA0G,CAAC,GAC5G,CAAC,8DAA8D,CAAC;AAEtE;AAEO,SAASnC,mCACdI,sBAAoC;IAEpC,OAAOD,yCAAyCC;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/client/request/search-params.browser.ts"], "sourcesContent": ["export const createRenderSearchParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (\n        require('./search-params.browser.dev') as typeof import('./search-params.browser.dev')\n      ).createRenderSearchParamsFromClient\n    : (\n        require('./search-params.browser.prod') as typeof import('./search-params.browser.prod')\n      ).createRenderSearchParamsFromClient\n"], "names": ["createRenderSearchParamsFromClient", "process", "env", "NODE_ENV", "require"], "mappings": "AACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BADdH,sCAAAA;;;eAAAA;;;AAAN,MAAMA,4EAGLI,QAAQ,kQACRJ,kCAAkC,GAElCI,QAAQ,gCACRJ,kCAAkC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/client/request/params.browser.dev.ts"], "sourcesContent": ["import type { Params } from '../../server/request/params'\n\nimport { ReflectAdapter } from '../../server/web/spec-extension/adapters/reflect'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nfunction makeDynamicallyTrackedParamsWithDevWarnings(\n  underlyingParams: Params\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n\n  const proxiedProperties = new Set<string>()\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      warnForEnumeration()\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction warnForSyncAccess(expression: string) {\n  console.error(\n    `A param property was accessed directly with ${expression}. ` +\n      `\\`params\\` is a Promise and must be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction warnForEnumeration() {\n  console.error(\n    `params are being enumerated. ` +\n      `\\`params\\` is a Promise and must be unwrapped with \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nexport function createRenderParamsFromClient(\n  clientParams: Params\n): Promise<Params> {\n  return makeDynamicallyTrackedParamsWithDevWarnings(clientParams)\n}\n"], "names": ["createRenderParamsFromClient", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "makeDynamicallyTrackedParamsWithDevWarnings", "underlyingParams", "cachedParams", "get", "promise", "Promise", "resolve", "proxiedProperties", "Set", "Object", "keys", "for<PERSON>ach", "prop", "wellKnownProperties", "has", "add", "proxiedPromise", "Proxy", "target", "receiver", "expression", "describeStringPropertyAccess", "warnForSyncAccess", "ReflectAdapter", "set", "value", "delete", "ownKeys", "warnForEnumeration", "Reflect", "console", "error", "clientParams"], "mappings": ";;;+BAgFgBA,gCAAAA;;;eAAAA;;;yBA9Ee;8BAIxB;AAGP,MAAMC,eAAe,IAAIC;AAEzB,SAASC,4CACPC,gBAAwB;IAExB,MAAMC,eAAeJ,aAAaK,GAAG,CAACF;IACtC,IAAIC,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAME,UAAUC,QAAQC,OAAO,CAACL;IAEhC,MAAMM,oBAAoB,IAAIC;IAE9BC,OAAOC,IAAI,CAACT,kBAAkBU,OAAO,CAAC,CAACC;QACrC,IAAIC,cAAAA,mBAAmB,CAACC,GAAG,CAACF,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLL,kBAAkBQ,GAAG,CAACH;QACxB;IACF;IAEA,MAAMI,iBAAiB,IAAIC,MAAMb,SAAS;QACxCD,KAAIe,MAAM,EAAEN,IAAI,EAAEO,QAAQ;YACxB,IAAI,OAAOP,SAAS,UAAU;gBAC5B,IACE,AACAL,kBAAkBO,GAAG,CAACF,OACtB,0CAFuE;oBAGvE,MAAMQ,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUT;oBAC1DU,kBAAkBF;gBACpB;YACF;YACA,OAAOG,SAAAA,cAAc,CAACpB,GAAG,CAACe,QAAQN,MAAMO;QAC1C;QACAK,KAAIN,MAAM,EAAEN,IAAI,EAAEa,KAAK,EAAEN,QAAQ;YAC/B,IAAI,OAAOP,SAAS,UAAU;gBAC5BL,kBAAkBmB,MAAM,CAACd;YAC3B;YACA,OAAOW,SAAAA,cAAc,CAACC,GAAG,CAACN,QAAQN,MAAMa,OAAON;QACjD;QACAQ,SAAQT,MAAM;YACZU;YACA,OAAOC,QAAQF,OAAO,CAACT;QACzB;IACF;IAEApB,aAAa0B,GAAG,CAACvB,kBAAkBe;IACnC,OAAOA;AACT;AAEA,SAASM,kBAAkBF,UAAkB;IAC3CU,QAAQC,KAAK,CACX,CAAC,4CAA4C,EAAEX,WAAW,EAAE,CAAC,GAC3D,CAAC,oGAAoG,CAAC,GACtG,CAAC,8DAA8D,CAAC;AAEtE;AAEA,SAASQ;IACPE,QAAQC,KAAK,CACX,CAAC,6BAA6B,CAAC,GAC7B,CAAC,oGAAoG,CAAC,GACtG,CAAC,8DAA8D,CAAC;AAEtE;AAEO,SAASlC,6BACdmC,YAAoB;IAEpB,OAAOhC,4CAA4CgC;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1064, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/client/request/params.browser.ts"], "sourcesContent": ["export const createRenderParamsFromClient =\n  process.env.NODE_ENV === 'development'\n    ? (require('./params.browser.dev') as typeof import('./params.browser.dev'))\n        .createRenderParamsFromClient\n    : (\n        require('./params.browser.prod') as typeof import('./params.browser.prod')\n      ).createRenderParamsFromClient\n"], "names": ["createRenderParamsFromClient", "process", "env", "NODE_ENV", "require"], "mappings": "AACEC,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BADdH,gCAAAA;;;eAAAA;;;AAAN,MAAMA,sEAENI,QAAQ,2PACNJ,4BAA4B,GAE7BI,QAAQ,yBACRJ,4BAA4B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1087, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/create-deduped-by-callsite-server-error-logger.ts"], "sourcesContent": ["import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Cache Components is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_CACHE_COMPONENTS\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n"], "names": ["createDedupedByCallsiteServerErrorLoggerDev", "errorRef", "current", "cache", "React", "fn", "logErrorOrWarn", "process", "env", "__NEXT_CACHE_COMPONENTS", "console", "error", "warn", "flushCurrentErrorIfNew", "key", "getMessage", "logDedupedError", "args", "message", "NODE_ENV", "callStackFrames", "Error", "stack", "split", "undefined", "length"], "mappings": "AAauBO,QAAQC,GAAG,CAACC,uBAAuB;;;;;+BA4B1CT,+CAAAA;;;eAAAA;;;+DAzCO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvB,MAAMC,WAAsC;IAAEC,SAAS;AAAK;AAE5D,iFAAiF;AACjF,MAAMC,QACJ,OAAOC,OAAMD,KAAK,KAAK,aACnBC,OAAMD,KAAK,GACX,CAACE,KAA+BA;AAEtC,2EAA2E;AAC3E,qEAAqE;AACrE,gBAAgB;AAChB,MAAMC,uDACFI,QAAQC,KAAK,aACbD,QAAQE,IAAI;AAEhB,2CAA2C;AAC3C,wGAAwG;AACxG,MAAMC,yBAAyBV,MAC7B,AACA,CAACW,yEADyE;IAExE,IAAI;QACFR,eAAeL,SAASC,OAAO;IACjC,SAAU;QACRD,SAASC,OAAO,GAAG;IACrB;AACF;AAcK,SAASF,4CACde,UAAoC;IAEpC,OAAO,SAASC,gBAAgB,GAAGC,IAAU;QAC3C,MAAMC,UAAUH,cAAcE;QAE9B,IAAIV,QAAQC,GAAG,CAACW,QAAQ,KAAK,WAAc;gBACjB;YAAxB,MAAMC,kBAAAA,CAAkB,SAAA,IAAIC,QAAQC,KAAK,KAAA,OAAA,KAAA,IAAjB,OAAmBC,KAAK,CAAC;YACjD,IAAIH,oBAAoBI,aAAaJ,gBAAgBK,MAAM,GAAG,GAAG;gBAC/DnB,eAAeY;YACjB,OAAO;gBACL,SAAS;gBACT,oBAAoB;gBACpB,uCAAuC;gBACvC,wBAAwB;gBACxB,iEAAiE;gBACjE,MAAMJ,MAAMM,eAAe,CAAC,EAAE;gBAC9BnB,SAASC,OAAO,GAAGgB;gBACnBL,uBAAuBC;YACzB;QACF,OAAO;;IAGT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1184, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/app-render/after-task-async-storage-instance.ts"], "sourcesContent": ["import type { AfterTaskAsyncStorage } from './after-task-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const afterTaskAsyncStorageInstance: AfterTaskAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["afterTaskAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;+BAGaA,iCAAAA;;;eAAAA;;;mCAF2B;AAEjC,MAAMA,gCACXC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1199, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/app-render/after-task-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { afterTaskAsyncStorageInstance as afterTaskAsyncStorage } from './after-task-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\nimport type { WorkUnitStore } from './work-unit-async-storage.external'\n\nexport interface AfterTaskStore {\n  /** The phase in which the topmost `after` was called.\n   *\n   * NOTE: Can be undefined when running `generateStaticParams`,\n   * where we only have a `workStore`, no `workUnitStore`.\n   */\n  readonly rootTaskSpawnPhase: WorkUnitStore['phase'] | undefined\n}\n\nexport type AfterTaskAsyncStorage = AsyncLocalStorage<AfterTaskStore>\n\nexport { afterTaskAsyncStorage }\n"], "names": ["afterTaskAsyncStorage"], "mappings": ";;;+BAiBSA,yBAAAA;;;eAAAA,+BAAAA,6BAAqB;;;+CAdyC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1213, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/request/utils.ts"], "sourcesContent": ["import { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\n\nexport function throwWithStaticGenerationBailoutErrorWithDynamicError(\n  route: string,\n  expression: string\n): never {\n  throw new StaticGenBailoutError(\n    `Route ${route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n  )\n}\n\nexport function throwForSearchParamsAccessInUseCache(\n  workStore: WorkStore,\n  constructorOpt: Function\n): never {\n  const error = new Error(\n    `Route ${workStore.route} used \\`searchParams\\` inside \"use cache\". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await \\`searchParams\\` outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n  )\n\n  Error.captureStackTrace(error, constructorOpt)\n  workStore.invalidDynamicUsageError ??= error\n\n  throw error\n}\n\nexport function isRequestAPICallableInsideAfter() {\n  const afterTaskStore = afterTaskAsyncStorage.getStore()\n  return afterTaskStore?.rootTaskSpawnPhase === 'action'\n}\n"], "names": ["isRequestAPICallableInsideAfter", "throwForSearchParamsAccessInUseCache", "throwWithStaticGenerationBailoutErrorWithDynamicError", "route", "expression", "StaticGenBailoutError", "workStore", "constructorOpt", "error", "Error", "captureStackTrace", "invalidDynamicUsageError", "afterTaskStore", "afterTaskAsyncStorage", "getStore", "rootTaskSpawnPhase"], "mappings": ";;;;;;;;;;;;;;;IA2BgBA,+BAA+B,EAAA;eAA/BA;;IAdAC,oCAAoC,EAAA;eAApCA;;IATAC,qDAAqD,EAAA;eAArDA;;;yCAJsB;+CACA;AAG/B,SAASA,sDACdC,KAAa,EACbC,UAAkB;IAElB,MAAM,OAAA,cAEL,CAFK,IAAIC,yBAAAA,qBAAqB,CAC7B,CAAC,MAAM,EAAEF,MAAM,4EAA4E,EAAEC,WAAW,0HAA0H,CAAC,GAD/N,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEO,SAASH,qCACdK,SAAoB,EACpBC,cAAwB;IAExB,MAAMC,QAAQ,OAAA,cAEb,CAFa,IAAIC,MAChB,CAAC,MAAM,EAAEH,UAAUH,KAAK,CAAC,2XAA2X,CAAC,GADzY,qBAAA;eAAA;oBAAA;sBAAA;IAEd;IAEAM,MAAMC,iBAAiB,CAACF,OAAOD;IAC/BD,UAAUK,wBAAwB,KAAKH;IAEvC,MAAMA;AACR;AAEO,SAASR;IACd,MAAMY,iBAAiBC,+BAAAA,qBAAqB,CAACC,QAAQ;IACrD,OAAOF,CAAAA,kBAAAA,OAAAA,KAAAA,IAAAA,eAAgBG,kBAAkB,MAAK;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1265, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/request/search-params.ts"], "sourcesContent": ["import type { WorkStore } from '../app-render/work-async-storage.external'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  annotateDynamicAccess,\n  delayUntilRuntimeStage,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStoreLegacy,\n  type PrerenderStorePPR,\n  type PrerenderStoreModern,\n  type PrerenderStoreModernRuntime,\n  type StaticPrerenderStore,\n  throwInvariantForMissingStore,\n  type RequestStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport {\n  describeStringPropertyAccess,\n  describeHasCheckingStringProperty,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  throwWithStaticGenerationBailoutErrorWithDynamicError,\n  throwForSearchParamsAccessInUseCache,\n} from './utils'\nimport { RenderStage } from '../app-render/staged-rendering'\n\nexport type SearchParams = { [key: string]: string | string[] | undefined }\n\nexport function createSearchParamsFromClient(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderSearchParams(workStore, workUnitStore)\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createSearchParamsFromClient should not be called in a runtime prerender.'\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createSearchParamsFromClient should not be called in cache contexts.'\n        )\n      case 'request':\n        return createRenderSearchParams(\n          underlyingSearchParams,\n          workStore,\n          workUnitStore\n        )\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport const createServerSearchParamsForMetadata =\n  createServerSearchParamsForServerPage\n\nexport function createServerSearchParamsForServerPage(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderSearchParams(workStore, workUnitStore)\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerSearchParamsForServerPage should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderSearchParams(\n          underlyingSearchParams,\n          workUnitStore\n        )\n      case 'request':\n        return createRenderSearchParams(\n          underlyingSearchParams,\n          workStore,\n          workUnitStore\n        )\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createPrerenderSearchParamsForClientPage(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n        // We're prerendering in a mode that aborts (cacheComponents) and should stall\n        // the promise to ensure the RSC side is considered dynamic\n        return makeHangingPromise(\n          workUnitStore.renderSignal,\n          workStore.route,\n          '`searchParams`'\n        )\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createPrerenderSearchParamsForClientPage should not be called in a runtime prerender.'\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createPrerenderSearchParamsForClientPage should not be called in cache contexts.'\n        )\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'request':\n        return Promise.resolve({})\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nfunction createStaticPrerenderSearchParams(\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  }\n\n  switch (prerenderStore.type) {\n    case 'prerender':\n    case 'prerender-client':\n      // We are in a cacheComponents (PPR or otherwise) prerender\n      return makeHangingSearchParams(workStore, prerenderStore)\n    case 'prerender-ppr':\n    case 'prerender-legacy':\n      // We are in a legacy static generation and need to interrupt the\n      // prerender when search params are accessed.\n      return makeErroringSearchParams(workStore, prerenderStore)\n    default:\n      return prerenderStore satisfies never\n  }\n}\n\nfunction createRuntimePrerenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workUnitStore: PrerenderStoreModernRuntime\n): Promise<SearchParams> {\n  return delayUntilRuntimeStage(\n    workUnitStore,\n    makeUntrackedSearchParams(underlyingSearchParams)\n  )\n}\n\nfunction createRenderSearchParams(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<SearchParams> {\n  if (workStore.forceStatic) {\n    // When using forceStatic we override all other logic and always just return an empty\n    // dictionary object.\n    return Promise.resolve({})\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      // Semantically we only need the dev tracking when running in `next dev`\n      // but since you would never use next dev with production NODE_ENV we use this\n      // as a proxy so we can statically exclude this code from production builds.\n      return makeUntrackedSearchParamsWithDevWarnings(\n        underlyingSearchParams,\n        workStore,\n        requestStore\n      )\n    } else {\n      return makeUntrackedSearchParams(underlyingSearchParams)\n    }\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedSearchParams = new WeakMap<CacheLifetime, Promise<SearchParams>>()\n\nconst CachedSearchParamsForUseCache = new WeakMap<\n  CacheLifetime,\n  Promise<SearchParams>\n>()\n\nfunction makeHangingSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreModern\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(prerenderStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = makeHangingPromise<SearchParams>(\n    prerenderStore.renderSignal,\n    workStore.route,\n    '`searchParams`'\n  )\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      switch (prop) {\n        case 'then': {\n          const expression =\n            '`await searchParams`, `searchParams.then`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'status': {\n          const expression =\n            '`use(searchParams)`, `searchParams.status`, or similar'\n          annotateDynamicAccess(expression, prerenderStore)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        default: {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n      }\n    },\n  })\n\n  CachedSearchParams.set(prerenderStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeErroringSearchParams(\n  workStore: WorkStore,\n  prerenderStore: PrerenderStoreLegacy | PrerenderStorePPR\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const underlyingSearchParams = {}\n  // For search params we don't construct a ReactPromise because we want to interrupt\n  // rendering on any property access that was not set from outside and so we only want\n  // to have properties like value and status if React sets them.\n  const promise = Promise.resolve(underlyingSearchParams)\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it.\n        // We know it isn't a dynamic access because it can only be something\n        // that was previously written to the promise and thus not an underlying searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      if (typeof prop === 'string' && prop === 'then') {\n        const expression =\n          '`await searchParams`, `searchParams.then`, or similar'\n        if (workStore.dynamicShouldError) {\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        } else if (prerenderStore.type === 'prerender-ppr') {\n          // PPR Prerender (no cacheComponents)\n          postponeWithTracking(\n            workStore.route,\n            expression,\n            prerenderStore.dynamicTracking\n          )\n        } else {\n          // Legacy Prerender\n          throwToInterruptStaticGeneration(\n            expression,\n            workStore,\n            prerenderStore\n          )\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  CachedSearchParams.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\n/**\n * This is a variation of `makeErroringSearchParams` that always throws an\n * error on access, because accessing searchParams inside of `\"use cache\"` is\n * not allowed.\n */\nexport function makeErroringSearchParamsForUseCache(\n  workStore: WorkStore\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParamsForUseCache.get(workStore)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve({})\n\n  const proxiedPromise = new Proxy(promise, {\n    get: function get(target, prop, receiver) {\n      if (Object.hasOwn(promise, prop)) {\n        // The promise has this property directly. we must return it. We know it\n        // isn't a dynamic access because it can only be something that was\n        // previously written to the promise and thus not an underlying\n        // searchParam value\n        return ReflectAdapter.get(target, prop, receiver)\n      }\n\n      if (\n        typeof prop === 'string' &&\n        (prop === 'then' || !wellKnownProperties.has(prop))\n      ) {\n        throwForSearchParamsAccessInUseCache(workStore, get)\n      }\n\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n  })\n\n  CachedSearchParamsForUseCache.set(workStore, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction makeUntrackedSearchParams(\n  underlyingSearchParams: SearchParams\n): Promise<SearchParams> {\n  const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n  if (cachedSearchParams) {\n    return cachedSearchParams\n  }\n\n  const promise = Promise.resolve(underlyingSearchParams)\n  CachedSearchParams.set(underlyingSearchParams, promise)\n\n  return promise\n}\n\nfunction makeUntrackedSearchParamsWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<SearchParams> {\n  if (requestStore.asyncApiPromises) {\n    // Do not cache the resulting promise. If we do, we'll only show the first \"awaited at\"\n    // across all segments that receive searchParams.\n    return makeUntrackedSearchParamsWithDevWarningsImpl(\n      underlyingSearchParams,\n      workStore,\n      requestStore\n    )\n  } else {\n    const cachedSearchParams = CachedSearchParams.get(underlyingSearchParams)\n    if (cachedSearchParams) {\n      return cachedSearchParams\n    }\n    const promise = makeUntrackedSearchParamsWithDevWarningsImpl(\n      underlyingSearchParams,\n      workStore,\n      requestStore\n    )\n    CachedSearchParams.set(requestStore, promise)\n    return promise\n  }\n}\n\nfunction makeUntrackedSearchParamsWithDevWarningsImpl(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<SearchParams> {\n  const promiseInitialized = { current: false }\n  const proxiedUnderlying = instrumentSearchParamsObjectWithDevWarnings(\n    underlyingSearchParams,\n    workStore,\n    promiseInitialized\n  )\n\n  let promise: Promise<SearchParams>\n  if (requestStore.asyncApiPromises) {\n    // We wrap each instance of searchParams in a `new Promise()`.\n    // This is important when all awaits are in third party which would otherwise\n    // track all the way to the internal params.\n    const sharedSearchParamsParent =\n      requestStore.asyncApiPromises.sharedSearchParamsParent\n    promise = new Promise((resolve, reject) => {\n      sharedSearchParamsParent.then(() => resolve(proxiedUnderlying), reject)\n    })\n    // @ts-expect-error\n    promise.displayName = 'searchParams'\n  } else {\n    promise = makeDevtoolsIOAwarePromise(\n      proxiedUnderlying,\n      requestStore,\n      RenderStage.Runtime\n    )\n  }\n  promise.then(\n    () => {\n      promiseInitialized.current = true\n    },\n    // If we're in staged rendering, this promise will reject if the render\n    // is aborted before it can reach the runtime stage.\n    // In that case, we have to prevent an unhandled rejection from the promise\n    // created by this `.then()` call.\n    // This does not affect the `promiseInitialized` logic above,\n    // because `proxiedUnderlying` will not be used to resolve the promise,\n    // so there's no risk of any of its properties being accessed and triggering\n    // an undesireable warning.\n    ignoreReject\n  )\n\n  return instrumentSearchParamsPromiseWithDevWarnings(\n    underlyingSearchParams,\n    promise,\n    workStore\n  )\n}\n\nfunction ignoreReject() {}\n\nfunction instrumentSearchParamsObjectWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  workStore: WorkStore,\n  promiseInitialized: { current: boolean }\n) {\n  // We have an unfortunate sequence of events that requires this initialization logic. We want to instrument the underlying\n  // searchParams object to detect if you are accessing values in dev. This is used for warnings and for things like the static prerender\n  // indicator. However when we pass this proxy to our Promise.resolve() below the VM checks if the resolved value is a promise by looking\n  // at the `.then` property. To our dynamic tracking logic this is indistinguishable from a `then` searchParam and so we would normally trigger\n  // dynamic tracking. However we know that this .then is not real dynamic access, it's just how thenables resolve in sequence. So we introduce\n  // this initialization concept so we omit the dynamic check until after we've constructed our resolved promise.\n  return new Proxy(underlyingSearchParams, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string' && promiseInitialized.current) {\n        if (workStore.dynamicShouldError) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (workStore.dynamicShouldError) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          throwWithStaticGenerationBailoutErrorWithDynamicError(\n            workStore.route,\n            expression\n          )\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      if (workStore.dynamicShouldError) {\n        const expression =\n          '`{...searchParams}`, `Object.keys(searchParams)`, or similar'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          workStore.route,\n          expression\n        )\n      }\n      return Reflect.ownKeys(target)\n    },\n  })\n}\n\nfunction instrumentSearchParamsPromiseWithDevWarnings(\n  underlyingSearchParams: SearchParams,\n  promise: Promise<SearchParams>,\n  workStore: WorkStore\n) {\n  // Track which properties we should warn for.\n  const proxiedProperties = new Set<string>()\n\n  Object.keys(underlyingSearchParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  return new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (prop === 'then' && workStore.dynamicShouldError) {\n        const expression = '`searchParams.then`'\n        throwWithStaticGenerationBailoutErrorWithDynamicError(\n          workStore.route,\n          expression\n        )\n      }\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeStringPropertyAccess('searchParams', prop)\n          warnForSyncAccess(workStore.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return Reflect.set(target, prop, value, receiver)\n    },\n    has(target, prop) {\n      if (typeof prop === 'string') {\n        if (\n          !wellKnownProperties.has(prop) &&\n          (proxiedProperties.has(prop) ||\n            // We are accessing a property that doesn't exist on the promise nor\n            // the underlying searchParams.\n            Reflect.has(target, prop) === false)\n        ) {\n          const expression = describeHasCheckingStringProperty(\n            'searchParams',\n            prop\n          )\n          warnForSyncAccess(workStore.route, expression)\n        }\n      }\n      return Reflect.has(target, prop)\n    },\n    ownKeys(target) {\n      const expression = '`Object.keys(searchParams)` or similar'\n      warnForSyncAccess(workStore.route, expression)\n      return Reflect.ownKeys(target)\n    },\n  })\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createSearchAccessError\n)\n\nfunction createSearchAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`searchParams\\` is a Promise and must be unwrapped with \\`await\\` or \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n"], "names": ["createPrerenderSearchParamsForClientPage", "createSearchParamsFromClient", "createServerSearchParamsForMetadata", "createServerSearchParamsForServerPage", "makeErroringSearchParamsForUseCache", "underlyingSearchParams", "workStore", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "createStaticPrerenderSearchParams", "InvariantError", "createRenderSearchParams", "throwInvariantForMissingStore", "createRuntimePrerenderSearchParams", "forceStatic", "Promise", "resolve", "makeHangingPromise", "renderSignal", "route", "prerenderStore", "makeHangingSearchParams", "makeErroringSearchParams", "delayUntilRuntimeStage", "makeUntrackedSearchParams", "requestStore", "process", "env", "NODE_ENV", "makeUntrackedSearchParamsWithDevWarnings", "CachedSearchParams", "WeakMap", "CachedSearchParamsForUseCache", "cachedSearchParams", "get", "promise", "proxiedPromise", "Proxy", "target", "prop", "receiver", "Object", "hasOwn", "ReflectAdapter", "expression", "annotateDynamicAccess", "set", "dynamicShouldError", "throwWithStaticGenerationBailoutErrorWithDynamicError", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "wellKnownProperties", "has", "throwForSearchParamsAccessInUseCache", "asyncApiPromises", "makeUntrackedSearchParamsWithDevWarningsImpl", "promiseInitialized", "current", "proxiedUnderlying", "instrumentSearchParamsObjectWithDevWarnings", "sharedSearchParamsParent", "reject", "then", "displayName", "makeDevtoolsIOAwarePromise", "RenderStage", "Runtime", "ignoreReject", "instrumentSearchParamsPromiseWithDevWarnings", "describeStringPropertyAccess", "describeHasCheckingStringProperty", "Reflect", "ownKeys", "proxiedProperties", "Set", "keys", "for<PERSON>ach", "add", "warnForSyncAccess", "value", "delete", "createDedupedByCallsiteServerErrorLoggerDev", "createSearchAccessError", "prefix", "Error"], "mappings": "AAyMQ4B,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;IAvFjB9B,wCAAwC,EAAA;eAAxCA;;IA3EAC,4BAA4B,EAAA;eAA5BA;;IAoCHC,mCAAmC,EAAA;eAAnCA;;IAGGC,qCAAqC,EAAA;eAArCA;;IAgQAC,mCAAmC,EAAA;eAAnCA;;;yBA5Ue;kCAMxB;8CAWA;gCACwB;uCAIxB;0DACqD;8BAKrD;uBAIA;iCACqB;AAIrB,SAASH,6BACdI,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,kCAAkCL,WAAWC;YACtD,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIK,gBAAAA,cAAc,CACtB,8EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIA,gBAAAA,cAAc,CACtB,yEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOC,yBACLR,wBACAC,WACAC;YAEJ;gBACEA;QACJ;IACF;IACAO,CAAAA,GAAAA,8BAAAA,6BAA6B;AAC/B;AAGO,MAAMZ,sCACXC;AAEK,SAASA,sCACdE,sBAAoC,EACpCC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,kCAAkCL,WAAWC;YACtD,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIK,gBAAAA,cAAc,CACtB,kFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOG,mCACLV,wBACAE;YAEJ,KAAK;gBACH,OAAOM,yBACLR,wBACAC,WACAC;YAEJ;gBACEA;QACJ;IACF;IACAO,CAAAA,GAAAA,8BAAAA,6BAA6B;AAC/B;AAEO,SAASd,yCACdM,SAAoB;IAEpB,IAAIA,UAAUU,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,MAAMX,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,8EAA8E;gBAC9E,2DAA2D;gBAC3D,OAAOS,CAAAA,GAAAA,uBAAAA,kBAAkB,EACvBZ,cAAca,YAAY,EAC1Bd,UAAUe,KAAK,EACf;YAEJ,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIT,gBAAAA,cAAc,CACtB,0FADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIA,gBAAAA,cAAc,CACtB,qFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOK,QAAQC,OAAO,CAAC,CAAC;YAC1B;gBACEX;QACJ;IACF;IACAO,CAAAA,GAAAA,8BAAAA,6BAA6B;AAC/B;AAEA,SAASH,kCACPL,SAAoB,EACpBgB,cAAoC;IAEpC,IAAIhB,UAAUU,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B;IAEA,OAAQI,eAAeZ,IAAI;QACzB,KAAK;QACL,KAAK;YACH,2DAA2D;YAC3D,OAAOa,wBAAwBjB,WAAWgB;QAC5C,KAAK;QACL,KAAK;YACH,iEAAiE;YACjE,6CAA6C;YAC7C,OAAOE,yBAAyBlB,WAAWgB;QAC7C;YACE,OAAOA;IACX;AACF;AAEA,SAASP,mCACPV,sBAAoC,EACpCE,aAA0C;IAE1C,OAAOkB,CAAAA,GAAAA,kBAAAA,sBAAsB,EAC3BlB,eACAmB,0BAA0BrB;AAE9B;AAEA,SAASQ,yBACPR,sBAAoC,EACpCC,SAAoB,EACpBqB,YAA0B;IAE1B,IAAIrB,UAAUU,WAAW,EAAE;QACzB,qFAAqF;QACrF,qBAAqB;QACrB,OAAOC,QAAQC,OAAO,CAAC,CAAC;IAC1B,OAAO;QACL,wCAA4C;YAC1C,wEAAwE;YACxE,8EAA8E;YAC9E,4EAA4E;YAC5E,OAAOa,yCACL1B,wBACAC,WACAqB;QAEJ,OAAO;;IAGT;AACF;AAGA,MAAMK,qBAAqB,IAAIC;AAE/B,MAAMC,gCAAgC,IAAID;AAK1C,SAASV,wBACPjB,SAAoB,EACpBgB,cAAoC;IAEpC,MAAMa,qBAAqBH,mBAAmBI,GAAG,CAACd;IAClD,IAAIa,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUlB,CAAAA,GAAAA,uBAAAA,kBAAkB,EAChCG,eAAeF,YAAY,EAC3Bd,UAAUe,KAAK,EACf;IAGF,MAAMiB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOI,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,OAAQD;gBACN,KAAK;oBAAQ;wBACX,MAAMK,aACJ;wBACFC,CAAAA,GAAAA,kBAAAA,qBAAqB,EAACD,YAAYxB;wBAClC,OAAOuB,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBACA,KAAK;oBAAU;wBACb,MAAMI,aACJ;wBACFC,CAAAA,GAAAA,kBAAAA,qBAAqB,EAACD,YAAYxB;wBAClC,OAAOuB,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;gBAEA;oBAAS;wBACP,OAAOG,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;oBAC1C;YACF;QACF;IACF;IAEAV,mBAAmBgB,GAAG,CAAC1B,gBAAgBgB;IACvC,OAAOA;AACT;AAEA,SAASd,yBACPlB,SAAoB,EACpBgB,cAAwD;IAExD,MAAMa,qBAAqBH,mBAAmBI,GAAG,CAAC9B;IAClD,IAAI6B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAM9B,yBAAyB,CAAC;IAChC,mFAAmF;IACnF,qFAAqF;IACrF,+DAA+D;IAC/D,MAAMgC,UAAUpB,QAAQC,OAAO,CAACb;IAEhC,MAAMiC,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,6DAA6D;gBAC7D,qEAAqE;gBACrE,0FAA0F;gBAC1F,OAAOI,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,IAAI,OAAOD,SAAS,YAAYA,SAAS,QAAQ;gBAC/C,MAAMK,aACJ;gBACF,IAAIxC,UAAU2C,kBAAkB,EAAE;oBAChCC,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD5C,UAAUe,KAAK,EACfyB;gBAEJ,OAAO,IAAIxB,eAAeZ,IAAI,KAAK,iBAAiB;oBAClD,qCAAqC;oBACrCyC,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClB7C,UAAUe,KAAK,EACfyB,YACAxB,eAAe8B,eAAe;gBAElC,OAAO;oBACL,mBAAmB;oBACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BP,YACAxC,WACAgB;gBAEJ;YACF;YACA,OAAOuB,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;IACF;IAEAV,mBAAmBgB,GAAG,CAAC1C,WAAWgC;IAClC,OAAOA;AACT;AAOO,SAASlC,oCACdE,SAAoB;IAEpB,MAAM6B,qBAAqBD,8BAA8BE,GAAG,CAAC9B;IAC7D,IAAI6B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUpB,QAAQC,OAAO,CAAC,CAAC;IAEjC,MAAMoB,iBAAiB,IAAIC,MAAMF,SAAS;QACxCD,KAAK,SAASA,IAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACtC,IAAIC,OAAOC,MAAM,CAACP,SAASI,OAAO;gBAChC,wEAAwE;gBACxE,mEAAmE;gBACnE,+DAA+D;gBAC/D,oBAAoB;gBACpB,OAAOI,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;YAC1C;YAEA,IACE,OAAOD,SAAS,YACfA,CAAAA,SAAS,UAAU,CAACa,cAAAA,mBAAmB,CAACC,GAAG,CAACd,KAAI,GACjD;gBACAe,CAAAA,GAAAA,OAAAA,oCAAoC,EAAClD,WAAW8B;YAClD;YAEA,OAAOS,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;IACF;IAEAR,8BAA8Bc,GAAG,CAAC1C,WAAWgC;IAC7C,OAAOA;AACT;AAEA,SAASZ,0BACPrB,sBAAoC;IAEpC,MAAM8B,qBAAqBH,mBAAmBI,GAAG,CAAC/B;IAClD,IAAI8B,oBAAoB;QACtB,OAAOA;IACT;IAEA,MAAME,UAAUpB,QAAQC,OAAO,CAACb;IAChC2B,mBAAmBgB,GAAG,CAAC3C,wBAAwBgC;IAE/C,OAAOA;AACT;AAEA,SAASN,yCACP1B,sBAAoC,EACpCC,SAAoB,EACpBqB,YAA0B;IAE1B,IAAIA,aAAa8B,gBAAgB,EAAE;QACjC,uFAAuF;QACvF,iDAAiD;QACjD,OAAOC,6CACLrD,wBACAC,WACAqB;IAEJ,OAAO;QACL,MAAMQ,qBAAqBH,mBAAmBI,GAAG,CAAC/B;QAClD,IAAI8B,oBAAoB;YACtB,OAAOA;QACT;QACA,MAAME,UAAUqB,6CACdrD,wBACAC,WACAqB;QAEFK,mBAAmBgB,GAAG,CAACrB,cAAcU;QACrC,OAAOA;IACT;AACF;AAEA,SAASqB,6CACPrD,sBAAoC,EACpCC,SAAoB,EACpBqB,YAA0B;IAE1B,MAAMgC,qBAAqB;QAAEC,SAAS;IAAM;IAC5C,MAAMC,oBAAoBC,4CACxBzD,wBACAC,WACAqD;IAGF,IAAItB;IACJ,IAAIV,aAAa8B,gBAAgB,EAAE;QACjC,8DAA8D;QAC9D,6EAA6E;QAC7E,4CAA4C;QAC5C,MAAMM,2BACJpC,aAAa8B,gBAAgB,CAACM,wBAAwB;QACxD1B,UAAU,IAAIpB,QAAQ,CAACC,SAAS8C;YAC9BD,yBAAyBE,IAAI,CAAC,IAAM/C,QAAQ2C,oBAAoBG;QAClE;QACA,mBAAmB;QACnB3B,QAAQ6B,WAAW,GAAG;IACxB,OAAO;QACL7B,UAAU8B,CAAAA,GAAAA,uBAAAA,0BAA0B,EAClCN,mBACAlC,cACAyC,iBAAAA,WAAW,CAACC,OAAO;IAEvB;IACAhC,QAAQ4B,IAAI,CACV;QACEN,mBAAmBC,OAAO,GAAG;IAC/B,GACA,AACA,oDAAoD,mBADmB;IAEvE,2EAA2E;IAC3E,kCAAkC;IAClC,6DAA6D;IAC7D,uEAAuE;IACvE,4EAA4E;IAC5E,2BAA2B;IAC3BU;IAGF,OAAOC,6CACLlE,wBACAgC,SACA/B;AAEJ;AAEA,SAASgE,gBAAgB;AAEzB,SAASR,4CACPzD,sBAAoC,EACpCC,SAAoB,EACpBqD,kBAAwC;IAExC,0HAA0H;IAC1H,uIAAuI;IACvI,wIAAwI;IACxI,8IAA8I;IAC9I,6IAA6I;IAC7I,+GAA+G;IAC/G,OAAO,IAAIpB,MAAMlC,wBAAwB;QACvC+B,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,YAAYkB,mBAAmBC,OAAO,EAAE;gBAC1D,IAAItD,UAAU2C,kBAAkB,EAAE;oBAChC,MAAMH,aAAa0B,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgB/B;oBAChES,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD5C,UAAUe,KAAK,EACfyB;gBAEJ;YACF;YACA,OAAOD,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAa,KAAIf,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IAAInC,UAAU2C,kBAAkB,EAAE;oBAChC,MAAMH,aAAa2B,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAhC;oBAEFS,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD5C,UAAUe,KAAK,EACfyB;gBAEJ;YACF;YACA,OAAO4B,QAAQnB,GAAG,CAACf,QAAQC;QAC7B;QACAkC,SAAQnC,MAAM;YACZ,IAAIlC,UAAU2C,kBAAkB,EAAE;gBAChC,MAAMH,aACJ;gBACFI,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD5C,UAAUe,KAAK,EACfyB;YAEJ;YACA,OAAO4B,QAAQC,OAAO,CAACnC;QACzB;IACF;AACF;AAEA,SAAS+B,6CACPlE,sBAAoC,EACpCgC,OAA8B,EAC9B/B,SAAoB;IAEpB,6CAA6C;IAC7C,MAAMsE,oBAAoB,IAAIC;IAE9BlC,OAAOmC,IAAI,CAACzE,wBAAwB0E,OAAO,CAAC,CAACtC;QAC3C,IAAIa,cAAAA,mBAAmB,CAACC,GAAG,CAACd,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLmC,kBAAkBI,GAAG,CAACvC;QACxB;IACF;IAEA,OAAO,IAAIF,MAAMF,SAAS;QACxBD,KAAII,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAID,SAAS,UAAUnC,UAAU2C,kBAAkB,EAAE;gBACnD,MAAMH,aAAa;gBACnBI,CAAAA,GAAAA,OAAAA,qDAAqD,EACnD5C,UAAUe,KAAK,EACfyB;YAEJ;YACA,IAAI,OAAOL,SAAS,UAAU;gBAC5B,IACE,CAACa,cAAAA,mBAAmB,CAACC,GAAG,CAACd,SACxBmC,CAAAA,kBAAkBrB,GAAG,CAACd,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BiC,QAAQnB,GAAG,CAACf,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMK,aAAa0B,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,gBAAgB/B;oBAChEwC,kBAAkB3E,UAAUe,KAAK,EAAEyB;gBACrC;YACF;YACA,OAAOD,SAAAA,cAAc,CAACT,GAAG,CAACI,QAAQC,MAAMC;QAC1C;QACAM,KAAIR,MAAM,EAAEC,IAAI,EAAEyC,KAAK,EAAExC,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5BmC,kBAAkBO,MAAM,CAAC1C;YAC3B;YACA,OAAOiC,QAAQ1B,GAAG,CAACR,QAAQC,MAAMyC,OAAOxC;QAC1C;QACAa,KAAIf,MAAM,EAAEC,IAAI;YACd,IAAI,OAAOA,SAAS,UAAU;gBAC5B,IACE,CAACa,cAAAA,mBAAmB,CAACC,GAAG,CAACd,SACxBmC,CAAAA,kBAAkBrB,GAAG,CAACd,SACrB,oEAAoE;gBACpE,+BAA+B;gBAC/BiC,QAAQnB,GAAG,CAACf,QAAQC,UAAU,KAAI,GACpC;oBACA,MAAMK,aAAa2B,CAAAA,GAAAA,cAAAA,iCAAiC,EAClD,gBACAhC;oBAEFwC,kBAAkB3E,UAAUe,KAAK,EAAEyB;gBACrC;YACF;YACA,OAAO4B,QAAQnB,GAAG,CAACf,QAAQC;QAC7B;QACAkC,SAAQnC,MAAM;YACZ,MAAMM,aAAa;YACnBmC,kBAAkB3E,UAAUe,KAAK,EAAEyB;YACnC,OAAO4B,QAAQC,OAAO,CAACnC;QACzB;IACF;AACF;AAEA,MAAMyC,oBAAoBG,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnEC;AAGF,SAASA,wBACPhE,KAAyB,EACzByB,UAAkB;IAElB,MAAMwC,SAASjE,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAIkE,MACT,GAAGD,OAAO,KAAK,EAAExC,WAAW,EAAE,CAAC,GAC7B,CAAC,uHAAuH,CAAC,GACzH,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1698, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/app-render/dynamic-access-async-storage-instance.ts"], "sourcesContent": ["import { createAsyncLocalStorage } from './async-local-storage'\nimport type { DynamicAccessStorage } from './dynamic-access-async-storage.external'\n\nexport const dynamicAccessAsyncStorageInstance: DynamicAccessStorage =\n  createAsyncLocalStorage()\n"], "names": ["dynamicAccessAsyncStorageInstance", "createAsyncLocalStorage"], "mappings": ";;;+BAGaA,qCAAAA;;;eAAAA;;;mCAH2B;AAGjC,MAAMA,oCACXC,CAAAA,GAAAA,mBAAAA,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1713, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/app-render/dynamic-access-async-storage.external.ts"], "sourcesContent": ["import type { AsyncLocalStorage } from 'async_hooks'\n\n// Share the instance module in the next-shared layer\nimport { dynamicAccessAsyncStorageInstance } from './dynamic-access-async-storage-instance' with { 'turbopack-transition': 'next-shared' }\n\nexport interface DynamicAccessAsyncStore {\n  readonly abortController: AbortController\n}\n\nexport type DynamicAccessStorage = AsyncLocalStorage<DynamicAccessAsyncStore>\nexport { dynamicAccessAsyncStorageInstance as dynamicAccessAsyncStorage }\n"], "names": ["dynamicAccessAsyncStorage", "dynamicAccessAsyncStorageInstance"], "mappings": ";;;+BAU8CA,6BAAAA;;;eAArCC,mCAAAA,iCAAiC;;;mDAPQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1727, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/server/request/params.ts"], "sourcesContent": ["import {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type { OpaqueFallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  delayUntilRuntimeStage,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type StaticPrerenderStoreModern,\n  type StaticPrerenderStore,\n  throwInvariantForMissingStore,\n  type PrerenderStoreModernRuntime,\n  type RequestStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport {\n  makeDevtoolsIOAwarePromise,\n  makeHangingPromise,\n} from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { dynamicAccessAsyncStorage } from '../app-render/dynamic-access-async-storage.external'\nimport { RenderStage } from '../app-render/staged-rendering'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createParamsFromClient should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        throw new InvariantError(\n          'createParamsFromClient should not be called in a runtime prerender.'\n        )\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore,\n            workUnitStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerParamsForRoute should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderParams(underlyingParams, workUnitStore)\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore,\n            workUnitStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createStaticPrerenderParams(\n          underlyingParams,\n          workStore,\n          workUnitStore\n        )\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createServerParamsForServerSegment should not be called in cache contexts.'\n        )\n      case 'prerender-runtime':\n        return createRuntimePrerenderParams(underlyingParams, workUnitStore)\n      case 'request':\n        if (process.env.NODE_ENV === 'development') {\n          // Semantically we only need the dev tracking when running in `next dev`\n          // but since you would never use next dev with production NODE_ENV we use this\n          // as a proxy so we can statically exclude this code from production builds.\n          const devFallbackParams = workUnitStore.devFallbackParams\n          return createRenderParamsInDev(\n            underlyingParams,\n            devFallbackParams,\n            workStore,\n            workUnitStore\n          )\n        } else {\n          return createRenderParamsInProd(underlyingParams)\n        }\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  throwInvariantForMissingStore()\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params\n): Promise<Params> {\n  const workStore = workAsyncStorage.getStore()\n  if (!workStore) {\n    throw new InvariantError(\n      'Missing workStore in createPrerenderParamsForClientSegment'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-client':\n        const fallbackParams = workUnitStore.fallbackRouteParams\n        if (fallbackParams) {\n          for (let key in underlyingParams) {\n            if (fallbackParams.has(key)) {\n              // This params object has one or more fallback params, so we need\n              // to consider the awaiting of this params object \"dynamic\". Since\n              // we are in cacheComponents mode we encode this as a promise that never\n              // resolves.\n              return makeHangingPromise(\n                workUnitStore.renderSignal,\n                workStore.route,\n                '`params`'\n              )\n            }\n          }\n        }\n        break\n      case 'cache':\n      case 'private-cache':\n      case 'unstable-cache':\n        throw new InvariantError(\n          'createPrerenderParamsForClientSegment should not be called in cache contexts.'\n        )\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n      case 'prerender-runtime':\n      case 'request':\n        break\n      default:\n        workUnitStore satisfies never\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createStaticPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStore\n): Promise<Params> {\n  switch (prerenderStore.type) {\n    case 'prerender':\n    case 'prerender-client': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            // This params object has one or more fallback params, so we need\n            // to consider the awaiting of this params object \"dynamic\". Since\n            // we are in cacheComponents mode we encode this as a promise that never\n            // resolves.\n            return makeHangingParams(\n              underlyingParams,\n              workStore,\n              prerenderStore\n            )\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-ppr': {\n      const fallbackParams = prerenderStore.fallbackRouteParams\n      if (fallbackParams) {\n        for (const key in underlyingParams) {\n          if (fallbackParams.has(key)) {\n            return makeErroringParams(\n              underlyingParams,\n              fallbackParams,\n              workStore,\n              prerenderStore\n            )\n          }\n        }\n      }\n      break\n    }\n    case 'prerender-legacy':\n      break\n    default:\n      prerenderStore satisfies never\n  }\n\n  return makeUntrackedParams(underlyingParams)\n}\n\nfunction createRuntimePrerenderParams(\n  underlyingParams: Params,\n  workUnitStore: PrerenderStoreModernRuntime\n): Promise<Params> {\n  return delayUntilRuntimeStage(\n    workUnitStore,\n    makeUntrackedParams(underlyingParams)\n  )\n}\n\nfunction createRenderParamsInProd(underlyingParams: Params): Promise<Params> {\n  return makeUntrackedParams(underlyingParams)\n}\n\nfunction createRenderParamsInDev(\n  underlyingParams: Params,\n  devFallbackParams: OpaqueFallbackRouteParams | null | undefined,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<Params> {\n  let hasFallbackParams = false\n  if (devFallbackParams) {\n    for (let key in underlyingParams) {\n      if (devFallbackParams.has(key)) {\n        hasFallbackParams = true\n        break\n      }\n    }\n  }\n\n  return makeDynamicallyTrackedParamsWithDevWarnings(\n    underlyingParams,\n    hasFallbackParams,\n    workStore,\n    requestStore\n  )\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nconst fallbackParamsProxyHandler: ProxyHandler<Promise<Params>> = {\n  get: function get(target, prop, receiver) {\n    if (prop === 'then' || prop === 'catch' || prop === 'finally') {\n      const originalMethod = ReflectAdapter.get(target, prop, receiver)\n\n      return {\n        [prop]: (...args: unknown[]) => {\n          const store = dynamicAccessAsyncStorage.getStore()\n\n          if (store) {\n            store.abortController.abort(\n              new Error(`Accessed fallback \\`params\\` during prerendering.`)\n            )\n          }\n\n          return new Proxy(\n            originalMethod.apply(target, args),\n            fallbackParamsProxyHandler\n          )\n        },\n      }[prop]\n    }\n\n    return ReflectAdapter.get(target, prop, receiver)\n  },\n}\n\nfunction makeHangingParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: StaticPrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = new Proxy(\n    makeHangingPromise<Params>(\n      prerenderStore.renderSignal,\n      workStore.route,\n      '`params`'\n    ),\n    fallbackParamsProxyHandler\n  )\n\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeErroringParams(\n  underlyingParams: Params,\n  fallbackParams: OpaqueFallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when cacheComponents is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no cacheComponents)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedParamsWithDevWarnings(\n  underlyingParams: Params,\n  hasFallbackParams: boolean,\n  workStore: WorkStore,\n  requestStore: RequestStore\n): Promise<Params> {\n  if (requestStore.asyncApiPromises && hasFallbackParams) {\n    // We wrap each instance of params in a `new Promise()`, because deduping\n    // them across requests doesn't work anyway and this let us show each\n    // await a different set of values. This is important when all awaits\n    // are in third party which would otherwise track all the way to the\n    // internal params.\n    const sharedParamsParent = requestStore.asyncApiPromises.sharedParamsParent\n    const promise: Promise<Params> = new Promise((resolve, reject) => {\n      sharedParamsParent.then(() => resolve(underlyingParams), reject)\n    })\n    // @ts-expect-error\n    promise.displayName = 'params'\n    return instrumentParamsPromiseWithDevWarnings(\n      underlyingParams,\n      promise,\n      workStore\n    )\n  }\n\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = hasFallbackParams\n    ? makeDevtoolsIOAwarePromise(\n        underlyingParams,\n        requestStore,\n        RenderStage.Runtime\n      )\n    : // We don't want to force an environment transition when this params is not part of the fallback params set\n      Promise.resolve(underlyingParams)\n\n  const proxiedPromise = instrumentParamsPromiseWithDevWarnings(\n    underlyingParams,\n    promise,\n    workStore\n  )\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction instrumentParamsPromiseWithDevWarnings(\n  underlyingParams: Params,\n  promise: Promise<Params>,\n  workStore: WorkStore\n): Promise<Params> {\n  // Track which properties we should warn for.\n  const proxiedProperties = new Set<string>()\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      proxiedProperties.add(prop)\n    }\n  })\n\n  return new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          warnForSyncAccess(workStore.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      warnForSyncAccess(workStore.route, expression)\n      return Reflect.ownKeys(target)\n    },\n  })\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` is a Promise and must be unwrapped with \\`await\\` or \\`React.use()\\` before accessing its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n"], "names": ["createParamsFromClient", "createPrerenderParamsForClientSegment", "createServerParamsForMetadata", "createServerParamsForRoute", "createServerParamsForServerSegment", "underlyingParams", "workStore", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "createStaticPrerenderParams", "InvariantError", "process", "env", "NODE_ENV", "devFallbackParams", "createRenderParamsInDev", "createRenderParamsInProd", "throwInvariantForMissingStore", "createRuntimePrerenderParams", "workAsyncStorage", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "key", "has", "makeHangingPromise", "renderSignal", "route", "Promise", "resolve", "prerenderStore", "makeHangingParams", "makeErroringParams", "makeUntrackedParams", "delayUntilRuntimeStage", "requestStore", "hasFallbackParams", "makeDynamicallyTrackedParamsWithDevWarnings", "C<PERSON>d<PERSON><PERSON><PERSON>", "WeakMap", "fallbackParamsProxyHandler", "get", "target", "prop", "receiver", "originalMethod", "ReflectAdapter", "args", "store", "dynamicAccessAsyncStorage", "abortController", "abort", "Error", "Proxy", "apply", "cachedParams", "promise", "set", "augmentedUnderlying", "Object", "keys", "for<PERSON>ach", "wellKnownProperties", "defineProperty", "expression", "describeStringPropertyAccess", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "enumerable", "asyncApiPromises", "sharedParamsParent", "reject", "then", "displayName", "instrumentParamsPromiseWithDevWarnings", "makeDevtoolsIOAwarePromise", "RenderStage", "Runtime", "proxiedPromise", "proxiedProperties", "Set", "add", "warnForSyncAccess", "value", "delete", "ownKeys", "Reflect", "createDedupedByCallsiteServerErrorLoggerDev", "createParamsAccessError", "prefix"], "mappings": "AAkEYa,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;IA3BrBf,sBAAsB,EAAA;eAAtBA;;IAiJAC,qCAAqC,EAAA;eAArCA;;IA/FHC,6BAA6B,EAAA;eAA7BA;;IAGGC,0BAA0B,EAAA;eAA1BA;;IA8CAC,kCAAkC,EAAA;eAAlCA;;;0CAvIT;yBAGwB;kCAKxB;8CAWA;gCACwB;8BAIxB;uCAIA;0DACqD;mDAClB;iCACd;AAKrB,SAASJ,uBACdK,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BACLN,kBACAC,WACAC;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIK,gBAAAA,cAAc,CACtB,mEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIA,gBAAAA,cAAc,CACtB,wEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,wCAA4C;oBAC1C,wEAAwE;oBACxE,8EAA8E;oBAC9E,4EAA4E;oBAC5E,MAAMI,oBAAoBT,cAAcS,iBAAiB;oBACzD,OAAOC,wBACLZ,kBACAW,mBACAV,WACAC;gBAEJ,OAAO;;YAGT;gBACEA;QACJ;IACF;IACAY,CAAAA,GAAAA,8BAAAA,6BAA6B;AAC/B;AAIO,MAAMjB,gCAAgCE;AAGtC,SAASD,2BACdE,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BACLN,kBACAC,WACAC;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIK,gBAAAA,cAAc,CACtB,uEADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOQ,6BAA6Bf,kBAAkBE;YACxD,KAAK;gBACH,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;oBAC1C,wEAAwE;oBACxE,8EAA8E;oBAC9E,4EAA4E;oBAC5E,MAAMC,oBAAoBT,cAAcS,iBAAiB;oBACzD,OAAOC,wBACLZ,kBACAW,mBACAV,WACAC;gBAEJ,OAAO;;YAGT;gBACEA;QACJ;IACF;IACAY,CAAAA,GAAAA,8BAAAA,6BAA6B;AAC/B;AAEO,SAASf,mCACdC,gBAAwB,EACxBC,SAAoB;IAEpB,MAAMC,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAOC,4BACLN,kBACAC,WACAC;YAEJ,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIK,gBAAAA,cAAc,CACtB,+EADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;gBACH,OAAOQ,6BAA6Bf,kBAAkBE;YACxD,KAAK;gBACH,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;oBAC1C,wEAAwE;oBACxE,8EAA8E;oBAC9E,4EAA4E;oBAC5E,MAAMC,oBAAoBT,cAAcS,iBAAiB;oBACzD,OAAOC,wBACLZ,kBACAW,mBACAV,WACAC;gBAEJ,OAAO;;YAGT;gBACEA;QACJ;IACF;IACAY,CAAAA,GAAAA,8BAAAA,6BAA6B;AAC/B;AAEO,SAASlB,sCACdI,gBAAwB;IAExB,MAAMC,YAAYe,0BAAAA,gBAAgB,CAACZ,QAAQ;IAC3C,IAAI,CAACH,WAAW;QACd,MAAM,OAAA,cAEL,CAFK,IAAIM,gBAAAA,cAAc,CACtB,+DADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAML,gBAAgBC,8BAAAA,oBAAoB,CAACC,QAAQ;IACnD,IAAIF,eAAe;QACjB,OAAQA,cAAcG,IAAI;YACxB,KAAK;YACL,KAAK;gBACH,MAAMY,iBAAiBf,cAAcgB,mBAAmB;gBACxD,IAAID,gBAAgB;oBAClB,IAAK,IAAIE,OAAOnB,iBAAkB;wBAChC,IAAIiB,eAAeG,GAAG,CAACD,MAAM;4BAC3B,iEAAiE;4BACjE,kEAAkE;4BAClE,wEAAwE;4BACxE,YAAY;4BACZ,OAAOE,CAAAA,GAAAA,uBAAAA,kBAAkB,EACvBnB,cAAcoB,YAAY,EAC1BrB,UAAUsB,KAAK,EACf;wBAEJ;oBACF;gBACF;gBACA;YACF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,MAAM,OAAA,cAEL,CAFK,IAAIhB,gBAAAA,cAAc,CACtB,kFADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACEL;QACJ;IACF;IACA,mFAAmF;IACnF,gGAAgG;IAChG,mBAAmB;IACnB,OAAOsB,QAAQC,OAAO,CAACzB;AACzB;AAEA,SAASM,4BACPN,gBAAwB,EACxBC,SAAoB,EACpByB,cAAoC;IAEpC,OAAQA,eAAerB,IAAI;QACzB,KAAK;QACL,KAAK;YAAoB;gBACvB,MAAMY,iBAAiBS,eAAeR,mBAAmB;gBACzD,IAAID,gBAAgB;oBAClB,IAAK,MAAME,OAAOnB,iBAAkB;wBAClC,IAAIiB,eAAeG,GAAG,CAACD,MAAM;4BAC3B,iEAAiE;4BACjE,kEAAkE;4BAClE,wEAAwE;4BACxE,YAAY;4BACZ,OAAOQ,kBACL3B,kBACAC,WACAyB;wBAEJ;oBACF;gBACF;gBACA;YACF;QACA,KAAK;YAAiB;gBACpB,MAAMT,iBAAiBS,eAAeR,mBAAmB;gBACzD,IAAID,gBAAgB;oBAClB,IAAK,MAAME,OAAOnB,iBAAkB;wBAClC,IAAIiB,eAAeG,GAAG,CAACD,MAAM;4BAC3B,OAAOS,mBACL5B,kBACAiB,gBACAhB,WACAyB;wBAEJ;oBACF;gBACF;gBACA;YACF;QACA,KAAK;YACH;QACF;YACEA;IACJ;IAEA,OAAOG,oBAAoB7B;AAC7B;AAEA,SAASe,6BACPf,gBAAwB,EACxBE,aAA0C;IAE1C,OAAO4B,CAAAA,GAAAA,kBAAAA,sBAAsB,EAC3B5B,eACA2B,oBAAoB7B;AAExB;AAEA,SAASa,yBAAyBb,gBAAwB;IACxD,OAAO6B,oBAAoB7B;AAC7B;AAEA,SAASY,wBACPZ,gBAAwB,EACxBW,iBAA+D,EAC/DV,SAAoB,EACpB8B,YAA0B;IAE1B,IAAIC,oBAAoB;IACxB,IAAIrB,mBAAmB;QACrB,IAAK,IAAIQ,OAAOnB,iBAAkB;YAChC,IAAIW,kBAAkBS,GAAG,CAACD,MAAM;gBAC9Ba,oBAAoB;gBACpB;YACF;QACF;IACF;IAEA,OAAOC,4CACLjC,kBACAgC,mBACA/B,WACA8B;AAEJ;AAGA,MAAMG,eAAe,IAAIC;AAEzB,MAAMC,6BAA4D;IAChEC,KAAK,SAASA,IAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;QACtC,IAAID,SAAS,UAAUA,SAAS,WAAWA,SAAS,WAAW;YAC7D,MAAME,iBAAiBC,SAAAA,cAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;YAExD,OAAO,CAAA;gBACL,CAACD,KAAK,EAAE,CAAC,GAAGI;oBACV,MAAMC,QAAQC,mCAAAA,yBAAyB,CAACzC,QAAQ;oBAEhD,IAAIwC,OAAO;wBACTA,MAAME,eAAe,CAACC,KAAK,CACzB,OAAA,cAA8D,CAA9D,IAAIC,MAAM,CAAC,iDAAiD,CAAC,GAA7D,qBAAA;mCAAA;wCAAA;0CAAA;wBAA6D;oBAEjE;oBAEA,OAAO,IAAIC,MACTR,eAAeS,KAAK,CAACZ,QAAQK,OAC7BP;gBAEJ;YACF,CAAA,CAAC,CAACG,KAAK;QACT;QAEA,OAAOG,SAAAA,cAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;IAC1C;AACF;AAEA,SAASb,kBACP3B,gBAAwB,EACxBC,SAAoB,EACpByB,cAA0C;IAE1C,MAAMyB,eAAejB,aAAaG,GAAG,CAACrC;IACtC,IAAImD,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMC,UAAU,IAAIH,MAClB5B,CAAAA,GAAAA,uBAAAA,kBAAkB,EAChBK,eAAeJ,YAAY,EAC3BrB,UAAUsB,KAAK,EACf,aAEFa;IAGFF,aAAamB,GAAG,CAACrD,kBAAkBoD;IAEnC,OAAOA;AACT;AAEA,SAASxB,mBACP5B,gBAAwB,EACxBiB,cAAyC,EACzChB,SAAoB,EACpByB,cAAwD;IAExD,MAAMyB,eAAejB,aAAaG,GAAG,CAACrC;IACtC,IAAImD,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMG,sBAAsB;QAAE,GAAGtD,gBAAgB;IAAC;IAElD,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMoD,UAAU5B,QAAQC,OAAO,CAAC6B;IAChCpB,aAAamB,GAAG,CAACrD,kBAAkBoD;IAEnCG,OAAOC,IAAI,CAACxD,kBAAkByD,OAAO,CAAC,CAAClB;QACrC,IAAImB,cAAAA,mBAAmB,CAACtC,GAAG,CAACmB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACL,IAAItB,eAAeG,GAAG,CAACmB,OAAO;gBAC5BgB,OAAOI,cAAc,CAACL,qBAAqBf,MAAM;oBAC/CF;wBACE,MAAMuB,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUtB;wBAC1D,oEAAoE;wBACpE,oEAAoE;wBACpE,wEAAwE;wBACxE,kBAAkB;wBAClB,2EAA2E;wBAC3E,iCAAiC;wBACjC,IAAIb,eAAerB,IAAI,KAAK,iBAAiB;4BAC3C,qCAAqC;4BACrCyD,CAAAA,GAAAA,kBAAAA,oBAAoB,EAClB7D,UAAUsB,KAAK,EACfqC,YACAlC,eAAeqC,eAAe;wBAElC,OAAO;4BACL,mBAAmB;4BACnBC,CAAAA,GAAAA,kBAAAA,gCAAgC,EAC9BJ,YACA3D,WACAyB;wBAEJ;oBACF;oBACAuC,YAAY;gBACd;YACF;QACF;IACF;IAEA,OAAOb;AACT;AAEA,SAASvB,oBAAoB7B,gBAAwB;IACnD,MAAMmD,eAAejB,aAAaG,GAAG,CAACrC;IACtC,IAAImD,cAAc;QAChB,OAAOA;IACT;IAEA,MAAMC,UAAU5B,QAAQC,OAAO,CAACzB;IAChCkC,aAAamB,GAAG,CAACrD,kBAAkBoD;IAEnC,OAAOA;AACT;AAEA,SAASnB,4CACPjC,gBAAwB,EACxBgC,iBAA0B,EAC1B/B,SAAoB,EACpB8B,YAA0B;IAE1B,IAAIA,aAAamC,gBAAgB,IAAIlC,mBAAmB;QACtD,yEAAyE;QACzE,qEAAqE;QACrE,qEAAqE;QACrE,oEAAoE;QACpE,mBAAmB;QACnB,MAAMmC,qBAAqBpC,aAAamC,gBAAgB,CAACC,kBAAkB;QAC3E,MAAMf,UAA2B,IAAI5B,QAAQ,CAACC,SAAS2C;YACrDD,mBAAmBE,IAAI,CAAC,IAAM5C,QAAQzB,mBAAmBoE;QAC3D;QACA,mBAAmB;QACnBhB,QAAQkB,WAAW,GAAG;QACtB,OAAOC,uCACLvE,kBACAoD,SACAnD;IAEJ;IAEA,MAAMkD,eAAejB,aAAaG,GAAG,CAACrC;IACtC,IAAImD,cAAc;QAChB,OAAOA;IACT;IAEA,4DAA4D;IAC5D,kEAAkE;IAClE,qEAAqE;IACrE,MAAMC,UAAUpB,oBACZwC,CAAAA,GAAAA,uBAAAA,0BAA0B,EACxBxE,kBACA+B,cACA0C,iBAAAA,WAAW,CAACC,OAAO,IAGrBlD,QAAQC,OAAO,CAACzB;IAEpB,MAAM2E,iBAAiBJ,uCACrBvE,kBACAoD,SACAnD;IAEFiC,aAAamB,GAAG,CAACrD,kBAAkB2E;IACnC,OAAOA;AACT;AAEA,SAASJ,uCACPvE,gBAAwB,EACxBoD,OAAwB,EACxBnD,SAAoB;IAEpB,6CAA6C;IAC7C,MAAM2E,oBAAoB,IAAIC;IAE9BtB,OAAOC,IAAI,CAACxD,kBAAkByD,OAAO,CAAC,CAAClB;QACrC,IAAImB,cAAAA,mBAAmB,CAACtC,GAAG,CAACmB,OAAO;QACjC,kEAAkE;QAClE,kEAAkE;QACpE,OAAO;YACLqC,kBAAkBE,GAAG,CAACvC;QACxB;IACF;IAEA,OAAO,IAAIU,MAAMG,SAAS;QACxBf,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;YACxB,IAAI,OAAOD,SAAS,UAAU;gBAC5B,IACE,AACAqC,kBAAkBxD,GAAG,CAACmB,OACtB,0CAFuE;oBAGvE,MAAMqB,aAAaC,CAAAA,GAAAA,cAAAA,4BAA4B,EAAC,UAAUtB;oBAC1DwC,kBAAkB9E,UAAUsB,KAAK,EAAEqC;gBACrC;YACF;YACA,OAAOlB,SAAAA,cAAc,CAACL,GAAG,CAACC,QAAQC,MAAMC;QAC1C;QACAa,KAAIf,MAAM,EAAEC,IAAI,EAAEyC,KAAK,EAAExC,QAAQ;YAC/B,IAAI,OAAOD,SAAS,UAAU;gBAC5BqC,kBAAkBK,MAAM,CAAC1C;YAC3B;YACA,OAAOG,SAAAA,cAAc,CAACW,GAAG,CAACf,QAAQC,MAAMyC,OAAOxC;QACjD;QACA0C,SAAQ5C,MAAM;YACZ,MAAMsB,aAAa;YACnBmB,kBAAkB9E,UAAUsB,KAAK,EAAEqC;YACnC,OAAOuB,QAAQD,OAAO,CAAC5C;QACzB;IACF;AACF;AAEA,MAAMyC,oBAAoBK,CAAAA,GAAAA,0CAAAA,2CAA2C,EACnEC;AAGF,SAASA,wBACP9D,KAAyB,EACzBqC,UAAkB;IAElB,MAAM0B,SAAS/D,QAAQ,CAAC,OAAO,EAAEA,MAAM,EAAE,CAAC,GAAG;IAC7C,OAAO,OAAA,cAIN,CAJM,IAAIyB,MACT,GAAGsC,OAAO,KAAK,EAAE1B,WAAW,EAAE,CAAC,GAC7B,CAAC,iHAAiH,CAAC,GACnH,CAAC,8DAA8D,CAAC,GAH7D,qBAAA;eAAA;oBAAA;sBAAA;IAIP;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2141, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/client/components/client-page.tsx"], "sourcesContent": ["'use client'\n\nimport type { ParsedUrlQuery } from 'querystring'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\nimport { LayoutRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { use } from 'react'\nimport { urlSearchParamsToParsedUrlQuery } from '../route-params'\nimport { SearchParamsContext } from '../../shared/lib/hooks-client-context.shared-runtime'\n\n/**\n * When the Page is a client component we send the params and searchParams to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Page component.\n *\n * additionally we may send promises representing the params and searchParams. We don't ever use these passed\n * values but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations.\n * It is up to the caller to decide if the promises are needed.\n */\nexport function ClientPageRoot({\n  Component,\n  serverProvidedParams,\n}: {\n  Component: React.ComponentType<any>\n  serverProvidedParams: null | {\n    searchParams: ParsedUrlQuery\n    params: Params\n    promises: Array<Promise<any>> | null\n  }\n}) {\n  let searchParams: ParsedUrlQuery\n  let params: Params\n  if (serverProvidedParams !== null) {\n    searchParams = serverProvidedParams.searchParams\n    params = serverProvidedParams.params\n  } else {\n    // When Cache Components is enabled, the server does not pass the params as\n    // props; they are parsed on the client and passed via context.\n    const layoutRouterContext = use(LayoutRouterContext)\n    params =\n      layoutRouterContext !== null ? layoutRouterContext.parentParams : {}\n\n    // This is an intentional behavior change: when Cache Components is enabled,\n    // client segments receive the \"canonical\" search params, not the\n    // rewritten ones. Users should either call useSearchParams directly or pass\n    // the rewritten ones in from a Server Component.\n    // TODO: Log a deprecation error when this object is accessed\n    searchParams = urlSearchParamsToParsedUrlQuery(use(SearchParamsContext)!)\n  }\n\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientSearchParams: Promise<ParsedUrlQuery>\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling searchParams in a client Page.'\n      )\n    }\n\n    const { createSearchParamsFromClient } =\n      require('../../server/request/search-params') as typeof import('../../server/request/search-params')\n    clientSearchParams = createSearchParamsFromClient(searchParams, store)\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  } else {\n    const { createRenderSearchParamsFromClient } =\n      require('../request/search-params.browser') as typeof import('../request/search-params.browser')\n    const clientSearchParams = createRenderSearchParamsFromClient(searchParams)\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n\n    return <Component params={clientParams} searchParams={clientSearchParams} />\n  }\n}\n"], "names": ["ClientPageRoot", "Component", "serverProvidedParams", "searchParams", "params", "layoutRouterContext", "use", "LayoutRouterContext", "parentParams", "urlSearchParamsToParsedUrlQuery", "SearchParamsContext", "window", "workAsyncStorage", "require", "clientSearchParams", "clientParams", "store", "getStore", "InvariantError", "createSearchParamsFromClient", "createParamsFromClient", "createRenderSearchParamsFromClient", "createRenderParamsFromClient"], "mappings": ";;;+BAmBgBA,kBAAAA;;;eAAAA;;;;gCAhBe;+CAGK;uBAChB;6BAC4B;iDACZ;AAU7B,SAASA,eAAe,EAC7BC,SAAS,EACTC,oBAAoB,EAQrB;IACC,IAAIC;IACJ,IAAIC;IACJ,IAAIF,yBAAyB,MAAM;QACjCC,eAAeD,qBAAqBC,YAAY;QAChDC,SAASF,qBAAqBE,MAAM;IACtC,OAAO;QACL,2EAA2E;QAC3E,+DAA+D;QAC/D,MAAMC,sBAAsBC,CAAAA,GAAAA,OAAAA,GAAG,EAACC,+BAAAA,mBAAmB;QACnDH,SACEC,wBAAwB,OAAOA,oBAAoBG,YAAY,GAAG,CAAC;QAErE,4EAA4E;QAC5E,iEAAiE;QACjE,4EAA4E;QAC5E,iDAAiD;QACjD,6DAA6D;QAC7DL,eAAeM,CAAAA,GAAAA,aAAAA,+BAA+B,EAACH,CAAAA,GAAAA,OAAAA,GAAG,EAACI,iCAAAA,mBAAmB;IACxE;IAEA,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQJ,iBAAiBK,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,IAAIE,gBAAAA,cAAc,CACtB,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEC,4BAA4B,EAAE,GACpCN,QAAQ;QACVC,qBAAqBK,6BAA6BhB,cAAca;QAEhE,MAAM,EAAEI,sBAAsB,EAAE,GAC9BP,QAAQ;QACVE,eAAeK,uBAAuBhB,QAAQY;QAE9C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACf,WAAAA;YAAUG,QAAQW;YAAcZ,cAAcW;;IACxD,OAAO;QACL,MAAM,EAAEO,kCAAkC,EAAE,GAC1CR,QAAQ;QACV,MAAMC,qBAAqBO,mCAAmClB;QAC9D,MAAM,EAAEmB,4BAA4B,EAAE,GACpCT,QAAQ;QACV,MAAME,eAAeO,6BAA6BlB;QAElD,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACH,WAAAA;YAAUG,QAAQW;YAAcZ,cAAcW;;IACxD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2218, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/client/components/client-segment.tsx"], "sourcesContent": ["'use client'\n\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport type { Params } from '../../server/request/params'\nimport { LayoutRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { use } from 'react'\n\n/**\n * When the Page is a client component we send the params to this client wrapper\n * where they are turned into dynamically tracked values before being passed to the actual Segment component.\n *\n * additionally we may send a promise representing params. We don't ever use this passed\n * value but it can be necessary for the sender to send a Promise that doesn't resolve in certain situations\n * such as when cacheComponents is enabled. It is up to the caller to decide if the promises are needed.\n */\nexport function ClientSegmentRoot({\n  Component,\n  slots,\n  serverProvidedParams,\n}: {\n  Component: React.ComponentType<any>\n  slots: { [key: string]: React.ReactNode }\n  serverProvidedParams: null | {\n    params: Params\n    promises: Array<Promise<any>> | null\n  }\n}) {\n  let params: Params\n  if (serverProvidedParams !== null) {\n    params = serverProvidedParams.params\n  } else {\n    // When Cache Components is enabled, the server does not pass the params\n    // as props; they are parsed on the client and passed via context.\n    const layoutRouterContext = use(LayoutRouterContext)\n    params =\n      layoutRouterContext !== null ? layoutRouterContext.parentParams : {}\n  }\n\n  if (typeof window === 'undefined') {\n    const { workAsyncStorage } =\n      require('../../server/app-render/work-async-storage.external') as typeof import('../../server/app-render/work-async-storage.external')\n\n    let clientParams: Promise<Params>\n    // We are going to instrument the searchParams prop with tracking for the\n    // appropriate context. We wrap differently in prerendering vs rendering\n    const store = workAsyncStorage.getStore()\n    if (!store) {\n      throw new InvariantError(\n        'Expected workStore to exist when handling params in a client segment such as a Layout or Template.'\n      )\n    }\n\n    const { createParamsFromClient } =\n      require('../../server/request/params') as typeof import('../../server/request/params')\n    clientParams = createParamsFromClient(params, store)\n\n    return <Component {...slots} params={clientParams} />\n  } else {\n    const { createRenderParamsFromClient } =\n      require('../request/params.browser') as typeof import('../request/params.browser')\n    const clientParams = createRenderParamsFromClient(params)\n    return <Component {...slots} params={clientParams} />\n  }\n}\n"], "names": ["ClientSegmentRoot", "Component", "slots", "serverProvidedParams", "params", "layoutRouterContext", "use", "LayoutRouterContext", "parentParams", "window", "workAsyncStorage", "require", "clientParams", "store", "getStore", "InvariantError", "createParamsFromClient", "createRenderParamsFromClient"], "mappings": ";;;+BAgBgBA,qBAAAA;;;eAAAA;;;;gCAde;+CAGK;uBAChB;AAUb,SAASA,kBAAkB,EAChCC,SAAS,EACTC,KAAK,EACLC,oBAAoB,EAQrB;IACC,IAAIC;IACJ,IAAID,yBAAyB,MAAM;QACjCC,SAASD,qBAAqBC,MAAM;IACtC,OAAO;QACL,wEAAwE;QACxE,kEAAkE;QAClE,MAAMC,sBAAsBC,CAAAA,GAAAA,OAAAA,GAAG,EAACC,+BAAAA,mBAAmB;QACnDH,SACEC,wBAAwB,OAAOA,oBAAoBG,YAAY,GAAG,CAAC;IACvE;IAEA,IAAI,OAAOC,WAAW,aAAa;QACjC,MAAM,EAAEC,gBAAgB,EAAE,GACxBC,QAAQ;QAEV,IAAIC;QACJ,yEAAyE;QACzE,wEAAwE;QACxE,MAAMC,QAAQH,iBAAiBI,QAAQ;QACvC,IAAI,CAACD,OAAO;YACV,MAAM,OAAA,cAEL,CAFK,IAAIE,gBAAAA,cAAc,CACtB,uGADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAM,EAAEC,sBAAsB,EAAE,GAC9BL,QAAQ;QACVC,eAAeI,uBAAuBZ,QAAQS;QAE9C,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACZ,WAAAA;YAAW,GAAGC,KAAK;YAAEE,QAAQQ;;IACvC,OAAO;QACL,MAAM,EAAEK,4BAA4B,EAAE,GACpCN,QAAQ;QACV,MAAMC,eAAeK,6BAA6Bb;QAClD,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACH,WAAAA;YAAW,GAAGC,KAAK;YAAEE,QAAQQ;;IACvC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2280, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next%4016.0.1_%40babel%2Bcore%407.28.5_react-dom%4019.2.0_react%4019.2.0__react%4019.2.0/node_modules/next/src/lib/metadata/generate/icon-mark.tsx"], "sourcesContent": ["'use client'\n\n// This is a client component that only renders during SSR,\n// but will be replaced during streaming with an icon insertion script tag.\n// We don't want it to be presented anywhere so it's only visible during streaming,\n// right after the icon meta tags so that browser can pick it up as soon as it's rendered.\n// Note: we don't just emit the script here because we only need the script if it's not in the head,\n// and we need it to be hoistable alongside the other metadata but sync scripts are not hoistable.\nexport const IconMark = () => {\n  if (typeof window !== 'undefined') {\n    return null\n  }\n  return <meta name=\"«nxt-icon»\" />\n}\n"], "names": ["IconMark", "window", "meta", "name"], "mappings": ";;;+BAQaA,YAAAA;;;eAAAA;;;;AAAN,MAAMA,WAAW;IACtB,IAAI,OAAOC,WAAW,aAAa;QACjC,OAAO;IACT;IACA,OAAA,WAAA,GAAO,CAAA,GAAA,YAAA,GAAA,EAACC,QAAAA;QAAKC,MAAK;;AACpB", "ignoreList": [0], "debugId": null}}]}