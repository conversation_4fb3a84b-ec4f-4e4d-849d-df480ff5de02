{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,gSAAO,EAAC,IAAA,0PAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?:\n    | 'default'\n    | 'destructive'\n    | 'outline'\n    | 'secondary'\n    | 'ghost'\n    | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    const baseClasses =\n      'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50';\n\n    const variants = {\n      default: 'bg-primary text-primary-foreground hover:bg-primary/90',\n      destructive:\n        'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n      outline:\n        'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n      secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n      ghost: 'hover:bg-accent hover:text-accent-foreground',\n      link: 'text-primary underline-offset-4 hover:underline',\n    };\n\n    const sizes = {\n      default: 'h-10 px-4 py-2',\n      sm: 'h-9 rounded-md px-3',\n      lg: 'h-11 rounded-md px-8',\n      icon: 'h-10 w-10',\n    };\n\n    return (\n      <button\n        className={cn(baseClasses, variants[variant], sizes[size], className)}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAeA,MAAM,uBAAS,qYAAgB,MAC7B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,MAAM,cACJ;IAEF,MAAM,WAAW;QACf,SAAS;QACT,aACE;QACF,SACE;QACF,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,uZAAC;QACC,WAAW,IAAA,qLAAE,EAAC,aAAa,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;QAC3D,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/flywheel-media/src/components/layout/header.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\n\nexport function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n              flywheel-media\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <Link\n              href=\"/\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Home\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              About Us\n            </Link>\n            <Link\n              href=\"/services\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Our Services\n            </Link>\n            <Link\n              href=\"/contact\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Contact Us\n            </Link>\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:block\">\n            <Link href=\"/contact\">\n              <Button>Get Started</Button>\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600\"\n            >\n              <svg\n                className=\"h-6 w-6\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                {isMenuOpen ? (\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M6 18L18 6M6 6l12 12\"\n                  />\n                ) : (\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M4 6h16M4 12h16M4 18h16\"\n                  />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t\">\n              <Link\n                href=\"/\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600\"\n              >\n                Home\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600\"\n              >\n                About Us\n              </Link>\n              <Link\n                href=\"/services\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600\"\n              >\n                Our Services\n              </Link>\n              <Link\n                href=\"/contact\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600\"\n              >\n                Contact Us\n              </Link>\n              <div className=\"px-3 py-2\">\n                <Link href=\"/contact\" className=\"w-full\">\n                  <Button className=\"w-full\">Get Started</Button>\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,mYAAQ,EAAC;IAE7C,qBACE,uZAAC;QAAO,WAAU;kBAChB,cAAA,uZAAC;YAAI,WAAU;;8BACb,uZAAC;oBAAI,WAAU;;sCAEb,uZAAC;4BAAI,WAAU;sCACb,cAAA,uZAAC,oYAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAmC;;;;;;;;;;;sCAM9D,uZAAC;4BAAI,WAAU;;8CACb,uZAAC,oYAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,uZAAC,oYAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,uZAAC,oYAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,uZAAC,oYAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,uZAAC;4BAAI,WAAU;sCACb,cAAA,uZAAC,oYAAI;gCAAC,MAAK;0CACT,cAAA,uZAAC,wMAAM;8CAAC;;;;;;;;;;;;;;;;sCAKZ,uZAAC;4BAAI,WAAU;sCACb,cAAA,uZAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAEV,cAAA,uZAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;oCACR,QAAO;8CAEN,2BACC,uZAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;6DAGJ,uZAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;gBASb,4BACC,uZAAC;oBAAI,WAAU;8BACb,cAAA,uZAAC;wBAAI,WAAU;;0CACb,uZAAC,oYAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,uZAAC,oYAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,uZAAC,oYAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,uZAAC,oYAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,uZAAC;gCAAI,WAAU;0CACb,cAAA,uZAAC,oYAAI;oCAAC,MAAK;oCAAW,WAAU;8CAC9B,cAAA,uZAAC,wMAAM;wCAAC,WAAU;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7C;GAxHgB;KAAA", "debugId": null}}]}