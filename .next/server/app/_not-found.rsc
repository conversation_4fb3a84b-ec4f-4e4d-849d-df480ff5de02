1:"$Sreact.fragment"
2:I[4890,["/_next/static/chunks/c1de7377511d8d77.js","/_next/static/chunks/5b5fb9a4a9aa84f7.js"],"Header"]
3:I[44751,["/_next/static/chunks/f7c3563687607f14.js","/_next/static/chunks/8cc8d9d664671468.js"],"default"]
4:I[56278,["/_next/static/chunks/f7c3563687607f14.js","/_next/static/chunks/8cc8d9d664671468.js"],"default"]
5:I[17400,["/_next/static/chunks/c1de7377511d8d77.js","/_next/static/chunks/5b5fb9a4a9aa84f7.js"],""]
a:I[33039,["/_next/static/chunks/f7c3563687607f14.js","/_next/static/chunks/8cc8d9d664671468.js"],"default"]
:HL["/_next/static/chunks/3041a9b478c2d47b.css","style"]
0:{"P":null,"b":"tjs5rA77tz19MqU3yHZlM","c":["","_not-found",""],"q":"","i":false,"f":[[["",{"children":["/_not-found",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],[["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/chunks/3041a9b478c2d47b.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}],["$","script","script-0",{"src":"/_next/static/chunks/c1de7377511d8d77.js","async":true,"nonce":"$undefined"}],["$","script","script-1",{"src":"/_next/static/chunks/5b5fb9a4a9aa84f7.js","async":true,"nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"inter_5901b7c6-module__ec5Qua__variable font-sans antialiased","children":[["$","$L2",null,{}],["$","main",null,{"className":"min-h-screen","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","footer",null,{"className":"bg-gray-900 text-white","children":["$","div",null,{"className":"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12","children":[["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-4 gap-8","children":[["$","div",null,{"className":"col-span-1 md:col-span-2","children":[["$","$L5",null,{"href":"/","className":"text-2xl font-bold text-blue-400 mb-4 block","children":"flywheel-media"}],["$","p",null,{"className":"text-gray-300 mb-6 max-w-md","children":"It's time to tell the world about what you do. Let's work together to share your story in a way that captivates and inspires."}],["$","div",null,{"className":"flex space-x-4","children":[["$","$L5",null,{"href":"#","className":"text-gray-300 hover:text-blue-400 transition-colors","children":"Visit LinkedIn Page"}],["$","$L5",null,{"href":"#","className":"text-gray-300 hover:text-blue-400 transition-colors","children":"Follow Us On Instagram"}],["$","$L5",null,{"href":"#","className":"text-gray-300 hover:text-blue-400 transition-colors","children":"Facebook Page"}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-semibold mb-4","children":"Important Links"}],["$","ul",null,{"className":"space-y-2","children":[["$","li",null,{"children":["$","$L5",null,{"href":"/","className":"text-gray-300 hover:text-blue-400 transition-colors","children":"Home"}]}],["$","li",null,{"children":["$","$L5",null,{"href":"/about","className":"text-gray-300 hover:text-blue-400 transition-colors","children":"About Us"}]}],["$","li",null,{"children":["$","$L5",null,{"href":"/services","className":"text-gray-300 hover:text-blue-400 transition-colors","children":"Services"}]}],["$","li",null,{"children":["$","$L5",null,{"href":"/contact","className":"text-gray-300 hover:text-blue-400 transition-colors","children":"Contact Us"}]}]]}]]}],["$","div",null,{"children":[["$","h3",null,{"className":"text-lg font-semibold mb-4","children":"Newsletter"}],["$","form",null,{"className":"space-y-3","children":[["$","input",null,{"type":"email","placeholder":"Enter Your Email","className":"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"}],["$","button",null,{"type":"submit","className":"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors","children":"Subscribe"}]]}]]}]]}],"$L6"]}]}]]}]}]]}],{"children":["$L7",{"children":["$L8",{},null,false,false]},null,false,false]},null,false,false],"$L9",false]],"m":"$undefined","G":["$a","$undefined"],"s":false,"S":true}
b:I[31606,["/_next/static/chunks/f7c3563687607f14.js","/_next/static/chunks/8cc8d9d664671468.js"],"OutletBoundary"]
c:"$Sreact.suspense"
e:I[31606,["/_next/static/chunks/f7c3563687607f14.js","/_next/static/chunks/8cc8d9d664671468.js"],"ViewportBoundary"]
10:I[31606,["/_next/static/chunks/f7c3563687607f14.js","/_next/static/chunks/8cc8d9d664671468.js"],"MetadataBoundary"]
6:["$","div",null,{"className":"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center","children":[["$","p",null,{"className":"text-gray-400 text-sm","children":"© 2024 flywheel-media, All rights reserved."}],["$","div",null,{"className":"flex space-x-6 mt-4 md:mt-0","children":[["$","$L5",null,{"href":"#","className":"text-gray-400 hover:text-blue-400 text-sm transition-colors","children":"Privacy Policy"}],["$","$L5",null,{"href":"#","className":"text-gray-400 hover:text-blue-400 text-sm transition-colors","children":"Terms of service"}]]}]]}]
7:["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}]
8:["$","$1","c",{"children":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":"$0:f:0:1:0:props:children:1:props:children:props:children:1:props:children:props:notFound:0:1:props:style","children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":"$0:f:0:1:0:props:children:1:props:children:props:children:1:props:children:props:notFound:0:1:props:children:props:children:1:props:style","children":404}],["$","div",null,{"style":"$0:f:0:1:0:props:children:1:props:children:props:children:1:props:children:props:notFound:0:1:props:children:props:children:2:props:style","children":["$","h2",null,{"style":"$0:f:0:1:0:props:children:1:props:children:props:children:1:props:children:props:notFound:0:1:props:children:props:children:2:props:children:props:style","children":"This page could not be found."}]}]]}]}]],null,["$","$Lb",null,{"children":["$","$c",null,{"name":"Next.MetadataOutlet","children":"$@d"}]}]]}]
9:["$","$1","h",{"children":[["$","meta",null,{"name":"robots","content":"noindex"}],["$","$Le",null,{"children":"$@f"}],["$","div",null,{"hidden":true,"children":["$","$L10",null,{"children":["$","$c",null,{"name":"Next.Metadata","children":"$@11"}]}]}],["$","meta",null,{"name":"next-size-adjust","content":""}]]}]
f:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
12:I[80282,["/_next/static/chunks/f7c3563687607f14.js","/_next/static/chunks/8cc8d9d664671468.js"],"IconMark"]
11:[["$","title","0",{"children":"flywheel-media - Driving Growth with Smart Affiliate Marketing"}],["$","meta","1",{"name":"description","content":"Join us in forging powerful connections between brands and customers through strategic affiliate marketing solutions."}],["$","link","2",{"rel":"icon","href":"/favicon.ico?favicon.0b3bf435.ico","sizes":"256x256","type":"image/x-icon"}],["$","$L12","3",{}]]
d:null
