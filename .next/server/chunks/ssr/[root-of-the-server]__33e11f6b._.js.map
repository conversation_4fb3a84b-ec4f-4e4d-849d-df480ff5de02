{"version": 3, "sources": ["turbopack:///[project]/Documents/augment-projects/flywheel-media/src/app/contact/page.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/Documents/augment-projects/flywheel-media/src/app/contact/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Documents/augment-projects/flywheel-media/src/app/contact/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "wUAEe,CAAA,EAAA,AADf,EAAA,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,oUAAsU,EACnW,mGACA,8DAHW,CAAA,EADf,AACe,EADf,CAAA,CAAA,OACe,uBAAA,AAAuB,EAClC,WAAa,MAAM,AAAI,MAAM,gTAAkT,EAC/U,+EACA", "ignoreList": [0]}