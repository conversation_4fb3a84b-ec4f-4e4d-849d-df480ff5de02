{"version": 3, "sources": ["turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/server/route-modules/app-page/vendored/contexts/app-router-context.ts", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-server-dom-turbopack-client.ts", "turbopack:///[project]/Documents/augment-projects/flywheel-media/src/components/layout/header.tsx"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['contexts'].AppRouterContext\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactServerDOMTurbopackClient\n", "'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\n\nexport function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"text-2xl font-bold text-blue-600\">\n              flywheel-media\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            <Link\n              href=\"/\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Home\n            </Link>\n            <Link\n              href=\"/about\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              About Us\n            </Link>\n            <Link\n              href=\"/services\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Our Services\n            </Link>\n            <Link\n              href=\"/contact\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              Contact Us\n            </Link>\n          </nav>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:block\">\n            <Link href=\"/contact\">\n              <Button>Get Started</Button>\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600\"\n            >\n              <svg\n                className=\"h-6 w-6\"\n                fill=\"none\"\n                viewBox=\"0 0 24 24\"\n                stroke=\"currentColor\"\n              >\n                {isMenuOpen ? (\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M6 18L18 6M6 6l12 12\"\n                  />\n                ) : (\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M4 6h16M4 12h16M4 18h16\"\n                  />\n                )}\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t\">\n              <Link\n                href=\"/\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600\"\n              >\n                Home\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600\"\n              >\n                About Us\n              </Link>\n              <Link\n                href=\"/services\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600\"\n              >\n                Our Services\n              </Link>\n              <Link\n                href=\"/contact\"\n                className=\"block px-3 py-2 text-gray-700 hover:text-blue-600\"\n              >\n                Contact Us\n              </Link>\n              <div className=\"px-3 py-2\">\n                <Link href=\"/contact\" className=\"w-full\">\n                  <Button className=\"w-full\">Get Started</Button>\n                </Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "vendored", "ReactJsxRuntime", "React", "AppRouterContext", "ReactServerDOMTurbopackClient"], "mappings": "0NA0BQG,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,iCC1BjCF,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEC,eAAe,8BCFxCP,GAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEE,KAAK,+BCF9BR,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,QAAW,CAACG,gBAAgB,+BCFvCT,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRI,QAAQ,CAAC,YAAY,CAAEI,6BAA6B,0CCAtD,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEO,SAAS,IACd,GAAM,CAAC,EAAY,EAAc,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,IAE7C,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,UAAU,uCAChB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,IAAI,UAAU,4CAAmC,qBAM9D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,IACL,UAAU,+DACX,SAGD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,SACL,UAAU,+DACX,aAGD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,YACL,UAAU,+DACX,iBAGD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,WACL,UAAU,+DACX,kBAMH,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,oBACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,UAAC,oBAKZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,QAAS,IAAM,EAAc,CAAC,GAC9B,UAAU,oFAEV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAU,UACV,KAAK,OACL,QAAQ,YACR,OAAO,wBAEN,EACC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,cAAc,QACd,eAAe,QACf,YAAa,EACb,EAAE,yBAGJ,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,cAAc,QACd,eAAe,QACf,YAAa,EACb,EAAE,qCASb,GACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,IACL,UAAU,6DACX,SAGD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,SACL,UAAU,6DACX,aAGD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,YACL,UAAU,6DACX,iBAGD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,WACL,UAAU,6DACX,eAGD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,WAAW,UAAU,kBAC9B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,UAAU,kBAAS,6BAS7C", "ignoreList": [0, 1, 2, 3, 4]}