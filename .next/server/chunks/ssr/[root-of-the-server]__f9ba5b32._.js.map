{"version": 3, "sources": ["turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react.ts", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/server/route-modules/app-page/module.compiled.js", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.ts", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.ts", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/cjs/_interop_require_wildcard.cjs", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/components/builtin/global-error.js/__nextjs-internal-proxy.cjs", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/client/components/builtin/global-error.tsx"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.React\n", "if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxRuntime\n", "module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactServerDOMTurbopackServer\n", "\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nconst { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/components/builtin/global-error.js\"));\n", "'use client'\n\nimport { HandleISRError } from '../handle-isr-error'\n\nconst styles = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  text: {\n    fontSize: '14px',\n    fontWeight: 400,\n    lineHeight: '28px',\n    margin: '0 8px',\n  },\n} as const\n\nexport type GlobalErrorComponent = React.ComponentType<{\n  error: any\n}>\nfunction DefaultGlobalError({ error }: { error: any }) {\n  const digest: string | undefined = error?.digest\n  return (\n    <html id=\"__next_error__\">\n      <head></head>\n      <body>\n        <HandleISRError error={error} />\n        <div style={styles.error}>\n          <div>\n            <h2 style={styles.text}>\n              Application error: a {digest ? 'server' : 'client'}-side exception\n              has occurred while loading {window.location.hostname} (see the{' '}\n              {digest ? 'server logs' : 'browser console'} for more\n              information).\n            </h2>\n            {digest ? <p style={styles.text}>{`Digest: ${digest}`}</p> : null}\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\n// Exported so that the import signature in the loaders can be identical to user\n// supplied custom global error signatures.\nexport default DefaultGlobalError\n"], "names": ["module", "exports", "require", "vendored", "React", "process", "env", "NEXT_RUNTIME", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK", "ReactJsxRuntime", "ReactServerDOMTurbopackServer", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "text", "fontSize", "fontWeight", "lineHeight", "margin", "DefaultGlobalError", "digest", "html", "id", "head", "body", "HandleISRError", "div", "style", "h2", "window", "location", "hostname", "p"], "mappings": "wsCAAAA,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK,+BCwBtBJ,EAAOC,OAAO,CAAGC,EAAQ,CAAA,CAAA,IAAA,gCC1BjCF,GAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAEQ,eAAe,+BCFxCX,EAAOC,OAAO,CACZC,EAAQ,CAAA,CAAA,IAAA,GACRC,QAAQ,CAAC,YAAY,CAAES,6BAA6B,+BCAtD,SAAS,EAAyB,CAAW,EACzC,GAAuB,YAAnB,OAAO,QAAwB,OAAO,KAE1C,IAAI,EAAoB,IAAI,QACxB,EAAmB,IAAI,QAE3B,MAAO,CAAC,EAA2B,SAAS,CAAW,EACnD,OAAO,EAAc,EAAmB,EAC5C,CAAC,CAAE,EACP,CA0BA,EAAQ,CAAC,CAzBT,EAyBY,OAzBH,AAA0B,CAAG,CAAE,CAAW,EAC/C,GAAI,CAAC,GAAe,GAAO,EAAI,UAAU,CAAE,OAAO,EAClD,GAAY,OAAR,GAA+B,UAAf,OAAO,GAAoB,AAAe,mBAAR,EAAoB,MAAO,CAAE,QAAS,CAAI,EAEhG,IAAI,EAAQ,EAAyB,GAErC,GAAI,GAAS,EAAM,GAAG,CAAC,GAAM,OAAO,EAAM,GAAG,CAAC,GAE9C,IAAI,EAAS,CAAE,UAAW,IAAK,EAC3B,EAAwB,OAAO,cAAc,EAAI,OAAO,wBAAwB,CAEpF,IAAK,IAAI,KAAO,EACZ,EADiB,CACL,YAAR,GAAqB,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,EAAK,GAAM,CACrE,IAAI,EAAO,EAAwB,OAAO,wBAAwB,CAAC,EAAK,GAAO,KAC3E,IAAS,EAAK,EAAN,CAAS,EAAI,EAAK,GAAA,AAAG,EAAG,OAAO,cAAc,CAAC,EAAQ,EAAK,GAClE,CAAM,CAAC,EAAI,CAAG,CAAG,CAAC,EAAI,AAC/B,CAOJ,OAJA,EAAO,OAAO,CAAG,EAEb,GAAO,EAAM,GAAG,CAAC,EAAK,GAEnB,CACX,mBCnCA,GAAM,yBAAE,CAAuB,CAAE,CAAA,EAAA,CAAA,CAAA,OAEjC,EAAsB,CAAC,CAAC,EAAwB,4PAFhD,GAAM,yBAAE,CAAuB,CAAE,CAAA,EAAA,CAAA,CAAA,OAEjC,EAAsB,CAAC,CAAC,EAAwB", "ignoreList": [0, 1, 2, 3, 4, 5]}