{"version": 3, "sources": ["turbopack:///[project]/Documents/augment-projects/flywheel-media/src/app/contact/page.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { Button } from \"@/components/ui/button\"\n\nexport default function ContactPage() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    phone: \"\",\n    skype: \"\",\n    email: \"\",\n    comment: \"\"\n  })\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    // Handle form submission here\n    console.log(\"Form submitted:\", formData)\n    // You can add form submission logic here\n  }\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    })\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-20\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header Section */}\n        <div className=\"text-center mb-16\">\n          <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Don't hesitate to reach out\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Ready to take the next step? Get in touch with us today and let's start crafting your success story together\n          </p>\n        </div>\n\n        {/* Contact Form */}\n        <div className=\"bg-white rounded-lg shadow-lg p-8 md:p-12\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Name Field */}\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  name=\"name\"\n                  value={formData.name}\n                  onChange={handleChange}\n                  placeholder=\"Full Name\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  required\n                />\n              </div>\n\n              {/* Phone Field */}\n              <div>\n                <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Phone No.\n                </label>\n                <input\n                  type=\"tel\"\n                  id=\"phone\"\n                  name=\"phone\"\n                  value={formData.phone}\n                  onChange={handleChange}\n                  placeholder=\"Phone No.\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  required\n                />\n              </div>\n\n              {/* Skype Field */}\n              <div>\n                <label htmlFor=\"skype\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Skype\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"skype\"\n                  name=\"skype\"\n                  value={formData.skype}\n                  onChange={handleChange}\n                  placeholder=\"Skype ID/No.\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                />\n              </div>\n\n              {/* Email Field */}\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Email\n                </label>\n                <input\n                  type=\"email\"\n                  id=\"email\"\n                  name=\"email\"\n                  value={formData.email}\n                  onChange={handleChange}\n                  placeholder=\"Email\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors\"\n                  required\n                />\n              </div>\n            </div>\n\n            {/* Comment Field */}\n            <div>\n              <label htmlFor=\"comment\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Comment\n              </label>\n              <textarea\n                id=\"comment\"\n                name=\"comment\"\n                value={formData.comment}\n                onChange={handleChange}\n                placeholder=\"Enter your comment/message...\"\n                rows={6}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-vertical\"\n                required\n              />\n            </div>\n\n            {/* Submit Button */}\n            <div className=\"text-center\">\n              <Button type=\"submit\" size=\"lg\" className=\"px-12 py-3\">\n                Send Message\n              </Button>\n            </div>\n          </form>\n        </div>\n\n        {/* Additional Contact Info */}\n        <div className=\"mt-16 text-center\">\n          <h3 className=\"text-2xl font-semibold text-gray-900 mb-8\">\n            Other Ways to Reach Us\n          </h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <div className=\"text-blue-600 text-2xl mb-4\">📧</div>\n              <h4 className=\"font-semibold text-gray-900 mb-2\">Email</h4>\n              <p className=\"text-gray-600\"><EMAIL></p>\n            </div>\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <div className=\"text-blue-600 text-2xl mb-4\">📱</div>\n              <h4 className=\"font-semibold text-gray-900 mb-2\">Phone</h4>\n              <p className=\"text-gray-600\">+****************</p>\n            </div>\n            <div className=\"bg-white rounded-lg p-6 shadow-sm\">\n              <div className=\"text-blue-600 text-2xl mb-4\">💬</div>\n              <h4 className=\"font-semibold text-gray-900 mb-2\">Live Chat</h4>\n              <p className=\"text-gray-600\">Available 24/7</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": "wDAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,GAAM,CAAC,EAAU,EAAY,CAAG,CAAA,EAAA,EAAA,QAAA,AAAQ,EAAC,CACvC,KAAM,GACN,MAAO,GACP,MAAO,GACP,MAAO,GACP,QAAS,EACX,GASM,EAAe,AAAC,IACpB,EAAY,CACV,GAAG,CAAQ,CACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAE,EAAE,MAAM,CAAC,KAAK,AACjC,EACF,EAEA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yCACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DAAoD,gCAGlE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mDAA0C,oHAMzD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qDACb,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,SA7BQ,AAAD,CA6BG,GA5BtB,EAAE,cAAc,GAEhB,QAAQ,GAAG,CAAC,kBAAmB,EAEjC,EAwBsC,UAAU,sBACtC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,OAAO,UAAU,wDAA+C,SAG/E,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,OACL,GAAG,OACH,KAAK,OACL,MAAO,EAAS,IAAI,CACpB,SAAU,EACV,YAAY,YACZ,UAAU,iIACV,QAAQ,CAAA,CAAA,OAKZ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,QAAQ,UAAU,wDAA+C,cAGhF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,MACL,GAAG,QACH,KAAK,QACL,MAAO,EAAS,KAAK,CACrB,SAAU,EACV,YAAY,YACZ,UAAU,iIACV,QAAQ,CAAA,CAAA,OAKZ,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,QAAQ,UAAU,wDAA+C,UAGhF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,OACL,GAAG,QACH,KAAK,QACL,MAAO,EAAS,KAAK,CACrB,SAAU,EACV,YAAY,eACZ,UAAU,sIAKd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,QAAQ,UAAU,wDAA+C,UAGhF,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,QACL,GAAG,QACH,KAAK,QACL,MAAO,EAAS,KAAK,CACrB,SAAU,EACV,YAAY,QACZ,UAAU,iIACV,QAAQ,CAAA,CAAA,UAMd,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CAAM,QAAQ,UAAU,UAAU,wDAA+C,YAGlF,CAAA,EAAA,EAAA,GAAA,EAAC,WAAA,CACC,GAAG,UACH,KAAK,UACL,MAAO,EAAS,OAAO,CACvB,SAAU,EACV,YAAY,gCACZ,KAAM,EACN,UAAU,iJACV,QAAQ,CAAA,CAAA,OAKZ,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,KAAK,SAAS,KAAK,KAAK,UAAU,sBAAa,wBAQ7D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,qDAA4C,2BAG1D,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAA8B,OAC7C,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4CAAmC,UACjD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,gCAE/B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAA8B,OAC7C,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4CAAmC,UACjD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,yBAE/B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,uCAA8B,OAC7C,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4CAAmC,cACjD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,gCAO3C"}