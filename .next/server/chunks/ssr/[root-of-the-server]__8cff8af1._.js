module.exports=[18622,(a,b,c)=>{b.exports=a.x("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js",()=>require("next/dist/compiled/next-server/app-page-turbo.runtime.prod.js"))},78343,(a,b,c)=>{"use strict";b.exports=a.r(18622)},59952,(a,b,c)=>{"use strict";b.exports=a.r(78343).vendored["react-ssr"].ReactJsxRuntime},27547,(a,b,c)=>{"use strict";b.exports=a.r(78343).vendored["react-ssr"].React},63386,(a,b,c)=>{"use strict";b.exports=a.r(78343).vendored.contexts.AppRouterContext},50974,(a,b,c)=>{"use strict";b.exports=a.r(78343).vendored["react-ssr"].ReactServerDOMTurbopackClient},61882,a=>{"use strict";var b=a.i(59952),c=a.i(67270),d=a.i(27547),e=a.i(87430);function f(){let[a,f]=(0,d.useState)(!1);return(0,b.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,b.jsx)("div",{className:"flex-shrink-0",children:(0,b.jsx)(c.default,{href:"/",className:"text-2xl font-bold text-blue-600",children:"flywheel-media"})}),(0,b.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,b.jsx)(c.default,{href:"/",className:"text-gray-700 hover:text-blue-600 transition-colors",children:"Home"}),(0,b.jsx)(c.default,{href:"/about",className:"text-gray-700 hover:text-blue-600 transition-colors",children:"About Us"}),(0,b.jsx)(c.default,{href:"/services",className:"text-gray-700 hover:text-blue-600 transition-colors",children:"Our Services"}),(0,b.jsx)(c.default,{href:"/contact",className:"text-gray-700 hover:text-blue-600 transition-colors",children:"Contact Us"})]}),(0,b.jsx)("div",{className:"hidden md:block",children:(0,b.jsx)(c.default,{href:"/contact",children:(0,b.jsx)(e.Button,{children:"Get Started"})})}),(0,b.jsx)("div",{className:"md:hidden",children:(0,b.jsx)("button",{onClick:()=>f(!a),className:"text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600",children:(0,b.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:a?(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),a&&(0,b.jsx)("div",{className:"md:hidden",children:(0,b.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t",children:[(0,b.jsx)(c.default,{href:"/",className:"block px-3 py-2 text-gray-700 hover:text-blue-600",children:"Home"}),(0,b.jsx)(c.default,{href:"/about",className:"block px-3 py-2 text-gray-700 hover:text-blue-600",children:"About Us"}),(0,b.jsx)(c.default,{href:"/services",className:"block px-3 py-2 text-gray-700 hover:text-blue-600",children:"Our Services"}),(0,b.jsx)(c.default,{href:"/contact",className:"block px-3 py-2 text-gray-700 hover:text-blue-600",children:"Contact Us"}),(0,b.jsx)("div",{className:"px-3 py-2",children:(0,b.jsx)(c.default,{href:"/contact",className:"w-full",children:(0,b.jsx)(e.Button,{className:"w-full",children:"Get Started"})})})]})})]})})}a.s(["Header",()=>f])}];

//# sourceMappingURL=%5Broot-of-the-server%5D__8cff8af1._.js.map