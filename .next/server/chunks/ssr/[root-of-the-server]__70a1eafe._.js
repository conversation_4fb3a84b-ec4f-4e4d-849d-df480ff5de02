module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},1555,a=>{a.n(a.i(9622))},80416,(a,b,c)=>{"use strict";c._=function(a){return a&&a.__esModule?a:{default:a}}},46080,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return k}}),a.r(80416);let d=a.r(49916);a.r(14788);let e={fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},f={lineHeight:"48px"},g={display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h={fontSize:14,fontWeight:400,lineHeight:"28px"},i={display:"inline-block"},j=`body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}
@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}`,k=function(){let a="Internal Server Error.",b=`500: ${a}`;return(0,d.jsxs)("html",{id:"__next_error__",children:[(0,d.jsx)("head",{children:(0,d.jsx)("title",{children:b})}),(0,d.jsx)("body",{children:(0,d.jsx)("div",{style:e,children:(0,d.jsxs)("div",{style:f,children:[(0,d.jsx)("style",{dangerouslySetInnerHTML:{__html:j}}),(0,d.jsx)("h1",{className:"next-error-h1",style:g,children:"500"}),(0,d.jsx)("div",{style:i,children:(0,d.jsx)("h2",{style:h,children:a})})]})})})]})};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)}];

//# sourceMappingURL=%5Broot-of-the-server%5D__70a1eafe._.js.map