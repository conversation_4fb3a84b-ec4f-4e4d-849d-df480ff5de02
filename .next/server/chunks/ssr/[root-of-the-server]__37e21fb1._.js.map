{"version": 3, "sources": ["turbopack:///[project]/Documents/augment-projects/flywheel-media/src/components/sections/hero.tsx", "turbopack:///[project]/Documents/augment-projects/flywheel-media/src/components/sections/services.tsx", "turbopack:///[project]/Documents/augment-projects/flywheel-media/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Button } from '@/components/ui/button';\n\nexport function Hero() {\n  return (\n    <section className=\"relative bg-gradient-to-br from-blue-50 to-indigo-100 py-20 lg:py-32\">\n      {/* Background decorative elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-32 w-80 h-80 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob\"></div>\n        <div className=\"absolute -bottom-40 -left-32 w-80 h-80 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000\"></div>\n        <div className=\"absolute top-40 left-40 w-80 h-80 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000\"></div>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center\">\n          <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n            Driving Growth with Smart{' '}\n            <span className=\"text-blue-600\">Affiliate Marketing</span>\n          </h1>\n          <p className=\"text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n            Join us in forging powerful connections between brands and customers\n            through strategic affiliate marketing solutions.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/contact\">\n              <Button size=\"lg\">Get Started Now</Button>\n            </Link>\n            <Link href=\"/services\">\n              <Button variant=\"outline\" size=\"lg\">\n                Learn More\n              </Button>\n            </Link>\n          </div>\n        </div>\n\n        {/* Trust indicators */}\n        <div className=\"mt-16\">\n          <p className=\"text-center text-gray-500 mb-8\">\n            Trusted By Top Clients\n          </p>\n          <div className=\"flex flex-wrap justify-center items-center gap-8 opacity-60\">\n            {/* Placeholder for client logos */}\n            <div className=\"bg-gray-200 h-12 w-32 rounded flex items-center justify-center\">\n              <span className=\"text-gray-500 text-sm\">Client Logo</span>\n            </div>\n            <div className=\"bg-gray-200 h-12 w-32 rounded flex items-center justify-center\">\n              <span className=\"text-gray-500 text-sm\">Client Logo</span>\n            </div>\n            <div className=\"bg-gray-200 h-12 w-32 rounded flex items-center justify-center\">\n              <span className=\"text-gray-500 text-sm\">Client Logo</span>\n            </div>\n            <div className=\"bg-gray-200 h-12 w-32 rounded flex items-center justify-center\">\n              <span className=\"text-gray-500 text-sm\">Client Logo</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Stats */}\n        <div className=\"mt-16 grid grid-cols-2 md:grid-cols-4 gap-8\">\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-blue-600\">2k</div>\n            <div className=\"text-gray-600\">Projects Done</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-blue-600\">2k+</div>\n            <div className=\"text-gray-600\">Satisfied Clients</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-blue-600\">48K</div>\n            <div className=\"text-gray-600\">Supported Locations</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-blue-600\">2024</div>\n            <div className=\"text-gray-600\">Year Founded</div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n", "import Link from 'next/link';\nimport { Button } from '@/components/ui/button';\n\nconst services = [\n  {\n    icon: '🎯',\n    title: 'Tailored Affiliate Strategies',\n    description:\n      \"We understand that one size doesn't fit all. Our team of experts crafts bespoke performance marketing strategies that align with your brand's unique goals and market dynamics, ensuring a personalized approach for success to your business.\",\n    features: [\n      'Creating Targeted Affiliate Content',\n      'Choosing the Right Affiliate Programs',\n      'Proven Techniques for Effective Affiliate Marketing',\n    ],\n  },\n  {\n    icon: '📊',\n    title: 'Advanced Analytics and Reporting',\n    description:\n      'Transparency and insight are central to our operations. Our advanced analytics platform offers real-time data and detailed reports, which helps in making it easy to track campaign performance and ROI for informed decision-making',\n    features: [\n      'Data-Driven Insights for Strategic Growth',\n      'Mastering Data-Driven Decision Making',\n      'Leveraging Analytics for Continuous Improvement',\n    ],\n  },\n  {\n    icon: '🌐',\n    title: 'Global Network',\n    description:\n      'Leverage our extensive global network to reach audiences beyond borders. With partners spanning multiple industries and regions, your brand gains unparalleled exposure and the ability to tap into new, lucrative markets.',\n    features: [\n      'Leverage a Global Network for Success',\n      'Partnerships for Enhanced Growth',\n      'Maximizing Opportunities Through Network Leverage',\n    ],\n  },\n  {\n    icon: '🚀',\n    title: 'Innovative Technology Solutions',\n    description:\n      'Stay ahead of the curve with our cutting-edge adtech solutions. We equip your campaigns with the tools they need to succeed in a competitive digital landscape.',\n  },\n  {\n    icon: '🔍',\n    title: 'Transparency & Accountability',\n    description:\n      \"You'll always be in the loop with comprehensive reports on your program's performance. We maintain full transparency so you can see exactly how your campaigns are performing.\",\n  },\n  {\n    icon: '👥',\n    title: 'Dedicated Support Team',\n    description:\n      'Success is a team effort. Our experienced dedicated account managers work closely with you to ensure your campaigns are running smoothly and optimally.',\n  },\n];\n\nexport function Services() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-sm font-semibold text-blue-600 uppercase tracking-wide mb-2\">\n            Our main services\n          </h2>\n          <h3 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n            Our solutions that help you grow up\n          </h3>\n        </div>\n\n        {/* Services Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {services.map((service, index) => (\n            <div\n              key={index}\n              className=\"bg-white rounded-lg border border-gray-200 p-8 hover:shadow-lg transition-shadow duration-300\"\n            >\n              <div className=\"text-4xl mb-4\">{service.icon}</div>\n              <h4 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                {service.title}\n              </h4>\n              <p className=\"text-gray-600 mb-6\">{service.description}</p>\n\n              {service.features && (\n                <ul className=\"space-y-2 mb-6\">\n                  {service.features.map((feature, featureIndex) => (\n                    <li\n                      key={featureIndex}\n                      className=\"text-sm text-gray-500 flex items-start\"\n                    >\n                      <span className=\"text-blue-500 mr-2\">•</span>\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n              )}\n\n              <Link href=\"/contact\" className=\"w-full\">\n                <Button variant=\"outline\" className=\"w-full\">\n                  Get Started\n                </Button>\n              </Link>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n", "import { Hero } from '@/components/sections/hero';\nimport { Services } from '@/components/sections/services';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <Hero />\n      <Services />\n    </div>\n  );\n}\n"], "names": [], "mappings": "oVAAA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OAEO,SAAS,IACd,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,UAAA,CAAQ,UAAU,iFAEjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,6CACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4HACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,qJACf,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iJAGjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,4DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,8DAAoD,4BACtC,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,yBAAgB,2BAElC,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,oEAA2D,0HAIxE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,oBACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,KAAK,cAAK,sBAEpB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,qBACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,KAAK,cAAK,uBAQ1C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kBACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,0CAAiC,2BAG9C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wEAEb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0EACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,iCAAwB,kBAE1C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0EACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,iCAAwB,kBAE1C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0EACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,iCAAwB,kBAE1C,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,0EACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,iCAAwB,wBAM9C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CAAmC,OAClD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,qBAEjC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CAAmC,QAClD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,yBAEjC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CAAmC,QAClD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,2BAEjC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4CAAmC,SAClD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,4BAM3C,CC5EA,IAAM,EAAW,CACf,CACE,KAAM,KACN,MAAO,gCACP,YACE,iPACF,SAAU,CACR,sCACA,wCACA,sDACD,AACH,EACA,CACE,KAAM,KACN,MAAO,mCACP,YACE,uOACF,SAAU,CACR,4CACA,wCACA,kDACD,AACH,EACA,CACE,KAAM,KACN,MAAO,iBACP,YACE,8NACF,SAAU,CACR,wCACA,mCACA,oDACD,AACH,EACA,CACE,KAAM,KACN,MAAO,kCACP,YACE,iKACJ,EACA,CACE,KAAM,KACN,MAAO,gCACP,YACE,gLACJ,EACA,CACE,KAAM,KACN,MAAO,yBACP,YACE,yJACJ,EACD,CAEM,SAAS,IACd,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,0BACjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8BACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,4EAAmE,sBAGjF,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DAAoD,2CAMpE,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gEACZ,EAAS,GAAG,CAAC,CAAC,EAAS,IACtB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAEC,UAAU,0GAEV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAiB,EAAQ,IAAI,GAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDACX,EAAQ,KAAK,GAEhB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAsB,EAAQ,WAAW,GAErD,EAAQ,QAAQ,EACf,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0BACX,EAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAS,IAC9B,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAEC,UAAU,mDAEV,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,8BAAqB,MACpC,IAJI,MAUb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,WAAW,UAAU,kBAC9B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,UAAU,kBAAS,oBAxB1C,UAkCnB,CC1Ge,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,yBACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,GACD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,KAGP"}