module.exports=[54241,a=>{"use strict";var b=a.i(14049),c=a.i(46933),d=a.i(9054),e=a.i(87952),f=a.i(68301),g=a.i(60122),h=a.i(30760),i=a.i(21694),j=a.i(77256),k=a.i(47801),l=a.i(89858),m=a.i(90787),n=a.i(61997),o=a.i(52401),p=a.i(79675),q=a.i(89535),r=a.i(48247),s=a.i(70311),t=a.i(49199),u=a.i(20022);a.i(56561);var v=a.i(9959),w=a.i(68489),x=a.i(96814),y=a.i(99993),z=a.i(33349),A=a.i(51666),B=a.i(93695),C=a.i(1555);a.i(28051);var D=a.i(10926),E=a.i(48885),F=a.i(37019),G=a.i(319),H=a.i(44746);let I=["",{children:["/_not-found",{children:["__PAGE__",{},{metadata:{},page:[()=>a.r(9154),"[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/components/builtin/not-found.js"]}]},{metadata:{}}]},{metadata:{icon:[async a=>[{url:(0,b.fillMetadataSegment)("//",await a.params,"favicon.ico")+`?${c.default.src.split("/").splice(-1)[0]}`,sizes:`${c.default.width}x${c.default.height}`,type:"image/x-icon"}]]},layout:[()=>a.r(62439),"[project]/Documents/augment-projects/flywheel-media/src/app/layout.tsx"],"not-found":[()=>a.r(9154),"[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/components/builtin/not-found.js"],forbidden:[()=>a.r(82749),"[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>a.r(62121),"[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/components/builtin/unauthorized.js"]}],J={require:a.r.bind(a),loadChunk:a.l.bind(a)},K=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:I},distDir:".next",relativeProjectDir:""});async function L(a,b,c){var d;K.isDev&&(0,h.addRequestMeta)(a,"devRequestTimingInternalsEnd",process.hrtime.bigint());let M="/_not-found/page";M=M.replace(/\/index$/,"")||"/";let N=!!(0,h.getRequestMeta)(a,"minimalMode"),O=await K.prepare(a,b,{srcPage:M,multiZoneDraftMode:!1});if(!O)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:P,query:Q,params:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac,parsedUrl:ad,interceptionRoutePatterns:ae}=O,af=(0,r.normalizeAppPath)(M),{isOnDemandRevalidate:ag}=O,ah=ac.experimental.ppr&&!ac.cacheComponents&&(0,H.isInterceptionRouteAppPath)(_)?null:K.match(_,Z),ai=!!Z.routes[_],aj=a.headers["user-agent"]||"",ak=(0,u.getBotType)(aj),al=(0,p.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??"1"===a.headers[t.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[t.RSC_HEADER],ao=(0,s.getIsPossibleServerAction)(a),ap=(0,m.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[af]??Z.dynamicRoutes[af])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?(0,h.getRequestMeta)(a,"postponed"):void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=(!al||!ap)&&(!aj||(0,p.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots)),aw=!!((ah||ai||Z.routes[af])&&!(al&&ap)),ax=ap&&!0===ac.cacheComponents,ay=!0===K.isDev||!aw||"string"==typeof as||(ax&&(0,h.getRequestMeta)(a,"onCacheEntryV2")?at&&!N:at),az=al&&ap,aA=null;$||!aw||ay||ao||as||at||(aA=_);let aB=aA;!aB&&K.isDev&&(aB=_),K.isDev||$||!aw||!an||at||(0,k.stripFlightHeaders)(a.headers);let aC={...D,tree:I,GlobalError:C.default,handler:L,routeModule:K,__next_app__:J};W&&X&&(0,o.setReferenceManifestsSingleton)({page:M,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,q.createServerModuleMap)({serverActionsManifest:W})});let aD=a.method||"GET",aE=(0,g.getTracer)(),aF=aE.getActiveScopeSpan(),aG=async()=>((null==ab?void 0:ab.render404)?await ab.render404(a,b,ad,!1):b.end("This page could not be found"),null);try{let d=K.getVaryHeader(_,ae);b.setHeader("Vary",d);let f=async(c,d)=>{let e=new l.NodeNextRequest(a),f=new l.NodeNextResponse(b);return K.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let a=aE.getRootSpanAttributes();if(!a)return;if(a.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${a.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let d=a.get("next.route");if(d){let a=`${aD} ${d}`;c.setAttributes({"next.route":d,"http.route":d,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aD} ${M}`)})},k=(0,h.getRequestMeta)(a,"incrementalCache"),m=async({span:d,postponed:e,fallbackRouteParams:g,forceStaticRender:i})=>{let l={query:Q,params:R,page:af,sharedContext:{buildId:P},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aC,Component:(0,j.interopDefault)(aC),params:R,routeModule:K,page:M,postponed:e,shouldWaitOnAllReady:az,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof e||ay,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setCacheStatus:null==ab?void 0:ab.setCacheStatus,setIsrStatus:null==ab?void 0:ab.setIsrStatus,setReactDebugChannel:null==ab?void 0:ab.setReactDebugChannel,dir:require("path").join(process.cwd(),K.relativeProjectDir),isDraftMode:$,botType:ak,isOnDemandRevalidate:ag,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,images:ac.images,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:!1,incrementalCache:k,cacheLifeProfiles:ac.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq||ar?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isDebugDynamicAccesses:aq}:{},cacheComponents:!!ac.cacheComponents,experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[],clientParamParsingOrigins:ac.experimental.clientParamParsingOrigins},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>K.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:K.isDev}};aq&&(l.renderOpts.nextExport=!0,l.renderOpts.supportsDynamicResponse=!1,l.renderOpts.isDebugDynamicAccesses=aq),i&&(l.renderOpts.supportsDynamicResponse=!1);let m=await f(d,l),{metadata:n}=m,{cacheControl:o,headers:p={},fetchTags:q,fetchMetrics:r}=n;if(q&&(p[y.NEXT_CACHE_TAGS_HEADER]=q),a.fetchMetrics=r,aw&&(null==o?void 0:o.revalidate)===0&&!K.isDev&&!ap){let a=n.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:v.CachedRouteKind.APP_PAGE,html:m,headers:p,rscData:n.flightData,postponed:n.postponed,status:n.statusCode,segmentData:n.segmentData},cacheControl:o}},o=async({hasResolved:d,previousCacheEntry:f,isRevalidating:g,span:i,forceStaticRender:j=!1})=>{let l,p=!1===K.isDev,q=d||b.writableEnded;if(ag&&aa&&!f&&!N)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ah&&(l=(0,w.parseFallbackField)(ah.fallback)),l===w.FallbackMode.PRERENDER&&(0,u.isBot)(aj)&&(!ap||al)&&(l=w.FallbackMode.BLOCKING_STATIC_RENDER),(null==f?void 0:f.isStale)===-1&&(ag=!0),ag&&(l!==w.FallbackMode.NOT_FOUND||f)&&(l=w.FallbackMode.BLOCKING_STATIC_RENDER),!N&&l!==w.FallbackMode.BLOCKING_STATIC_RENDER&&aB&&!q&&!$&&S&&(p||!ai)){if((p||ah)&&l===w.FallbackMode.NOT_FOUND){if(ac.experimental.adapterPath)return await aG();throw new B.NoFallbackError}if(ap&&(ac.cacheComponents?!at:!an)){let b=p&&"string"==typeof(null==ah?void 0:ah.fallback)?ah.fallback:af,d=p&&(null==ah?void 0:ah.fallbackRouteParams)?(0,n.createOpaqueFallbackRouteParams)(ah.fallbackRouteParams):ar?(0,n.getFallbackRouteParams)(af,K):null,f=await K.handleResponse({cacheKey:b,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>m({span:i,postponed:void 0,fallbackRouteParams:d,forceStaticRender:!1}),waitUntil:c.waitUntil,isMinimalMode:N});if(null===f)return null;if(f)return delete f.cacheControl,f}}let r=ag||g||!as?void 0:as;if(ax&&!N&&k&&at&&!j){let b=await k.get(_,{kind:v.IncrementalCacheKind.APP_PAGE,isRoutePPREnabled:!0,isFallback:!1});b&&b.value&&b.value.kind===v.CachedRouteKind.APP_PAGE&&(r=b.value.postponed,b&&(-1===b.isStale||!0===b.isStale)&&(0,G.scheduleOnNextTick)(async()=>{let b=K.getResponseCache(a);try{await b.revalidate(_,k,ap,!1,a=>o({...a,forceStaticRender:!0}),null,d,c.waitUntil)}catch(a){console.error("Error revalidating the page in the background",a)}}))}if(aq&&void 0!==r)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:v.CachedRouteKind.PAGES,html:x.default.EMPTY,pageData:{},headers:void 0,status:void 0}};let s=p&&(null==ah?void 0:ah.fallbackRouteParams)&&(0,h.getRequestMeta)(a,"renderFallbackShell")?(0,n.createOpaqueFallbackRouteParams)(ah.fallbackRouteParams):ar?(0,n.getFallbackRouteParams)(af,K):null;return m({span:i,postponed:r,fallbackRouteParams:s,forceStaticRender:j})},p=async d=>{var f,g,i,j,k;let l,n=await K.handleResponse({cacheKey:aA,responseGenerator:a=>o({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:ag,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil,isMinimalMode:N});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),K.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!n){if(aA)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(f=n.value)?void 0:f.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(i=n.value)?void 0:i.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof n.value.postponed;aw&&!at&&(!p||am)&&(N||b.setHeader("x-nextjs-cache",ag?"REVALIDATED":n.isMiss?"MISS":n.isStale?"STALE":"HIT"),b.setHeader(t.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=n;if(as)l={revalidate:0,expire:void 0};else if(at)l={revalidate:0,expire:void 0};else if(!K.isDev)if($)l={revalidate:0,expire:void 0};else if(aw){if(n.cacheControl)if("number"==typeof n.cacheControl.revalidate){if(n.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${n.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});l={revalidate:n.cacheControl.revalidate,expire:(null==(j=n.cacheControl)?void 0:j.expire)??ac.expireTime}}else l={revalidate:y.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(l={revalidate:0,expire:void 0});if(n.cacheControl=l,"string"==typeof au&&(null==q?void 0:q.kind)===v.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(k=q.headers)?void 0:k[y.NEXT_CACHE_TAGS_HEADER];N&&aw&&c&&"string"==typeof c&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,A.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:x.default.fromStatic(d,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl}):(b.statusCode=204,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl}))}let r=ax?(0,h.getRequestMeta)(a,"onCacheEntryV2")??(0,h.getRequestMeta)(a,"onCacheEntry"):(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r(n,{url:(0,h.getRequestMeta)(a,"initURL")??a.url}))return null;if(q.headers){let a={...q.headers};for(let[c,d]of(N&&aw||delete a[y.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let s=null==(g=q.headers)?void 0:g[y.NEXT_CACHE_TAGS_HEADER];if(N&&aw&&s&&"string"==typeof s&&b.setHeader(y.NEXT_CACHE_TAGS_HEADER,s),!q.status||an&&ap||(b.statusCode=q.status),!N&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&!at&&b.setHeader(t.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.html.contentType!==t.RSC_CONTENT_TYPE_HEADER)if(ac.cacheComponents)return b.statusCode=404,(0,A.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:x.default.EMPTY,cacheControl:n.cacheControl});else throw Object.defineProperty(new F.InvariantError(`Expected RSC response, got ${q.html.contentType}`),"__NEXT_ERROR_CODE",{value:"E789",enumerable:!1,configurable:!0});return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:n.cacheControl})}return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:x.default.fromStatic(q.rscData,t.RSC_CONTENT_TYPE_HEADER),cacheControl:n.cacheControl})}let u=q.html;if(!p||N||an)return(0,A.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:u,cacheControl:n.cacheControl});if(aq)return u.push(new ReadableStream({start(a){a.enqueue(z.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}});let w=new TransformStream;return u.push(w.readable),m({span:d,postponed:q.postponed,fallbackRouteParams:null,forceStaticRender:!1}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==v.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(w.writable)}).catch(a=>{w.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,A.sendRenderResult)({req:a,res:b,generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:u,cacheControl:{revalidate:0,expire:void 0}})};if(!aF)return await aE.withPropagatedContext(a.headers,()=>aE.trace(i.BaseServerSpan.handleRequest,{spanName:`${aD} ${M}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aD,"http.target":a.url}},p));await p(aF)}catch(b){throw b instanceof B.NoFallbackError||await K.onRequestError(a,b,{routerKind:"App Router",routePath:M,routeType:"render",revalidateReason:(0,f.getRevalidateReason)({isStaticGeneration:aw,isOnDemandRevalidate:ag})},ab),b}}a.s(["__next_app__",0,J,"handler",()=>L,"routeModule",0,K],74303),a.i(74303),a.s(["ClientPageRoot",()=>D.ClientPageRoot,"ClientSegmentRoot",()=>D.ClientSegmentRoot,"Fragment",()=>D.Fragment,"GlobalError",()=>C.default,"HTTPAccessFallbackBoundary",()=>D.HTTPAccessFallbackBoundary,"LayoutRouter",()=>D.LayoutRouter,"Postpone",()=>D.Postpone,"RenderFromTemplateContext",()=>D.RenderFromTemplateContext,"RootLayoutBoundary",()=>D.RootLayoutBoundary,"SegmentViewNode",()=>D.SegmentViewNode,"SegmentViewStateNode",()=>D.SegmentViewStateNode,"__next_app__",0,J,"actionAsyncStorage",()=>D.actionAsyncStorage,"captureOwnerStack",()=>D.captureOwnerStack,"collectSegmentData",()=>D.collectSegmentData,"createElement",()=>D.createElement,"createMetadataComponents",()=>D.createMetadataComponents,"createPrerenderParamsForClientSegment",()=>D.createPrerenderParamsForClientSegment,"createPrerenderSearchParamsForClientPage",()=>D.createPrerenderSearchParamsForClientPage,"createServerParamsForServerSegment",()=>D.createServerParamsForServerSegment,"createServerSearchParamsForServerPage",()=>D.createServerSearchParamsForServerPage,"createTemporaryReferenceSet",()=>D.createTemporaryReferenceSet,"decodeAction",()=>D.decodeAction,"decodeFormState",()=>D.decodeFormState,"decodeReply",()=>D.decodeReply,"handler",()=>L,"patchFetch",()=>D.patchFetch,"preconnect",()=>D.preconnect,"preloadFont",()=>D.preloadFont,"preloadStyle",()=>D.preloadStyle,"prerender",()=>D.prerender,"renderToReadableStream",()=>D.renderToReadableStream,"routeModule",0,K,"serverHooks",()=>D.serverHooks,"taintObjectReference",()=>D.taintObjectReference,"workAsyncStorage",()=>D.workAsyncStorage,"workUnitAsyncStorage",()=>D.workUnitAsyncStorage],54241)}];

//# sourceMappingURL=83e11_next_dist_esm_build_templates_app-page_d4ad6774.js.map