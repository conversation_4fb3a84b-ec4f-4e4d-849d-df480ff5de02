{"version": 3, "sources": ["turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/@swc+helpers@0.5.15/node_modules/@swc/helpers/cjs/_interop_require_default.cjs", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/client/components/builtin/app-error.tsx"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n", "import React from 'react'\n\nconst styles: Record<string, React.CSSProperties> = {\n  error: {\n    // https://github.com/sindresorhus/modern-normalize/blob/main/modern-normalize.css#L38-L52\n    fontFamily:\n      'system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"',\n    height: '100vh',\n    textAlign: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  desc: {\n    lineHeight: '48px',\n  },\n  h1: {\n    display: 'inline-block',\n    margin: '0 20px 0 0',\n    paddingRight: 23,\n    fontSize: 24,\n    fontWeight: 500,\n    verticalAlign: 'top',\n  },\n  h2: {\n    fontSize: 14,\n    fontWeight: 400,\n    lineHeight: '28px',\n  },\n  wrap: {\n    display: 'inline-block',\n  },\n} as const\n\n/* CSS minified from\nbody { margin: 0; color: #000; background: #fff; }\n.next-error-h1 {\n  border-right: 1px solid rgba(0, 0, 0, .3);\n}\n@media (prefers-color-scheme: dark) {\n  body { color: #fff; background: #000; }\n  .next-error-h1 {\n    border-right: 1px solid rgba(255, 255, 255, .3);\n  }\n}\n*/\nconst themeCss = `body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}\n@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}`\n\nfunction AppError() {\n  const errorMessage = 'Internal Server Error.'\n  const title = `500: ${errorMessage}`\n  return (\n    <html id=\"__next_error__\">\n      <head>\n        <title>{title}</title>\n      </head>\n      <body>\n        <div style={styles.error}>\n          <div style={styles.desc}>\n            <style\n              dangerouslySetInnerHTML={{\n                __html: themeCss,\n              }}\n            />\n            <h1 className=\"next-error-h1\" style={styles.h1}>\n              500\n            </h1>\n            <div style={styles.wrap}>\n              <h2 style={styles.h2}>{errorMessage}</h2>\n            </div>\n          </div>\n        </div>\n      </body>\n    </html>\n  )\n}\n\nexport default AppError\n"], "names": ["styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "lineHeight", "h1", "margin", "paddingRight", "fontSize", "fontWeight", "verticalAlign", "h2", "wrap", "themeCss", "AppError", "errorMessage", "title", "html", "id", "head", "body", "div", "style", "dangerouslySetInnerHTML", "__html", "className"], "mappings": "6NAKA,EAAQ,CAAC,CAHT,EAGY,OAHsB,AAAzB,CAA4B,EACjC,OAAO,GAAO,EAAI,UAAU,CAAG,EAAM,CAAE,QAAS,CAAI,CACxD,yGC2EA,UAAA,qCAAA,mCA/EkB,CAAA,CAAA,IAAA,GAElB,MAAMA,AACG,CAELE,QAHgD,GAI9C,8FACFC,OAAQ,QACRC,UAAW,SACXC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,QAClB,IACM,CACJE,WAAY,MACd,IACI,CACFL,QAAS,eACTO,OAAQ,aACRC,aAAc,GACdC,SAAU,GACVC,WAAY,IACZC,cAAe,KACjB,IACI,CACFF,SAAU,GACVC,WAAY,IACZL,WAAY,MACd,IACM,CACJL,QAAS,cACX,EAeIc,EAAW,CAAC;+HAC6G,CAAC,CA+BhI,EA7BA,SAASC,AA6BMA,EA5Bb,IAAMC,EAAe,yBACfC,EAAQ,CAAC,KAAK,EAAED,EAAAA,CAAc,CACpC,MACE,CAAA,AADF,EACE,EAAA,IAAA,EAACE,CADH,MACGA,CAAKC,GAAG,2BACP,CAAA,EAAA,EAAA,GAAA,EAACC,OAAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAACH,EAAD,MAACA,UAAOA,MAEV,CAAA,EAAA,EAAA,GAAA,EAACI,OAAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAACC,EAAD,IAACA,CAAIC,KAAAA,EAAO5B,OAAOC,GACjB,CAAA,CADsB,CACtB,EAAA,IAAA,EAAC0B,CAAD,KAACA,CAAIC,KAAAA,EAAO5B,OAAOS,IAAI,AACrB,CAAA,EAAA,EAAA,GAAA,EAACmB,QAAAA,CACCC,wBAAyB,CACvBC,OAAQX,CACV,IAEF,CAAA,EAAA,EAAA,GAAA,EAACR,KAAAA,CAAGoB,UAAU,gBAAgBH,KAAAA,EAAO5B,OAAOW,EAAE,CAAE,QAGhD,CAAA,EAAA,EAAA,GAAA,EAACgB,MAAAA,CAAIC,KAAAA,EAAO5B,OAAOkB,GACjB,CADqB,AACrB,EAAA,EAAA,GAAA,EAACD,EAAD,GAACA,CAAGW,KAAAA,EAAO5B,OAAOiB,EAAE,CAAGI,eAOrC", "ignoreList": [0, 1]}