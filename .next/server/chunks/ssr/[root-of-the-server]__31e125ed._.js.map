{"version": 3, "sources": ["turbopack:///[project]/Documents/augment-projects/flywheel-media/src/app/services/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Button } from '@/components/ui/button';\n\nconst services = [\n  {\n    icon: '🎯',\n    title: 'Strategic Affiliate Partnerships',\n    description:\n      'Unlock growth with tailored affiliate partnerships that enhance visibility and drive customer engagement.',\n    features: [\n      'Partner Network Development',\n      'Performance Tracking',\n      'Revenue Optimization',\n      'Strategic Planning',\n    ],\n  },\n  {\n    icon: '📊',\n    title: 'Affiliate Marketing',\n    description:\n      'At flywheel-media, we empower brands through strategic affiliate marketing, connecting them with ideal customers for meaningful growth.',\n    features: [\n      'Campaign Management',\n      'Audience Targeting',\n      'Conversion Optimization',\n      'ROI Analysis',\n    ],\n  },\n  {\n    icon: '🌐',\n    title: 'Global Network Access',\n    description:\n      'Leverage our extensive global network to reach audiences beyond borders with partners spanning multiple industries.',\n    features: [\n      'International Reach',\n      'Multi-Industry Partners',\n      'Cross-Border Solutions',\n      'Market Expansion',\n    ],\n  },\n  {\n    icon: '🚀',\n    title: 'Technology Solutions',\n    description:\n      'Stay ahead with our cutting-edge adtech solutions designed for competitive digital landscapes.',\n    features: [\n      'Advanced Analytics',\n      'Real-time Tracking',\n      'Automated Optimization',\n      'Custom Integrations',\n    ],\n  },\n  {\n    icon: '🔍',\n    title: 'Transparency & Reporting',\n    description:\n      'Comprehensive reports and full transparency so you can see exactly how your campaigns perform.',\n    features: [\n      'Detailed Analytics',\n      'Performance Metrics',\n      'Custom Reports',\n      'Real-time Data',\n    ],\n  },\n  {\n    icon: '👥',\n    title: 'Dedicated Support',\n    description:\n      'Our experienced account managers work closely with you to ensure optimal campaign performance.',\n    features: [\n      'Personal Account Manager',\n      '24/7 Support',\n      'Strategic Consultation',\n      'Ongoing Optimization',\n    ],\n  },\n];\n\nexport default function ServicesPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Our Services\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Comprehensive affiliate marketing solutions designed to drive\n              growth and maximize your business potential\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Services Grid */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {services.map((service, index) => (\n              <div\n                key={index}\n                className=\"bg-white rounded-lg border border-gray-200 p-8 hover:shadow-lg transition-shadow duration-300\"\n              >\n                <div className=\"text-4xl mb-4\">{service.icon}</div>\n                <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                  {service.title}\n                </h3>\n                <p className=\"text-gray-600 mb-6\">{service.description}</p>\n\n                <ul className=\"space-y-2 mb-6\">\n                  {service.features.map((feature, featureIndex) => (\n                    <li\n                      key={featureIndex}\n                      className=\"text-sm text-gray-500 flex items-start\"\n                    >\n                      <span className=\"text-blue-500 mr-2\">✓</span>\n                      {feature}\n                    </li>\n                  ))}\n                </ul>\n\n                <Link href=\"/contact\" className=\"w-full\">\n                  <Button variant=\"outline\" className=\"w-full\">\n                    Get Started\n                  </Button>\n                </Link>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-blue-600 py-20\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-6\">\n            Ready to Get Started?\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8\">\n            Let's discuss how our services can help accelerate your business\n            growth through strategic affiliate marketing.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link href=\"/contact\">\n              <Button variant=\"secondary\" size=\"lg\">\n                Contact Us Today\n              </Button>\n            </Link>\n            <Link href=\"/about\">\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                className=\"border-white text-white hover:bg-white hover:text-blue-600\"\n              >\n                Learn More About Us\n              </Button>\n            </Link>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": "oVAAA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,EAAW,CACf,CACE,KAAM,KACN,MAAO,mCACP,YACE,4GACF,SAAU,CACR,8BACA,uBACA,uBACA,qBACD,AACH,EACA,CACE,KAAM,KACN,MAAO,sBACP,YACE,0IACF,SAAU,CACR,sBACA,qBACA,0BACA,eACD,AACH,EACA,CACE,KAAM,KACN,MAAO,wBACP,YACE,sHACF,SAAU,CACR,sBACA,0BACA,yBACA,mBACD,AACH,EACA,CACE,KAAM,KACN,MAAO,uBACP,YACE,iGACF,SAAU,CACR,qBACA,qBACA,yBACA,sBACD,AACH,EACA,CACE,KAAM,KACN,MAAO,2BACP,YACE,iGACF,SAAU,CACR,qBACA,sBACA,iBACA,iBACD,AACH,EACA,CACE,KAAM,KACN,MAAO,oBACP,YACE,iGACF,SAAU,CACR,2BACA,eACA,yBACA,uBACD,AACH,EACD,CAEc,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kCAEb,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,8DACjB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DAAoD,iBAGlE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mDAA0C,qHAS7D,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,iBACjB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,gEACZ,EAAS,GAAG,CAAC,CAAC,EAAS,IACtB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAEC,UAAU,0GAEV,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAiB,EAAQ,IAAI,GAC5C,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDACX,EAAQ,KAAK,GAEhB,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,8BAAsB,EAAQ,WAAW,GAEtD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0BACX,EAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAS,IAC9B,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAEC,UAAU,mDAEV,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,8BAAqB,MACpC,IAJI,MASX,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,WAAW,UAAU,kBAC9B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,UAAU,UAAU,kBAAS,oBAtB1C,UAiCf,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,6BACjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0DAAiD,0BAG/D,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,mHAI1C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2DACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,oBACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,YAAY,KAAK,cAAK,uBAIxC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,kBACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CACL,QAAQ,UACR,KAAK,KACL,UAAU,sEACX,oCASf"}