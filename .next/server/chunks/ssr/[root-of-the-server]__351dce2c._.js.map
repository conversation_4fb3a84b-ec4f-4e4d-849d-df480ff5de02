{"version": 3, "sources": ["turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/app-dir/link.js/__nextjs-internal-proxy.cjs", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/client/app-dir/link.tsx", "turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/src/client/app-dir/link.react-server.tsx", "turbopack:///[next]/internal/font/google/inter_5901b7c6.module.css [app-rsc] (css module)", "turbopack:///[project]/Documents/augment-projects/flywheel-media/src/components/layout/header.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[next]/internal/font/google/inter_5901b7c6.js", "turbopack:///[project]/Documents/augment-projects/flywheel-media/src/app/layout.tsx", "turbopack:///[project]/Documents/augment-projects/flywheel-media/src/components/layout/footer.tsx"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nconst { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/app-dir/link.js\"));\n", "'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport {\n  FetchStrategy,\n  type PrefetchTaskFetchStrategy,\n} from '../components/segment-cache'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref legacyBehavior>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `\"auto\"`, `null`, `undefined` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | 'auto' | null\n\n  /**\n   * (unstable) Switch to a full prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior.\n   *\n   * @deprecated This will be removed in a future version\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when type generation runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  if (typeof window !== 'undefined') {\n    const { nodeName } = e.currentTarget\n\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n    if (\n      (isAnchorNodeName && isModifiedEvent(e)) ||\n      e.currentTarget.hasAttribute('download')\n    ) {\n      // ignore click for browser’s default behavior\n      return\n    }\n\n    if (!isLocalURL(href)) {\n      if (replace) {\n        // browser default behavior does not replace the history state\n        // so we need to do it manually\n        e.preventDefault()\n        location.replace(href)\n      }\n\n      // ignore click for browser’s default behavior\n      return\n    }\n\n    e.preventDefault()\n\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    const { dispatchNavigateAction } =\n      require('../components/app-router-instance') as typeof import('../components/app-router-instance')\n\n    React.startTransition(() => {\n      dispatchNavigateAction(\n        as || href,\n        replace ? 'replace' : 'push',\n        scroll ?? true,\n        linkInstanceRef.current\n      )\n    })\n  }\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n\n  const fetchStrategy =\n    prefetchProp !== false\n      ? getFetchStrategyFromPrefetchProp(prefetchProp)\n      : // TODO: it makes no sense to assign a fetchStrategy when prefetching is disabled.\n        FetchStrategy.PPR\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else if (key === 'prefetch') {\n        if (\n          props[key] != null &&\n          valType !== 'boolean' &&\n          props[key] !== 'auto'\n        ) {\n          throw createPropError({\n            key,\n            expected: '`boolean | \"auto\"`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if ((children as any)?.$$typeof === Symbol.for('react.lazy')) {\n      throw new Error(\n        `\\`<Link legacyBehavior>\\` received a direct child that is either a Server Component, or JSX that was loaded with React.lazy(). This is not supported. Either remove legacyBehavior, or make the direct child a Client Component that renders the Link's \\`<a>\\` tag.`\n      )\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          fetchStrategy,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, fetchStrategy, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n      if (e.defaultPrevented) {\n        return\n      }\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n\nfunction getFetchStrategyFromPrefetchProp(\n  prefetchProp: Exclude<LinkProps['prefetch'], undefined | false>\n): PrefetchTaskFetchStrategy {\n  if (\n    process.env.__NEXT_CACHE_COMPONENTS &&\n    process.env.__NEXT_CLIENT_SEGMENT_CACHE\n  ) {\n    if (prefetchProp === true) {\n      return FetchStrategy.Full\n    }\n\n    // `null` or `\"auto\"`: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport.\n    // This will also include invalid prop values that don't match the types specified here.\n    // (although those should've been filtered out by prop validation in dev)\n    prefetchProp satisfies null | 'auto'\n    // In `clientSegmentCache`, we default to PPR, and we'll discover whether or not the route supports it with the initial prefetch.\n    // If we're not using `clientSegmentCache`, this will be converted into a `PrefetchKind.AUTO`.\n    return FetchStrategy.PPR\n  } else {\n    return prefetchProp === null || prefetchProp === 'auto'\n      ? // In `clientSegmentCache`, we default to PPR, and we'll discover whether or not the route supports it with the initial prefetch.\n        // If we're not using `clientSegmentCache`, this will be converted into a `PrefetchKind.AUTO`.\n        FetchStrategy.PPR\n      : // In the old implementation without runtime prefetches, `prefetch={true}` forces all dynamic data to be prefetched.\n        // To preserve backwards-compatibility, anything other than `false`, `null`, or `\"auto\"` results in a full prefetch.\n        // (although invalid values should've been filtered out by prop validation in dev)\n        FetchStrategy.Full\n  }\n}\n", "import type { ComponentProps } from 'react'\nimport ClientLinkComponent, { type LinkProps, useLinkStatus } from './link'\n\nexport default function LinkComponent(\n  props: ComponentProps<typeof ClientLinkComponent>\n) {\n  const isLegacyBehavior = props.legacyBehavior\n  const childIsHostComponent =\n    typeof props.children === 'string' ||\n    typeof props.children === 'number' ||\n    typeof (props.children as any)?.type === 'string'\n  const childIsClientComponent =\n    (props.children as any)?.type?.$$typeof ===\n    Symbol.for('react.client.reference')\n\n  if (isLegacyBehavior && !childIsHostComponent && !childIsClientComponent) {\n    if ((props.children as any)?.type?.$$typeof === Symbol.for('react.lazy')) {\n      console.error(\n        `Using a Lazy Component as a direct child of \\`<Link legacyBehavior>\\` from a Server Component is not supported. If you need legacyBehavior, wrap your Lazy Component in a Client Component that renders the Link's \\`<a>\\` tag.`\n      )\n    } else {\n      console.error(\n        `Using a Server Component as a direct child of \\`<Link legacyBehavior>\\` is not supported. If you need legacyBehavior, wrap your Server Component in a Client Component that renders the Link's \\`<a>\\` tag.`\n      )\n    }\n  }\n\n  return <ClientLinkComponent {...props} />\n}\n\nexport { type LinkProps, useLinkStatus }\n", "__turbopack_context__.v({\n  \"className\": \"inter_5901b7c6-module__ec5Qua__className\",\n  \"variable\": \"inter_5901b7c6-module__ec5Qua__variable\",\n});\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/Documents/augment-projects/flywheel-media/src/components/layout/header.tsx\",\n    \"Header\",\n);\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import type { <PERSON>ada<PERSON> } from 'next';\nimport { Inter } from 'next/font/google';\nimport './globals.css';\nimport { Header } from '@/components/layout/header';\nimport { Footer } from '@/components/layout/footer';\n\nconst inter = Inter({\n  subsets: ['latin'],\n  variable: '--font-inter',\n});\n\nexport const metadata: Metadata = {\n  title: 'flywheel-media - Driving Growth with Smart Affiliate Marketing',\n  description:\n    'Join us in forging powerful connections between brands and customers through strategic affiliate marketing solutions.',\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body className={`${inter.variable} font-sans antialiased`}>\n        <Header />\n        <main className=\"min-h-screen\">{children}</main>\n        <Footer />\n      </body>\n    </html>\n  );\n}\n", "import Link from 'next/link';\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Logo and Description */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <Link\n              href=\"/\"\n              className=\"text-2xl font-bold text-blue-400 mb-4 block\"\n            >\n              flywheel-media\n            </Link>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              It's time to tell the world about what you do. Let's work together\n              to share your story in a way that captivates and inspires.\n            </p>\n            <div className=\"flex space-x-4\">\n              <Link\n                href=\"#\"\n                className=\"text-gray-300 hover:text-blue-400 transition-colors\"\n              >\n                Visit LinkedIn Page\n              </Link>\n              <Link\n                href=\"#\"\n                className=\"text-gray-300 hover:text-blue-400 transition-colors\"\n              >\n                Follow Us On Instagram\n              </Link>\n              <Link\n                href=\"#\"\n                className=\"text-gray-300 hover:text-blue-400 transition-colors\"\n              >\n                Facebook Page\n              </Link>\n            </div>\n          </div>\n\n          {/* Important Links */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Important Links</h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <Link\n                  href=\"/\"\n                  className=\"text-gray-300 hover:text-blue-400 transition-colors\"\n                >\n                  Home\n                </Link>\n              </li>\n              <li>\n                <Link\n                  href=\"/about\"\n                  className=\"text-gray-300 hover:text-blue-400 transition-colors\"\n                >\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link\n                  href=\"/services\"\n                  className=\"text-gray-300 hover:text-blue-400 transition-colors\"\n                >\n                  Services\n                </Link>\n              </li>\n              <li>\n                <Link\n                  href=\"/contact\"\n                  className=\"text-gray-300 hover:text-blue-400 transition-colors\"\n                >\n                  Contact Us\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Newsletter */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-4\">Newsletter</h3>\n            <form className=\"space-y-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter Your Email\"\n                className=\"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n              <button\n                type=\"submit\"\n                className=\"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\"\n              >\n                Subscribe\n              </button>\n            </form>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-gray-400 text-sm\">\n            © 2024 flywheel-media, All rights reserved.\n          </p>\n          <div className=\"flex space-x-6 mt-4 md:mt-0\">\n            <Link\n              href=\"#\"\n              className=\"text-gray-400 hover:text-blue-400 text-sm transition-colors\"\n            >\n              Privacy Policy\n            </Link>\n            <Link\n              href=\"#\"\n              className=\"text-gray-400 hover:text-blue-400 text-sm transition-colors\"\n            >\n              Terms of service\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "window", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "isDefaultPrevented", "dispatchNavigateAction", "require", "React", "startTransition", "current", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "fetchStrategy", "getFetchStrategyFromPrefetchProp", "FetchStrategy", "PPR", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "$$typeof", "Symbol", "for", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext", "__NEXT_CACHE_COMPONENTS", "__NEXT_CLIENT_SEGMENT_CACHE", "Full", "isLegacyBehavior", "childIsHostComponent", "childIsClientComponent", "error", "ClientLinkComponent"], "mappings": "gCACA,GAAM,yBAAE,CAAuB,CAAE,CAAA,EAAA,CAAA,CAAA,OAEjC,EAAsB,CAAC,CAAC,EAAwB,yOAFhD,GAAM,CAAE,yBAAuB,CAAE,CAAA,EAAA,CAAA,CAAA,OAEjC,EAAsB,CAAC,CAAC,EAAwB,oVEAhD,OAyBC,CAAA,kBAzBuBA,GA2BCC,aAAa,CAAA,kBAAbA,EAAAA,aAAa,4GA7B6B,CAAA,CAAA,IAAA,IAEpD,SAASD,EACtBuC,CAAiD,EAEjD,IAAMmG,EAAmBnG,EAAMmB,cAAc,CACvCiF,EACsB,UAA1B,OAAOpG,EAAMK,QAAQ,EACrB,AAA0B,iBAAnBL,EAAMK,QAAQ,EACoB,UAAzC,OAAQL,EAAMK,QAAQ,EAAUkE,KAC5B8B,EACHrG,EAAMK,QAAQ,EAAUkE,MAAMR,WAC/BC,OAAOC,GAAG,CAAC,0BAcb,OAZIkC,AAYJ,GAZyBC,GAAyBC,IAC3CrG,CAWP,CAXaK,QAAQ,AADG,EACOkE,KADkB,CACZR,EADqC,SACxBC,OAAOC,GAAG,CAAC,cACzDC,CADwE,OAChEoC,KAAK,CACX,CAAC,8NAGHpC,CAHkO,CAAC,MAG3NoC,KAAK,CACX,CAAC,2MAA2M,AAK3M,CAL4M,AAK5M,EAAA,EAAA,GAAA,EAACC,EAAAA,OAAmB,CAAA,CAAE,GAAGvG,CAAK,EACvC,mOC5BA,EAAA,CAAA,CAAA,CACA,UAAA,2CACA,SAAA,yCACA,2BCDO,IAAM,EAAS,CAAA,EADtB,AACsB,EADtB,CAAA,CAAA,OACsB,uBAAA,AAAuB,EACzC,WAAa,MAAU,AAAJ,MAAU,0NAA4N,EACzP,2GACA,sDAHG,IAAM,EAAS,CAAA,EADtB,AACsB,EADtB,CAAA,CAAA,OACsB,uBAAA,AAAuB,EACzC,WAAa,MAAM,AAAI,MAAM,0NAA4N,EACzP,uFACA,kICLJ,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,4BACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,EAClB,GAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAA,AAAQ,ECR1C,IAAA,EAAA,EAAA,CAAA,CAAA,OCHA,EAAA,EAAA,CAAA,CAAA,MAEO,SAAS,IACd,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CAAO,UAAU,kCAChB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,yDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kDAEb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,qCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,IACL,UAAU,uDACX,mBAGD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,uCAA8B,kIAI3C,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,2BACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,IACL,UAAU,+DACX,wBAGD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,IACL,UAAU,+DACX,2BAGD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,IACL,UAAU,+DACX,wBAOL,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,sCAA6B,oBAC3C,CAAA,EAAA,EAAA,IAAA,EAAC,KAAA,CAAG,UAAU,sBACZ,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,IACL,UAAU,+DACX,WAIH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,SACL,UAAU,+DACX,eAIH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,YACL,UAAU,+DACX,eAIH,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,UACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,WACL,UAAU,+DACX,uBAQP,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,sCAA6B,eAC3C,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAU,sBACd,CAAA,EAAA,EAAA,GAAA,EAAC,QAAA,CACC,KAAK,QACL,YAAY,mBACZ,UAAU,uJAEZ,CAAA,EAAA,EAAA,GAAA,EAAC,SAAA,CACC,KAAK,SACL,UAAU,kGACX,uBAQP,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,sGACb,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,iCAAwB,gDAGrC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wCACb,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,IACL,UAAU,uEACX,mBAGD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CACH,KAAK,IACL,UAAU,uEACX,+BAQb,CDzGe,SAAS,EAAW,UACjC,CAAQ,CAGR,EACA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,cACT,CAAA,EAAA,EAAA,IAAA,EAAC,OAAA,CAAK,UAAW,CAAA,EDVR,ACUW,EAAM,QAAQ,CAAC,sBAAsB,CAAC,WACxD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAA,GACP,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,wBAAgB,IAChC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,OAIT,mCApBkC,CAChC,MAAO,iEACP,YACE,uHACJ", "ignoreList": [0, 2, 3, 4, 5]}