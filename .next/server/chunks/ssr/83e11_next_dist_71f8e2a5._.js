module.exports=[9799,(a,b,c)=>{"use strict";b.exports=a.r(78343).vendored["react-ssr"].ReactDOM},92073,36694,6139,a=>{"use strict";let b="_rsc",c="x-nextjs-rewritten-path",d="x-nextjs-rewritten-query";function e(a){return"("===a[0]&&a.endsWith(")")}function f(a,b){if(a.includes(h)){let a=JSON.stringify(b);return"{}"!==a?h+"?"+a:h}return a}function g(a,b){if(!a||0===a.length)return null;let c="children"===b?a[0]:a[a.length-1];return c===i?null:c}a.s(["NEXT_DID_POSTPONE_HEADER",0,"x-nextjs-postponed","NEXT_REWRITTEN_PATH_HEADER",0,c,"NEXT_REWRITTEN_QUERY_HEADER",0,d,"NEXT_ROUTER_PREFETCH_HEADER",0,"next-router-prefetch","NEXT_ROUTER_SEGMENT_PREFETCH_HEADER",0,"next-router-segment-prefetch","NEXT_ROUTER_STALE_TIME_HEADER",0,"x-nextjs-stale-time","NEXT_ROUTER_STATE_TREE_HEADER",0,"next-router-state-tree","NEXT_RSC_UNION_QUERY",0,b,"NEXT_URL",0,"next-url","RSC_CONTENT_TYPE_HEADER",0,"text/x-component","RSC_HEADER",0,"rsc"],92073);let h="__PAGE__",i="__DEFAULT__";function j(a){let b=a.headers.get(d);return null!==b?""===b?"":"?"+b:o(new URL(a.url)).search}function k(a){return a.headers.get(c)??o(new URL(a.url)).pathname}function l(a,b,c){switch(a){case"c":case"ci":return c<b.length?b.slice(c).map(a=>encodeURIComponent(a)):[];case"oc":return c<b.length?b.slice(c).map(a=>encodeURIComponent(a)):null;case"d":case"di":if(c>=b.length)return"";return encodeURIComponent(b[c]);default:return""}}function m(a){return!(""===a||a.startsWith(h)||"("===a[0]&&a.endsWith(")"))&&a!==i&&"/_not-found"!==a}function n(a,b){return"string"==typeof a?f(a,Object.fromEntries(new URLSearchParams(b))):null===a?"":a.join("/")}function o(a){let c=new URL(a);if(c.searchParams.delete(b),c.pathname.endsWith(".txt")){let{pathname:a}=c,b=a.endsWith("/index.txt")?10:4;c.pathname=a.slice(0,-b)}return c}function p(a,b){return"c"===b||"oc"===b?a.split("/"):a}function q(a){let b={};for(let[c,d]of a.entries())void 0===b[c]?b[c]=d:Array.isArray(b[c])?b[c].push(d):b[c]=[b[c],d];return b}a.s(["DEFAULT_SEGMENT_KEY",0,i,"PAGE_SEGMENT_KEY",0,h,"addSearchParamsIfPageSegment",()=>f,"computeSelectedLayoutSegment",()=>g,"getSelectedLayoutSegmentPath",()=>function a(b,c,d=!0,e=[]){var f;let g;if(d)g=b[1][c];else{let a=b[1];g=a.children??Object.values(a)[0]}if(!g)return e;let i=Array.isArray(f=g[0])?f[1]:f;return!i||i.startsWith(h)?e:(e.push(i),a(g,c,!1,e))},"isGroupSegment",()=>e],36694),a.s(["doesStaticSegmentAppearInURL",()=>m,"getCacheKeyForDynamicParam",()=>n,"getParamValueFromCacheKey",()=>p,"getRenderedPathname",()=>k,"getRenderedSearch",()=>j,"parseDynamicParamFromURLPart",()=>l,"urlSearchParamsToParsedUrlQuery",()=>q,"urlToUrlWithoutFlightMarker",()=>o],6139)},8863,(a,b,c)=>{"use strict";b.exports=a.r(78343).vendored.contexts.HooksClientContext},55331,a=>{"use strict";let b={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},c=new Set(Object.values(b)),d="NEXT_HTTP_ERROR_FALLBACK";function e(a){if("object"!=typeof a||null===a||!("digest"in a)||"string"!=typeof a.digest)return!1;let[b,e]=a.digest.split(";");return b===d&&c.has(Number(e))}function f(a){return Number(a.digest.split(";")[1])}function g(a){switch(a){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}a.s(["HTTPAccessErrorStatus",0,b,"HTTP_ERROR_FALLBACK_ERROR_CODE",0,d,"getAccessFallbackErrorTypeByStatus",()=>g,"getAccessFallbackHTTPStatus",()=>f,"isHTTPAccessFallbackError",()=>e])},15388,41629,58272,a=>{"use strict";var b,c,d=a.i(55331),e=((b={})[b.SeeOther=303]="SeeOther",b[b.TemporaryRedirect=307]="TemporaryRedirect",b[b.PermanentRedirect=308]="PermanentRedirect",b);a.s(["RedirectStatusCode",()=>e],41629);let f="NEXT_REDIRECT";var g=((c={}).push="push",c.replace="replace",c);function h(a){if("object"!=typeof a||null===a||!("digest"in a)||"string"!=typeof a.digest)return!1;let b=a.digest.split(";"),[c,d]=b,g=b.slice(2,-2).join(";"),h=Number(b.at(-2));return c===f&&("replace"===d||"push"===d)&&"string"==typeof g&&!isNaN(h)&&h in e}function i(a){return h(a)||(0,d.isHTTPAccessFallbackError)(a)}a.s(["REDIRECT_ERROR_CODE",0,f,"RedirectType",()=>g,"isRedirectError",()=>h],58272),a.s(["isNextRouterError",()=>i],15388)},91398,(a,b,c)=>{"use strict";b.exports=a.r(78343).vendored.contexts.ServerInsertedHtml},74724,a=>{"use strict";a.s(["METADATA_BOUNDARY_NAME",0,"__next_metadata_boundary__","OUTLET_BOUNDARY_NAME",0,"__next_outlet_boundary__","ROOT_LAYOUT_BOUNDARY_NAME",0,"__next_root_layout_boundary__","VIEWPORT_BOUNDARY_NAME",0,"__next_viewport_boundary__"])},86794,a=>{"use strict";class b extends Error{constructor(a,b){super(`Invariant: ${a.endsWith(".")?a:a+"."} This is a bug in Next.js.`,b),this.name="InvariantError"}}a.s(["InvariantError",()=>b])},25758,10290,84659,6445,25284,38499,a=>{"use strict";function b(a){return"object"==typeof a&&null!==a&&"digest"in a&&a.digest===c}let c="HANGING_PROMISE_REJECTION";class d extends Error{constructor(a,b){super(`During prerendering, ${b} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${b} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route "${a}".`),this.route=a,this.expression=b,this.digest=c}}let e=new WeakMap;function f(a,b,c){if(a.aborted)return Promise.reject(new d(b,c));{let f=new Promise((f,g)=>{let h=g.bind(null,new d(b,c)),i=e.get(a);if(i)i.push(h);else{let b=[h];e.set(a,b),a.addEventListener("abort",()=>{for(let a=0;a<b.length;a++)b[a]()},{once:!0})}});return f.catch(g),f}}function g(){}function h(a,b,c){return b.stagedRendering?b.stagedRendering.delayUntilStage(c,void 0,a):new Promise(b=>{setTimeout(()=>{b(a)},0)})}a.s(["isHangingPromiseRejectionError",()=>b,"makeDevtoolsIOAwarePromise",()=>h,"makeHangingPromise",()=>f],25758);let i="BAILOUT_TO_CLIENT_SIDE_RENDERING";class j extends Error{constructor(a){super(`Bail out to client-side rendering: ${a}`),this.reason=a,this.digest=i}}function k(a){return"object"==typeof a&&null!==a&&"digest"in a&&a.digest===i}a.s(["BailoutToCSRError",()=>j,"isBailoutToCSRError",()=>k],10290);var l,m,n=a.i(27547);let o="DYNAMIC_SERVER_USAGE";class p extends Error{constructor(a){super(`Dynamic server usage: ${a}`),this.description=a,this.digest=o}}function q(a){return"object"==typeof a&&null!==a&&"digest"in a&&"string"==typeof a.digest&&a.digest===o}a.s(["DynamicServerError",()=>p,"isDynamicServerError",()=>q],84659);class r extends Error{constructor(...a){super(...a),this.code="NEXT_STATIC_GEN_BAILOUT"}}a.s(["StaticGenBailoutError",()=>r],6445);var s=a.i(32319),t=a.i(56704),u=a.i(74724);let v=a=>{Promise.resolve().then(()=>{process.nextTick(a)})};var w=a.i(86794),x=((l={})[l.Static=1]="Static",l[l.Runtime=2]="Runtime",l[l.Dynamic=3]="Dynamic",l);a.s(["RenderStage",()=>x],25284);let y="function"==typeof n.default.unstable_postpone;function z(a){return{isDebugDynamicAccesses:a,dynamicAccesses:[],syncDynamicErrorWithStack:null}}function A(){return{hasSuspenseAboveBody:!1,hasDynamicMetadata:!1,hasDynamicViewport:!1,hasAllowedDynamic:!1,dynamicErrors:[]}}function B(a){var b;return null==(b=a.dynamicAccesses[0])?void 0:b.expression}function C(a,b,c){if(b)switch(b.type){case"cache":case"unstable-cache":case"private-cache":return}if(!a.forceDynamic&&!a.forceStatic){if(a.dynamicShouldError)throw Object.defineProperty(new r(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${c}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(b)switch(b.type){case"prerender-ppr":return K(a.route,c,b.dynamicTracking);case"prerender-legacy":b.revalidate=0;let d=Object.defineProperty(new p(`Route ${a.route} couldn't be rendered statically because it used ${c}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:!1,configurable:!0});throw a.dynamicUsageDescription=c,a.dynamicUsageStack=d.stack,d}}}function D(a,b,c){let d=Object.defineProperty(new p(`Route ${b.route} couldn't be rendered statically because it used \`${a}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw c.revalidate=0,b.dynamicUsageDescription=a,b.dynamicUsageStack=d.stack,d}function E(a){switch(a.type){case"cache":case"unstable-cache":case"private-cache":return}}function F(a,b,c){let d=P(`Route ${a} needs to bail out of prerendering at this point because it used ${b}.`);c.controller.abort(d);let e=c.dynamicTracking;e&&e.dynamicAccesses.push({stack:e.isDebugDynamicAccesses?Error().stack:void 0,expression:b})}function G(a,b,c,d){let e=d.dynamicTracking;F(a,b,d),e&&null===e.syncDynamicErrorWithStack&&(e.syncDynamicErrorWithStack=c)}function H(a){a.stagedRendering&&a.stagedRendering.advanceStage(x.Dynamic)}function I(a,b,c,d){if(!1===d.controller.signal.aborted){F(a,b,d);let e=d.dynamicTracking;e&&null===e.syncDynamicErrorWithStack&&(e.syncDynamicErrorWithStack=c)}throw P(`Route ${a} needs to bail out of prerendering at this point because it used ${b}.`)}function J({reason:a,route:b}){let c=s.workUnitAsyncStorage.getStore();K(b,a,c&&"prerender-ppr"===c.type?c.dynamicTracking:null)}function K(a,b,c){(function(){if(!y)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),c&&c.dynamicAccesses.push({stack:c.isDebugDynamicAccesses?Error().stack:void 0,expression:b}),n.default.unstable_postpone(L(a,b))}function L(a,b){return`Route ${a} needs to bail out of prerendering at this point because it used ${b}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function M(a){return"object"==typeof a&&null!==a&&"string"==typeof a.message&&N(a.message)}function N(a){return a.includes("needs to bail out of prerendering at this point because it used")&&a.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===N(L("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});let O="NEXT_PRERENDER_INTERRUPTED";function P(a){let b=Object.defineProperty(Error(a),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return b.digest=O,b}function Q(a){return"object"==typeof a&&null!==a&&a.digest===O&&"name"in a&&"message"in a&&a instanceof Error}function R(a){return a.length>0}function S(a,b){return a.dynamicAccesses.push(...b.dynamicAccesses),a.dynamicAccesses}function T(a){return a.filter(a=>"string"==typeof a.stack&&a.stack.length>0).map(({expression:a,stack:b})=>(b=b.split("\n").slice(4).filter(a=>!(a.includes("node_modules/next/")||a.includes(" (<anonymous>)")||a.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${a}:
${b}`))}function U(){let a=new AbortController;return a.abort(Object.defineProperty(new j("Render in Browser"),"__NEXT_ERROR_CODE",{value:"E721",enumerable:!1,configurable:!0})),a.signal}function V(a){switch(a.type){case"prerender":case"prerender-runtime":let b=new AbortController;if(a.cacheSignal)a.cacheSignal.inputReady().then(()=>{b.abort()});else{let c=(0,s.getRuntimeStagePromise)(a);c?c.then(()=>v(()=>b.abort())):v(()=>b.abort())}return b.signal;case"prerender-client":case"prerender-ppr":case"prerender-legacy":case"request":case"cache":case"private-cache":case"unstable-cache":return}}function W(a,b){let c=b.dynamicTracking;c&&c.dynamicAccesses.push({stack:c.isDebugDynamicAccesses?Error().stack:void 0,expression:a})}function X(a){let b=t.workAsyncStorage.getStore(),c=s.workUnitAsyncStorage.getStore();if(b&&c)switch(c.type){case"prerender-client":case"prerender":{let d=c.fallbackRouteParams;d&&d.size>0&&n.default.use(f(c.renderSignal,b.route,a));break}case"prerender-ppr":{let d=c.fallbackRouteParams;if(d&&d.size>0)return K(b.route,a,c.dynamicTracking);break}case"prerender-runtime":throw Object.defineProperty(new w.InvariantError(`\`${a}\` was called during a runtime prerender. Next.js should be preventing ${a} from being included in server components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E771",enumerable:!1,configurable:!0});case"cache":case"private-cache":throw Object.defineProperty(new w.InvariantError(`\`${a}\` was called inside a cache scope. Next.js should be preventing ${a} from being included in server components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E745",enumerable:!1,configurable:!0})}}function Y(a){let b=t.workAsyncStorage.getStore(),c=s.workUnitAsyncStorage.getStore();if(b)switch(!c&&(0,s.throwForMissingRequestStore)(a),c.type){case"prerender-client":n.default.use(f(c.renderSignal,b.route,a));break;case"prerender-legacy":case"prerender-ppr":if(b.forceStatic)return;throw Object.defineProperty(new j(a),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});case"prerender":case"prerender-runtime":throw Object.defineProperty(new w.InvariantError(`\`${a}\` was called from a Server Component. Next.js should be preventing ${a} from being included in server components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E795",enumerable:!1,configurable:!0});case"cache":case"unstable-cache":case"private-cache":throw Object.defineProperty(new w.InvariantError(`\`${a}\` was called inside a cache scope. Next.js should be preventing ${a} from being included in server components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E745",enumerable:!1,configurable:!0});case"request":return}}let Z=/\n\s+at Suspense \(<anonymous>\)/,$=RegExp(`\\n\\s+at Suspense \\(<anonymous>\\)(?:(?!\\n\\s+at (?:body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6) \\(<anonymous>\\))[\\s\\S])*?\\n\\s+at ${u.ROOT_LAYOUT_BOUNDARY_NAME} \\([^\\n]*\\)`),_=RegExp(`\\n\\s+at ${u.METADATA_BOUNDARY_NAME}[\\n\\s]`),aa=RegExp(`\\n\\s+at ${u.VIEWPORT_BOUNDARY_NAME}[\\n\\s]`),ab=RegExp(`\\n\\s+at ${u.OUTLET_BOUNDARY_NAME}[\\n\\s]`);function ac(a,b,c,d){if(!ab.test(b)){if(_.test(b)){c.hasDynamicMetadata=!0;return}if(aa.test(b)){c.hasDynamicViewport=!0;return}if($.test(b)){c.hasAllowedDynamic=!0,c.hasSuspenseAboveBody=!0;return}else if(Z.test(b)){c.hasAllowedDynamic=!0;return}else{var e,f;let g;if(d.syncDynamicErrorWithStack)return void c.dynamicErrors.push(d.syncDynamicErrorWithStack);let h=(e=`Route "${a.route}": Uncached data was accessed outside of <Suspense>. This delays the entire page from rendering, resulting in a slow user experience. Learn more: https://nextjs.org/docs/messages/blocking-route`,f=b,(g=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})).stack=g.name+": "+e+f,g);return void c.dynamicErrors.push(h)}}}var ad=((m={})[m.Full=0]="Full",m[m.Empty=1]="Empty",m[m.Errored=2]="Errored",m);function ae(a,b){console.error(b),a.dev||(a.hasReadableErrorStacks?console.error(`To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \`next dev\`, then open "${a.route}" in your browser to investigate the error.`):console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:
  - Start the app in development mode by running \`next dev\`, then open "${a.route}" in your browser to investigate the error.
  - Rerun the production build with \`next build --debug-prerender\` to generate better stack traces.`))}function af(a,b,c,d){if(d.syncDynamicErrorWithStack)throw ae(a,d.syncDynamicErrorWithStack),new r;if(0!==b){if(c.hasSuspenseAboveBody)return;let d=c.dynamicErrors;if(d.length>0){for(let b=0;b<d.length;b++)ae(a,d[b]);throw new r}if(c.hasDynamicViewport)throw console.error(`Route "${a.route}" has a \`generateViewport\` that depends on Request data (\`cookies()\`, etc...) or uncached external data (\`fetch(...)\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`),new r;if(1===b)throw console.error(`Route "${a.route}" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`),new r}else if(!1===c.hasAllowedDynamic&&c.hasDynamicMetadata)throw console.error(`Route "${a.route}" has a \`generateMetadata\` that depends on Request data (\`cookies()\`, etc...) or uncached external data (\`fetch(...)\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`),new r}function ag(a,b){return a.runtimeStagePromise?a.runtimeStagePromise.then(()=>b):b}a.s(["Postpone",()=>J,"PreludeState",()=>ad,"abortAndThrowOnSynchronousRequestDataAccess",()=>I,"abortOnSynchronousPlatformIOAccess",()=>G,"accessedDynamicData",()=>R,"annotateDynamicAccess",()=>W,"consumeDynamicAccess",()=>S,"createDynamicTrackingState",()=>z,"createDynamicValidationState",()=>A,"createHangingInputAbortSignal",()=>V,"createRenderInBrowserAbortSignal",()=>U,"delayUntilRuntimeStage",()=>ag,"formatDynamicAPIAccesses",()=>T,"getFirstDynamicReason",()=>B,"isDynamicPostpone",()=>M,"isPrerenderInterruptedError",()=>Q,"logDisallowedDynamicError",()=>ae,"markCurrentScopeAsDynamic",()=>C,"postponeWithTracking",()=>K,"throwIfDisallowedDynamic",()=>af,"throwToInterruptStaticGeneration",()=>D,"trackAllowedDynamicAccess",()=>ac,"trackDynamicDataInDynamicRender",()=>E,"trackSynchronousPlatformIOAccessInDev",()=>H,"useDynamicRouteParams",()=>X,"useDynamicSearchParams",()=>Y],38499)},49997,a=>{"use strict";var b=a.i(25758);let c=Symbol.for("react.postpone");var d=a.i(10290),e=a.i(15388),f=a.i(38499),g=a.i(84659);a.s(["unstable_rethrow",()=>function a(h){if((0,e.isNextRouterError)(h)||(0,d.isBailoutToCSRError)(h)||(0,g.isDynamicServerError)(h)||(0,f.isDynamicPostpone)(h)||"object"==typeof h&&null!==h&&h.$$typeof===c||(0,b.isHangingPromiseRejectionError)(h)||(0,f.isPrerenderInterruptedError)(h))throw h;h instanceof Error&&"cause"in h&&a(h.cause)}],49997)},84094,81447,63300,a=>{"use strict";var b=a.i(27547),c=a.i(8863);function d(){return!function(){{let{workUnitAsyncStorage:b}=a.r(32319),c=b.getStore();if(!c)return!1;switch(c.type){case"prerender":case"prerender-client":case"prerender-ppr":let d=c.fallbackRouteParams;return!!d&&d.size>0}return!1}}()?(0,b.useContext)(c.PathnameContext):null}a.s(["useUntrackedPathname",()=>d],84094),a.s([],81447);var e=a.i(59952),f=b,g=a.i(55331),h=a.i(63386);class i extends f.default.Component{constructor(a){super(a),this.state={triggeredStatus:void 0,previousPathname:a.pathname}}componentDidCatch(){}static getDerivedStateFromError(a){if((0,g.isHTTPAccessFallbackError)(a))return{triggeredStatus:(0,g.getAccessFallbackHTTPStatus)(a)};throw a}static getDerivedStateFromProps(a,b){return a.pathname!==b.previousPathname&&b.triggeredStatus?{triggeredStatus:void 0,previousPathname:a.pathname}:{triggeredStatus:b.triggeredStatus,previousPathname:a.pathname}}render(){let{notFound:a,forbidden:b,unauthorized:c,children:d}=this.props,{triggeredStatus:f}=this.state,h={[g.HTTPAccessErrorStatus.NOT_FOUND]:a,[g.HTTPAccessErrorStatus.FORBIDDEN]:b,[g.HTTPAccessErrorStatus.UNAUTHORIZED]:c};if(f){let i=f===g.HTTPAccessErrorStatus.NOT_FOUND&&a,j=f===g.HTTPAccessErrorStatus.FORBIDDEN&&b,k=f===g.HTTPAccessErrorStatus.UNAUTHORIZED&&c;return i||j||k?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)("meta",{name:"robots",content:"noindex"}),!1,h[f]]}):d}return d}}function j({notFound:a,forbidden:b,unauthorized:c,children:g}){let j=d(),k=(0,f.useContext)(h.MissingSlotContext);return a||b||c?(0,e.jsx)(i,{pathname:j,notFound:a,forbidden:b,unauthorized:c,missingSlots:k,children:g}):(0,e.jsx)(e.Fragment,{children:g})}a.s(["HTTPAccessFallbackBoundary",()=>j],63300)},37782,a=>{"use strict";let b,c;var d,e=a.i(59952),f=((d={}).AUTO="auto",d.FULL="full",d.TEMPORARY="temporary",d),g=a.i(27547),h=a.i(9799),i=a.i(63386),j=a.i(50974),k=a.i(92073);function l(a){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}async function m(a,b){return new Promise((c,d)=>{(0,g.startTransition)(()=>{l({type:"server-action",actionId:a,actionArgs:b,resolve:c,reject:d})})})}var n=a.i(36694),o=a.i(6139);let p=j.createFromReadableStream,q=j.createFromFetch;function r(a){return(0,o.urlToUrlWithoutFlightMarker)(new URL(a,location.origin)).toString()}let s=new AbortController;async function t(a,d){var e,g,h;let{flightRouterState:i,nextUrl:j,prefetchKind:l}=d,q={[k.RSC_HEADER]:"1",[k.NEXT_ROUTER_STATE_TREE_HEADER]:d.isHmrRefresh?encodeURIComponent(JSON.stringify(i)):encodeURIComponent(JSON.stringify(function a(b){var c,d;let[e,f,g,h,i,j]=b,k="string"==typeof(c=e)&&c.startsWith(n.PAGE_SEGMENT_KEY+"?")?n.PAGE_SEGMENT_KEY:c,l={};for(let[b,c]of Object.entries(f))l[b]=a(c);let m=[k,l,null,(d=h)&&"refresh"!==d?h:null];return void 0!==i&&(m[4]=i),void 0!==j&&(m[5]=j),m}(i)))};l===f.AUTO&&(q[k.NEXT_ROUTER_PREFETCH_HEADER]="1"),j&&(q[k.NEXT_URL]=j);let t=a;try{let d=l?l===f.TEMPORARY?"high":"low":"auto";(a=new URL(a)).pathname.endsWith("/")?a.pathname+="index.txt":a.pathname+=".txt";let i=await u(a,q,d,!0,s.signal),j=(0,o.urlToUrlWithoutFlightMarker)(new URL(i.url)),n=i.redirected?j:t,v=i.headers.get("content-type")||"",w=!!i.headers.get("vary")?.includes(k.NEXT_URL),x=!!i.headers.get(k.NEXT_DID_POSTPONE_HEADER),y=i.headers.get(k.NEXT_ROUTER_STALE_TIME_HEADER),z=null!==y?1e3*parseInt(y,10):-1,A=v.startsWith(k.RSC_CONTENT_TYPE_HEADER);if(A||(A=v.startsWith("text/plain")),!A||!i.ok||!i.body)return a.hash&&(j.hash=a.hash),r(j.toString());let B=i.flightResponse;if(null===B){let a;g=x?(a=i.body.getReader(),new ReadableStream({async pull(b){for(;;){let{done:c,value:d}=await a.read();if(!c){b.enqueue(d);continue}return}}})):i.body,h=q,B=p(g,{callServer:m,findSourceMapURL:c,debugChannel:b&&b(h)})}let C=await B;if(""!==C.b)return r(i.url);let D=(e=C.f,"string"==typeof e?e:e.map(a=>(function(a){let[b,c,d,e]=a.slice(-4),f=a.slice(0,-4);return{pathToSegment:f.slice(0,-1),segmentPath:f,segment:f[f.length-1]??"",tree:b,seedData:c,head:d,isHeadPartial:e,isRootRender:4===a.length}})(a)));if("string"==typeof D)return r(D);return{flightData:D,canonicalUrl:n,renderedSearch:(0,o.getRenderedSearch)(i),couldBeIntercepted:w,prerendered:C.S,postponed:x,staleTime:z,debugInfo:B._debugInfo??null}}catch(a){return s.signal.aborted||console.error(`Failed to fetch RSC payload for ${t}. Falling back to browser navigation.`,a),t.toString()}}async function u(a,d,e,f,g){var h,i,j,l,n,o,p,r;let s,t,u=new URL(a);n=u,o=(h=d[k.NEXT_ROUTER_PREFETCH_HEADER],i=d[k.NEXT_ROUTER_SEGMENT_PREFETCH_HEADER],j=d[k.NEXT_ROUTER_STATE_TREE_HEADER],l=d[k.NEXT_URL],(void 0===h||"0"===h)&&void 0===i&&void 0===j&&void 0===l?"":(function(a){let b=5381;for(let c=0;c<a.length;c++)b=(b<<5)+b+a.charCodeAt(c)|0;return b>>>0})([h||"0",i||"0",j||"0",l||"0"].join(",")).toString(36).slice(0,5)),t=((s=n.search).startsWith("?")?s.slice(1):s).split("&").filter(a=>a&&!a.startsWith(`${k.NEXT_RSC_UNION_QUERY}=`)),o.length>0?t.push(`${k.NEXT_RSC_UNION_QUERY}=${o}`):t.push(`${k.NEXT_RSC_UNION_QUERY}`),n.search=t.length?`?${t.join("&")}`:"";let v=fetch(u,{credentials:"same-origin",headers:d,priority:e||void 0,signal:g}),w=f?(p=v,r=d,q(p,{callServer:m,findSourceMapURL:c,debugChannel:b&&b(r)})):null,x=await v,y=x.redirected,z=new URL(x.url,u);return z.searchParams.delete(k.NEXT_RSC_UNION_QUERY),{url:z.href,redirected:y,ok:x.ok,headers:x.headers,body:x.body,status:x.status,flightResponse:w}}let v={then:()=>{}};var w=g,x=a.i(84094),y=a.i(15388);let z=a.r(56704).workAsyncStorage;function A({error:a}){if(z){let b=z.getStore();if(b?.isStaticGeneration)throw a&&console.error(a),a}return null}/[\w-]+-Google|Google-[\w-]+|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti|googleweblight/i.source;class B extends w.default.Component{constructor(a){super(a),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}static getDerivedStateFromError(a){if((0,y.isNextRouterError)(a))throw a;return{error:a}}static getDerivedStateFromProps(a,b){let{error:c}=b;return a.pathname!==b.previousPathname&&b.error?{error:null,previousPathname:a.pathname}:{error:b.error,previousPathname:a.pathname}}render(){return this.state.error&&1?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(A,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,e.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}}function C({errorComponent:a,errorStyles:b,errorScripts:c,children:d}){let f=(0,x.useUntrackedPathname)();return a?(0,e.jsx)(B,{pathname:f,errorComponent:a,errorStyles:b,errorScripts:c,children:d}):(0,e.jsx)(e.Fragment,{children:d})}let D=(a,b)=>"string"==typeof a?"string"==typeof b&&a===b:"string"!=typeof b&&a[0]===b[0]&&a[1]===b[1];a.i(81447);var E=g,F=a.i(8863);URLSearchParams,a.i(91398),a.i(41629);var G=a.i(58272);a.r(20635).actionAsyncStorage;var H=a.i(55331);function I(){let a=(0,g.useContext)(i.AppRouterContext);if(null===a)throw Object.defineProperty(Error("invariant expected app router to be mounted"),"__NEXT_ERROR_CODE",{value:"E238",enumerable:!1,configurable:!0});return a}function J({redirect:a,reset:b,redirectType:c}){let d=I();return(0,E.useEffect)(()=>{E.default.startTransition(()=>{c===G.RedirectType.push?d.push(a,{}):d.replace(a,{}),b()})},[a,c,b,d]),null}H.HTTP_ERROR_FALLBACK_ERROR_CODE,H.HTTP_ERROR_FALLBACK_ERROR_CODE,H.HTTP_ERROR_FALLBACK_ERROR_CODE,a.r(49997).unstable_rethrow,a.r(38499).useDynamicRouteParams,a.r(38499).useDynamicSearchParams;class K extends E.default.Component{constructor(a){super(a),this.state={redirect:null,redirectType:null}}static getDerivedStateFromError(a){if((0,G.isRedirectError)(a))return{redirect:(0,G.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null,redirectType:function(a){if(!(0,G.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}(a)};throw a}render(){let{redirect:a,redirectType:b}=this.state;return null!==a&&null!==b?(0,e.jsx)(J,{redirect:a,redirectType:b,reset:()=>this.setState({redirect:null})}):this.props.children}}function L({children:a}){let b=I();return(0,e.jsx)(K,{router:b,children:a})}var M=a.i(63300);function N(a,b=!1){return Array.isArray(a)?`${a[0]}|${a[1]}|${a[2]}`:b&&a.startsWith(n.PAGE_SEGMENT_KEY)?n.PAGE_SEGMENT_KEY:a}let O=["(..)(..)","(.)","(..)","(...)"];h.default.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;let P=["bottom","height","left","right","top","width","x","y"];function Q(a,b){let c=a.getBoundingClientRect();return c.top>=0&&c.top<=b}class R extends g.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...a){super(...a),this.handlePotentialScroll=()=>{let{focusAndScrollRef:a,segmentPath:b}=this.props;if(a.apply){if(0!==a.segmentPaths.length&&!a.segmentPaths.some(a=>b.every((b,c)=>D(b,a[c]))))return;let c=null,d=a.hashFragment;if(d&&(c="top"===d?document.body:document.getElementById(d)??document.getElementsByName(d)[0]),c||(c=null),!(c instanceof Element))return;for(;!(c instanceof HTMLElement)||function(a){if(["sticky","fixed"].includes(getComputedStyle(a).position))return!0;let b=a.getBoundingClientRect();return P.every(a=>0===b[a])}(c);){if(null===c.nextElementSibling)return;c=c.nextElementSibling}a.apply=!1,a.hashFragment=null,a.segmentPaths=[],function(a,b={}){if(b.onlyHashChange)return a();let c=document.documentElement;if("smooth"!==c.dataset.scrollBehavior)return a();let d=c.style.scrollBehavior;c.style.scrollBehavior="auto",b.dontForceLayout||c.getClientRects(),a(),c.style.scrollBehavior=d}(()=>{if(d)return void c.scrollIntoView();let a=document.documentElement,b=a.clientHeight;!Q(c,b)&&(a.scrollTop=0,Q(c,b)||c.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:a.onlyHashChange}),a.onlyHashChange=!1,c.focus()}}}}function S({segmentPath:a,children:b}){let c=(0,g.useContext)(i.GlobalLayoutRouterContext);if(!c)throw Object.defineProperty(Error("invariant global layout router not mounted"),"__NEXT_ERROR_CODE",{value:"E473",enumerable:!1,configurable:!0});return(0,e.jsx)(R,{segmentPath:a,focusAndScrollRef:c.focusAndScrollRef,children:b})}function T({tree:a,segmentPath:b,debugNameContext:c,cacheNode:d,params:f,url:h,isActive:j}){let k=(0,g.useContext)(i.GlobalLayoutRouterContext);if((0,g.useContext)(F.NavigationPromisesContext),!k)throw Object.defineProperty(Error("invariant global layout router not mounted"),"__NEXT_ERROR_CODE",{value:"E473",enumerable:!1,configurable:!0});let{tree:m}=k,n=null!==d.prefetchRsc?d.prefetchRsc:d.rsc,o=(0,g.useDeferredValue)(d.rsc,n),p="object"==typeof o&&null!==o&&"function"==typeof o.then?(0,g.use)(o):o;if(!p){if(j){let a=d.lazyData;if(null===a){let c=function a(b,c){if(b){let[d,e]=b,f=2===b.length;if(D(c[0],d)&&c[1].hasOwnProperty(e)){if(f){let b=a(void 0,c[1][e]);return[c[0],{...c[1],[e]:[b[0],b[1],b[2],"refetch"]}]}return[c[0],{...c[1],[e]:a(b.slice(2),c[1][e])}]}}return c}(["",...b],m),e=function a([b,c]){if(Array.isArray(b)&&("di"===b[2]||"ci"===b[2])||"string"==typeof b&&void 0!==b.split("/").find(a=>O.find(b=>a.startsWith(b))))return!0;if(c){for(let b in c)if(a(c[b]))return!0}return!1}(m),f=Date.now();d.lazyData=a=t(new URL(h,location.origin),{flightRouterState:c,nextUrl:e?k.previousNextUrl||k.nextUrl:null}).then(a=>((0,g.startTransition)(()=>{l({type:"server-patch",previousTree:m,serverResponse:a,navigatedAt:f})}),a)),(0,g.use)(a)}}(0,g.use)(v)}return(0,e.jsx)(i.LayoutRouterContext.Provider,{value:{parentTree:a,parentCacheNode:d,parentSegmentPath:b,parentParams:f,debugNameContext:c,url:h,isActive:j},children:p})}function U({name:a,loading:b,children:c}){let d;if(d="object"==typeof b&&null!==b&&"function"==typeof b.then?(0,g.use)(b):b){let b=d[0],f=d[1],h=d[2];return(0,e.jsx)(g.Suspense,{name:a,fallback:(0,e.jsxs)(e.Fragment,{children:[f,h,b]}),children:c})}return(0,e.jsx)(e.Fragment,{children:c})}function V({parallelRouterKey:a,error:b,errorStyles:c,errorScripts:d,templateStyles:f,templateScripts:h,template:j,notFound:k,forbidden:l,unauthorized:m,segmentViewBoundaries:n}){let p=(0,g.useContext)(i.LayoutRouterContext);if(!p)throw Object.defineProperty(Error("invariant expected layout router to be mounted"),"__NEXT_ERROR_CODE",{value:"E56",enumerable:!1,configurable:!0});let{parentTree:q,parentCacheNode:r,parentSegmentPath:s,parentParams:t,url:u,isActive:v,debugNameContext:w}=p,x=r.parallelRoutes,y=x.get(a);y||(y=new Map,x.set(a,y));let z=q[0],A=null===s?[a]:s.concat([z,a]),B=q[1][a],D=N(B[0],!0),E=function(a,b){let[c,d]=(0,g.useState)(()=>({tree:a,stateKey:b,next:null}));if(c.tree===a)return c;let e={tree:a,stateKey:b,next:null},f=1,h=c,i=e;for(;null!==h&&f<1;){if(h.stateKey===b){i.next=h.next;break}{f++;let a={tree:h.tree,stateKey:h.stateKey,next:null};i.next=a,i=a}h=h.next}return d(e),e}(B,D),F=[];do{let a=E.tree,g=E.stateKey,n=a[0],p=N(n),q=y.get(p);if(void 0===q){let a={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};q=a,y.set(p,a)}let s=t;if(Array.isArray(n)){let a=n[0],b=n[1],c=n[2],d=(0,o.getParamValueFromCacheKey)(b,c);null!==d&&(s={...t,[a]:d})}let x=function(a){if("/"===a)return"/";if("string"==typeof a)if("(slot)"===a)return;else return a+"/";return a[1]+"/"}(n),z=x??w,B=void 0===x?void 0:w,G=r.loading,H=(0,e.jsxs)(i.TemplateContext.Provider,{value:(0,e.jsxs)(S,{segmentPath:A,children:[(0,e.jsx)(C,{errorComponent:b,errorStyles:c,errorScripts:d,children:(0,e.jsx)(U,{name:B,loading:G,children:(0,e.jsx)(M.HTTPAccessFallbackBoundary,{notFound:k,forbidden:l,unauthorized:m,children:(0,e.jsxs)(L,{children:[(0,e.jsx)(T,{url:u,tree:a,params:s,cacheNode:q,segmentPath:A,debugNameContext:z,isActive:v&&g===D}),null]})})})}),null]}),children:[f,h,j]},g);F.push(H),E=E.next}while(null!==E)return F}a.s(["default",()=>V],37782)},24967,a=>{"use strict";var b=a.i(59952),c=a.i(27547),d=a.i(63386);function e(){let a=(0,c.useContext)(d.TemplateContext);return(0,b.jsx)(b.Fragment,{children:a})}a.s(["default",()=>e])},33335,19692,59097,a=>{"use strict";class b{static get(a,b,c){let d=Reflect.get(a,b,c);return"function"==typeof d?d.bind(a):d}static set(a,b,c,d){return Reflect.set(a,b,c,d)}static has(a,b){return Reflect.has(a,b)}static deleteProperty(a,b){return Reflect.deleteProperty(a,b)}}a.s(["ReflectAdapter",()=>b],33335);var c=a.i(27547);let d={current:null},e="function"==typeof c.cache?c.cache:a=>a,f=console.warn;function g(a){return function(...b){f(a(...b))}}e(a=>{try{f(d.current)}finally{d.current=null}}),a.s(["createDedupedByCallsiteServerErrorLoggerDev",()=>g],19692);let h=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function i(a,b){return h.test(b)?`\`${a}.${b}\``:`\`${a}[${JSON.stringify(b)}]\``}function j(a,b){let c=JSON.stringify(b);return`\`Reflect.has(${a}, ${c})\`, \`${c} in ${a}\`, or similar`}let k=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","_debugInfo","toJSON","$$typeof","__esModule"]);a.s(["describeHasCheckingStringProperty",()=>j,"describeStringPropertyAccess",()=>i,"wellKnownProperties",0,k],59097)},79303,a=>{"use strict";var b=a.i(33335),c=a.i(38499),d=a.i(32319),e=a.i(86794),f=a.i(25758),g=a.i(19692),h=a.i(59097),i=a.i(6445);function j(a,b){let c=d.workUnitAsyncStorage.getStore();if(c)switch(c.type){case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return m(b,c);case"prerender-runtime":throw Object.defineProperty(new e.InvariantError("createSearchParamsFromClient should not be called in a runtime prerender."),"__NEXT_ERROR_CODE",{value:"E769",enumerable:!1,configurable:!0});case"cache":case"private-cache":case"unstable-cache":throw Object.defineProperty(new e.InvariantError("createSearchParamsFromClient should not be called in cache contexts."),"__NEXT_ERROR_CODE",{value:"E739",enumerable:!1,configurable:!0});case"request":return n(a,b,c)}(0,d.throwInvariantForMissingStore)()}function k(a,b){let f=d.workUnitAsyncStorage.getStore();if(f)switch(f.type){case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return m(b,f);case"cache":case"private-cache":case"unstable-cache":throw Object.defineProperty(new e.InvariantError("createServerSearchParamsForServerPage should not be called in cache contexts."),"__NEXT_ERROR_CODE",{value:"E747",enumerable:!1,configurable:!0});case"prerender-runtime":var g,h;return g=a,h=f,(0,c.delayUntilRuntimeStage)(h,r(g));case"request":return n(a,b,f)}(0,d.throwInvariantForMissingStore)()}function l(a){if(a.forceStatic)return Promise.resolve({});let b=d.workUnitAsyncStorage.getStore();if(b)switch(b.type){case"prerender":case"prerender-client":return(0,f.makeHangingPromise)(b.renderSignal,a.route,"`searchParams`");case"prerender-runtime":throw Object.defineProperty(new e.InvariantError("createPrerenderSearchParamsForClientPage should not be called in a runtime prerender."),"__NEXT_ERROR_CODE",{value:"E768",enumerable:!1,configurable:!0});case"cache":case"private-cache":case"unstable-cache":throw Object.defineProperty(new e.InvariantError("createPrerenderSearchParamsForClientPage should not be called in cache contexts."),"__NEXT_ERROR_CODE",{value:"E746",enumerable:!1,configurable:!0});case"prerender-ppr":case"prerender-legacy":case"request":return Promise.resolve({})}(0,d.throwInvariantForMissingStore)()}function m(a,d){if(a.forceStatic)return Promise.resolve({});switch(d.type){case"prerender":case"prerender-client":var e=a,g=d;let h=o.get(g);if(h)return h;let j=(0,f.makeHangingPromise)(g.renderSignal,e.route,"`searchParams`"),k=new Proxy(j,{get(a,d,e){if(Object.hasOwn(j,d))return b.ReflectAdapter.get(a,d,e);switch(d){case"then":return(0,c.annotateDynamicAccess)("`await searchParams`, `searchParams.then`, or similar",g),b.ReflectAdapter.get(a,d,e);case"status":return(0,c.annotateDynamicAccess)("`use(searchParams)`, `searchParams.status`, or similar",g),b.ReflectAdapter.get(a,d,e);default:return b.ReflectAdapter.get(a,d,e)}}});return o.set(g,k),k;case"prerender-ppr":case"prerender-legacy":var l=a,m=d;let n=o.get(l);if(n)return n;let p=Promise.resolve({}),q=new Proxy(p,{get(a,d,e){if(Object.hasOwn(p,d))return b.ReflectAdapter.get(a,d,e);if("string"==typeof d&&"then"===d){let a="`await searchParams`, `searchParams.then`, or similar";if(l.dynamicShouldError){var f=l.route;throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${f} with \`dynamic = "error"\` couldn't be rendered statically because it used ${a}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}"prerender-ppr"===m.type?(0,c.postponeWithTracking)(l.route,a,m.dynamicTracking):(0,c.throwToInterruptStaticGeneration)(a,l,m)}return b.ReflectAdapter.get(a,d,e)}});return o.set(l,q),q;default:return d}}function n(a,b,c){return b.forceStatic?Promise.resolve({}):r(a)}a.i(24725),a.i(25284);let o=new WeakMap,p=new WeakMap;function q(a){let c=p.get(a);if(c)return c;let d=Promise.resolve({}),e=new Proxy(d,{get:function c(e,f,g){return Object.hasOwn(d,f)||"string"!=typeof f||"then"!==f&&h.wellKnownProperties.has(f)||function(a,b){let c=Object.defineProperty(Error(`Route ${a.route} used \`searchParams\` inside "use cache". Accessing dynamic request data inside a cache scope is not supported. If you need some search params inside a cached function await \`searchParams\` outside of the cached function and pass only the required search params as arguments to the cached function. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E842",enumerable:!1,configurable:!0});throw Error.captureStackTrace(c,b),a.invalidDynamicUsageError??=c,c}(a,c),b.ReflectAdapter.get(e,f,g)}});return p.set(a,e),e}function r(a){let b=o.get(a);if(b)return b;let c=Promise.resolve(a);return o.set(a,c),c}(0,g.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`searchParams\` is a Promise and must be unwrapped with \`await\` or \`React.use()\` before accessing its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E848",enumerable:!1,configurable:!0})}),a.s(["createPrerenderSearchParamsForClientPage",()=>l,"createSearchParamsFromClient",()=>j,"createServerSearchParamsForMetadata",0,k,"createServerSearchParamsForServerPage",()=>k,"makeErroringSearchParamsForUseCache",()=>q],79303)},95576,a=>{"use strict";var b=a.i(56704),c=a.i(33335),d=a.i(38499),e=a.i(32319),f=a.i(86794),g=a.i(59097),h=a.i(25758),i=a.i(19692),j=a.i(43285);function k(a,b){let c=e.workUnitAsyncStorage.getStore();if(c)switch(c.type){case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return o(a,b,c);case"cache":case"private-cache":case"unstable-cache":throw Object.defineProperty(new f.InvariantError("createParamsFromClient should not be called in cache contexts."),"__NEXT_ERROR_CODE",{value:"E736",enumerable:!1,configurable:!0});case"prerender-runtime":throw Object.defineProperty(new f.InvariantError("createParamsFromClient should not be called in a runtime prerender."),"__NEXT_ERROR_CODE",{value:"E770",enumerable:!1,configurable:!0});case"request":return s(a)}(0,e.throwInvariantForMissingStore)()}function l(a,b){let c=e.workUnitAsyncStorage.getStore();if(c)switch(c.type){case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return o(a,b,c);case"cache":case"private-cache":case"unstable-cache":throw Object.defineProperty(new f.InvariantError("createServerParamsForRoute should not be called in cache contexts."),"__NEXT_ERROR_CODE",{value:"E738",enumerable:!1,configurable:!0});case"prerender-runtime":return p(a,c);case"request":return s(a)}(0,e.throwInvariantForMissingStore)()}function m(a,b){let c=e.workUnitAsyncStorage.getStore();if(c)switch(c.type){case"prerender":case"prerender-client":case"prerender-ppr":case"prerender-legacy":return o(a,b,c);case"cache":case"private-cache":case"unstable-cache":throw Object.defineProperty(new f.InvariantError("createServerParamsForServerSegment should not be called in cache contexts."),"__NEXT_ERROR_CODE",{value:"E743",enumerable:!1,configurable:!0});case"prerender-runtime":return p(a,c);case"request":return s(a)}(0,e.throwInvariantForMissingStore)()}function n(a){let c=b.workAsyncStorage.getStore();if(!c)throw Object.defineProperty(new f.InvariantError("Missing workStore in createPrerenderParamsForClientSegment"),"__NEXT_ERROR_CODE",{value:"E773",enumerable:!1,configurable:!0});let d=e.workUnitAsyncStorage.getStore();if(d)switch(d.type){case"prerender":case"prerender-client":let g=d.fallbackRouteParams;if(g){for(let b in a)if(g.has(b))return(0,h.makeHangingPromise)(d.renderSignal,c.route,"`params`")}break;case"cache":case"private-cache":case"unstable-cache":throw Object.defineProperty(new f.InvariantError("createPrerenderParamsForClientSegment should not be called in cache contexts."),"__NEXT_ERROR_CODE",{value:"E734",enumerable:!1,configurable:!0})}return Promise.resolve(a)}function o(a,b,c){switch(c.type){case"prerender":case"prerender-client":{let d=c.fallbackRouteParams;if(d){for(let e in a)if(d.has(e))return function(a,b,c){let d=q.get(a);if(d)return d;let e=new Proxy((0,h.makeHangingPromise)(c.renderSignal,b.route,"`params`"),r);return q.set(a,e),e}(a,b,c)}break}case"prerender-ppr":{let e=c.fallbackRouteParams;if(e){for(let f in a)if(e.has(f))return function(a,b,c,e){let f=q.get(a);if(f)return f;let h={...a},i=Promise.resolve(h);return q.set(a,i),Object.keys(a).forEach(a=>{g.wellKnownProperties.has(a)||b.has(a)&&Object.defineProperty(h,a,{get(){let b=(0,g.describeStringPropertyAccess)("params",a);"prerender-ppr"===e.type?(0,d.postponeWithTracking)(c.route,b,e.dynamicTracking):(0,d.throwToInterruptStaticGeneration)(b,c,e)},enumerable:!0})}),i}(a,e,b,c)}}}return s(a)}function p(a,b){return(0,d.delayUntilRuntimeStage)(b,s(a))}a.i(25284);let q=new WeakMap,r={get:function(a,b,d){if("then"===b||"catch"===b||"finally"===b){let e=c.ReflectAdapter.get(a,b,d);return({[b]:(...b)=>{let c=j.dynamicAccessAsyncStorage.getStore();return c&&c.abortController.abort(Object.defineProperty(Error("Accessed fallback `params` during prerendering."),"__NEXT_ERROR_CODE",{value:"E691",enumerable:!1,configurable:!0})),new Proxy(e.apply(a,b),r)}})[b]}return c.ReflectAdapter.get(a,b,d)}};function s(a){let b=q.get(a);if(b)return b;let c=Promise.resolve(a);return q.set(a,c),c}(0,i.createDedupedByCallsiteServerErrorLoggerDev)(function(a,b){let c=a?`Route "${a}" `:"This route ";return Object.defineProperty(Error(`${c}used ${b}. \`params\` is a Promise and must be unwrapped with \`await\` or \`React.use()\` before accessing its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E834",enumerable:!1,configurable:!0})}),a.s(["createParamsFromClient",()=>k,"createPrerenderParamsForClientSegment",()=>n,"createServerParamsForMetadata",0,m,"createServerParamsForRoute",()=>l,"createServerParamsForServerSegment",()=>m])},15681,a=>{"use strict";var b=a.i(59952),c=a.i(86794),d=a.i(63386),e=a.i(27547),f=a.i(6139),g=a.i(8863);function h({Component:h,serverProvidedParams:i}){let j,k;if(null!==i)j=i.searchParams,k=i.params;else{let a=(0,e.use)(d.LayoutRouterContext);k=null!==a?a.parentParams:{},j=(0,f.urlSearchParamsToParsedUrlQuery)((0,e.use)(g.SearchParamsContext))}{let d,e,{workAsyncStorage:f}=a.r(56704),g=f.getStore();if(!g)throw Object.defineProperty(new c.InvariantError("Expected workStore to exist when handling searchParams in a client Page."),"__NEXT_ERROR_CODE",{value:"E564",enumerable:!1,configurable:!0});let{createSearchParamsFromClient:i}=a.r(79303);d=i(j,g);let{createParamsFromClient:l}=a.r(95576);return e=l(k,g),(0,b.jsx)(h,{params:e,searchParams:d})}}a.s(["ClientPageRoot",()=>h])},37303,a=>{"use strict";var b=a.i(59952),c=a.i(86794),d=a.i(63386),e=a.i(27547);function f({Component:f,slots:g,serverProvidedParams:h}){let i;if(null!==h)i=h.params;else{let a=(0,e.use)(d.LayoutRouterContext);i=null!==a?a.parentParams:{}}{let d,{workAsyncStorage:e}=a.r(56704),h=e.getStore();if(!h)throw Object.defineProperty(new c.InvariantError("Expected workStore to exist when handling params in a client segment such as a Layout or Template."),"__NEXT_ERROR_CODE",{value:"E600",enumerable:!1,configurable:!0});let{createParamsFromClient:j}=a.r(95576);return d=j(i,h),(0,b.jsx)(f,{...g,params:d})}}a.s(["ClientSegmentRoot",()=>f])},80654,a=>{"use strict";var b=a.i(59952);a.s(["IconMark",0,()=>(0,b.jsx)("meta",{name:"«nxt-icon»"})])},46528,a=>{"use strict";var b=a.i(74724);let c={[b.METADATA_BOUNDARY_NAME]:function({children:a}){return a},[b.VIEWPORT_BOUNDARY_NAME]:function({children:a}){return a},[b.OUTLET_BOUNDARY_NAME]:function({children:a}){return a},[b.ROOT_LAYOUT_BOUNDARY_NAME]:function({children:a}){return a}},d=c[b.METADATA_BOUNDARY_NAME.slice(0)],e=c[b.VIEWPORT_BOUNDARY_NAME.slice(0)],f=c[b.OUTLET_BOUNDARY_NAME.slice(0)],g=c[b.ROOT_LAYOUT_BOUNDARY_NAME.slice(0)];a.s(["MetadataBoundary",0,d,"OutletBoundary",0,f,"RootLayoutBoundary",0,g,"ViewportBoundary",0,e])}];

//# sourceMappingURL=83e11_next_dist_71f8e2a5._.js.map