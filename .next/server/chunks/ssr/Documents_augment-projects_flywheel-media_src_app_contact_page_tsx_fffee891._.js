module.exports=[94806,a=>{"use strict";var b=a.i(59952),c=a.i(27547),d=a.i(87430);function e(){let[a,e]=(0,c.useState)({name:"",phone:"",skype:"",email:"",comment:""}),f=b=>{e({...a,[b.target.name]:b.target.value})};return(0,b.jsx)("div",{className:"min-h-screen bg-gray-50 py-20",children:(0,b.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,b.jsxs)("div",{className:"text-center mb-16",children:[(0,b.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:"Don't hesitate to reach out"}),(0,b.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Ready to take the next step? Get in touch with us today and let's start crafting your success story together"})]}),(0,b.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8 md:p-12",children:(0,b.jsxs)("form",{onSubmit:b=>{b.preventDefault(),console.log("Form submitted:",a)},className:"space-y-6",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),(0,b.jsx)("input",{type:"text",id:"name",name:"name",value:a.name,onChange:f,placeholder:"Full Name",className:"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",required:!0})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone No."}),(0,b.jsx)("input",{type:"tel",id:"phone",name:"phone",value:a.phone,onChange:f,placeholder:"Phone No.",className:"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",required:!0})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"skype",className:"block text-sm font-medium text-gray-700 mb-2",children:"Skype"}),(0,b.jsx)("input",{type:"text",id:"skype",name:"skype",value:a.skype,onChange:f,placeholder:"Skype ID/No.",className:"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,b.jsx)("input",{type:"email",id:"email",name:"email",value:a.email,onChange:f,placeholder:"Email",className:"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",required:!0})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"comment",className:"block text-sm font-medium text-gray-700 mb-2",children:"Comment"}),(0,b.jsx)("textarea",{id:"comment",name:"comment",value:a.comment,onChange:f,placeholder:"Enter your comment/message...",rows:6,className:"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-vertical",required:!0})]}),(0,b.jsx)("div",{className:"text-center",children:(0,b.jsx)(d.Button,{type:"submit",size:"lg",className:"px-12 py-3",children:"Send Message"})})]})}),(0,b.jsxs)("div",{className:"mt-16 text-center",children:[(0,b.jsx)("h3",{className:"text-2xl font-semibold text-gray-900 mb-8",children:"Other Ways to Reach Us"}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,b.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,b.jsx)("div",{className:"text-blue-600 text-2xl mb-4",children:"📧"}),(0,b.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Email"}),(0,b.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,b.jsx)("div",{className:"text-blue-600 text-2xl mb-4",children:"📱"}),(0,b.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Phone"}),(0,b.jsx)("p",{className:"text-gray-600",children:"+****************"})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,b.jsx)("div",{className:"text-blue-600 text-2xl mb-4",children:"💬"}),(0,b.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Live Chat"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Available 24/7"})]})]})]})]})})}a.s(["default",()=>e])}];

//# sourceMappingURL=Documents_augment-projects_flywheel-media_src_app_contact_page_tsx_fffee891._.js.map