{"version": 3, "sources": ["turbopack:///[project]/Documents/augment-projects/flywheel-media/src/app/about/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport { Button } from '@/components/ui/button';\n\nexport default function AboutPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-br from-blue-50 to-indigo-100 py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              A little about us\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Unleashing the power of Affiliate Marketing tactics\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Process Section */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-12\">\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-2xl font-bold text-blue-600\">01</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                Tailored Affiliate Strategies\n              </h3>\n              <p className=\"text-gray-600\">\n                Optimizing your website for each of the main components to put a\n                good and fit in house strategy in place.\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-2xl font-bold text-blue-600\">02</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                Building your site\n              </h3>\n              <p className=\"text-gray-600\">\n                Optimizing your website for each of the main components to put a\n                good and fit in house strategy in place.\n              </p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6\">\n                <span className=\"text-2xl font-bold text-blue-600\">03</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-4\">\n                Promoting your site\n              </h3>\n              <p className=\"text-gray-600\">\n                Optimizing your website for each of the main components to put a\n                good and fit in house strategy in place.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Mission Statement */}\n      <section className=\"bg-gray-50 py-20\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-8\">\n            Our Mission\n          </h2>\n          <p className=\"text-xl text-gray-600 mb-8\">\n            It's time to shift the focus back to the creators. Let's work\n            together to ensure that quality content is recognized and rewarded.\n            With flywheel-media, you can transform your passion into profit\n            while making a positive impact.\n          </p>\n          <Link href=\"/contact\">\n            <Button size=\"lg\">Get Started Today</Button>\n          </Link>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"py-20\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">2k</div>\n              <div className=\"text-gray-600\">Project Done</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">2k+</div>\n              <div className=\"text-gray-600\">Satisfied Clients</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">48K</div>\n              <div className=\"text-gray-600\">Supported locations</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-blue-600 mb-2\">2024</div>\n              <div className=\"text-gray-600\">Year founded</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-blue-600 py-20\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-6\">\n            It's time to tell the world about it\n          </h2>\n          <p className=\"text-xl text-blue-100 mb-8\">\n            It's time to tell the world about what you do. Let's work together\n            to share your story in a way that captivates and inspires.\n          </p>\n          <Link href=\"/contact\">\n            <Button variant=\"secondary\" size=\"lg\">\n              Start Your Journey\n            </Button>\n          </Link>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": "oVAAA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OAEe,SAAS,IACtB,MACE,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,kCAEb,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,8DACjB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DAAoD,sBAGlE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,mDAA0C,+DAQ7D,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,iBACjB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,mDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4FACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4CAAmC,SAErD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,kCAGzD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,iHAM/B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4FACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4CAAmC,SAErD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,uBAGzD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,iHAM/B,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,wBACb,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,4FACb,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,UAAU,4CAAmC,SAErD,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,oDAA2C,wBAGzD,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,yBAAgB,wHAUrC,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,4BACjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,6DAAoD,gBAGlE,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,sOAM1C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,oBACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,KAAK,cAAK,6BAMxB,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,iBACjB,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,kDACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,8DACb,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDAAwC,OACvD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,oBAEjC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDAAwC,QACvD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,yBAEjC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDAAwC,QACvD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,2BAEjC,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,iDAAwC,SACvD,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CAAI,UAAU,yBAAgB,2BAOvC,CAAA,EAAA,EAAA,GAAA,EAAC,UAAA,CAAQ,UAAU,6BACjB,CAAA,EAAA,EAAA,IAAA,EAAC,MAAA,CAAI,UAAU,+DACb,CAAA,EAAA,EAAA,GAAA,EAAC,KAAA,CAAG,UAAU,0DAAiD,yCAG/D,CAAA,EAAA,EAAA,GAAA,EAAC,IAAA,CAAE,UAAU,sCAA6B,kIAI1C,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAI,CAAA,CAAC,KAAK,oBACT,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,MAAM,CAAA,CAAC,QAAQ,YAAY,KAAK,cAAK,gCAQlD"}