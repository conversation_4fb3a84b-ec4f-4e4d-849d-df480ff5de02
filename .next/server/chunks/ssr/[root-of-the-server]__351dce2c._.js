module.exports=[61755,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(83879);a.n(d("[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/app-dir/link.js <module evaluation>"))},61190,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(83879);a.n(d("[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/app-dir/link.js"))},11874,a=>{"use strict";a.i(61755);var b=a.i(61190);a.n(b)},3595,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0});var d={default:function(){return i},useLinkStatus:function(){return h.useLinkStatus}};for(var e in d)Object.defineProperty(c,e,{enumerable:!0,get:d[e]});let f=a.r(12556),g=a.r(49916),h=f._(a.r(11874));function i(a){let b=a.legacyBehavior,c="string"==typeof a.children||"number"==typeof a.children||"string"==typeof a.children?.type,d=a.children?.type?.$$typeof===Symbol.for("react.client.reference");return!b||c||d||(a.children?.type?.$$typeof===Symbol.for("react.lazy")?console.error("Using a Lazy Component as a direct child of `<Link legacyBehavior>` from a Server Component is not supported. If you need legacyBehavior, wrap your Lazy Component in a Client Component that renders the Link's `<a>` tag."):console.error("Using a Server Component as a direct child of `<Link legacyBehavior>` is not supported. If you need legacyBehavior, wrap your Server Component in a Client Component that renders the Link's `<a>` tag.")),(0,g.jsx)(h.default,{...a})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},21290,a=>{a.v({className:"inter_5901b7c6-module__ec5Qua__className",variable:"inter_5901b7c6-module__ec5Qua__variable"})},86666,a=>{"use strict";let b=(0,a.i(83879).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/Documents/augment-projects/flywheel-media/src/components/layout/header.tsx <module evaluation>","Header");a.s(["Header",0,b])},88964,a=>{"use strict";let b=(0,a.i(83879).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/Documents/augment-projects/flywheel-media/src/components/layout/header.tsx","Header");a.s(["Header",0,b])},26847,a=>{"use strict";a.i(86666);var b=a.i(88964);a.n(b)},60755,a=>{"use strict";var b=a.i(49916),c=a.i(21290);let d={className:c.default.className,style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"}};null!=c.default.variable&&(d.variable=c.default.variable);var e=a.i(26847),f=a.i(3595);function g(){return(0,b.jsx)("footer",{className:"bg-gray-900 text-white",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,b.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,b.jsx)(f.default,{href:"/",className:"text-2xl font-bold text-blue-400 mb-4 block",children:"flywheel-media"}),(0,b.jsx)("p",{className:"text-gray-300 mb-6 max-w-md",children:"It's time to tell the world about what you do. Let's work together to share your story in a way that captivates and inspires."}),(0,b.jsxs)("div",{className:"flex space-x-4",children:[(0,b.jsx)(f.default,{href:"#",className:"text-gray-300 hover:text-blue-400 transition-colors",children:"Visit LinkedIn Page"}),(0,b.jsx)(f.default,{href:"#",className:"text-gray-300 hover:text-blue-400 transition-colors",children:"Follow Us On Instagram"}),(0,b.jsx)(f.default,{href:"#",className:"text-gray-300 hover:text-blue-400 transition-colors",children:"Facebook Page"})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Important Links"}),(0,b.jsxs)("ul",{className:"space-y-2",children:[(0,b.jsx)("li",{children:(0,b.jsx)(f.default,{href:"/",className:"text-gray-300 hover:text-blue-400 transition-colors",children:"Home"})}),(0,b.jsx)("li",{children:(0,b.jsx)(f.default,{href:"/about",className:"text-gray-300 hover:text-blue-400 transition-colors",children:"About Us"})}),(0,b.jsx)("li",{children:(0,b.jsx)(f.default,{href:"/services",className:"text-gray-300 hover:text-blue-400 transition-colors",children:"Services"})}),(0,b.jsx)("li",{children:(0,b.jsx)(f.default,{href:"/contact",className:"text-gray-300 hover:text-blue-400 transition-colors",children:"Contact Us"})})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Newsletter"}),(0,b.jsxs)("form",{className:"space-y-3",children:[(0,b.jsx)("input",{type:"email",placeholder:"Enter Your Email",className:"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,b.jsx)("button",{type:"submit",className:"w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors",children:"Subscribe"})]})]})]}),(0,b.jsxs)("div",{className:"border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,b.jsx)("p",{className:"text-gray-400 text-sm",children:"© 2024 flywheel-media, All rights reserved."}),(0,b.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[(0,b.jsx)(f.default,{href:"#",className:"text-gray-400 hover:text-blue-400 text-sm transition-colors",children:"Privacy Policy"}),(0,b.jsx)(f.default,{href:"#",className:"text-gray-400 hover:text-blue-400 text-sm transition-colors",children:"Terms of service"})]})]})]})})}function h({children:a}){return(0,b.jsx)("html",{lang:"en",children:(0,b.jsxs)("body",{className:`${d.variable} font-sans antialiased`,children:[(0,b.jsx)(e.Header,{}),(0,b.jsx)("main",{className:"min-h-screen",children:a}),(0,b.jsx)(g,{})]})})}a.s(["default",()=>h,"metadata",0,{title:"flywheel-media - Driving Growth with Smart Affiliate Marketing",description:"Join us in forging powerful connections between brands and customers through strategic affiliate marketing solutions."}],60755)}];

//# sourceMappingURL=%5Broot-of-the-server%5D__351dce2c._.js.map