{"version": 3, "sources": ["turbopack:///[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/services/page"], "sourcesContent": ["import { fillMetadataSegment } from 'next/dist/lib/metadata/get-metadata-route' with { 'turbopack-transition': 'next-server-utility' }\nimport __TURBOPACK__icon__$23$0__ from \"METADATA_0\";\nconst __TURBOPACK__layout__$23$1__ = () => require(\"MODULE_1\");\n\nconst __TURBOPACK__not$2d$found__$23$2__ = () => require(\"MODULE_2\");\n\nconst __TURBOPACK__forbidden__$23$3__ = () => require(\"MODULE_3\");\n\nconst __TURBOPACK__unauthorized__$23$4__ = () => require(\"MODULE_4\");\n\nconst __TURBOPACK__global$2d$error__$23$5__ = () => require(\"MODULE_5\");\n\nconst __TURBOPACK__page__$23$6__ = () => require(\"MODULE_6\");\n\nimport { AppPageRouteModule } from \"next/dist/esm/server/route-modules/app-page/module.compiled\" with {\n    'turbopack-transition': 'next-ssr'\n};\nimport { RouteKind } from \"next/dist/esm/server/route-kind\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nimport { getRevalidateReason } from \"next/dist/esm/server/instrumentation/utils\";\nimport { getTracer, SpanKind } from \"next/dist/esm/server/lib/trace/tracer\";\nimport { addRequestMeta, getRequestMeta } from \"next/dist/esm/server/request-meta\";\nimport { BaseServerSpan } from \"next/dist/esm/server/lib/trace/constants\";\nimport { interopDefault } from \"next/dist/esm/server/app-render/interop-default\";\nimport { stripFlightHeaders } from \"next/dist/esm/server/app-render/strip-flight-headers\";\nimport { NodeNextRequest, NodeNextResponse } from \"next/dist/esm/server/base-http/node\";\nimport { checkIsAppPPREnabled } from \"next/dist/esm/server/lib/experimental/ppr\";\nimport { getFallbackRouteParams, createOpaqueFallbackRouteParams } from \"next/dist/esm/server/request/fallback-params\";\nimport { setReferenceManifestsSingleton } from \"next/dist/esm/server/app-render/encryption-utils\";\nimport { isHtmlBotRequest, shouldServeStreamingMetadata } from \"next/dist/esm/server/lib/streaming-metadata\";\nimport { createServerModuleMap } from \"next/dist/esm/server/app-render/action-utils\";\nimport { normalizeAppPath } from \"next/dist/esm/shared/lib/router/utils/app-paths\";\nimport { getIsPossibleServerAction } from \"next/dist/esm/server/lib/server-action-request-meta\";\nimport { RSC_HEADER, NEXT_ROUTER_PREFETCH_HEADER, NEXT_IS_PRERENDER_HEADER, NEXT_DID_POSTPONE_HEADER, RSC_CONTENT_TYPE_HEADER } from \"next/dist/esm/client/components/app-router-headers\";\nimport { getBotType, isBot } from \"next/dist/esm/shared/lib/router/utils/is-bot\";\nimport { CachedRouteKind, IncrementalCacheKind } from \"next/dist/esm/server/response-cache\";\nimport { FallbackMode, parseFallbackField } from \"next/dist/esm/lib/fallback\";\nimport RenderResult from \"next/dist/esm/server/render-result\";\nimport { CACHE_ONE_YEAR, HTML_CONTENT_TYPE_HEADER, NEXT_CACHE_TAGS_HEADER } from \"next/dist/esm/lib/constants\";\nimport { ENCODED_TAGS } from \"next/dist/esm/server/stream-utils/encoded-tags\";\nimport { sendRenderResult } from \"next/dist/esm/server/send-payload\";\nimport { NoFallbackError } from \"next/dist/esm/shared/lib/no-fallback-error.external\";\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = [\"\", {\n\"children\": [\"services\", {\n\"children\": [\"__PAGE__\", {\n}, {\n  metadata: {  },  \"page\": [__TURBOPACK__page__$23$6__, \"[project]/Documents/augment-projects/flywheel-media/src/app/services/page.tsx\"],\n}],\n}, {\n  metadata: {  },}],\n}, {\n  metadata: {    icon: [\n      (async (props) => [{\n        url: fillMetadataSegment(\"//\", await props.params, \"favicon.ico\") + `?${__TURBOPACK__icon__$23$0__.src.split(\"/\").splice(-1)[0]}`,\n        sizes: `${__TURBOPACK__icon__$23$0__.width}x${__TURBOPACK__icon__$23$0__.height}`,\n        type: `image/x-icon`,\n      }]),\n    ],\n  },  \"layout\": [__TURBOPACK__layout__$23$1__, \"[project]/Documents/augment-projects/flywheel-media/src/app/layout.tsx\"],\n  \"not-found\": [__TURBOPACK__not$2d$found__$23$2__, \"[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/components/builtin/not-found.js\"],\n  \"forbidden\": [__TURBOPACK__forbidden__$23$3__, \"[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/components/builtin/forbidden.js\"],\n  \"unauthorized\": [__TURBOPACK__unauthorized__$23$4__, \"[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/components/builtin/unauthorized.js\"],\n  \"global-error\": [__TURBOPACK__global$2d$error__$23$5__, \"[project]/Documents/augment-projects/flywheel-media/node_modules/.pnpm/next@16.0.1_@babel+core@7.28.5_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/components/builtin/global-error.js\"],\n}]\nimport GlobalError from \"GLOBAL_ERROR_MODULE\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nexport { GlobalError };\nconst __next_app_require__ = __turbopack_context__.r.bind(__turbopack_context__)\nconst __next_app_load_chunk__ = __turbopack_context__.l.bind(__turbopack_context__)\nexport const __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\nimport * as entryBase from \"next/dist/esm/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\nimport { RedirectStatusCode } from \"next/dist/esm/client/components/redirect-status-code\";\nimport { InvariantError } from \"next/dist/esm/shared/lib/invariant-error\";\nimport { scheduleOnNextTick } from \"next/dist/esm/lib/scheduler\";\nimport { isInterceptionRouteAppPath } from \"next/dist/esm/shared/lib/router/utils/interception-routes\";\nexport * from \"next/dist/esm/server/app-render/entry-base\" with {\n    'turbopack-transition': 'next-server-utility'\n};\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n    definition: {\n        kind: RouteKind.APP_PAGE,\n        page: \"/services/page\",\n        pathname: \"/services\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n    relativeProjectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || ''\n});\nexport async function handler(req, res, ctx) {\n    var _this;\n    if (routeModule.isDev) {\n        addRequestMeta(req, 'devRequestTimingInternalsEnd', process.hrtime.bigint());\n    }\n    let srcPage = \"/services/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (process.env.TURBOPACK) {\n        srcPage = srcPage.replace(/\\/index$/, '') || '/';\n    } else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = process.env.__NEXT_MULTI_ZONE_DRAFT_MODE;\n    const isMinimalMode = Boolean(process.env.MINIMAL_MODE || getRequestMeta(req, 'minimalMode'));\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig, parsedUrl, interceptionRoutePatterns } = prepareResult;\n    const normalizedSrcPage = normalizeAppPath(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    // We use the resolvedPathname instead of the parsedUrl.pathname because it\n    // is not rewritten as resolvedPathname is. This will ensure that the correct\n    // prerender info is used instead of using the original pathname as the\n    // source. If however PPR is enabled and cacheComponents is disabled, we\n    // treat the pathname as dynamic. Currently, there's a bug in the PPR\n    // implementation that incorrectly leaves %%drp placeholders in the output of\n    // parallel routes. This is addressed with cacheComponents.\n    const prerenderInfo = nextConfig.experimental.ppr && !nextConfig.cacheComponents && isInterceptionRouteAppPath(resolvedPathname) ? null : routeModule.match(resolvedPathname, prerenderManifest);\n    const isPrerendered = !!prerenderManifest.routes[resolvedPathname];\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = getBotType(userAgent);\n    const isHtmlBot = isHtmlBotRequest(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = getRequestMeta(req, 'isPrefetchRSCRequest') ?? req.headers[NEXT_ROUTER_PREFETCH_HEADER] === '1' // exclude runtime prefetches, which use '2'\n    ;\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = getRequestMeta(req, 'isRSCRequest') ?? Boolean(req.headers[RSC_HEADER]);\n    const isPossibleServerAction = getIsPossibleServerAction(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = checkIsAppPPREnabled(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery = process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' && typeof query.__nextppronly !== 'undefined' && couldSupportPPR;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? getRequestMeta(req, 'postponed') : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = getRequestMeta(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    const serveStreamingMetadata = isHtmlBot && isRoutePPREnabled ? false : !userAgent ? true : shouldServeStreamingMetadata(userAgent, nextConfig.htmlLimitedBots);\n    const isSSG = Boolean((prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]) && // If this is a html bot request and PPR is enabled, then we don't want\n    // to serve a static response.\n    !(isHtmlBot && isRoutePPREnabled));\n    // When a page supports cacheComponents, we can support RDC for Navigations\n    const supportsRDCForNavigations = isRoutePPREnabled && nextConfig.cacheComponents === true;\n    // In development, we always want to generate dynamic HTML.\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof minimalPostponed === 'string' || // If this handler supports onCacheEntryV2, then we can only support\n    // dynamic responses if it's a dynamic RSC request and not in minimal mode. If it\n    // doesn't support it we must fallback to the default behavior.\n    (supportsRDCForNavigations && getRequestMeta(req, 'onCacheEntryV2') ? // which will generate the RDC for the route. When resuming a Dynamic\n    // RSC request, we'll pass the minimal postponed data to the render\n    // which will trigger the `supportsDynamicResponse` to be true.\n    isDynamicRSCRequest && !isMinimalMode : isDynamicRSCRequest);\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    // If this is a request for an app path that should be statically generated\n    // and we aren't in the edge runtime, strip the flight headers so it will\n    // generate the static response.\n    if (!routeModule.isDev && !isDraftMode && isSSG && isRSCRequest && !isDynamicRSCRequest) {\n        stripFlightHeaders(req.headers);\n    }\n    const ComponentMod = {\n        ...entryBase,\n        tree,\n        GlobalError,\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        setReferenceManifestsSingleton({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: createServerModuleMap({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = getTracer();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const render404 = async ()=>{\n        // TODO: should route-module itself handle rendering the 404\n        if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n            await routerServerContext.render404(req, res, parsedUrl, false);\n        } else {\n            res.end('This page could not be found');\n        }\n        return null;\n    };\n    try {\n        const varyHeader = routeModule.getVaryHeader(resolvedPathname, interceptionRoutePatterns);\n        res.setHeader('Vary', varyHeader);\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new NodeNextRequest(req);\n            const nextRes = new NodeNextResponse(res);\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${srcPage}`);\n                }\n            });\n        };\n        const incrementalCache = getRequestMeta(req, 'incrementalCache');\n        const doRender = async ({ span, postponed, fallbackRouteParams, forceStaticRender })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: getRequestMeta(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: interopDefault(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setCacheStatus: routerServerContext == null ? void 0 : routerServerContext.setCacheStatus,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    setReactDebugChannel: routerServerContext == null ? void 0 : routerServerContext.setReactDebugChannel,\n                    dir: process.env.NEXT_RUNTIME === 'nodejs' ? require('path').join(/* turbopackIgnore: true */ process.cwd(), routeModule.relativeProjectDir) : `${process.cwd()}/${routeModule.relativeProjectDir}`,\n                    isDraftMode,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    images: nextConfig.images,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache,\n                    cacheLifeProfiles: nextConfig.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses || isDebugFallbackShell ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    cacheComponents: Boolean(nextConfig.cacheComponents),\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || [],\n                        clientParamParsingOrigins: nextConfig.experimental.clientParamParsingOrigins\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: getRequestMeta(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                context.renderOpts.nextExport = true;\n                context.renderOpts.supportsDynamicResponse = false;\n                context.renderOpts.isDebugDynamicAccesses = isDebugDynamicAccesses;\n            }\n            // When we're revalidating in the background, we should not allow dynamic\n            // responses.\n            if (forceStaticRender) {\n                context.renderOpts.supportsDynamicResponse = false;\n            }\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags, fetchMetrics } = metadata;\n            if (cacheTags) {\n                headers[NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry: previousIncrementalCacheEntry, isRevalidating, span, forceStaticRender = false })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousIncrementalCacheEntry && !isMinimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = parseFallbackField(prerenderInfo.fallback);\n            }\n            // When serving a HTML bot request, we want to serve a blocking render and\n            // not the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === FallbackMode.PRERENDER && isBot(userAgent)) {\n                if (!isRoutePPREnabled || isHtmlBot) {\n                    fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER;\n                }\n            }\n            if ((previousIncrementalCacheEntry == null ? void 0 : previousIncrementalCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== FallbackMode.NOT_FOUND || previousIncrementalCacheEntry)) {\n                fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!isMinimalMode && fallbackMode !== FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === FallbackMode.NOT_FOUND) {\n                    if (nextConfig.experimental.adapterPath) {\n                        return await render404();\n                    }\n                    throw new NoFallbackError();\n                }\n                // When cacheComponents is enabled, we can use the fallback\n                // response if the request is not a dynamic RSC request because the\n                // RSC data when this feature flag is enabled does not contain any\n                // param references. Without this feature flag enabled, the RSC data\n                // contains param references, and therefore we can't use the fallback.\n                if (isRoutePPREnabled && (nextConfig.cacheComponents ? !isDynamicRSCRequest : !isRSCRequest)) {\n                    const cacheKey = isProduction && typeof (prerenderInfo == null ? void 0 : prerenderInfo.fallback) === 'string' ? prerenderInfo.fallback : normalizedSrcPage;\n                    const fallbackRouteParams = // If we're in production and we have fallback route params, then we\n                    // can use the manifest fallback route params.\n                    isProduction && (prerenderInfo == null ? void 0 : prerenderInfo.fallbackRouteParams) ? createOpaqueFallbackRouteParams(prerenderInfo.fallbackRouteParams) : // have to manually generate the fallback route params.\n                    isDebugFallbackShell ? getFallbackRouteParams(normalizedSrcPage, routeModule) : null;\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    const fallbackResponse = await routeModule.handleResponse({\n                        cacheKey,\n                        req,\n                        nextConfig,\n                        routeKind: RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams,\n                                forceStaticRender: false\n                            }),\n                        waitUntil: ctx.waitUntil,\n                        isMinimalMode\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            let postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // If this is a dynamic RSC request, we should use the postponed data from\n            // the static render (if available). This ensures that we can utilize the\n            // resume data cache (RDC) from the static render to ensure that the data\n            // is consistent between the static and dynamic renders.\n            if (// Only enable RDC for Navigations if the feature is enabled.\n            supportsRDCForNavigations && process.env.NEXT_RUNTIME !== 'edge' && !isMinimalMode && incrementalCache && isDynamicRSCRequest && // We don't typically trigger an on-demand revalidation for dynamic RSC\n            // requests, as we're typically revalidating the page in the background\n            // instead. However, if the cache entry is stale, we should trigger a\n            // background revalidation on dynamic RSC requests. This prevents us\n            // from entering an infinite loop of revalidations.\n            !forceStaticRender) {\n                const incrementalCacheEntry = await incrementalCache.get(resolvedPathname, {\n                    kind: IncrementalCacheKind.APP_PAGE,\n                    isRoutePPREnabled: true,\n                    isFallback: false\n                });\n                // If the cache entry is found, we should use the postponed data from\n                // the cache.\n                if (incrementalCacheEntry && incrementalCacheEntry.value && incrementalCacheEntry.value.kind === CachedRouteKind.APP_PAGE) {\n                    // CRITICAL: we're assigning the postponed data from the cache entry\n                    // here as we're using the RDC to resume the render.\n                    postponed = incrementalCacheEntry.value.postponed;\n                    // If the cache entry is stale, we should trigger a background\n                    // revalidation so that subsequent requests will get a fresh response.\n                    if (incrementalCacheEntry && // We want to trigger this flow if the cache entry is stale and if\n                    // the requested revalidation flow is either foreground or\n                    // background.\n                    (incrementalCacheEntry.isStale === -1 || incrementalCacheEntry.isStale === true)) {\n                        // We want to schedule this on the next tick to ensure that the\n                        // render is not blocked on it.\n                        scheduleOnNextTick(async ()=>{\n                            const responseCache = routeModule.getResponseCache(req);\n                            try {\n                                await responseCache.revalidate(resolvedPathname, incrementalCache, isRoutePPREnabled, false, (c)=>responseGenerator({\n                                        ...c,\n                                        // CRITICAL: we need to set this to true as we're\n                                        // revalidating in the background and typically this dynamic\n                                        // RSC request is not treated as static.\n                                        forceStaticRender: true\n                                    }), // CRITICAL: we need to pass null here because passing the\n                                // previous cache entry here (which is stale) will switch on\n                                // isOnDemandRevalidate and break the prerendering.\n                                null, hasResolved, ctx.waitUntil);\n                            } catch (err) {\n                                console.error('Error revalidating the page in the background', err);\n                            }\n                        });\n                    }\n                }\n            }\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: CachedRouteKind.PAGES,\n                        html: RenderResult.EMPTY,\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            const fallbackRouteParams = // If we're in production and we have fallback route params, then we\n            // can use the manifest fallback route params if we need to render the\n            // fallback shell.\n            isProduction && (prerenderInfo == null ? void 0 : prerenderInfo.fallbackRouteParams) && getRequestMeta(req, 'renderFallbackShell') ? createOpaqueFallbackRouteParams(prerenderInfo.fallbackRouteParams) : // manually generate the fallback route params.\n            isDebugFallbackShell ? getFallbackRouteParams(normalizedSrcPage, routeModule) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams,\n                forceStaticRender\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil,\n                isMinimalMode\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!isMinimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (isDynamicRSCRequest) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[NEXT_CACHE_TAGS_HEADER];\n                if (isMinimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return sendRenderResult({\n                        req,\n                        res,\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: RenderResult.fromStatic(matchedSegment, RSC_CONTENT_TYPE_HEADER),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return sendRenderResult({\n                    req,\n                    res,\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: RenderResult.EMPTY,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options. If we support RDC for Navigations, we\n            // prefer the `onCacheEntryV2` callback. Once RDC for Navigations is the\n            // default, we can remove the fallback to `onCacheEntry` as\n            // `onCacheEntryV2` is now fully supported.\n            const onCacheEntry = supportsRDCForNavigations ? getRequestMeta(req, 'onCacheEntryV2') ?? getRequestMeta(req, 'onCacheEntry') : getRequestMeta(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry(cacheEntry, {\n                    url: getRequestMeta(req, 'initURL') ?? req.url\n                });\n                if (finished) return null;\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!isMinimalMode || !isSSG) {\n                    delete headers[NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[NEXT_CACHE_TAGS_HEADER];\n            if (isMinimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!isMinimalMode && cachedData.status && RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone && !isDynamicRSCRequest) {\n                res.setHeader(NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    // If the response is not an RSC response, then we can't serve it.\n                    if (cachedData.html.contentType !== RSC_CONTENT_TYPE_HEADER) {\n                        if (nextConfig.cacheComponents) {\n                            res.statusCode = 404;\n                            return sendRenderResult({\n                                req,\n                                res,\n                                generateEtags: nextConfig.generateEtags,\n                                poweredByHeader: nextConfig.poweredByHeader,\n                                result: RenderResult.EMPTY,\n                                cacheControl: cacheEntry.cacheControl\n                            });\n                        } else {\n                            // Otherwise this case is not expected.\n                            throw Object.defineProperty(new InvariantError(`Expected RSC response, got ${cachedData.html.contentType}`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E789\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                    }\n                    return sendRenderResult({\n                        req,\n                        res,\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return sendRenderResult({\n                    req,\n                    res,\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: RenderResult.fromStatic(cachedData.rscData, RSC_CONTENT_TYPE_HEADER),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            const body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || isMinimalMode || isRSCRequest) {\n                // If we're in test mode, we should add a sentinel chunk to the response\n                // that's between the static and dynamic parts so we can compare the\n                // chunks and add assertions.\n                if (process.env.__NEXT_TEST_MODE && isMinimalMode && isRoutePPREnabled && body.contentType === HTML_CONTENT_TYPE_HEADER) {\n                    // As we're in minimal mode, the static part would have already been\n                    // streamed first. The only part that this streams is the dynamic part\n                    // so we should FIRST stream the sentinel and THEN the dynamic part.\n                    body.unshift(createPPRBoundarySentinel());\n                }\n                return sendRenderResult({\n                    req,\n                    res,\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.push(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return sendRenderResult({\n                    req,\n                    res,\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // If we're in test mode, we should add a sentinel chunk to the response\n            // that's between the static and dynamic parts so we can compare the\n            // chunks and add assertions.\n            if (process.env.__NEXT_TEST_MODE) {\n                body.push(createPPRBoundarySentinel());\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.push(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null,\n                forceStaticRender: false\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return sendRenderResult({\n                req,\n                res,\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${srcPage}`,\n                    kind: SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        if (!(err instanceof NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: getRevalidateReason({\n                    isStaticGeneration: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n// TODO: omit this from production builds, only test builds should include it\n/**\n * Creates a readable stream that emits a PPR boundary sentinel.\n *\n * @returns A readable stream that emits a PPR boundary sentinel.\n */ function createPPRBoundarySentinel() {\n    return new ReadableStream({\n        start (controller) {\n            controller.enqueue(new TextEncoder().encode('<!-- PPR_BOUNDARY_SENTINEL -->'));\n            controller.close();\n        }\n    });\n}\n\n//# sourceMappingURL=app-page.js.map\n"], "names": [], "mappings": "kEAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAaA,EAAA,EAAA,CAAA,CAAA,MAGA,EAAA,EAAA,CAAA,CAAA,OAGA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,MACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAyBA,EAAA,EAAA,CAAA,CAAA,OAUA,EAAA,CAAA,CAAA,OAAA,IAAA,EAAA,EAAA,CAAA,CAAA,OAGA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,KACA,EAAA,EAAA,CAAA,CAAA,OAtCA,IAAM,EAAO,CAAC,GAAI,CAClB,SAAY,CAAC,WAAY,CACzB,SAAY,CAAC,WAAY,CACzB,EAAG,CACD,SAAU,CAAG,EAAI,KAAQ,CArCQ,IAAA,EAAA,CAAA,CAAA,OAqCqB,gFAAgF,AACxI,EAAE,AACF,EAAG,CACD,SAAU,CAAG,CAAE,EAAE,AACnB,EAAG,CACD,SAAU,CAAK,KAAM,CAChB,MAAO,GAAU,CAAC,CACjB,IAAK,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,KAAM,MAAM,EAAM,MAAM,CAAE,eAAiB,CAAC,CAAC,EAAE,EAAA,OAA0B,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAA,CAAE,CACjI,MAAO,CAAA,EAAG,EAAA,OAA0B,CAAC,KAAK,CAAC,CAAC,EAAE,EAAA,OAA0B,CAAC,MAAM,CAAA,CAAE,CACjF,KAAM,CAAC,YAAY,CAAC,AACtB,EAAE,CACH,AACH,EAAI,OAAU,CA3DqB,IAAA,EAAA,CAAA,CAAA,OA2DU,yEAAyE,CACtH,YAAa,CA1D4B,IAAA,EAAA,CAAA,CAAA,MA0DS,kNAAkN,CACpQ,UAAa,CAzDyB,IAAA,EAAA,CAAA,CAAA,OAyDS,kNAAkN,CACjQ,aAAgB,CAxDyB,IAAA,EAAA,CAAA,CAAA,OAwDY,qNAAqN,CAC1Q,eAAgB,CAvD4B,IAAA,EAAA,CAAA,CAAA,OAuDY,qNAC1D,AAD+Q,EAC7Q,CAOW,EAAe,CACxB,QAHyB,CAGhB,CAHsC,CAAC,CAAC,IAAI,CAAC,GAItD,UAH4B,CAGjB,CAHuC,CAAC,CAAC,IAAI,CAAC,EAI7D,EAYa,EAAc,IAAI,EAAA,kBAAkB,CAAC,CAC9C,WAAY,CACR,KAAM,EAAA,SAAS,CAAC,QAAQ,CACxB,KAAM,iBACN,SAAU,YAEV,WAAY,GACZ,SAAU,GACV,SAAU,EAAE,AAChB,EACA,SAAU,CACN,WAAY,CAChB,EACA,QAAS,CAAA,OACT,IADiD,eACc,CAA3C,CACxB,GACO,eAAe,EAAQ,CAAG,CAAE,CAAG,CAAE,CAAG,EACvC,IAAI,EACA,EAAY,KAAK,EAAE,AACnB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,+BAAgC,QAAQ,MAAM,CAAC,MAAM,IAE7E,IAAI,EAAU,iBAKV,EAAU,EAAQ,OAAO,CAAC,WAAY,KAAO,IAMjD,IAAM,GAAgB,CAAoC,CAAA,EAAA,EAAA,EAA5B,YAA4B,AAAc,EAAC,EAAK,eACxE,EAAgB,MAAM,EAAY,OAAO,CAAC,EAAK,EAAK,SACtD,EACA,mBAJE,CAAA,CAKN,GACA,GAAI,CAAC,EAID,OAHA,EAAI,IADY,MACF,CAAG,IACjB,EAAI,GAAG,CAAC,eACS,MAAjB,CAAwB,CAApB,IAAyB,KAAhB,EAAoB,EAAI,SAAS,CAAC,IAAI,CAAC,EAAK,QAAQ,OAAO,IACjE,KAEX,GAAM,SAAE,CAAO,CAAE,OAAK,QAAE,CAAM,eAAE,CAAa,eAAE,CAAa,kBAAE,CAAgB,uBAAE,CAAqB,uBAAE,CAAqB,yBAAE,CAAuB,8BAAE,CAA4B,mBAAE,CAAiB,aAAE,CAAW,kBAAE,CAAgB,yBAAE,EAAuB,qBAAE,EAAmB,YAAE,EAAU,WAAE,EAAS,CAAE,4BAAyB,CAAE,CAAG,EACpU,GAAoB,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GACvC,CAAE,uBAAoB,CAAE,CAAG,EAQzB,GAAgB,GAAW,YAAY,CAAC,GAAG,EAAI,CAAC,GAAW,eAAe,EAAI,CAAA,EAAA,EAAA,0BAAA,AAA0B,EAAC,GAAoB,KAAO,EAAY,KAAK,CAAC,EAAkB,GACxK,GAAgB,CAAC,CAAC,EAAkB,MAAM,CAAC,EAAiB,CAC5D,GAAY,EAAI,OAAO,CAAC,aAAa,EAAI,GACzC,GAAU,CAAA,EAAA,EAAA,UAAA,AAAU,EAAC,IACrB,GAAY,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,GAI3B,GAAuB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,yBAAwE,IAAI,EAAjD,EAAI,OAAO,CAAC,EAAA,2BAA2B,CAAC,CAGhH,CAHqK,EAGtJ,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,kBAAmB,CAAQ,EAAI,OAAO,CAAC,EAAA,UAAU,CAAC,CACrF,GAAyB,CAAA,EAAA,EAAA,yBAAA,AAAyB,EAAC,GAanD,GAToB,AASA,AATA,CAAA,EAAA,EAAA,cASmB,MATnB,AAAoB,EAAC,GAAW,YAAY,CAAC,GAAG,GAS5B,CAAgH,AAA/G,OAAC,EAAQ,EAAkB,MAAM,CAAC,GAAkB,EAAI,EAAkB,aAAa,CAAC,GAAA,AAAkB,EAAY,KAAK,EAAI,EAAM,aAAA,AAAa,IAAM,mBAQjM,GARuN,GASvN,IAf2B,EAmB3B,GAAmB,GAAoB,CAAA,EAAA,EAAA,EALd,IACF,QAIgB,AAAc,EAAC,EAAK,MALZ,OAduC,KAejC,AAIqB,AALf,EAdkC,AAuB7F,GAAsB,AAT0C,IAdoC,AAuBzD,CAT0B,GASV,CAAC,CAvB8C,AAMoL,EAsB9R,GAAwB,CAAA,EAAA,EAAA,GA5B+F,KAAK,MA4BpG,AAAc,EAAC,EAAK,KA5B+F,wBAiC3I,KAAyB,KAAa,EAAA,IAAoB,AAAQ,CAAC,IAAmB,CAAA,EAAA,EAAA,GAAP,yBAAO,AAA4B,EAAC,GAAW,GAAW,gBAAe,EACxJ,IAAQ,EAAQ,CAAC,IAAiB,IAAiB,EAAkB,MAAM,CAAC,GAAA,AAAkB,GAEpG,CAAC,CAAC,AAFuG,IAE1F,EAAA,CAAiB,EAE1B,GAA4B,IAAqB,AAA+B,QAApB,eAAe,CAE3E,IAEgB,IAAtB,EAAY,EAAkB,GAAb,EAEjB,CAAC,IAE2B,IAL5B,CAGU,IAVsK,CAYhL,EAAwC,KAAjC,KAGN,IAA6B,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,UAPiD,MADtC,EAW7D,EAFA,EAEuB,CAAC,AARqD,EAQrC,EAAA,CAAmB,CAErD,EARsG,CAQ/E,IAAa,GACtC,GAAc,IACd,CAAC,IAAe,IAAU,IAA4B,CAA7B,GAAwD,IAAqB,KACtG,GAAc,CAAA,EAMlB,IAAI,AAPqD,CAAgD,EAOrF,AAPgE,EAQhF,AAd+D,CAM4D,CAQ1H,IAAiB,EAAY,KAAK,EAAE,CACrC,GAAgB,CAAA,EAKhB,AAAC,EAAY,KAAK,EAAK,EAAD,EAAgB,KAAS,IAAiB,IAChE,CAAA,EAAA,EAAA,GAD+D,SAAsB,MACrF,AAAkB,EAAC,EAAI,OAAO,EAElC,IAAM,GAAe,CACjB,GAAG,CAAS,MACZ,EACA,YAAA,EAAA,OAAW,SACX,cACA,eACA,CACJ,EAII,GAAyB,GACzB,CAAA,EAAA,EAAA,iBADkD,aAClD,AAA8B,EAAC,CAC3B,KAAM,0BACN,wBACA,EACA,gBAAiB,CAAA,EAAA,EAAA,qBAAA,AAAqB,EAAC,uBACnC,CACJ,EACJ,GAEJ,IAAM,GAAS,EAAI,MAAM,EAAI,MACvB,GAAS,CAAA,EAAA,EAAA,SAAA,AAAS,IAClB,GAAa,GAAO,kBAAkB,GACtC,GAAY,WAEa,MAAvB,GAA8B,KAAK,EAAI,GAAoB,SAAA,AAAS,EAAE,AACtE,MAAM,GAAoB,SAAS,CAAC,EAAK,EAAK,IAAW,GAEzD,EAAI,GAAG,CAAC,gCAEL,MAEX,GAAI,CACA,IAAM,EAAa,EAAY,aAAa,CAAC,EAAkB,IAC/D,EAAI,SAAS,CAAC,OAAQ,GACtB,IAAM,EAAoB,MAAO,EAAM,KACnC,IAAM,EAAU,IAAI,EAAA,eAAe,CAAC,GAC9B,EAAU,IAAI,EAAA,gBAAgB,CAAC,GACrC,OAAO,EAAY,MAAM,CAAC,EAAS,EAAS,GAAS,OAAO,CAAC,KACzD,GAAI,CAAC,EAAM,OACX,EAAK,aAAa,CAAC,CACf,mBAAoB,EAAI,UAAU,CAClC,YAAY,CAChB,GACA,IAAM,EAAqB,GAAO,qBAAqB,GAEvD,GAAI,CAAC,EACD,OAEJ,GAAI,EAAmB,GAAG,CAAC,EAHF,kBAGwB,EAAA,cAAc,CAAC,aAAa,CAAE,YAC3E,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,EAAmB,GAAG,CAAC,kBAAkB,qEAAqE,CAAC,EAG9J,IAAM,EAAQ,EAAmB,GAAG,CAAC,cACrC,GAAI,EAAO,CACP,IAAM,EAAO,CAAA,EAAG,GAAO,CAAC,EAAE,EAAA,CAAO,CACjC,EAAK,aAAa,CAAC,CACf,aAAc,EACd,aAAc,EACd,iBAAkB,CACtB,GACA,EAAK,UAAU,CAAC,EACpB,MACI,CADG,CACE,UAAU,CAAC,CAAA,EAAG,GAAO,CAAC,EAAE,EAAA,CAAS,CAE9C,EACJ,EACM,EAAmB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,oBACvC,EAAW,MAAO,MAAE,CAAI,WAAE,CAAS,qBAAE,CAAmB,mBAAE,CAAiB,CAAE,IAC/E,IAAM,EAAU,OACZ,SACA,EACA,KAAM,GACN,cAAe,SACX,CACJ,EACA,yBAA0B,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gDAC9C,EACA,WAAY,CACR,IAAK,IAAI,KACT,SAAU,IAAI,KACd,WAAY,CAAC,eACb,GACA,UAAW,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,WAC1B,cACA,EACA,KAAM,YACN,uBACA,0BACA,GACA,wBAA8C,UAArB,OAAO,GAA0B,GAC1D,gBACA,mBACA,qDACA,wBACA,0BACA,EACA,eAAuC,MAAvB,GAA8B,KAAK,EAAI,GAAoB,cAAc,CACzF,aAAqC,MAAvB,GAA8B,KAAK,EAAI,GAAoB,YAAY,CACrF,qBAA6C,MAAvB,GAA8B,KAAK,EAAI,GAAoB,oBAAoB,CACrG,IAA6C,CAAxC,OAAgD,QAAQ,IAAI,CAAC,AAA4B,QAAQ,GAAG,GAAI,EAAY,SAA9B,SAAgD,EAC3I,EAD+I,oBAE/I,wBACA,GACA,0BACA,YAAa,GAAW,WAAW,CACnC,iBAAkB,GAAW,MAAM,CACnC,YAAa,GAAW,WAAW,CACnC,cAAe,GAAW,aAAa,CACvC,OAAQ,GAAW,MAAM,CACzB,aAAc,EAAkB,OAAO,CACvC,aAAc,GAAW,YAAY,CACrC,eAAgB,GAAW,YAAY,CAAC,KAAK,CAC7C,gBAAiB,GAAW,eAAe,CAC3C,sBAAuB,GAAW,qBAAqB,CACvD,mBAtOV,CAAA,EAuOU,mBACA,kBAAmB,GAAW,SAAS,CACvC,SAAU,GAAW,QAAQ,CAC7B,cAAe,GAAW,YAAY,CAAC,aAAa,CACpD,GAAG,AAAsB,IAA0B,GAAuB,CACtE,YAAY,EACZ,yBAAyB,EACzB,oBAAoB,EACpB,uBAAwB,EAC5B,EAAI,CAAC,CAAC,CACN,iBAAiB,CAAQ,GAAW,eAAe,CACnD,aAAc,mBACV,GACA,WAAY,GAAW,UAAU,CACjC,WAAY,GAAW,YAAY,CAAC,UAAU,CAC9C,oBAAoB,CAAQ,GAAW,YAAY,CAAC,kBAAkB,CACtE,gBAAgB,CAAQ,GAAW,YAAY,CAAC,cAAc,CAC9D,WAAW,CAAQ,GAAW,YAAY,CAAC,SAAS,CACpD,gBAAgB,CAAQ,GAAW,YAAY,CAAC,cAAc,CAC9D,oBAAqB,GAAW,YAAY,CAAC,mBAAmB,EAAI,EAAE,CACtE,0BAA2B,GAAW,YAAY,CAAC,yBAAyB,AAChF,EACA,UAAW,EAAI,SAAS,CACxB,QAAS,AAAC,IACN,EAAI,EAAE,CAAC,QAAS,EACpB,EACA,iBAAkB,KAAK,EACvB,8BAA+B,CAAC,EAAO,EAAU,IAAe,EAAY,cAAc,CAAC,EAAK,EAAO,EAAc,IACrH,IAAK,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,eACzB,IAAK,EAAY,KAAK,AAC1B,CACJ,EAC0B,KACtB,EAAQ,UAAU,CAAC,MAD2B,IACjB,EAAG,EAChC,EAAQ,UAAU,CAAC,uBAAuB,EAAG,EAC7C,EAAQ,UAAU,CAAC,sBAAsB,CAAG,IAI5C,IACA,EAAQ,UAAU,CAAC,EADA,qBACuB,CAAG,EAAA,EAEjD,IAAM,EAAS,MAAM,EAAkB,EAAM,GACvC,UAAE,CAAQ,CAAE,CAAG,EACf,cAAE,CAAY,CAAE,UAAU,CAAC,CAAC,CAClC,UAAW,CAAS,cAAE,CAAY,CAAE,CAAG,EAUvC,GATI,IACA,CAAO,CAAC,EAAA,GADG,mBACmB,CAAC,CAAG,CAAA,EAItC,EAAI,YAAY,CAAG,EAIf,IAAS,CAAiB,MAAhB,EAAuB,KAAK,EAAI,EAAa,UAAA,AAAU,IAAM,GAAK,CAAC,EAAY,KAAK,EAAI,CAAC,GAAmB,CACtH,IAAM,EAAoB,EAAS,iBAAiB,CAC9C,EAAM,OAAO,cAAc,CAAK,AAAJ,MAAU,CAAC,+CAA+C,EAAE,EAAA,EAAmB,CAAsB,MAArB,EAA4B,KAAK,EAAI,EAAkB,WAAA,AAAW,EAAI,CAAC,UAAU,EAAE,EAAkB,WAAW,CAAA,CAAE,CAAG,EAAE;AAAM,0EAA4E,CAAhF,AAAiF,EAAG,CAAjF,CAAC,kBAAqG,CAChV,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GACA,GAAyB,MAArB,EAA4B,KAAK,EAAI,EAAkB,KAAK,CAAE,CAC9D,IAAM,EAAQ,EAAkB,KAAK,CACrC,EAAI,KAAK,CAAG,EAAI,OAAO,CAAG,EAAM,SAAS,CAAC,EAAM,OAAO,CAAC,MAC5D,CACA,MAAM,CACV,CACA,MAAO,CACH,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,QAAQ,CAC9B,KAAM,UACN,EACA,QAAS,EAAS,UAAU,CAC5B,UAAW,EAAS,SAAS,CAC7B,OAAQ,EAAS,UAAU,CAC3B,YAAa,EAAS,WAAW,AACrC,eACA,CACJ,CACJ,EACM,EAAoB,MAAO,aAAE,CAAW,CAAE,mBAAoB,CAA6B,gBAAE,CAAc,MAAE,CAAI,mBAAE,GAAoB,CAAK,CAAE,IAChJ,IAaI,EAbE,GAAqC,IAAtB,EAAY,KAAK,CAChC,EAAa,GAAe,EAAI,aAAa,CAGnD,GAAI,IAAwB,IAA2B,CAAC,GAAiC,CAAC,EAOtF,OAN2B,MAD0E,AACjG,GAA8B,KAAK,EAAI,GAAoB,SAAA,AAAS,EAAE,AACtE,MAAM,GAAoB,SAAS,CAAC,EAAK,IAEzC,EAAI,UAAU,CAAG,IACjB,EAAI,GAAG,CAAC,iCAEL,KAuBX,GApBI,KACA,EAAe,CAAA,EAAA,EAAA,GADA,eACA,AAAkB,EAAC,GAAc,SAAQ,EAKxD,IAAiB,EAAA,YAAY,CAAC,SAAS,EAAI,CAAA,EAAA,EAAA,KAAA,AAAK,EAAC,MAC7C,CAAC,IAAqB,CADmC,CACnC,GACtB,AADiC,GAClB,EAAA,YAAY,CAAC,sBAAA,AAAsB,EAGtD,CAAkC,MAAjC,EAAwC,KAAK,EAAI,EAA8B,OAAA,AAAO,IAAM,CAAC,GAAG,CACjG,IAAuB,CAAA,EAKvB,KAAyB,IAAiB,EAAA,YAAY,CAA9B,AAA+B,SAAS,EAAI,CAAA,CAA6B,GAAG,AACpG,EAAe,EAAA,YAAY,CAAC,sBAAA,AAAsB,EAElD,CAAC,GAAiB,IAAiB,EAAA,YAAY,CAAC,sBAAsB,EAAI,IAAiB,CAAC,GAAc,CAAC,GAAe,IAAkB,GAAgB,CAAC,EAAA,CAAa,CAAG,CAG7K,GAEA,CAAC,AAL0I,AAI3I,GACiB,EAAA,CAAa,EAC9B,GADmC,CAClB,EAAA,IAFC,QAEW,CAAC,SAAS,CAAE,CACrC,GAAI,GAAW,YAAY,CAAC,WAAW,CACnC,CADqC,AAFiD,MAG/E,MAAM,IAEjB,OAAM,IAAI,EAAA,eAAe,AAC7B,CAMA,GAAI,KAAsB,GAAW,aAAZ,EAA2B,CAAG,CAAC,GAAsB,CAAC,EAAA,CAAY,CAAG,CAC1F,IAAM,EAAW,GAAqF,UAArE,MAAQ,CAAD,AAAkB,SAAO,KAAK,EAAI,GAAc,QAAA,AAAQ,EAAiB,GAAc,QAAQ,CAAG,GACpI,EAEN,IAAkC,MAAjB,GAAwB,GAAzB,EAA8B,EAAI,AADlD,GACgE,mBAAA,AAAmB,EAAI,CAAA,EAAA,EAAA,iBADzC,cACyC,AAA+B,EAAC,GAAc,mBAAmB,EACxJ,GAAuB,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,GAAmB,GAAe,KAG1E,EAAmB,MAAM,EAAY,cAAc,CAAC,UACtD,MACA,aACA,GACA,UAAW,EAAA,SAAS,CAAC,QAAQ,CAC7B,YAAY,oBACZ,EACA,qBACA,kBAAmB,SAAU,EAAS,MAC9B,EAGA,eAAW,sBACX,EACA,mBAAmB,CACvB,GACJ,UAAW,EAAI,SAAS,eACxB,CACJ,GAEA,GAAyB,AAArB,SAA2B,OAAO,KAEtC,GAAI,EAIA,OADA,OAAO,EAHW,AAGM,YAAY,CAC7B,CAEf,CACJ,CAGA,IAAI,EAAY,AAAC,IAAyB,IAAkB,GAAsC,OAAnB,GAK/E,GALyC,AAMzC,IAAoE,CAAC,GAAiB,GAAoB,IAK1G,CAAC,EAAmB,CAChB,IAAM,EAAwB,IANL,EAMW,EAAiB,CANwE,EAMrE,CAAC,EAAkB,CACvE,KAAM,EAAA,oBAAoB,CAAC,QAPuB,AAOf,CACnC,mBAAmB,EACnB,OATgM,KASpL,CAChB,GAGI,GAAyB,EAAsB,KAAK,EAAI,EAAsB,KAAK,CAAC,IAAI,GAAK,EAAA,eAAe,CAAC,QAAQ,EAAE,CAGvH,EAAY,EAAsB,KAAK,CAAC,SAAS,CAG7C,IAG+B,CAAC,IAAnC,EAAsB,OAAO,EAA6C,AAAlC,KAHZ,EAGkC,OAAY,AAAL,CAAS,EAG3E,CAH8E,AAG9E,EAAA,EAAA,kBAAA,AAAkB,EAAC,UACf,IAAM,EAAgB,EAAY,WAPqD,KAOrC,CAAC,GACnD,GAAI,CACA,MAAM,EAAc,UAAU,CAAC,EAAkB,EAAkB,IAAmB,EAAO,AAAC,GAAI,EAAkB,CAC5G,GAAG,CAAC,CAIJ,kBAAmB,EACvB,GAGJ,CAFA,IAEM,EAAa,EAAI,SAAS,CACpC,CAAE,MAAO,EAAK,CACV,QAAQ,KAAK,CAAC,kBAJ8C,8BAIG,EACnE,CACJ,GAGZ,CAGA,GAAI,AAAuB,CAAtB,GAAiD,KAAqB,IAAd,EACzD,MAAO,CAD6E,AAEhF,AAFyC,aAE3B,CACV,WAAY,EACZ,YAAQ,CACZ,EACA,MAAO,CACH,KAAM,EAAA,eAAe,CAAC,KAAK,CAC3B,KAAM,EAAA,OAAY,CAAC,KAAK,CACxB,SAAU,CAAC,EACX,QAAS,OACT,YAAQ,CACZ,CACJ,EAEJ,IAAM,EAGN,IAAkC,MAAjB,GAAwB,GAAzB,EAA8B,EAF9C,AAEkD,GAAc,mBAAA,AAAmB,GAAK,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,sBAFtC,CAE+D,CAAA,EAAA,EAAA,+BAAA,AAA+B,EAAC,GAAc,mBAAmB,EACtM,GAAuB,CAAA,EAAA,EAAA,sBAAA,AAAsB,EAAC,GAAmB,GAAe,KAEhF,OAAO,EAAS,MACZ,YACA,sBACA,oBACA,CACJ,EACJ,EACM,EAAiB,MAAO,QACtB,EAAmB,EAuCf,EAsDQ,EAsBR,MArDJ,EA7DE,EAAa,MAAM,EAAY,cAAc,CAAC,CAChD,SAAU,GACV,kBAAmB,AAAC,GAAI,EAAkB,MAClC,EACA,GAAG,CACP,AADQ,GAEZ,UAAW,EAAA,SAAS,CAAC,QAAQ,sBAC7B,GACA,qBACA,MACA,gCACA,EACA,UAAW,EAAI,SAAS,eACxB,CACJ,GAQA,GAPI,GACA,EAAI,QADS,CACA,CAAC,gBAAiB,2DAG/B,EAAY,KAAK,EAAE,AACnB,EAAI,SAAS,CAAC,gBAAiB,6BAE/B,CAAC,EAAY,CACb,GAAI,GAMA,MAAM,IANO,GAMA,cAAc,CAAK,AAAJ,MAAU,qDAAsD,oBAAqB,CAC7G,MAAO,MACP,YAAY,EACZ,cAAc,CAClB,GAEJ,OAAO,IACX,CACA,GAAI,CAA2C,AAA1C,OAAC,EAAoB,EAAW,KAAK,AAAL,EAAiB,KAAK,EAAI,EAAkB,IAAA,AAAI,IAAM,EAAA,eAAe,CAAC,QAAQ,CAE/G,CAFiH,KAE3G,OAAO,cAAc,CAAK,AAAJ,MAAU,CAAC,wDAAwD,EAA6C,AAA3C,OAAC,EAAqB,EAAW,KAAA,AAAK,EAAY,KAAK,EAAI,EAAmB,IAAI,CAAA,CAAE,EAAG,oBAAqB,CACzM,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAEJ,IAAM,EAAoD,UAAtC,OAAO,EAAW,KAAK,CAAC,SAAS,CACjD,IAGJ,CAAC,IAHY,CAGY,CAAC,GAAe,EAAA,CAAoB,GAAG,AACxD,AAAC,GAGD,EAAI,GAJY,MAIH,CAHG,AAGF,iBAAkB,GAAuB,cAAgB,EAAW,MAAM,CAAG,IAPb,GAOsB,EAAW,OAAO,CAAG,QAAU,OAIvI,EAAI,SAAS,CAAC,EAAA,wBAAwB,CAAE,MAE5C,GAAM,CAAE,MAAO,CAAU,CAAE,CAAG,EAK9B,GAAI,GACA,EAAe,CACX,WAAY,CAFE,CAGd,YAAQ,CACZ,OACG,GAAI,GACP,EAAe,CACX,WAAY,EACZ,EAHwB,UAGhB,CACZ,OACG,GAAI,CAAC,EAAY,KAAK,CAEzB,CAF2B,EAEvB,EACA,EAAe,CACX,QAFS,GAEG,EACZ,YAAQ,CACZ,OACG,GAAK,CAAD,GAOJ,GAPY,AAOR,EAAW,YAAY,CAG9B,CAHgC,EAGkB,UAA9C,OAAO,EAAW,YAAY,CAAC,UAAU,CAAe,CAExD,GAAI,EAAW,YAAY,CAAC,UAAU,CAAG,EACrC,CADwC,KAClC,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,2CAA2C,EAAE,EAAW,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,EAAG,oBAAqB,CAChJ,MAAO,MACP,WAAY,GACZ,cAAc,CAClB,GAEJ,EAAe,CACX,WAAY,EAAW,YAAY,CAAC,UAAU,CAC9C,OAAQ,AAAC,CAAwD,OAAvD,EAA2B,EAAW,YAAA,AAAY,EAAY,KAAK,EAAI,EAAyB,MAAA,AAAM,GAAK,GAAW,UAAU,AAC9I,CACJ,MACI,CADG,CACY,CACX,WAAY,EAAA,cAAc,CAC1B,YAAQ,CACZ,CAER,MA5BQ,AAAC,EAAI,SAAS,CAAC,kBAAkB,CACjC,EAAe,CACX,WAAY,EACZ,YAAQ,CACZ,GA2BZ,GADA,EAAW,YAAY,CAAG,EACW,UAAjC,OAAO,IAAsC,CAAe,MAAd,EAAqB,KAAK,EAAI,EAAW,IAAA,AAAI,IAAM,EAAA,eAAe,CAAC,QAAQ,EAAI,EAAW,WAAW,CAAE,CAWrJ,EAAI,SAAS,CAAC,EAAA,wBAAwB,CAAE,KAGxC,IAAM,EAAsD,AAA/C,OAAC,EAAuB,EAAW,OAAA,AAAO,EAAY,KAAK,EAAI,CAAoB,CAAC,EAAA,sBAAsB,CAAC,CACpH,GAAiB,IAAS,GAAwB,UAAU,AAA1B,OAAO,GACzC,EAAI,SAAS,CAAC,EAAA,sBAAsB,CAAE,GAE1C,IAAM,EAAiB,EAAW,WAAW,CAAC,GAAG,CAAC,WAClD,KAAuB,IAAnB,EAEO,CAAA,EAAA,EAAA,AAFuB,gBAEvB,AAAgB,EAAC,KACpB,MACA,EACA,cAAe,GAAW,aAAa,CACvC,gBAAiB,GAAW,eAAe,CAC3C,OAAQ,EAAA,OAAY,CAAC,UAAU,CAAC,EAAgB,EAAA,uBAAuB,EACvE,aAAc,EAAW,YAAY,AACzC,IAQJ,EAAI,UAAU,CAAG,IACV,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,KACpB,MACA,EACA,cAAe,GAAW,aAAa,CACvC,gBAAiB,GAAW,eAAe,CAC3C,OAAQ,EAAA,OAAY,CAAC,KAAK,CAC1B,aAAc,EAAW,YAC7B,AADyC,GAE7C,CAMA,IAAM,EAAe,GAA4B,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,mBAAqB,CAAA,EAAA,EAAA,cAAc,AAAd,EAAe,EAAK,gBAAkB,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,gBACpJ,GAAI,GACiB,MAAM,EAAa,EAAY,CADlC,AAEV,IAAK,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,EAAK,YAAc,EAAI,GAAG,AAClD,GACc,OAAO,KAEzB,GAAI,EAAW,OAAO,CAAE,CACpB,IAAM,EAAU,CACZ,GAAG,EAAW,OAAO,AACzB,EAIA,IAAK,GAAI,CAAC,EAAK,EAAM,GAHjB,AAAC,GAAkB,IACnB,GAD0B,IACnB,CAAO,CAAC,CADG,CACH,sBAAsB,CAAC,CAEjB,OAAO,OAAO,CAAC,IACpC,GAAqB,AAAjB,EADyC,OAClC,EACX,EADkC,CAC9B,MAAM,OAAO,CAAC,GACd,IAAK,CADiB,GACX,KAAK,EACZ,EAAI,EADc,UACF,CAAC,EAAK,OAEF,UAAjB,AAA2B,OAApB,GACd,GAAQ,EAAM,QAAQ,EAAA,EAGtB,EAAI,YAAY,CAAC,EAAK,EAGlC,CAGA,IAAM,EAAO,AAA8C,OAA7C,EAAsB,EAAW,OAAA,AAAO,EAAY,KAAK,EAAI,CAAmB,CAAC,EAAA,sBAAsB,CAAC,CAsBtH,GArBI,GAAiB,IAAS,GAAwB,UAAhB,AAA0B,OAAnB,GACzC,EAAI,SAAS,CAAC,EAAA,sBAAsB,CAAE,IAKtC,EAAW,MAAM,EAAK,AAAC,EAAF,EAAmB,KACxC,EAAI,KADmC,KAAkB,AAC3C,CAAG,EAD2C,AAChC,MAAA,AAAM,EAGlC,CAAC,GAAiB,EAAW,MAAM,EAAI,EAAA,kBAAkB,CAAC,EAAW,MAAM,CAAC,EAAI,KAChF,EAAI,OAD0F,GAChF,CAAG,GAAA,EAGjB,GAAe,CAAC,IAChB,EAAI,SAAS,CAAC,EAAA,GADuB,qBACC,CAAE,KAMxC,IAAgB,CAAC,EAAa,CAE9B,GAAI,AAA8B,SAAvB,EAAW,OAAO,CAAkB,CAE3C,GAAI,EAAW,IAAI,CAAC,WAAW,GAAK,EAAA,uBAAuB,CACvD,CADyD,EACrD,GAAW,eAAe,CAE1B,CAF4B,MAC5B,EAAI,UAAU,CAAG,IACV,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,KACpB,MACA,EACA,cAAe,GAAW,aAAa,CACvC,gBAAiB,GAAW,eAAe,CAC3C,OAAQ,EAAA,OAAY,CAAC,KAAK,CAC1B,aAAc,EAAW,YAAY,AACzC,QAGA,MAAM,OAAO,cAAc,CAAC,IAAI,EAAA,cAAc,CAAC,CAAC,2BAA2B,EAAE,EAAW,IAAI,CAAC,WAAW,CAAA,CAAE,EAAG,oBAAqB,CAC9H,MAAO,OACP,WAAY,GACZ,cAAc,CAClB,GAGR,MAAO,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,KACpB,MACA,EACA,cAAe,GAAW,aAAa,CACvC,gBAAiB,GAAW,eAAe,CAC3C,OAAQ,EAAW,IAAI,CACvB,aAAc,EAAW,YAAY,AACzC,EACJ,CAGA,MAAO,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,KACpB,MACA,EACA,cAAe,GAAW,aAAa,CACvC,gBAAiB,GAAW,eAAe,CAC3C,OAAQ,EAAA,OAAY,CAAC,UAAU,CAAC,EAAW,OAAO,CAAE,EAAA,uBAAuB,EAC3E,aAAc,EAAW,YAAY,AACzC,EACJ,CAEA,IAAM,EAAO,EAAW,IAAI,CAI5B,GAAI,CAAC,GAAe,GAAiB,GAUjC,MAAO,CAAA,EAAA,EAAA,AAVwC,gBAUxC,AAAgB,EAAC,KACpB,MACA,EACA,cAAe,GAAW,aAAa,CACvC,gBAAiB,GAAW,eAAe,CAC3C,OAAQ,EACR,aAAc,EAAW,YAAY,AACzC,GAMJ,GAA0B,CAAtB,EASA,OANA,EAAK,IAAI,CAAC,IAAI,GAHgC,YAGjB,CACzB,MAAO,CAAU,EACb,EAAW,OAAO,CAAC,EAAA,YAAY,CAAC,MAAM,CAAC,aAAa,EACpD,EAAW,KAAK,EACpB,CACJ,IACO,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,KACpB,MACA,EACA,cAAe,GAAW,aAAa,CACvC,gBAAiB,GAAW,eAAe,CAC3C,OAAQ,EACR,aAAc,CACV,WAAY,EACZ,YAAQ,CACZ,CACJ,GAWJ,IAAM,EAAc,IAAI,gBAsCxB,OArCA,EAAK,IAAI,CAAC,EAAY,QAAQ,EAI9B,EAAS,MACL,EACA,UAAW,EAAW,SAAS,CAG/B,oBAAqB,KACrB,mBAAmB,CACvB,GAAG,IAAI,CAAC,MAAO,QACP,EASI,EARR,GAAI,CAAC,EACD,MADS,AACH,OAAO,cAAc,CAAC,AAAI,MAAM,+CAAgD,oBAAqB,CACvG,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,GAEJ,GAAI,CAAC,AAAkC,OAAjC,EAAgB,EAAO,KAAA,AAAK,EAAY,KAAK,EAAI,EAAc,IAAA,AAAI,IAAM,EAAA,eAAe,CAAC,QAAQ,CAEnG,CAFqG,KAE/F,OAAO,cAAc,CAAC,AAAI,MAAM,CAAC,yCAAyC,EAAE,AAAmC,OAAlC,EAAiB,EAAO,KAAA,AAAK,EAAY,KAAK,EAAI,EAAe,IAAI,CAAA,CAAE,EAAG,oBAAqB,CAC9K,MAAO,OACP,YAAY,EACZ,cAAc,CAClB,EAGJ,OAAM,EAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAY,QAAQ,CACvD,GAAG,KAAK,CAAC,AAAC,IAGN,EAAY,QAAQ,CAAC,KAAK,CAAC,GAAK,KAAK,CAAC,AAAC,IACnC,QAAQ,KAAK,CAAC,6BAA8B,EAChD,EACJ,GACO,CAAA,EAAA,EAAA,gBAAA,AAAgB,EAAC,KACpB,MACA,EACA,cAAe,GAAW,aAAa,CACvC,gBAAiB,GAAW,eAAe,CAC3C,OAAQ,EAIR,aAAc,CACV,WAAY,EACZ,YAAQ,CACZ,CACJ,EACJ,EAGA,IAAI,GAGA,OAAO,EAHK,IAGC,GAAO,qBAAqB,CAAC,EAAI,OAAO,CAAE,IAAI,GAAO,KAAK,CAAC,EAAA,cAAc,CAAC,aAAa,CAAE,CAC9F,SAAU,CAAA,EAAG,GAAO,CAAC,EAAE,EAAA,CAAS,CAChC,KAAM,EAAA,QAAQ,CAAC,MAAM,CACrB,WAAY,CACR,cAAe,GACf,cAAe,EAAI,GAAG,AAC1B,CACJ,EAAG,GATP,OAAM,EAAe,GAW7B,CAAE,MAAO,EAAK,CAaV,MAZI,AAAE,CAAD,YAAgB,EAAA,eAAe,EAChC,CADmC,KAC7B,EAAY,cAAc,CAAC,EAAK,EAAK,CACvC,WAAY,aACZ,UAAW,EACX,UAAW,SACX,iBAAkB,CAAA,EAAA,EAAA,mBAAA,AAAmB,EAAC,CAClC,mBAAoB,wBACpB,EACJ,EACJ,EAAG,IAGD,CACV,CACJ", "ignoreList": [0]}