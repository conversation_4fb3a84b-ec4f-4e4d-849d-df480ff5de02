module.exports=[93695,(a,b,c)=>{b.exports=a.x("next/dist/shared/lib/no-fallback-error.external.js",()=>require("next/dist/shared/lib/no-fallback-error.external.js"))},62439,a=>{a.n(a.i(60755))},9154,a=>{a.n(a.i(41665))},82749,a=>{a.n(a.i(36869))},62121,a=>{a.n(a.i(74619))},53039,a=>{a.n(a.i(9622))},60099,a=>{"use strict";var b=a.i(49916),c=a.i(3595),d=a.i(75434);let e=[{icon:"🎯",title:"Strategic Affiliate Partnerships",description:"Unlock growth with tailored affiliate partnerships that enhance visibility and drive customer engagement.",features:["Partner Network Development","Performance Tracking","Revenue Optimization","Strategic Planning"]},{icon:"📊",title:"Affiliate Marketing",description:"At flywheel-media, we empower brands through strategic affiliate marketing, connecting them with ideal customers for meaningful growth.",features:["Campaign Management","Audience Targeting","Conversion Optimization","ROI Analysis"]},{icon:"🌐",title:"Global Network Access",description:"Leverage our extensive global network to reach audiences beyond borders with partners spanning multiple industries.",features:["International Reach","Multi-Industry Partners","Cross-Border Solutions","Market Expansion"]},{icon:"🚀",title:"Technology Solutions",description:"Stay ahead with our cutting-edge adtech solutions designed for competitive digital landscapes.",features:["Advanced Analytics","Real-time Tracking","Automated Optimization","Custom Integrations"]},{icon:"🔍",title:"Transparency & Reporting",description:"Comprehensive reports and full transparency so you can see exactly how your campaigns perform.",features:["Detailed Analytics","Performance Metrics","Custom Reports","Real-time Data"]},{icon:"👥",title:"Dedicated Support",description:"Our experienced account managers work closely with you to ensure optimal campaign performance.",features:["Personal Account Manager","24/7 Support","Strategic Consultation","Ongoing Optimization"]}];function f(){return(0,b.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,b.jsx)("section",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 py-20",children:(0,b.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:"Our Services"}),(0,b.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Comprehensive affiliate marketing solutions designed to drive growth and maximize your business potential"})]})})}),(0,b.jsx)("section",{className:"py-20",children:(0,b.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,b.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map((a,e)=>(0,b.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-8 hover:shadow-lg transition-shadow duration-300",children:[(0,b.jsx)("div",{className:"text-4xl mb-4",children:a.icon}),(0,b.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:a.title}),(0,b.jsx)("p",{className:"text-gray-600 mb-6",children:a.description}),(0,b.jsx)("ul",{className:"space-y-2 mb-6",children:a.features.map((a,c)=>(0,b.jsxs)("li",{className:"text-sm text-gray-500 flex items-start",children:[(0,b.jsx)("span",{className:"text-blue-500 mr-2",children:"✓"}),a]},c))}),(0,b.jsx)(c.default,{href:"/contact",className:"w-full",children:(0,b.jsx)(d.Button,{variant:"outline",className:"w-full",children:"Get Started"})})]},e))})})}),(0,b.jsx)("section",{className:"bg-blue-600 py-20",children:(0,b.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,b.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-white mb-6",children:"Ready to Get Started?"}),(0,b.jsx)("p",{className:"text-xl text-blue-100 mb-8",children:"Let's discuss how our services can help accelerate your business growth through strategic affiliate marketing."}),(0,b.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,b.jsx)(c.default,{href:"/contact",children:(0,b.jsx)(d.Button,{variant:"secondary",size:"lg",children:"Contact Us Today"})}),(0,b.jsx)(c.default,{href:"/about",children:(0,b.jsx)(d.Button,{variant:"outline",size:"lg",className:"border-white text-white hover:bg-white hover:text-blue-600",children:"Learn More About Us"})})]})]})})]})}a.s(["default",()=>f])}];

//# sourceMappingURL=%5Broot-of-the-server%5D__31e125ed._.js.map