(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,76936,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},14036,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n={assign:function(){return s},searchParamsToUrlQuery:function(){return a},urlQueryToSearchParams:function(){return l}};for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]});function a(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function u(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function l(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,u(e));else t.set(r,u(n));return t}function s(e,...t){for(let r of t){for(let t of r.keys())e.delete(t);for(let[t,n]of r.entries())e.append(t,n)}return e}},172,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n={formatUrl:function(){return l},formatWithValidation:function(){return i},urlObjectKeys:function(){return s}};for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]});let a=e.r(15065)._(e.r(14036)),u=/https?|ftp|gopher|file/;function l(e){let{auth:t,hostname:r}=e,n=e.protocol||"",o=e.pathname||"",l=e.hash||"",s=e.query||"",i=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?i=t+e.host:r&&(i=t+(~r.indexOf(":")?`[${r}]`:r),e.port&&(i+=":"+e.port)),s&&"object"==typeof s&&(s=String(a.urlQueryToSearchParams(s)));let c=e.search||s&&`?${s}`||"";return n&&!n.endsWith(":")&&(n+=":"),e.slashes||(!n||u.test(n))&&!1!==i?(i="//"+(i||""),o&&"/"!==o[0]&&(o="/"+o)):i||(i=""),l&&"#"!==l[0]&&(l="#"+l),c&&"?"!==c[0]&&(c="?"+c),o=o.replace(/[?#]/g,encodeURIComponent),c=c.replace("#","%23"),`${n}${i}${o}${c}${l}`}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function i(e){return l(e)}},67703,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=e.r(46739);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=a(e,n)),t&&(o.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},41878,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n={DecodeError:function(){return x},MiddlewareNotFoundError:function(){return j},MissingStaticPage:function(){return v},NormalizeError:function(){return b},PageNotFoundError:function(){return g},SP:function(){return m},ST:function(){return y},WEB_VITALS:function(){return a},execOnce:function(){return u},getDisplayName:function(){return f},getLocationOrigin:function(){return i},getURL:function(){return c},isAbsoluteUrl:function(){return s},isResSent:function(){return d},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return p},stringifyError:function(){return P}};for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]});let a=["CLS","FCP","FID","INP","LCP","TTFB"];function u(e){let t,r=!1;return(...n)=>(r||(r=!0,t=e(...n)),t)}let l=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>l.test(e);function i(){let{protocol:e,hostname:t,port:r}=window.location;return`${e}//${t}${r?":"+r:""}`}function c(){let{href:e}=window.location,t=i();return e.substring(t.length)}function f(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function d(e){return e.finished||e.headersSent}function p(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?`?${t.slice(1).join("?")}`:"")}async function h(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&d(r))return n;if(!n)throw Object.defineProperty(Error(`"${f(e)}.getInitialProps()" should resolve to an object. But found "${n}" instead.`),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let m="undefined"!=typeof performance,y=m&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class x extends Error{}class b extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message=`Cannot find module for page: ${e}`}}class v extends Error{constructor(e,t){super(),this.message=`Failed to load static file for page: ${e} ${t}`}}class j extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function P(e){return JSON.stringify({message:e.message,stack:e.stack})}},97046,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isLocalURL",{enumerable:!0,get:function(){return a}});let n=e.r(41878),o=e.r(50028);function a(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},43164,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},17400,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n={default:function(){return x},useLinkStatus:function(){return g}};for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]});let a=e.r(15065),u=e.r(87722),l=a._(e.r(46739)),s=e.r(172),i=e.r(572),c=e.r(67703),f=e.r(41878),d=e.r(62977);e.r(76936);let p=e.r(20215),h=e.r(97046),m=e.r(86811);function y(e){return"string"==typeof e?e:(0,s.formatUrl)(e)}function x(t){var r;let n,o,a,[s,x]=(0,l.useOptimistic)(p.IDLE_LINK_STATUS),g=(0,l.useRef)(null),{href:v,as:j,children:P,prefetch:N=null,passHref:O,replace:_,shallow:E,scroll:S,onClick:C,onMouseEnter:k,onTouchStart:w,legacyBehavior:T=!1,onNavigate:L,ref:M,unstable_dynamicOnHover:R,...U}=t;n=P,T&&("string"==typeof n||"number"==typeof n)&&(n=(0,u.jsx)("a",{children:n}));let A=l.default.useContext(i.AppRouterContext),$=!1!==N,I=!1!==N?null===(r=N)||"auto"===r?m.FetchStrategy.PPR:m.FetchStrategy.Full:m.FetchStrategy.PPR,{href:B,as:F}=l.default.useMemo(()=>{let e=y(v);return{href:e,as:j?y(j):e}},[v,j]);if(T){if(n?.$$typeof===Symbol.for("react.lazy"))throw Object.defineProperty(Error("`<Link legacyBehavior>` received a direct child that is either a Server Component, or JSX that was loaded with React.lazy(). This is not supported. Either remove legacyBehavior, or make the direct child a Client Component that renders the Link's `<a>` tag."),"__NEXT_ERROR_CODE",{value:"E863",enumerable:!1,configurable:!0});o=l.default.Children.only(n)}let D=T?o&&"object"==typeof o&&o.ref:M,K=l.default.useCallback(e=>(null!==A&&(g.current=(0,p.mountLinkInstance)(e,B,A,I,$,x)),()=>{g.current&&((0,p.unmountLinkForCurrentNavigation)(g.current),g.current=null),(0,p.unmountPrefetchableInstance)(e)}),[$,B,A,I,x]),z={ref:(0,c.useMergedRef)(K,D),onClick(t){T||"function"!=typeof C||C(t),T&&o.props&&"function"==typeof o.props.onClick&&o.props.onClick(t),!A||t.defaultPrevented||function(t,r,n,o,a,u,s){if("undefined"!=typeof window){let i,{nodeName:c}=t.currentTarget;if("A"===c.toUpperCase()&&((i=t.currentTarget.getAttribute("target"))&&"_self"!==i||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which)||t.currentTarget.hasAttribute("download"))return;if(!(0,h.isLocalURL)(r)){a&&(t.preventDefault(),location.replace(r));return}if(t.preventDefault(),s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}let{dispatchNavigateAction:f}=e.r(7677);l.default.startTransition(()=>{f(n||r,a?"replace":"push",u??!0,o.current)})}}(t,B,F,g,_,S,L)},onMouseEnter(e){T||"function"!=typeof k||k(e),T&&o.props&&"function"==typeof o.props.onMouseEnter&&o.props.onMouseEnter(e),A&&$&&(0,p.onNavigationIntent)(e.currentTarget,!0===R)},onTouchStart:function(e){T||"function"!=typeof w||w(e),T&&o.props&&"function"==typeof o.props.onTouchStart&&o.props.onTouchStart(e),A&&$&&(0,p.onNavigationIntent)(e.currentTarget,!0===R)}};return(0,f.isAbsoluteUrl)(F)?z.href=F:T&&!O&&("a"!==o.type||"href"in o.props)||(z.href=(0,d.addBasePath)(F)),a=T?l.default.cloneElement(o,z):(0,u.jsx)("a",{...U,...z,children:n}),(0,u.jsx)(b.Provider,{value:s,children:a})}e.r(43164);let b=(0,l.createContext)(p.IDLE_LINK_STATUS),g=()=>(0,l.useContext)(b);("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},4890,e=>{"use strict";var t=e.i(87722),r=e.i(17400),n=e.i(46739),o=e.i(50222);function a(){let[e,a]=(0,n.useState)(!1);return(0,t.jsx)("header",{className:"bg-white shadow-sm border-b",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(r.default,{href:"/",className:"text-2xl font-bold text-blue-600",children:"flywheel-media"})}),(0,t.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,t.jsx)(r.default,{href:"/",className:"text-gray-700 hover:text-blue-600 transition-colors",children:"Home"}),(0,t.jsx)(r.default,{href:"/about",className:"text-gray-700 hover:text-blue-600 transition-colors",children:"About Us"}),(0,t.jsx)(r.default,{href:"/services",className:"text-gray-700 hover:text-blue-600 transition-colors",children:"Our Services"}),(0,t.jsx)(r.default,{href:"/contact",className:"text-gray-700 hover:text-blue-600 transition-colors",children:"Contact Us"})]}),(0,t.jsx)("div",{className:"hidden md:block",children:(0,t.jsx)(r.default,{href:"/contact",children:(0,t.jsx)(o.Button,{children:"Get Started"})})}),(0,t.jsx)("div",{className:"md:hidden",children:(0,t.jsx)("button",{onClick:()=>a(!e),className:"text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600",children:(0,t.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e?(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"}):(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),e&&(0,t.jsx)("div",{className:"md:hidden",children:(0,t.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t",children:[(0,t.jsx)(r.default,{href:"/",className:"block px-3 py-2 text-gray-700 hover:text-blue-600",children:"Home"}),(0,t.jsx)(r.default,{href:"/about",className:"block px-3 py-2 text-gray-700 hover:text-blue-600",children:"About Us"}),(0,t.jsx)(r.default,{href:"/services",className:"block px-3 py-2 text-gray-700 hover:text-blue-600",children:"Our Services"}),(0,t.jsx)(r.default,{href:"/contact",className:"block px-3 py-2 text-gray-700 hover:text-blue-600",children:"Contact Us"}),(0,t.jsx)("div",{className:"px-3 py-2",children:(0,t.jsx)(r.default,{href:"/contact",className:"w-full",children:(0,t.jsx)(o.Button,{className:"w-full",children:"Get Started"})})})]})})]})})}e.s(["Header",()=>a])}]);