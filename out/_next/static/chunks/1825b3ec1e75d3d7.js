(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,4133,e=>{"use strict";var s=e.i(87722),a=e.i(46739),t=e.i(50222);function l(){let[e,l]=(0,a.useState)({name:"",phone:"",skype:"",email:"",comment:""}),r=s=>{l({...e,[s.target.name]:s.target.value})};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 py-20",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-16",children:[(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:"Don't hesitate to reach out"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Ready to take the next step? Get in touch with us today and let's start crafting your success story together"})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-8 md:p-12",children:(0,s.jsxs)("form",{onSubmit:s=>{s.preventDefault(),console.log("Form submitted:",e)},className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Name"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:r,placeholder:"Full Name",className:"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-2",children:"Phone No."}),(0,s.jsx)("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:r,placeholder:"Phone No.",className:"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"skype",className:"block text-sm font-medium text-gray-700 mb-2",children:"Skype"}),(0,s.jsx)("input",{type:"text",id:"skype",name:"skype",value:e.skype,onChange:r,placeholder:"Skype ID/No.",className:"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:r,placeholder:"Email",className:"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"comment",className:"block text-sm font-medium text-gray-700 mb-2",children:"Comment"}),(0,s.jsx)("textarea",{id:"comment",name:"comment",value:e.comment,onChange:r,placeholder:"Enter your comment/message...",rows:6,className:"w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-vertical",required:!0})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(t.Button,{type:"submit",size:"lg",className:"px-12 py-3",children:"Send Message"})})]})}),(0,s.jsxs)("div",{className:"mt-16 text-center",children:[(0,s.jsx)("h3",{className:"text-2xl font-semibold text-gray-900 mb-8",children:"Other Ways to Reach Us"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,s.jsx)("div",{className:"text-blue-600 text-2xl mb-4",children:"📧"}),(0,s.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Email"}),(0,s.jsx)("p",{className:"text-gray-600",children:"<EMAIL>"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,s.jsx)("div",{className:"text-blue-600 text-2xl mb-4",children:"📱"}),(0,s.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Phone"}),(0,s.jsx)("p",{className:"text-gray-600",children:"+****************"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,s.jsx)("div",{className:"text-blue-600 text-2xl mb-4",children:"💬"}),(0,s.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Live Chat"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Available 24/7"})]})]})]})]})})}e.s(["default",()=>l])}]);