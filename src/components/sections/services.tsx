import Link from 'next/link';
import { Button } from '@/components/ui/button';

const services = [
  {
    icon: '🎯',
    title: 'Tailored Affiliate Strategies',
    description:
      "We understand that one size doesn't fit all. Our team of experts crafts bespoke performance marketing strategies that align with your brand's unique goals and market dynamics, ensuring a personalized approach for success to your business.",
    features: [
      'Creating Targeted Affiliate Content',
      'Choosing the Right Affiliate Programs',
      'Proven Techniques for Effective Affiliate Marketing',
    ],
  },
  {
    icon: '📊',
    title: 'Advanced Analytics and Reporting',
    description:
      'Transparency and insight are central to our operations. Our advanced analytics platform offers real-time data and detailed reports, which helps in making it easy to track campaign performance and ROI for informed decision-making',
    features: [
      'Data-Driven Insights for Strategic Growth',
      'Mastering Data-Driven Decision Making',
      'Leveraging Analytics for Continuous Improvement',
    ],
  },
  {
    icon: '🌐',
    title: 'Global Network',
    description:
      'Leverage our extensive global network to reach audiences beyond borders. With partners spanning multiple industries and regions, your brand gains unparalleled exposure and the ability to tap into new, lucrative markets.',
    features: [
      'Leverage a Global Network for Success',
      'Partnerships for Enhanced Growth',
      'Maximizing Opportunities Through Network Leverage',
    ],
  },
  {
    icon: '🚀',
    title: 'Innovative Technology Solutions',
    description:
      'Stay ahead of the curve with our cutting-edge adtech solutions. We equip your campaigns with the tools they need to succeed in a competitive digital landscape.',
  },
  {
    icon: '🔍',
    title: 'Transparency & Accountability',
    description:
      "You'll always be in the loop with comprehensive reports on your program's performance. We maintain full transparency so you can see exactly how your campaigns are performing.",
  },
  {
    icon: '👥',
    title: 'Dedicated Support Team',
    description:
      'Success is a team effort. Our experienced dedicated account managers work closely with you to ensure your campaigns are running smoothly and optimally.',
  },
];

export function Services() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-sm font-semibold text-blue-600 uppercase tracking-wide mb-2">
            Our main services
          </h2>
          <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Our solutions that help you grow up
          </h3>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className="bg-white rounded-lg border border-gray-200 p-8 hover:shadow-lg transition-shadow duration-300"
            >
              <div className="text-4xl mb-4">{service.icon}</div>
              <h4 className="text-xl font-semibold text-gray-900 mb-4">
                {service.title}
              </h4>
              <p className="text-gray-600 mb-6">{service.description}</p>

              {service.features && (
                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <li
                      key={featureIndex}
                      className="text-sm text-gray-500 flex items-start"
                    >
                      <span className="text-blue-500 mr-2">•</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              )}

              <Link href="/contact" className="w-full">
                <Button variant="outline" className="w-full">
                  Get Started
                </Button>
              </Link>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
