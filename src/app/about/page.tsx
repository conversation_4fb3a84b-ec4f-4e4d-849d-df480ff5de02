import Link from 'next/link';
import { Button } from '@/components/ui/button';

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              A little about us
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Unleashing the power of Affiliate Marketing tactics
            </p>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-blue-600">01</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Tailored Affiliate Strategies
              </h3>
              <p className="text-gray-600">
                Optimizing your website for each of the main components to put a
                good and fit in house strategy in place.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-blue-600">02</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Building your site
              </h3>
              <p className="text-gray-600">
                Optimizing your website for each of the main components to put a
                good and fit in house strategy in place.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold text-blue-600">03</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Promoting your site
              </h3>
              <p className="text-gray-600">
                Optimizing your website for each of the main components to put a
                good and fit in house strategy in place.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="bg-gray-50 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-8">
            Our Mission
          </h2>
          <p className="text-xl text-gray-600 mb-8">
            It's time to shift the focus back to the creators. Let's work
            together to ensure that quality content is recognized and rewarded.
            With flywheel-media, you can transform your passion into profit
            while making a positive impact.
          </p>
          <Link href="/contact">
            <Button size="lg">Get Started Today</Button>
          </Link>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">2k</div>
              <div className="text-gray-600">Project Done</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">2k+</div>
              <div className="text-gray-600">Satisfied Clients</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">48K</div>
              <div className="text-gray-600">Supported locations</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">2024</div>
              <div className="text-gray-600">Year founded</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            It's time to tell the world about it
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            It's time to tell the world about what you do. Let's work together
            to share your story in a way that captivates and inspires.
          </p>
          <Link href="/contact">
            <Button variant="secondary" size="lg">
              Start Your Journey
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
}
