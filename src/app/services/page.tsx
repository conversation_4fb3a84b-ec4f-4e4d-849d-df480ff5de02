import Link from 'next/link';
import { Button } from '@/components/ui/button';

const services = [
  {
    icon: '🎯',
    title: 'Strategic Affiliate Partnerships',
    description:
      'Unlock growth with tailored affiliate partnerships that enhance visibility and drive customer engagement.',
    features: [
      'Partner Network Development',
      'Performance Tracking',
      'Revenue Optimization',
      'Strategic Planning',
    ],
  },
  {
    icon: '📊',
    title: 'Affiliate Marketing',
    description:
      'At flywheel-media, we empower brands through strategic affiliate marketing, connecting them with ideal customers for meaningful growth.',
    features: [
      'Campaign Management',
      'Audience Targeting',
      'Conversion Optimization',
      'ROI Analysis',
    ],
  },
  {
    icon: '🌐',
    title: 'Global Network Access',
    description:
      'Leverage our extensive global network to reach audiences beyond borders with partners spanning multiple industries.',
    features: [
      'International Reach',
      'Multi-Industry Partners',
      'Cross-Border Solutions',
      'Market Expansion',
    ],
  },
  {
    icon: '🚀',
    title: 'Technology Solutions',
    description:
      'Stay ahead with our cutting-edge adtech solutions designed for competitive digital landscapes.',
    features: [
      'Advanced Analytics',
      'Real-time Tracking',
      'Automated Optimization',
      'Custom Integrations',
    ],
  },
  {
    icon: '🔍',
    title: 'Transparency & Reporting',
    description:
      'Comprehensive reports and full transparency so you can see exactly how your campaigns perform.',
    features: [
      'Detailed Analytics',
      'Performance Metrics',
      'Custom Reports',
      'Real-time Data',
    ],
  },
  {
    icon: '👥',
    title: 'Dedicated Support',
    description:
      'Our experienced account managers work closely with you to ensure optimal campaign performance.',
    features: [
      'Personal Account Manager',
      '24/7 Support',
      'Strategic Consultation',
      'Ongoing Optimization',
    ],
  },
];

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Our Services
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Comprehensive affiliate marketing solutions designed to drive
              growth and maximize your business potential
            </p>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div
                key={index}
                className="bg-white rounded-lg border border-gray-200 p-8 hover:shadow-lg transition-shadow duration-300"
              >
                <div className="text-4xl mb-4">{service.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {service.title}
                </h3>
                <p className="text-gray-600 mb-6">{service.description}</p>

                <ul className="space-y-2 mb-6">
                  {service.features.map((feature, featureIndex) => (
                    <li
                      key={featureIndex}
                      className="text-sm text-gray-500 flex items-start"
                    >
                      <span className="text-blue-500 mr-2">✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>

                <Link href="/contact" className="w-full">
                  <Button variant="outline" className="w-full">
                    Get Started
                  </Button>
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-blue-600 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Let's discuss how our services can help accelerate your business
            growth through strategic affiliate marketing.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button variant="secondary" size="lg">
                Contact Us Today
              </Button>
            </Link>
            <Link href="/about">
              <Button
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white hover:text-blue-600"
              >
                Learn More About Us
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
